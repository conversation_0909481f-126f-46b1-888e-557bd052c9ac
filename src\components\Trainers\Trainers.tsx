import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import TrainerGridCard from "@/components/TrainerGridCard/TrainerGridCard";

interface Trainer {
  id: string;
  name: string;
  description: string;
  image: string;
  rating: number;
  startingPrice: number;
  isFavorite?: boolean;
}

interface TrainersProps {
  trainers?: Trainer[];
  onFavoriteToggle?: (trainerId: string, isFavorite: boolean) => void;
}

const sampleTrainers: Trainer[] = [
  {
    id: "1",
    name: "<PERSON>",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1566753323558-f4e0952af115?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: false,
  },
  {
    id: "2",
    name: "<PERSON>",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: true,
  },
  {
    id: "3",
    name: "James Parker",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: false,
  },
  {
    id: "4",
    name: "Maya Patel",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: false,
  },
  {
    id: "5",
    name: "Carlos Santos",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: true,
  },
  {
    id: "6",
    name: "Rachel Green",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: false,
  },
  {
    id: "7",
    name: "Kevin Wong",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1567013127542-490d757e51cd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: false,
  },
  {
    id: "8",
    name: "Amanda Foster",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1550345332-09e3ac987658?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: true,
  },
];

const Trainers = ({
  trainers = sampleTrainers,
  onFavoriteToggle,
}: TrainersProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <div className="w-full">
      {/* Section Title */}
      <h2
        className="text-2xl font-bold mb-8 transition-colors duration-200"
        style={{ color: THEME_COLORS[mode].TEXT }}
      >
        All Trainers
      </h2>

      {/* Trainers Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-6">
        {trainers.map((trainer) => (
          <TrainerGridCard
            key={trainer.id}
            trainer={trainer}
            onFavoriteToggle={onFavoriteToggle}
          />
        ))}
      </div>
    </div>
  );
};

export default Trainers;
// const data = {
//   full_name: "Benjamin  Possible",
//   gender: "Man",
//   bio: "toughest",
//   years_of_experience: "3-5 years",
//   qualifications: [
//     "Certified Personal Trainer",
//     "Nutrition and Dietetics Certification",
//     "Corrective Exercise Specialist",
//   ],
//   specializations: ["Body Building", "Cross Fit", "Calisthenics"],
//   email_notifications: true,
//   in_app_notifications: true,
//   photo: "https://images.unsplash.com/photo-1566753323558-f4e0952af115?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
// };
