import React from "react";
import { MkdInputV2 } from "@/components/MkdInputV2";

interface CourseDetailsSectionProps {
  stepOneData?: any;
  stepTwoData?: any;
  selectedSplit?: string;
  onSplitChange?: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  availableSplits?: Array<{
    value: string;
    label: string;
    split_id: string;
  }>;
}

const CourseDetailsSection: React.FC<CourseDetailsSectionProps> = ({
  stepOneData,
  stepTwoData,
  selectedSplit,
  onSplitChange,
  availableSplits = [],
}) => {
  // Helper functions to format data
  const formatDuration = () => {
    if (!stepTwoData?.splitConfigurations) return "N/A";

    // Calculate total weeks from all splits
    const allWeeks = Object.values(
      stepTwoData.splitConfigurations
    ).flat() as any[];
    const totalWeeks = allWeeks.length;
    return totalWeeks > 0
      ? `${totalWeeks} Week${totalWeeks !== 1 ? "s" : ""}`
      : "N/A";
  };

  const formatTargetLevels = () => {
    if (!stepOneData?.target_levels || stepOneData.target_levels.length === 0)
      return "N/A";
    return stepOneData.target_levels.join(", ");
  };

  const formatCommunication = () => {
    const settings = [];
    if (stepOneData?.allow_comments) settings.push("Comments");
    if (stepOneData?.allow_private_messages) settings.push("Private messages");

    if (settings.length === 0) return "Not allowed";
    return `${settings.join(" and ")} allowed`;
  };

  const getCurrentSplit = () => {
    if (!selectedSplit || !stepOneData?.splits) return null;
    return stepOneData.splits.find(
      (split: any) => split.split_id === selectedSplit
    );
  };

  const currentSplit = getCurrentSplit();

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-medium text-text">Course Details</h2>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
        {/* Left Column */}
        <div className="space-y-6">
          {/* Course Description */}
          <div className="space-y-2">
            <h3 className="text-base font-medium text-text">Description</h3>
            <p className="text-sm text-text-secondary leading-relaxed">
              {stepOneData?.program_description || "No description provided"}
            </p>
          </div>

          {/* Split Selection */}
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
              <span className="text-base text-text">Split</span>
              {availableSplits.length > 1 ? (
                <div className="w-full sm:w-auto sm:min-w-[144px]">
                  <MkdInputV2
                    name="splitSelection"
                    type="dropdown"
                    uniqueKey="split_id"
                    display="label"
                    options={availableSplits}
                    value={selectedSplit}
                    onChange={onSplitChange}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Field
                        placeholder="Select split..."
                        className="text-base"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>
              ) : (
                <div className="flex items-center gap-2 bg-input border border-border rounded-md px-3 py-2 min-w-[144px] w-full sm:w-auto">
                  <span className="text-base text-text flex-1 truncate">
                    {currentSplit?.title || "Split Name 1"}
                  </span>
                </div>
              )}
            </div>

            {/* Days for Preview */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
              <span className="text-base text-text">Days for Preview</span>
              <div className="flex items-center gap-2 justify-end">
                <div className="bg-input border border-border rounded-md px-3 py-2 min-w-[48px] text-center">
                  <span className="text-base text-text">
                    {stepOneData?.days_for_preview || 3}
                  </span>
                </div>
                <div className="flex flex-col gap-1">
                  <button className="w-4 h-4 flex items-center justify-center hover:bg-background-hover rounded">
                    <div className="w-2 h-0.5 bg-text-secondary"></div>
                  </button>
                  <button className="w-4 h-4 flex items-center justify-center hover:bg-background-hover rounded">
                    <div className="w-2 h-0.5 bg-text-secondary"></div>
                  </button>
                </div>
              </div>
            </div>
            <p className="text-xs text-text-secondary text-right">
              Min 1, Max 7
            </p>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
            <span className="text-base text-text">Duration</span>
            <span className="text-base text-text font-medium">
              {formatDuration()}
            </span>
          </div>

          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
            <span className="text-base text-text">Tracking</span>
            <span className="text-base text-text font-medium">
              {stepOneData?.track_progress ? "Allowed" : "Not allowed"}
            </span>
          </div>

          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
            <span className="text-base text-text">Level</span>
            <span className="text-base text-text font-medium">
              {formatTargetLevels()}
            </span>
          </div>

          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
            <span className="text-base text-text">Category</span>
            <span className="text-base text-text font-medium">
              {stepOneData?.type_of_program || "N/A"}
            </span>
          </div>

          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
            <span className="text-base text-text">Enrolled Athletes</span>
            <span className="text-base text-text font-medium">0</span>
          </div>

          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
            <span className="text-base text-text">Communication</span>
            <span className="text-base text-text font-medium text-right sm:text-left">
              {formatCommunication()}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseDetailsSection;
