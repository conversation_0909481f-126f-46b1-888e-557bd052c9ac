import React, { useState, forwardRef, useImperativeHandle } from "react";

interface MkdWizardContainerProps {
  children: React.ReactNode;
  className?: string;
  showButton?: boolean;
  onStepChange?: (activeId: number, activeIndex: number) => void;
}

export interface MkdWizardContainerRef {
  goToNext: () => boolean;
  goToPrevious: () => boolean;
  goToStep: (stepId: number) => boolean;
  getCurrentStep: () => { activeId: number; activeIndex: number };
  canGoNext: () => boolean;
  canGoPrevious: () => boolean;
}

const MkdWizardContainer = forwardRef<
  MkdWizardContainerRef,
  MkdWizardContainerProps
>(({ children, className, showButton = true, onStepChange }, ref) => {
  const [activeId, setActiveId] = useState(1);

  const childrenArray = React.Children.toArray(
    children
  ) as React.ReactElement[];

  const activeIndex = childrenArray.findIndex(
    (child) => child.props?.componentId === activeId
  );

  const handlePreviousClick = () => {
    const newIndex = activeIndex - 1;
    if (newIndex >= 0) {
      const newActiveId = childrenArray[newIndex].props.componentId;
      setActiveId(newActiveId);
      onStepChange?.(newActiveId, newIndex);
      return true;
    }
    return false;
  };

  const handleNextClick = () => {
    const newIndex = activeIndex + 1;
    if (newIndex < childrenArray.length) {
      const newActiveId = childrenArray[newIndex].props.componentId;
      setActiveId(newActiveId);
      onStepChange?.(newActiveId, newIndex);
      return true;
    }
    return false;
  };

  const goToStep = (stepId: number) => {
    const targetIndex = childrenArray.findIndex(
      (child) => child.props?.componentId === stepId
    );
    if (targetIndex !== -1) {
      setActiveId(stepId);
      onStepChange?.(stepId, targetIndex);
      return true;
    }
    return false;
  };

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    goToNext: handleNextClick,
    goToPrevious: handlePreviousClick,
    goToStep,
    getCurrentStep: () => ({ activeId, activeIndex }),
    canGoNext: () => activeIndex < childrenArray.length - 1,
    canGoPrevious: () => activeIndex > 0,
  }));

  return (
    <div className={`flex w-full h-full flex-col items-center ${className}`}>
      <div className="component-wrapper w-full flex-1 overflow-auto">
        {childrenArray.map((child) =>
          child.props.componentId === activeId ? child : null
        )}
      </div>

      <div
        className={`w-full items-center justify-between flex-shrink-0 ${
          showButton ? "flex" : "hidden"
        }`}
      >
        <button
          className="rounded-md bg-primary hover:bg-primary-hover active:bg-primary-active disabled:bg-primary-disabled disabled:cursor-not-allowed px-6 py-2 text-white transition-colors duration-200"
          onClick={handlePreviousClick}
          disabled={activeIndex === 0}
        >
          Previous
        </button>
        <button
          className="rounded-md bg-primary hover:bg-primary-hover active:bg-primary-active disabled:bg-primary-disabled disabled:cursor-not-allowed px-6 py-2 text-white transition-colors duration-200"
          onClick={handleNextClick}
          disabled={activeIndex === childrenArray.length - 1}
        >
          Next
        </button>
      </div>
    </div>
  );
});

export default MkdWizardContainer;
