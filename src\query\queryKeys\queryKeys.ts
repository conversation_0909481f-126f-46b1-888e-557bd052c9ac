const queryKeys = {
  project: {
    all: "project:all",
    byId: "project:byId",
    byName: "project:byName",
    bySlug: "project:bySlug",
    create: "project:create",
    update: "project:update",
    delete: "project:delete",
    list: "project:list",
    many: "project:many",
    paginate: "project:paginate",
  },
  user: {
    all: "user:all",
    byId: "user:byId",
    byName: "user:byName",
    bySlug: "user:bySlug",
    create: "user:create",
    update: "user:update",
    delete: "user:delete",
    list: "user:list",
    many: "user:many",
    paginate: "user:paginate",
  },
  program: {
    all: "program:all",
    byId: "program:byId",
    byName: "program:byName",
    bySlug: "program:bySlug",
    create: "program:create",
    update: "program:update",
    delete: "program:delete",
    list: "program:list",
    many: "program:many",
    paginate: "program:paginate",
    byTrainer: "program:byTrainer",
    published: "program:published",
    drafts: "program:drafts",
  },
  trainer: {
    all: "trainer:all",
    byId: "trainer:byId",
    byName: "trainer:byName",
    bySlug: "trainer:bySlug",
    create: "trainer:create",
    update: "trainer:update",
    delete: "trainer:delete",
    list: "trainer:list",
    many: "trainer:many",
    paginate: "trainer:paginate",
    profile: "trainer:profile",
    stats: "trainer:stats",
  },
  athlete: {
    all: "athlete:all",
    byId: "athlete:byId",
    byName: "athlete:byName",
    bySlug: "athlete:bySlug",
    create: "athlete:create",
    update: "athlete:update",
    delete: "athlete:delete",
    list: "athlete:list",
    many: "athlete:many",
    paginate: "athlete:paginate",
    profile: "athlete:profile",
  },
  split: {
    all: "split:all",
    byId: "split:byId",
    byProgram: "split:byProgram",
    create: "split:create",
    update: "split:update",
    delete: "split:delete",
    list: "split:list",
    many: "split:many",
    paginate: "split:paginate",
    pricing: "split:pricing",
    eligibility: "split:eligibility",
  },
  week: {
    all: "week:all",
    byId: "week:byId",
    bySplit: "week:bySplit",
    create: "week:create",
    update: "week:update",
    delete: "week:delete",
    list: "week:list",
    many: "week:many",
    paginate: "week:paginate",
  },
  day: {
    all: "day:all",
    byId: "day:byId",
    byWeek: "day:byWeek",
    create: "day:create",
    update: "day:update",
    delete: "day:delete",
    list: "day:list",
    many: "day:many",
    paginate: "day:paginate",
  },
  session: {
    all: "session:all",
    byId: "session:byId",
    byDay: "session:byDay",
    create: "session:create",
    update: "session:update",
    delete: "session:delete",
    list: "session:list",
    many: "session:many",
    paginate: "session:paginate",
  },
  exercise: {
    all: "exercise:all",
    byId: "exercise:byId",
    byName: "exercise:byName",
    byType: "exercise:byType",
    create: "exercise:create",
    update: "exercise:update",
    delete: "exercise:delete",
    list: "exercise:list",
    many: "exercise:many",
    paginate: "exercise:paginate",
    library: "exercise:library",
  },
  exerciseInstance: {
    all: "exerciseInstance:all",
    byId: "exerciseInstance:byId",
    bySession: "exerciseInstance:bySession",
    create: "exerciseInstance:create",
    update: "exerciseInstance:update",
    delete: "exerciseInstance:delete",
    list: "exerciseInstance:list",
    many: "exerciseInstance:many",
    paginate: "exerciseInstance:paginate",
  },
  video: {
    all: "video:all",
    byId: "video:byId",
    byName: "video:byName",
    byType: "video:byType",
    create: "video:create",
    update: "video:update",
    delete: "video:delete",
    list: "video:list",
    many: "video:many",
    paginate: "video:paginate",
    library: "video:library",
  },
  enrollment: {
    all: "enrollment:all",
    byId: "enrollment:byId",
    byAthlete: "enrollment:byAthlete",
    byTrainer: "enrollment:byTrainer",
    byProgram: "enrollment:byProgram",
    bySplit: "enrollment:bySplit",
    create: "enrollment:create",
    update: "enrollment:update",
    delete: "enrollment:delete",
    cancel: "enrollment:cancel",
    list: "enrollment:list",
    many: "enrollment:many",
    paginate: "enrollment:paginate",
    status: "enrollment:status",
    active: "enrollment:active",
    expired: "enrollment:expired",
  },
  discount: {
    all: "discount:all",
    byId: "discount:byId",
    byProgram: "discount:byProgram",
    bySplit: "discount:bySplit",
    create: "discount:create",
    update: "discount:update",
    delete: "discount:delete",
    list: "discount:list",
    many: "discount:many",
    paginate: "discount:paginate",
    active: "discount:active",
  },
  coupon: {
    all: "coupon:all",
    byId: "coupon:byId",
    byCode: "coupon:byCode",
    byProgram: "coupon:byProgram",
    create: "coupon:create",
    update: "coupon:update",
    delete: "coupon:delete",
    list: "coupon:list",
    many: "coupon:many",
    paginate: "coupon:paginate",
    validate: "coupon:validate",
    usage: "coupon:usage",
  },
  couponUsage: {
    all: "couponUsage:all",
    byId: "couponUsage:byId",
    byCoupon: "couponUsage:byCoupon",
    byUser: "couponUsage:byUser",
    byProgram: "couponUsage:byProgram",
    create: "couponUsage:create",
    list: "couponUsage:list",
    many: "couponUsage:many",
    paginate: "couponUsage:paginate",
  },
  programDiscount: {
    all: "programDiscount:all",
    byId: "programDiscount:byId",
    byProgram: "programDiscount:byProgram",
    create: "programDiscount:create",
    update: "programDiscount:update",
    delete: "programDiscount:delete",
    list: "programDiscount:list",
    many: "programDiscount:many",
    paginate: "programDiscount:paginate",
    preview: "programDiscount:preview",
  },
  postFeed: {
    all: "postFeed:all",
    byId: "postFeed:byId",
    byUser: "postFeed:byUser",
    byProgram: "postFeed:byProgram",
    bySplit: "postFeed:bySplit",
    byType: "postFeed:byType",
    create: "postFeed:create",
    update: "postFeed:update",
    delete: "postFeed:delete",
    list: "postFeed:list",
    many: "postFeed:many",
    paginate: "postFeed:paginate",
    pinned: "postFeed:pinned",
    flagged: "postFeed:flagged",
  },
  comment: {
    all: "comment:all",
    byId: "comment:byId",
    byPost: "comment:byPost",
    byUser: "comment:byUser",
    create: "comment:create",
    update: "comment:update",
    delete: "comment:delete",
    list: "comment:list",
    many: "comment:many",
    paginate: "comment:paginate",
    replies: "comment:replies",
  },
  reaction: {
    all: "reaction:all",
    byId: "reaction:byId",
    byTarget: "reaction:byTarget",
    byUser: "reaction:byUser",
    create: "reaction:create",
    update: "reaction:update",
    delete: "reaction:delete",
    list: "reaction:list",
    many: "reaction:many",
    paginate: "reaction:paginate",
    toggle: "reaction:toggle",
  },
  programReview: {
    all: "programReview:all",
    byId: "programReview:byId",
    byProgram: "programReview:byProgram",
    byUser: "programReview:byUser",
    byRating: "programReview:byRating",
    create: "programReview:create",
    update: "programReview:update",
    delete: "programReview:delete",
    list: "programReview:list",
    many: "programReview:many",
    paginate: "programReview:paginate",
    stats: "programReview:stats",
  },
  notification: {
    all: "notification:all",
    byId: "notification:byId",
    byUser: "notification:byUser",
    byType: "notification:byType",
    create: "notification:create",
    update: "notification:update",
    delete: "notification:delete",
    list: "notification:list",
    many: "notification:many",
    paginate: "notification:paginate",
    unread: "notification:unread",
    markRead: "notification:markRead",
  },
  activity: {
    all: "activity:all",
    byId: "activity:byId",
    byUser: "activity:byUser",
    byType: "activity:byType",
    byProgram: "activity:byProgram",
    create: "activity:create",
    update: "activity:update",
    delete: "activity:delete",
    list: "activity:list",
    many: "activity:many",
    paginate: "activity:paginate",
    recent: "activity:recent",
  },
  dashboardStats: {
    all: "dashboardStats:all",
    byUser: "dashboardStats:byUser",
    byTrainer: "dashboardStats:byTrainer",
    byPeriod: "dashboardStats:byPeriod",
    revenue: "dashboardStats:revenue",
    programs: "dashboardStats:programs",
    athletes: "dashboardStats:athletes",
    reviews: "dashboardStats:reviews",
  },
};

export default queryKeys;
