import { useQuery } from "@tanstack/react-query";
import { queryKeys } from "../queryKeys";
import { useSDK } from "@/hooks/useSDK";
import { TreeSDKOptions, ApiResponse } from "@/utils/TreeSDK";
import { getOfflineQueryOptions } from "../offline/offlineQueryClient";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";

// Extended interface for offline-aware API responses
interface OfflineAwareApiResponse<T = any> extends ApiResponse<T> {
  _offline?: boolean;
}

export const useGetPaginateQuery = (
  table: string,
  options?: TreeSDKOptions,
  role?: any,
  config?: {
    enabled?: boolean;
    enableOfflineCache?: boolean;
    showToast?: boolean;
    [key: string]: any;
  }
) => {
  const { tdk, isOfflineMode } = useSDK({ role });
  const { tokenExpireError, showToast } = useContexts();

  const queryFn = async (table: string, options?: TreeSDKOptions) => {
    try {
      const response = (await tdk.getPaginate(
        table,
        options
      )) as OfflineAwareApiResponse;

      return {
        data: response?.list ?? response?.data ?? response?.model,
        total: response?.total,
        limit: response?.limit,
        num_pages: response?.num_pages,
        page: response?.page,
        _offline: response?._offline || false,
      };
    } catch (error: any) {
      console.error("Error fetching data:", error);
      const message =
        error?.response?.data?.message || error?.message || "An error occurred";
      if (config?.showToast) {
        showToast(message, 5000, ToastStatusEnum.ERROR);
      }
      tokenExpireError(message);
      throw error;
    }
  };

  // Get offline-aware query options
  const offlineOptions = isOfflineMode
    ? getOfflineQueryOptions(undefined, {
        enableOfflineCache: config?.enableOfflineCache,
      })
    : {};

  return useQuery({
    queryKey: [(queryKeys as any)?.[table]?.paginate, table, options],
    queryFn: () => queryFn(table, options),
    ...offlineOptions,
    ...config,
  });
};

export const useGetListQuery = (
  table: string,
  options?: TreeSDKOptions,
  role?: string,
  config?: {
    enabled?: boolean;
    enableOfflineCache?: boolean;
    showToast?: boolean;
    [key: string]: any;
  }
) => {
  const { tdk, isOfflineMode } = useSDK({ role });
  const { tokenExpireError, showToast } = useContexts();

  const queryFn = async (table: string, options?: TreeSDKOptions) => {
    try {
      const response = (await tdk.getList(
        table,
        options
      )) as OfflineAwareApiResponse;

      return {
        data: response?.list ?? response?.data ?? response?.model,
        total: response?.total,
        limit: response?.limit,
        num_pages: response?.num_pages,
        page: response?.page,
        _offline: response?._offline || false,
      };
    } catch (error: any) {
      console.error("Error fetching data:", error);
      const message =
        error?.response?.data?.message || error?.message || "An error occurred";
      if (config?.showToast) {
        showToast(message, 5000, ToastStatusEnum.ERROR);
      }
      tokenExpireError(message);
      throw error;
    }
  };

  // Get offline-aware query options
  const offlineOptions = isOfflineMode
    ? getOfflineQueryOptions(undefined, {
        enableOfflineCache: config?.enableOfflineCache,
      })
    : {};

  return useQuery({
    queryKey: [(queryKeys as any)?.[table]?.list, table, options],
    enabled: !!table,
    queryFn: () => queryFn(table, options),
    ...offlineOptions,
    ...config,
  });
};

export const useGetManyQuery = (
  table: string,
  ids: number | string | (number | string)[],
  role?: string,
  options?: TreeSDKOptions,
  config?: {
    enabled?: boolean;
    enableOfflineCache?: boolean;
    showToast?: boolean;
    [key: string]: any;
  }
) => {
  const { tdk } = useSDK({ role });
  const { tokenExpireError, showToast } = useContexts();

  const queryFn = async (
    table: string,
    ids: number | string | (number | string)[],
    options?: TreeSDKOptions
  ) => {
    try {
      const response = await tdk.getMany(table, ids, options);

      return {
        data: response?.list ?? response?.data ?? response?.model,
        total: response?.total,
        limit: response?.limit,
        num_pages: response?.num_pages,
        page: response?.page,
      };
    } catch (error: any) {
      console.error("Error fetching data:", error);
      const message =
        error?.response?.data?.message || error?.message || "An error occurred";
      if (config?.showToast) {
        showToast(message, 5000, ToastStatusEnum.ERROR);
      }
      tokenExpireError(message);
      throw error;
    }
  };

  return useQuery({
    queryKey: [(queryKeys as any)?.[table]?.many, table, ids, options],
    enabled: !!table && !!ids,
    ...config,
    queryFn: () => queryFn(table, ids, options),
  });
};
