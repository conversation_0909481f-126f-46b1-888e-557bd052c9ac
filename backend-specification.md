# Comprehensive Backend Specification for Fitness Platform

## Table of Contents

1. [Application Overview](#application-overview)
2. [User Management & Authentication APIs](#user-management--authentication-apis)
3. [Program Management APIs](#program-management-apis)
4. [Transaction & Payment APIs](#transaction--payment-apis)
5. [Content & Media Management APIs](#content--media-management-apis)
6. [Communication & Social Features APIs](#communication--social-features-apis)
7. [Admin Management & Analytics APIs](#admin-management--analytics-apis)
8. [Data Models & Database Schema](#data-models--database-schema)
9. [Business Logic & Workflows](#business-logic--workflows)

---

## Application Overview

### Platform Architecture

This is a comprehensive fitness platform with three main user roles:

- **Athletes/Members**: End users who purchase and follow workout programs
- **Trainers**: Content creators who design and sell workout programs
- **Admins/Super Admins**: Platform administrators who manage users, content, and transactions

### Core Features

- Multi-role authentication system with role-based access control
- Workout program creation with hierarchical structure (Programs → Splits → Weeks → Days → Sessions → Exercises)
- Payment processing with one-time and subscription models
- Content management with approval workflows
- Social features including feeds, comments, and messaging
- Exercise library with video content
- Transaction management and refund processing
- Analytics and reporting dashboards

### Technology Stack Requirements

- RESTful API architecture
- JWT-based authentication
- File upload and media storage
- Payment gateway integration (Stripe)
- Real-time notifications
- Offline-capable data synchronization

---

## User Management & Authentication APIs

### Base URL

```
https://api.kanglink.com/v2/api/kanglink
```

### Authentication Endpoints

#### 1. User Registration

**Athlete Registration**

```
POST /auth/register/athlete
```

**Request Body:**

```json
{
  "fullName": "John Doe",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "confirmPassword": "securePassword123",
  "dob": "1990-05-15",
  "level": "beginner", // "beginner", "intermediate", "advanced"
  "fitnessGoals": "Weight Loss", // from predefined list
  "terms": true,
  "profilePicture": null, // optional file upload
  "socialProviders": {
    // optional
    "facebook": "facebook_profile_url",
    "instagram": "instagram_handle",
    "linkedin": "linkedin_profile"
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "fullName": "John Doe",
      "role": "member",
      "status": "active",
      "profileComplete": false,
      "createdAt": "2024-01-15T10:30:00Z"
    },
    "token": "jwt_token_here",
    "refreshToken": "refresh_token_here",
    "expiresIn": 3600
  }
}
```

**Trainer Registration**

```
POST /auth/register/trainer
```

**Request Body:**

```json
{
  "fullName": "Jane Smith",
  "email": "<EMAIL>",
  "phone": "+**********",
  "password": "securePassword123",
  "confirmPassword": "securePassword123",
  "yearsOfExperience": "3-5 years",
  "gender": "Female",
  "qualifications": [
    "Certified Personal Trainer",
    "Nutrition and Dietetics Certification"
  ],
  "specializations": ["Body Building", "Strength Training"],
  "bio": "Experienced trainer with focus on strength training...",
  "profilePicture": null, // file upload
  "socialLinks": {
    "facebook": "facebook_url",
    "instagram": "instagram_url",
    "linkedin": "linkedin_url"
  },
  "terms": true,
  "privacyPolicy": true
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "trainer_456",
      "email": "<EMAIL>",
      "fullName": "Jane Smith",
      "role": "trainer",
      "status": "pending_approval",
      "profileComplete": true,
      "createdAt": "2024-01-15T10:30:00Z"
    },
    "token": "jwt_token_here",
    "refreshToken": "refresh_token_here",
    "expiresIn": 3600,
    "approvalRequired": true,
    "estimatedApprovalTime": "2-3 business days"
  }
}
```

#### 2. User Login

**Standard Login**

```
POST /auth/login
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "userPassword",
  "remember_me": false,
  "role": "member" // optional: "member", "trainer", "admin"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "fullName": "John Doe",
      "role": "member",
      "status": "active",
      "profileImage": "https://cdn.example.com/profiles/user_123.jpg",
      "lastLoginAt": "2024-01-15T10:30:00Z",
      "profileComplete": true
    },
    "token": "jwt_token_here",
    "refreshToken": "refresh_token_here",
    "expiresIn": 3600,
    "permissions": ["read_programs", "purchase_programs", "access_feed"]
  }
}
```

**Admin Login**

```
POST /auth/admin/login
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "adminPassword",
  "remember_me": false
}
```

#### 3. Magic Link Authentication

**Request Magic Link**

```
POST /auth/magic-link/request
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "redirectUrl": "https://app.example.com/dashboard"
}
```

**Verify Magic Link**

```
POST /auth/magic-link/verify
```

**Request Body:**

```json
{
  "token": "magic_link_token",
  "email": "<EMAIL>"
}
```

#### 4. Password Management

**Forgot Password**

```
POST /auth/forgot-password
```

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

**Reset Password**

```
POST /auth/reset-password
```

**Request Body:**

```json
{
  "token": "reset_token",
  "email": "<EMAIL>",
  "newPassword": "newSecurePassword",
  "confirmPassword": "newSecurePassword"
}
```

**Change Password**

```
POST /auth/change-password
```

**Request Body:**

```json
{
  "currentPassword": "currentPassword",
  "newPassword": "newSecurePassword",
  "confirmPassword": "newSecurePassword"
}
```

#### 5. Token Management

**Refresh Token**

```
POST /auth/refresh
```

**Request Body:**

```json
{
  "refreshToken": "refresh_token_here"
}
```

**Logout**

```
POST /auth/logout
```

**Request Body:**

```json
{
  "refreshToken": "refresh_token_here"
}
```

#### 6. Two-Factor Authentication

**Enable 2FA**

```
POST /auth/2fa/enable
```

**Verify 2FA**

```
POST /auth/2fa/verify
```

**Request Body:**

```json
{
  "code": "123456",
  "backupCode": "backup_code_if_needed"
}
```

### User Profile Management

#### 1. Get User Profile

**Get Own Profile**

```
GET /users/profile
```

**Get User by ID**

```
GET /users/{userId}
```

#### 2. Update User Profile

**Update Profile**

```
PUT /users/profile
```

**Request Body:**

```json
{
  "fullName": "Updated Name",
  "bio": "Updated bio",
  "profilePicture": null, // file upload
  "socialLinks": {
    "facebook": "updated_facebook_url"
  },
  "preferences": {
    "notifications": {
      "email": true,
      "push": false,
      "sms": false
    },
    "privacy": {
      "profileVisibility": "public", // "public", "private", "friends"
      "showProgress": true
    }
  }
}
```

#### 3. User Status Management

**Update User Status (Admin only)**

```
POST /users/{userId}/status
```

**Request Body:**

```json
{
  "status": "suspended", // "active", "suspended", "banned"
  "reason": "Violation of terms",
  "duration": "30d", // optional
  "notifyUser": true
}
```

### Role-Based Access Control

#### Permission System

- **Members/Athletes**:
  - View and purchase programs
  - Access owned content
  - Participate in feeds and messaging
  - Track progress
- **Trainers**:
  - All member permissions
  - Create and manage programs
  - View earnings and analytics
  - Manage athlete interactions
- **Admins**:
  - All platform access
  - User management
  - Content moderation
  - Financial oversight
  - System analytics

#### Authorization Headers

```
Authorization: Bearer {jwt_token}
X-User-Role: {user_role}
X-User-ID: {user_id}
```

---

## Program Management APIs

### Base URL

```
https://api.kanglink.com/v2/api/kanglink/custom/trainer
```

### Program Creation & Management

#### 1. Create Program (Multi-Step Process)

**Save Program Draft (Step 1)**

```
POST /programs/draft
```

**Request Body:**

```json
{
  "stepOneData": {
    "programName": "Advanced Fitness Program",
    "typeOfProgram": "Body building", // predefined categories
    "programDescription": "Comprehensive fitness program description",
    "paymentPlan": ["oneTime", "monthly"], // payment options
    "trackProgress": true,
    "allowComments": true,
    "allowPrivateMessages": false,
    "targetLevels": ["intermediate", "expert"], // "beginner", "intermediate", "expert"
    "splitProgram": 2, // number of splits
    "splits": [
      {
        "split_id": "split-1",
        "title": "Upper Body",
        "fullPrice": 99.99,
        "subscription": 19.99
      },
      {
        "split_id": "split-2",
        "title": "Lower Body",
        "fullPrice": 89.99,
        "subscription": 17.99
      }
    ],
    "currency": "USD",
    "daysForPreview": 7 // free preview period
  }
}
```

**Complete Program Creation (Step 2)**

```
PUT /programs/draft/{programId}
```

**Request Body:**

```json
{
  "stepTwoData": {
    "programSplit": "split-1",
    "description": "Detailed program description",
    "equipmentRequired": "Dumbbells, Resistance bands, Pull-up bar",
    "splitConfigurations": {
      "split-1": [
        {
          "id": "week-1",
          "name": "Foundation Week",
          "weekNumber": 1,
          "isCollapsed": false,
          "days": [
            {
              "id": "day-1",
              "name": "Upper Body Strength",
              "dayNumber": 1,
              "isRestDay": false,
              "isCollapsed": false,
              "sessions": [
                {
                  "id": "session-1",
                  "name": "Compound Movements",
                  "duration": "45 mins",
                  "sessionLetter": "A",
                  "sessionNumber": 1,
                  "isCollapsed": false,
                  "exercises": [
                    {
                      "id": "exercise-1",
                      "name": "Push-ups",
                      "sets": "4",
                      "reps": "12-15",
                      "rest": "60s",
                      "weight": "Body",
                      "duration": "2:45",
                      "description": "Start in plank position...",
                      "muscleGroups": ["Chest", "Triceps", "Shoulders"],
                      "thumbnailUrl": "https://cdn.example.com/exercises/pushups.jpg",
                      "videoUrl": "https://cdn.example.com/videos/pushups.mp4",
                      "isCompact": false
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  },
  "status": "draft" // "draft" or "published"
}
```

#### 2. Program Publishing

**Publish Program**

```
POST /programs/{programId}/publish
```

**Request Body:**

```json
{
  "publishDate": "2024-01-20T00:00:00Z", // optional: schedule publication
  "notifyFollowers": true,
  "marketingMessage": "New program available!"
}
```

#### 3. Program Management

**Get Trainer Programs**

```
GET /programs
```

**Query Parameters:**

- `status` (string): "draft", "published", "pending_approval", "rejected"
- `page` (number): Page number
- `limit` (number): Items per page
- `search` (string): Search by program name

**Response:**

```json
{
  "success": true,
  "data": {
    "programs": [
      {
        "id": "program_123",
        "name": "Advanced Fitness Program",
        "description": "Program description",
        "status": "published",
        "createdAt": "2024-01-15T10:30:00Z",
        "publishedAt": "2024-01-16T00:00:00Z",
        "enrollments": 25,
        "revenue": 2499.75,
        "rating": 4.7,
        "reviewCount": 18,
        "splits": [
          {
            "id": "split-1",
            "title": "Upper Body",
            "price": 99.99,
            "enrollments": 15
          }
        ]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

**Get Program Details**

```
GET /programs/{programId}
```

**Update Program**

```
PUT /programs/{programId}
```

**Delete Program**

```
DELETE /programs/{programId}
```

#### 4. Program Enrollment Management

**Get Program Enrollments**

```
GET /programs/{programId}/enrollments
```

**Response:**

```json
{
  "success": true,
  "data": {
    "enrollments": [
      {
        "id": "enrollment_123",
        "athlete": {
          "id": "athlete_456",
          "fullName": "John Doe",
          "email": "<EMAIL>",
          "profileImage": "https://cdn.example.com/profiles/athlete_456.jpg"
        },
        "enrolledAt": "2024-01-15T10:30:00Z",
        "progress": 35.5,
        "lastActivity": "2024-01-20T14:20:00Z",
        "paymentStatus": "completed",
        "splitAccess": ["split-1", "split-2"]
      }
    ],
    "summary": {
      "totalEnrollments": 25,
      "activeUsers": 18,
      "averageProgress": 42.3,
      "completionRate": 68.5
    }
  }
}
```

### Program Discovery & Purchase (Athlete APIs)

#### 1. Browse Programs

**Get Featured Programs**

```
GET /public/programs/featured
```

**Get Programs by Category**

```
GET /public/programs
```

**Query Parameters:**

- `category` (string): Program category
- `level` (string): "beginner", "intermediate", "expert"
- `priceMin` (number): Minimum price
- `priceMax` (number): Maximum price
- `rating` (number): Minimum rating
- `duration` (string): Program duration filter
- `page` (number): Page number
- `limit` (number): Items per page

#### 2. Program Purchase

**Purchase Program**

```
POST /programs/{programId}/purchase
```

**Request Body:**

```json
{
  "splitIds": ["split-1", "split-2"], // specific splits to purchase
  "paymentMethod": "stripe", // payment gateway
  "paymentType": "oneTime", // "oneTime" or "subscription"
  "couponCode": "DISCOUNT20", // optional
  "paymentDetails": {
    "stripePaymentMethodId": "pm_**********",
    "billingAddress": {
      "line1": "123 Main St",
      "city": "New York",
      "state": "NY",
      "postal_code": "10001",
      "country": "US"
    }
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "purchaseId": "purchase_789",
    "transactionId": "txn_456",
    "amount": 199.98,
    "currency": "USD",
    "paymentStatus": "completed",
    "accessGranted": true,
    "enrollmentId": "enrollment_123",
    "accessDetails": {
      "programId": "program_123",
      "splitAccess": ["split-1", "split-2"],
      "expiresAt": null, // null for lifetime access
      "previewEndsAt": "2024-01-22T00:00:00Z"
    }
  }
}
```

#### 3. Program Access & Progress

**Get Owned Programs**

```
GET /athlete/programs
```

**Get Program Content**

```
GET /athlete/programs/{programId}/content
```

**Update Exercise Progress**

```
POST /athlete/programs/{programId}/progress
```

**Request Body:**

```json
{
  "exerciseId": "exercise_123",
  "sessionId": "session_456",
  "dayId": "day_789",
  "weekId": "week_101",
  "completed": true,
  "sets": [
    {
      "setNumber": 1,
      "reps": 15,
      "weight": 25,
      "restTime": 60,
      "completed": true
    }
  ],
  "notes": "Felt strong today, increased weight",
  "completedAt": "2024-01-20T15:30:00Z"
}
```

---

## Transaction & Payment APIs

### Base URL

```
https://api.kanglink.com/v2/api/kanglink/custom/payments
```

### Payment Processing

#### 1. Payment Methods Management

**Add Payment Method**

```
POST /payment-methods
```

**Request Body:**

```json
{
  "type": "stripe", // "stripe", "paypal", "apple_pay", "google_pay"
  "stripePaymentMethodId": "pm_**********",
  "isDefault": true,
  "billingAddress": {
    "line1": "123 Main St",
    "line2": "Apt 4B",
    "city": "New York",
    "state": "NY",
    "postal_code": "10001",
    "country": "US"
  }
}
```

**Get Payment Methods**

```
GET /payment-methods
```

**Response:**

```json
{
  "success": true,
  "data": {
    "paymentMethods": [
      {
        "id": "pm_123",
        "type": "stripe",
        "last4": "4242",
        "brand": "visa",
        "expiryMonth": 12,
        "expiryYear": 2025,
        "isDefault": true,
        "billingAddress": {
          "line1": "123 Main St",
          "city": "New York",
          "state": "NY",
          "postal_code": "10001",
          "country": "US"
        }
      }
    ]
  }
}
```

#### 2. Transaction Processing

**Process Payment**

```
POST /transactions
```

**Request Body:**

```json
{
  "type": "program_purchase", // "program_purchase", "subscription", "refund"
  "programId": "program_123",
  "splitIds": ["split-1", "split-2"],
  "paymentMethodId": "pm_123",
  "amount": 199.98,
  "currency": "USD",
  "paymentType": "oneTime", // "oneTime", "subscription"
  "couponCode": "DISCOUNT20",
  "metadata": {
    "trainerId": "trainer_456",
    "programName": "Advanced Fitness Program"
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "transactionId": "txn_789",
    "status": "completed", // "pending", "completed", "failed", "refunded"
    "amount": 199.98,
    "currency": "USD",
    "paymentMethod": "visa_4242",
    "processedAt": "2024-01-20T15:30:00Z",
    "stripePaymentIntentId": "pi_**********",
    "receipt": {
      "receiptNumber": "RCP-2024-001234",
      "receiptUrl": "https://cdn.example.com/receipts/txn_789.pdf"
    },
    "revenueDistribution": {
      "trainerEarning": 139.99,
      "platformFee": 59.99,
      "processingFee": 5.8
    }
  }
}
```

#### 3. Subscription Management

**Create Subscription**

```
POST /subscriptions
```

**Request Body:**

```json
{
  "programId": "program_123",
  "splitId": "split-1",
  "paymentMethodId": "pm_123",
  "planType": "monthly", // "monthly", "quarterly", "yearly"
  "amount": 29.99,
  "currency": "USD",
  "startDate": "2024-01-20T00:00:00Z"
}
```

**Get User Subscriptions**

```
GET /subscriptions
```

**Update Subscription**

```
PUT /subscriptions/{subscriptionId}
```

**Cancel Subscription**

```
POST /subscriptions/{subscriptionId}/cancel
```

**Request Body:**

```json
{
  "reason": "No longer needed",
  "cancelAtPeriodEnd": true,
  "refundProrated": false
}
```

### Transaction Management

#### 1. Transaction History

**Get User Transactions**

```
GET /transactions
```

**Query Parameters:**

- `type` (string): "purchase", "subscription", "refund"
- `status` (string): "completed", "pending", "failed", "refunded"
- `dateFrom` (string): Start date (ISO format)
- `dateTo` (string): End date (ISO format)
- `page` (number): Page number
- `limit` (number): Items per page

**Response:**

```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": "txn_789",
        "type": "program_purchase",
        "status": "completed",
        "amount": 199.98,
        "currency": "USD",
        "description": "Purchase: Advanced Fitness Program",
        "program": {
          "id": "program_123",
          "name": "Advanced Fitness Program",
          "trainer": {
            "id": "trainer_456",
            "name": "Jane Smith"
          }
        },
        "paymentMethod": "visa_4242",
        "processedAt": "2024-01-20T15:30:00Z",
        "receiptUrl": "https://cdn.example.com/receipts/txn_789.pdf"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 15,
      "totalPages": 1
    },
    "summary": {
      "totalSpent": 1299.85,
      "totalTransactions": 15,
      "averageTransaction": 86.66
    }
  }
}
```

**Get Transaction Details**

```
GET /transactions/{transactionId}
```

#### 2. Trainer Revenue Management

**Get Trainer Earnings**

```
GET /trainer/earnings
```

**Query Parameters:**

- `period` (string): "week", "month", "quarter", "year"
- `dateFrom` (string): Start date
- `dateTo` (string): End date

**Response:**

```json
{
  "success": true,
  "data": {
    "earnings": {
      "totalEarnings": 5600.0,
      "pendingEarnings": 450.0,
      "availableForWithdrawal": 5150.0,
      "currency": "USD"
    },
    "breakdown": [
      {
        "programId": "program_123",
        "programName": "Advanced Fitness Program",
        "totalSales": 25,
        "grossRevenue": 4999.75,
        "trainerEarning": 3499.83,
        "platformFee": 1499.93
      }
    ],
    "recentTransactions": [
      {
        "id": "txn_789",
        "athleteName": "John Doe",
        "programName": "Advanced Fitness Program",
        "amount": 199.98,
        "trainerEarning": 139.99,
        "date": "2024-01-20T15:30:00Z"
      }
    ]
  }
}
```

**Request Payout**

```
POST /trainer/payouts
```

**Request Body:**

```json
{
  "amount": 1000.0,
  "payoutMethod": "bank_transfer", // "bank_transfer", "paypal"
  "bankDetails": {
    "accountNumber": "**********",
    "routingNumber": "*********",
    "accountHolderName": "Jane Smith"
  }
}
```

### Refund Management

#### 1. Refund Requests

**Request Refund**

```
POST /refunds
```

**Request Body:**

```json
{
  "transactionId": "txn_789",
  "reason": "Not satisfied with program content",
  "reasonCategory": "quality_issue", // predefined categories
  "requestedAmount": 199.98, // can be partial
  "additionalDetails": "The program didn't match the description"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "refundId": "refund_123",
    "status": "pending", // "pending", "approved", "rejected", "processed"
    "requestedAmount": 199.98,
    "eligibilityCheck": {
      "isEligible": true,
      "daysFromPurchase": 4,
      "refundPolicy": "7-day refund policy",
      "programProgress": "15%"
    },
    "estimatedProcessingTime": "2-3 business days",
    "requestedAt": "2024-01-20T16:00:00Z"
  }
}
```

**Get Refund Status**

```
GET /refunds/{refundId}
```

**Get User Refunds**

```
GET /refunds
```

#### 2. Admin Refund Processing

**Get Refund Requests (Admin)**

```
GET /admin/refunds
```

**Process Refund (Admin)**

```
POST /admin/refunds/{refundId}/process
```

**Request Body:**

```json
{
  "action": "approve", // "approve", "reject"
  "refundAmount": 199.98,
  "adminNotes": "Approved as per 7-day refund policy",
  "refundMethod": "original_payment", // "original_payment", "store_credit"
  "processingFee": 0.0
}
```

### Coupon & Discount Management

#### 1. Coupon System

**Create Coupon (Trainer)**

```
POST /trainer/coupons
```

**Request Body:**

```json
{
  "code": "SUMMER20",
  "type": "percentage", // "percentage", "fixed_amount"
  "value": 20, // 20% or $20
  "programIds": ["program_123"], // specific programs or null for all
  "maxUses": 100,
  "expiresAt": "2024-08-31T23:59:59Z",
  "minimumPurchase": 50.0,
  "description": "Summer discount - 20% off"
}
```

**Validate Coupon**

```
POST /coupons/validate
```

**Request Body:**

```json
{
  "code": "SUMMER20",
  "programId": "program_123",
  "amount": 199.98
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "valid": true,
    "discount": {
      "type": "percentage",
      "value": 20,
      "discountAmount": 39.996,
      "finalAmount": 159.984
    },
    "coupon": {
      "id": "coupon_456",
      "code": "SUMMER20",
      "description": "Summer discount - 20% off",
      "remainingUses": 85
    }
  }
}
```

---

## Content & Media Management APIs

### Base URL

```
https://api.kanglink.com/v2/api/kanglink/custom/content
```

### Exercise Library Management

#### 1. Exercise Management

**Create Exercise**

```
POST /exercises
```

**Request Body:**

```json
{
  "name": "Push-ups",
  "description": "Start in plank position with hands slightly wider than shoulders...",
  "category": "Bodybuilding", // "Bodybuilding", "Calisthenics", "Yoga", "Cardio"
  "muscleGroups": ["Chest", "Triceps", "Shoulders"],
  "equipment": ["None"], // equipment required
  "difficulty": "beginner", // "beginner", "intermediate", "advanced"
  "instructions": [
    "Start in plank position",
    "Lower body until chest nearly touches floor",
    "Push back up to starting position"
  ],
  "tips": [
    "Keep core tight throughout movement",
    "Maintain straight line from head to heels"
  ],
  "thumbnailImage": null, // file upload
  "videoFile": null, // file upload
  "isPublic": true, // visible to all trainers
  "tags": ["bodyweight", "upper_body", "compound"]
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "exerciseId": "exercise_123",
    "name": "Push-ups",
    "description": "Start in plank position...",
    "category": "Bodybuilding",
    "muscleGroups": ["Chest", "Triceps", "Shoulders"],
    "thumbnailUrl": "https://cdn.example.com/exercises/pushups_thumb.jpg",
    "videoUrl": "https://cdn.example.com/exercises/pushups_video.mp4",
    "createdBy": "trainer_456",
    "createdAt": "2024-01-20T16:00:00Z",
    "isPublic": true,
    "status": "active"
  }
}
```

**Get Exercise Library**

```
GET /exercises
```

**Query Parameters:**

- `category` (string): Exercise category
- `muscleGroup` (string): Target muscle group
- `equipment` (string): Required equipment
- `difficulty` (string): Difficulty level
- `search` (string): Search by name or description
- `createdBy` (string): Filter by creator (trainer ID)
- `isPublic` (boolean): Public exercises only
- `page` (number): Page number
- `limit` (number): Items per page

**Response:**

```json
{
  "success": true,
  "data": {
    "exercises": [
      {
        "id": "exercise_123",
        "name": "Push-ups",
        "description": "Start in plank position...",
        "category": "Bodybuilding",
        "muscleGroups": ["Chest", "Triceps", "Shoulders"],
        "equipment": ["None"],
        "difficulty": "beginner",
        "thumbnailUrl": "https://cdn.example.com/exercises/pushups_thumb.jpg",
        "videoUrl": "https://cdn.example.com/exercises/pushups_video.mp4",
        "duration": "2:45",
        "createdBy": {
          "id": "trainer_456",
          "name": "Jane Smith"
        },
        "isPublic": true,
        "usageCount": 25, // how many programs use this exercise
        "rating": 4.7,
        "createdAt": "2024-01-20T16:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8
    },
    "filters": {
      "categories": ["Bodybuilding", "Calisthenics", "Yoga", "Cardio"],
      "muscleGroups": ["Chest", "Back", "Legs", "Arms", "Core"],
      "equipment": ["None", "Dumbbells", "Resistance Bands", "Pull-up Bar"]
    }
  }
}
```

**Get Exercise Details**

```
GET /exercises/{exerciseId}
```

**Update Exercise**

```
PUT /exercises/{exerciseId}
```

**Delete Exercise**

```
DELETE /exercises/{exerciseId}
```

#### 2. Video Management

**Upload Video**

```
POST /videos/upload
```

**Request Body (multipart/form-data):**

- `file`: Video file
- `title`: Video title
- `description`: Video description
- `category`: Video category
- `exerciseId`: Associated exercise ID (optional)
- `thumbnailTime`: Timestamp for auto-generated thumbnail (optional)

**Response:**

```json
{
  "success": true,
  "data": {
    "videoId": "video_789",
    "title": "Push-ups Demonstration",
    "description": "Proper form for push-ups",
    "originalFilename": "pushups_demo.mp4",
    "fileSize": 15728640, // bytes
    "duration": 165, // seconds
    "resolution": "1920x1080",
    "format": "mp4",
    "uploadedAt": "2024-01-20T16:30:00Z",
    "status": "processing", // "uploading", "processing", "ready", "failed"
    "urls": {
      "original": "https://cdn.example.com/videos/original/video_789.mp4",
      "hd": "https://cdn.example.com/videos/hd/video_789.mp4",
      "sd": "https://cdn.example.com/videos/sd/video_789.mp4",
      "thumbnail": "https://cdn.example.com/videos/thumbs/video_789.jpg"
    },
    "processingProgress": 0 // 0-100
  }
}
```

**Get Video Processing Status**

```
GET /videos/{videoId}/status
```

**Get Video Library**

```
GET /videos
```

**Query Parameters:**

- `category` (string): Video category
- `status` (string): Processing status
- `search` (string): Search by title
- `uploadedBy` (string): Filter by uploader
- `page` (number): Page number
- `limit` (number): Items per page

### File Upload Management

#### 1. General File Upload

**Upload File**

```
POST /files/upload
```

**Request Body (multipart/form-data):**

- `file`: File to upload
- `type`: File type ("image", "video", "document", "audio")
- `category`: File category
- `isPublic`: Whether file is publicly accessible
- `metadata`: Additional metadata (JSON string)

**Response:**

```json
{
  "success": true,
  "data": {
    "fileId": "file_456",
    "originalFilename": "workout_plan.pdf",
    "fileSize": 2048576,
    "mimeType": "application/pdf",
    "type": "document",
    "category": "program_materials",
    "uploadedAt": "2024-01-20T17:00:00Z",
    "urls": {
      "original": "https://cdn.example.com/files/file_456.pdf",
      "thumbnail": "https://cdn.example.com/files/thumbs/file_456.jpg"
    },
    "isPublic": false,
    "uploadedBy": "trainer_456",
    "metadata": {
      "programId": "program_123",
      "description": "Workout plan PDF"
    }
  }
}
```

#### 2. Image Processing

**Upload and Process Image**

```
POST /images/upload
```

**Request Body (multipart/form-data):**

- `file`: Image file
- `type`: Image type ("profile", "exercise", "program", "thumbnail")
- `resize`: Resize options (JSON string)
- `crop`: Crop options (JSON string)

**Response:**

```json
{
  "success": true,
  "data": {
    "imageId": "image_789",
    "originalFilename": "exercise_demo.jpg",
    "fileSize": 1024768,
    "dimensions": {
      "width": 1920,
      "height": 1080
    },
    "format": "jpeg",
    "uploadedAt": "2024-01-20T17:15:00Z",
    "urls": {
      "original": "https://cdn.example.com/images/original/image_789.jpg",
      "large": "https://cdn.example.com/images/large/image_789.jpg",
      "medium": "https://cdn.example.com/images/medium/image_789.jpg",
      "small": "https://cdn.example.com/images/small/image_789.jpg",
      "thumbnail": "https://cdn.example.com/images/thumbs/image_789.jpg"
    },
    "variants": [
      {
        "size": "large",
        "dimensions": { "width": 1200, "height": 675 },
        "fileSize": 512384
      },
      {
        "size": "medium",
        "dimensions": { "width": 800, "height": 450 },
        "fileSize": 256192
      }
    ]
  }
}
```

### Content Categorization & Search

#### 1. Category Management

**Get Content Categories**

```
GET /categories
```

**Response:**

```json
{
  "success": true,
  "data": {
    "exerciseCategories": [
      {
        "id": "bodybuilding",
        "name": "Bodybuilding",
        "description": "Muscle building exercises",
        "exerciseCount": 45,
        "icon": "https://cdn.example.com/icons/bodybuilding.svg"
      },
      {
        "id": "calisthenics",
        "name": "Calisthenics",
        "description": "Bodyweight exercises",
        "exerciseCount": 32,
        "icon": "https://cdn.example.com/icons/calisthenics.svg"
      }
    ],
    "programCategories": [
      {
        "id": "strength",
        "name": "Strength Training",
        "description": "Programs focused on building strength",
        "programCount": 28
      }
    ],
    "muscleGroups": [
      {
        "id": "chest",
        "name": "Chest",
        "exerciseCount": 15
      },
      {
        "id": "back",
        "name": "Back",
        "exerciseCount": 22
      }
    ]
  }
}
```

#### 2. Content Search

**Advanced Content Search**

```
GET /search
```

**Query Parameters:**

- `q` (string): Search query
- `type` (string): Content type ("exercises", "programs", "trainers", "videos")
- `category` (string): Category filter
- `tags` (string): Comma-separated tags
- `difficulty` (string): Difficulty level
- `rating` (number): Minimum rating
- `page` (number): Page number
- `limit` (number): Items per page
- `sortBy` (string): Sort field ("relevance", "rating", "date", "popularity")
- `sortOrder` (string): Sort order ("asc", "desc")

**Response:**

```json
{
  "success": true,
  "data": {
    "results": [
      {
        "type": "exercise",
        "id": "exercise_123",
        "title": "Push-ups",
        "description": "Start in plank position...",
        "category": "Bodybuilding",
        "rating": 4.7,
        "thumbnailUrl": "https://cdn.example.com/exercises/pushups_thumb.jpg",
        "relevanceScore": 0.95,
        "matchedFields": ["title", "description", "tags"]
      },
      {
        "type": "program",
        "id": "program_456",
        "title": "Upper Body Strength Program",
        "description": "Comprehensive upper body training...",
        "trainer": {
          "id": "trainer_789",
          "name": "John Trainer"
        },
        "rating": 4.8,
        "price": 99.99,
        "thumbnailUrl": "https://cdn.example.com/programs/program_456_thumb.jpg",
        "relevanceScore": 0.87
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "totalPages": 3
    },
    "facets": {
      "categories": {
        "Bodybuilding": 15,
        "Calisthenics": 12,
        "Yoga": 8
      },
      "difficulty": {
        "beginner": 20,
        "intermediate": 18,
        "advanced": 7
      }
    }
  }
}
```

---

## Communication & Social Features APIs

### Base URL

```
https://api.kanglink.com/v2/api/kanglink/custom/social
```

### Feed Management

#### 1. Feed Posts

**Create Feed Post**

```
POST /feed/posts
```

**Request Body:**

```json
{
  "content": "Just completed an amazing workout session!",
  "type": "text", // "text", "image", "video", "workout_update", "program_announcement"
  "isPrivate": false, // public or private post
  "programId": "program_123", // optional: associate with program
  "workoutData": {
    // optional: for workout updates
    "exerciseId": "exercise_456",
    "sets": 4,
    "reps": 15,
    "weight": 25,
    "notes": "Felt strong today!"
  },
  "media": [
    // optional: attached media
    {
      "type": "image",
      "fileId": "file_789",
      "caption": "Post-workout selfie"
    }
  ],
  "tags": ["#fitness", "#strength", "#motivation"],
  "mentionedUsers": ["athlete_123", "trainer_456"] // optional: user mentions
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "postId": "post_789",
    "content": "Just completed an amazing workout session!",
    "type": "text",
    "isPrivate": false,
    "author": {
      "id": "user_123",
      "fullName": "John Doe",
      "profileImage": "https://cdn.example.com/profiles/user_123.jpg",
      "role": "member"
    },
    "createdAt": "2024-01-20T18:00:00Z",
    "reactions": {
      "like": 0,
      "love": 0,
      "fire": 0,
      "strong": 0
    },
    "commentCount": 0,
    "shareCount": 0,
    "visibility": "public"
  }
}
```

**Get Feed**

```
GET /feed
```

**Query Parameters:**

- `type` (string): "all", "following", "program", "trainer"
- `programId` (string): Filter by specific program
- `trainerId` (string): Filter by specific trainer
- `page` (number): Page number
- `limit` (number): Items per page
- `since` (string): Get posts since timestamp

**Response:**

```json
{
  "success": true,
  "data": {
    "posts": [
      {
        "id": "post_789",
        "content": "Just completed an amazing workout session!",
        "type": "text",
        "author": {
          "id": "user_123",
          "fullName": "John Doe",
          "profileImage": "https://cdn.example.com/profiles/user_123.jpg",
          "role": "member",
          "isTrainer": false
        },
        "createdAt": "2024-01-20T18:00:00Z",
        "isPrivate": false,
        "reactions": [
          {
            "type": "like",
            "count": 5,
            "isActive": false // if current user reacted
          },
          {
            "type": "love",
            "count": 2,
            "isActive": true
          }
        ],
        "comments": [
          {
            "id": "comment_456",
            "content": "Great job! Keep it up!",
            "author": {
              "id": "trainer_789",
              "fullName": "Jane Trainer",
              "profileImage": "https://cdn.example.com/profiles/trainer_789.jpg",
              "isTrainer": true
            },
            "createdAt": "2024-01-20T18:15:00Z",
            "reactions": [
              {
                "type": "like",
                "count": 1,
                "isActive": false
              }
            ],
            "replies": []
          }
        ],
        "media": [],
        "program": null,
        "canEdit": false,
        "canDelete": false
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "hasNext": true
    }
  }
}
```

#### 2. Post Interactions

**React to Post**

```
POST /feed/posts/{postId}/reactions
```

**Request Body:**

```json
{
  "type": "like" // "like", "love", "fire", "strong"
}
```

**Remove Reaction**

```
DELETE /feed/posts/{postId}/reactions/{reactionType}
```

**Share Post**

```
POST /feed/posts/{postId}/share
```

**Request Body:**

```json
{
  "message": "Check out this amazing workout!", // optional
  "shareType": "repost" // "repost", "quote", "private_message"
}
```

### Comments & Replies

#### 1. Comment Management

**Add Comment**

```
POST /feed/posts/{postId}/comments
```

**Request Body:**

```json
{
  "content": "Great workout! What was your favorite exercise?",
  "isPrivate": false, // private comments only visible to post author
  "mentionedUsers": ["user_456"], // optional mentions
  "replyToCommentId": null // optional: reply to specific comment
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "commentId": "comment_789",
    "content": "Great workout! What was your favorite exercise?",
    "author": {
      "id": "trainer_456",
      "fullName": "Jane Trainer",
      "profileImage": "https://cdn.example.com/profiles/trainer_456.jpg",
      "isTrainer": true
    },
    "createdAt": "2024-01-20T18:30:00Z",
    "isPrivate": false,
    "reactions": [],
    "replies": [],
    "canEdit": true,
    "canDelete": true
  }
}
```

**Get Comments**

```
GET /feed/posts/{postId}/comments
```

**Update Comment**

```
PUT /feed/comments/{commentId}
```

**Delete Comment**

```
DELETE /feed/comments/{commentId}
```

### Direct Messaging

#### 1. Conversation Management

**Get Conversations**

```
GET /messages/conversations
```

**Response:**

```json
{
  "success": true,
  "data": {
    "conversations": [
      {
        "id": "conv_123",
        "type": "direct", // "direct", "group", "program_thread"
        "participants": [
          {
            "id": "user_456",
            "fullName": "Jane Smith",
            "profileImage": "https://cdn.example.com/profiles/user_456.jpg",
            "role": "trainer",
            "isOnline": true,
            "lastSeen": "2024-01-20T18:45:00Z"
          }
        ],
        "lastMessage": {
          "id": "msg_789",
          "content": "Thanks for the workout tips!",
          "senderId": "user_123",
          "sentAt": "2024-01-20T18:40:00Z",
          "type": "text"
        },
        "unreadCount": 2,
        "updatedAt": "2024-01-20T18:40:00Z",
        "isPinned": false,
        "isMuted": false
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 8
    }
  }
}
```

**Create Conversation**

```
POST /messages/conversations
```

**Request Body:**

```json
{
  "participantIds": ["user_456", "user_789"],
  "type": "direct", // "direct", "group"
  "title": "Workout Discussion", // optional for group chats
  "initialMessage": "Hey! I have a question about the program"
}
```

#### 2. Message Management

**Send Message**

```
POST /messages/conversations/{conversationId}/messages
```

**Request Body:**

```json
{
  "content": "Thanks for the great workout program!",
  "type": "text", // "text", "image", "video", "file", "workout_share"
  "replyToMessageId": null, // optional: reply to specific message
  "media": [
    // optional: for media messages
    {
      "fileId": "file_456",
      "type": "image",
      "caption": "My progress photo"
    }
  ],
  "workoutData": null // optional: for workout sharing
}
```

**Get Messages**

```
GET /messages/conversations/{conversationId}/messages
```

**Query Parameters:**

- `page` (number): Page number
- `limit` (number): Items per page
- `before` (string): Get messages before timestamp
- `after` (string): Get messages after timestamp

**Mark Messages as Read**

```
POST /messages/conversations/{conversationId}/read
```

**Request Body:**

```json
{
  "messageIds": ["msg_123", "msg_456"] // specific messages or empty for all
}
```

### Notifications

#### 1. Notification Management

**Get Notifications**

```
GET /notifications
```

**Query Parameters:**

- `type` (string): "all", "mentions", "reactions", "comments", "messages", "program_updates"
- `unreadOnly` (boolean): Show only unread notifications
- `page` (number): Page number
- `limit` (number): Items per page

**Response:**

```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "notif_123",
        "type": "comment", // "comment", "reaction", "mention", "message", "program_update", "follow"
        "title": "New comment on your post",
        "message": "Jane Trainer commented on your workout post",
        "isRead": false,
        "createdAt": "2024-01-20T19:00:00Z",
        "actor": {
          "id": "trainer_456",
          "fullName": "Jane Trainer",
          "profileImage": "https://cdn.example.com/profiles/trainer_456.jpg"
        },
        "target": {
          "type": "post",
          "id": "post_789",
          "title": "Workout Update"
        },
        "actionUrl": "/feed/posts/post_789",
        "metadata": {
          "commentId": "comment_456",
          "commentPreview": "Great workout! Keep it up!"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 15
    },
    "summary": {
      "unreadCount": 5,
      "totalCount": 15
    }
  }
}
```

**Mark Notifications as Read**

```
POST /notifications/read
```

**Request Body:**

```json
{
  "notificationIds": ["notif_123", "notif_456"], // specific notifications
  "markAllAsRead": false // or true to mark all as read
}
```

**Update Notification Preferences**

```
PUT /notifications/preferences
```

**Request Body:**

```json
{
  "email": {
    "comments": true,
    "reactions": false,
    "mentions": true,
    "messages": true,
    "programUpdates": true,
    "marketing": false
  },
  "push": {
    "comments": true,
    "reactions": true,
    "mentions": true,
    "messages": true,
    "programUpdates": false
  },
  "inApp": {
    "comments": true,
    "reactions": true,
    "mentions": true,
    "messages": true,
    "programUpdates": true
  }
}
```

---

## Admin Management & Analytics APIs

### Base URL

```
https://api.kanglink.com/v2/api/kanglink/custom/admin
```

### Dashboard Analytics

#### 1. Dashboard Statistics

**Get Dashboard Overview**

```
GET /dashboard/stats
```

**Query Parameters:**

- `period` (string): "7d", "30d", "90d", "1y"
- `timezone` (string): Timezone for calculations

**Response:**

```json
{
  "success": true,
  "data": {
    "athletes": {
      "total": 120,
      "newThisMonth": 15,
      "activeThisMonth": 98,
      "growthPercentage": 12.5
    },
    "trainers": {
      "total": 60,
      "newThisMonth": 5,
      "activeThisMonth": 52,
      "growthPercentage": 8.3,
      "pendingApproval": 3
    },
    "programs": {
      "total": 245,
      "pendingApproval": 12,
      "publishedThisMonth": 18,
      "rejectedThisMonth": 2
    },
    "refunds": {
      "pendingRequests": 5,
      "processedThisMonth": 23,
      "totalAmountPending": 1250.0,
      "averageProcessingTime": "2.5 days"
    },
    "revenue": {
      "totalThisMonth": 45600.0,
      "totalLastMonth": 42300.0,
      "growthPercentage": 7.8,
      "currency": "USD"
    },
    "lastUpdated": "2024-01-15T10:30:00Z"
  }
}
```

#### 2. System Alerts

**Get System Alerts**

```
GET /dashboard/alerts
```

**Query Parameters:**

- `priority` (string): "high", "medium", "low"
- `type` (string): "user_flagged", "program_approval", "refund_request", "system"
- `limit` (number): Number of alerts

**Response:**

```json
{
  "success": true,
  "data": {
    "alerts": [
      {
        "id": "alert_123",
        "type": "program_approval",
        "priority": "medium",
        "title": "Program Approval Pending",
        "message": "5 new programs pending approval",
        "count": 5,
        "actionUrl": "/admin/programs?status=pending",
        "createdAt": "2024-01-15T09:00:00Z",
        "isRead": false
      }
    ],
    "totalUnread": 7,
    "lastChecked": "2024-01-15T10:30:00Z"
  }
}
```

### User Management

#### 1. User Administration

**Get Users List**

```
GET /users
```

**Query Parameters:**

- `role` (string): "member", "trainer", "admin"
- `status` (string): "active", "inactive", "suspended", "pending"
- `search` (string): Search by name or email
- `joinedAfter` (string): Filter by join date
- `page` (number): Page number
- `limit` (number): Items per page

**Response:**

```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "user_123",
        "fullName": "John Doe",
        "email": "<EMAIL>",
        "role": "member",
        "status": "active",
        "joinedAt": "2024-01-10T14:30:00Z",
        "lastActiveAt": "2024-01-15T09:45:00Z",
        "programsEnrolled": 3,
        "totalSpent": 299.99,
        "flaggedReports": 0,
        "verificationStatus": "verified"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 120,
      "totalPages": 6
    }
  }
}
```

**Update User Status**

```
POST /users/{userId}/status
```

**Request Body:**

```json
{
  "status": "suspended",
  "reason": "Violation of terms of service",
  "duration": "30d",
  "notifyUser": true,
  "adminNotes": "Multiple reports of inappropriate behavior"
}
```

#### 2. Content Moderation

**Get Programs for Review**

```
GET /programs/review
```

**Query Parameters:**

- `status` (string): "pending", "flagged", "reported"
- `priority` (string): "high", "medium", "low"
- `submittedAfter` (string): Filter by submission date

**Approve/Reject Program**

```
POST /programs/{programId}/review
```

**Request Body:**

```json
{
  "action": "approve", // "approve", "reject", "request_changes"
  "notes": "Program meets all quality standards",
  "flaggedContent": [],
  "requiresChanges": false,
  "publicationDate": "2024-01-16T00:00:00Z"
}
```

### Analytics & Reporting

#### 1. Platform Analytics

**Get Platform Metrics**

```
GET /analytics/platform
```

**Query Parameters:**

- `period` (string): Time period for analysis
- `metrics` (string): Comma-separated list of metrics
- `groupBy` (string): "day", "week", "month"

**Response:**

```json
{
  "success": true,
  "data": {
    "userGrowth": {
      "period": "30d",
      "data": [
        {
          "date": "2024-01-01",
          "newUsers": 15,
          "totalUsers": 1205,
          "activeUsers": 890
        }
      ]
    },
    "revenueMetrics": {
      "totalRevenue": 125600.0,
      "averageOrderValue": 89.5,
      "conversionRate": 3.2,
      "refundRate": 2.1
    },
    "contentMetrics": {
      "totalPrograms": 245,
      "averageRating": 4.6,
      "completionRate": 68.5,
      "engagementRate": 75.2
    }
  }
}
```

#### 2. Financial Reports

**Get Revenue Report**

```
GET /analytics/revenue
```

**Query Parameters:**

- `period` (string): Reporting period
- `breakdown` (string): "trainer", "program", "category"
- `format` (string): "json", "csv", "pdf"

**Response:**

```json
{
  "success": true,
  "data": {
    "summary": {
      "totalRevenue": 125600.0,
      "platformFee": 37680.0,
      "trainerEarnings": 87920.0,
      "refunds": 2450.0,
      "netRevenue": 123150.0
    },
    "breakdown": [
      {
        "trainerId": "trainer_456",
        "trainerName": "Jane Smith",
        "grossRevenue": 15600.0,
        "platformFee": 4680.0,
        "trainerEarning": 10920.0,
        "programsSold": 78,
        "averageOrderValue": 200.0
      }
    ],
    "trends": {
      "monthlyGrowth": 12.5,
      "quarterlyGrowth": 35.2,
      "yearlyGrowth": 145.8
    }
  }
}
```

### System Configuration

#### 1. Platform Settings

**Get Platform Configuration**

```
GET /config/platform
```

**Update Platform Settings**

```
PUT /config/platform
```

**Request Body:**

```json
{
  "general": {
    "platformName": "Fitness Platform",
    "supportEmail": "<EMAIL>",
    "maintenanceMode": false
  },
  "payments": {
    "stripePublishableKey": "pk_test_...",
    "platformFeePercentage": 30.0,
    "minimumPayout": 50.0
  },
  "content": {
    "autoApprovePrograms": false,
    "maxVideoSize": 500, // MB
    "allowedVideoFormats": ["mp4", "mov", "avi"],
    "maxProgramDuration": 52 // weeks
  },
  "notifications": {
    "emailProvider": "sendgrid",
    "pushProvider": "firebase",
    "smsProvider": "twilio"
  }
}
```

#### 2. Feature Flags

**Get Feature Flags**

```
GET /config/features
```

**Update Feature Flag**

```
PUT /config/features/{flagName}
```

**Request Body:**

```json
{
  "enabled": true,
  "rolloutPercentage": 100,
  "targetRoles": ["member", "trainer"],
  "description": "Enable new workout tracking feature"
}
```

---

## Data Models & Database Schema

### Core Entities

#### 1. User Model

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  role VARCHAR(50) NOT NULL CHECK (role IN ('member', 'trainer', 'admin', 'super_admin')),
  status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'banned', 'pending_approval')),
  profile_image_url TEXT,
  bio TEXT,
  date_of_birth DATE,
  phone VARCHAR(20),
  email_verified BOOLEAN DEFAULT FALSE,
  phone_verified BOOLEAN DEFAULT FALSE,
  two_factor_enabled BOOLEAN DEFAULT FALSE,
  last_login_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### 2. Program Model

```sql
CREATE TABLE programs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  trainer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(100) NOT NULL,
  difficulty VARCHAR(50) CHECK (difficulty IN ('beginner', 'intermediate', 'advanced')),
  duration_weeks INTEGER,
  equipment_required TEXT[],
  target_levels VARCHAR(50)[],
  status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'pending_approval', 'approved', 'published', 'rejected', 'archived')),
  track_progress BOOLEAN DEFAULT TRUE,
  allow_comments BOOLEAN DEFAULT TRUE,
  allow_private_messages BOOLEAN DEFAULT FALSE,
  preview_days INTEGER DEFAULT 7,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  published_at TIMESTAMP,
  approved_at TIMESTAMP,
  approved_by UUID REFERENCES users(id)
);

CREATE INDEX idx_programs_trainer_id ON programs(trainer_id);
CREATE INDEX idx_programs_status ON programs(status);
CREATE INDEX idx_programs_category ON programs(category);
CREATE INDEX idx_programs_published_at ON programs(published_at);
```

#### 3. Transaction Model

```sql
CREATE TABLE transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id),
  program_id UUID REFERENCES programs(id),
  type VARCHAR(50) NOT NULL CHECK (type IN ('purchase', 'subscription', 'refund', 'payout')),
  status VARCHAR(50) NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'cancelled', 'refunded')),
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  payment_method VARCHAR(50),
  payment_gateway VARCHAR(50),
  gateway_transaction_id VARCHAR(255),
  description TEXT,
  metadata JSONB,
  processed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_transactions_program_id ON transactions(program_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_type ON transactions(type);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
```

#### 4. Exercise Model

```sql
CREATE TABLE exercises (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_by UUID NOT NULL REFERENCES users(id),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(100) NOT NULL,
  muscle_groups TEXT[],
  equipment TEXT[],
  difficulty VARCHAR(50) CHECK (difficulty IN ('beginner', 'intermediate', 'advanced')),
  instructions TEXT[],
  tips TEXT[],
  thumbnail_url TEXT,
  video_url TEXT,
  duration INTEGER, -- seconds
  is_public BOOLEAN DEFAULT TRUE,
  tags TEXT[],
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_exercises_created_by ON exercises(created_by);
CREATE INDEX idx_exercises_category ON exercises(category);
CREATE INDEX idx_exercises_difficulty ON exercises(difficulty);
CREATE INDEX idx_exercises_is_public ON exercises(is_public);
```

#### 5. Program Structure Models

```sql
-- Program Splits
CREATE TABLE program_splits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  program_id UUID NOT NULL REFERENCES programs(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  price DECIMAL(10,2),
  subscription_price DECIMAL(10,2),
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Program Weeks
CREATE TABLE program_weeks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  split_id UUID NOT NULL REFERENCES program_splits(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  week_number INTEGER NOT NULL,
  description TEXT,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Program Days
CREATE TABLE program_days (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  week_id UUID NOT NULL REFERENCES program_weeks(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  day_number INTEGER NOT NULL,
  is_rest_day BOOLEAN DEFAULT FALSE,
  description TEXT,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Program Sessions
CREATE TABLE program_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  day_id UUID NOT NULL REFERENCES program_days(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  session_number INTEGER NOT NULL,
  session_letter VARCHAR(5),
  duration VARCHAR(50),
  description TEXT,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Program Exercises
CREATE TABLE program_exercises (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID NOT NULL REFERENCES program_sessions(id) ON DELETE CASCADE,
  exercise_id UUID NOT NULL REFERENCES exercises(id),
  sets VARCHAR(50),
  reps VARCHAR(50),
  weight VARCHAR(50),
  rest_time VARCHAR(50),
  duration VARCHAR(50),
  notes TEXT,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 6. Social Features Models

```sql
-- Feed Posts
CREATE TABLE feed_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  author_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  type VARCHAR(50) NOT NULL CHECK (type IN ('text', 'image', 'video', 'workout_update', 'program_announcement')),
  is_private BOOLEAN DEFAULT FALSE,
  program_id UUID REFERENCES programs(id),
  workout_data JSONB,
  media_urls TEXT[],
  tags TEXT[],
  mentioned_users UUID[],
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Comments
CREATE TABLE comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id UUID NOT NULL REFERENCES feed_posts(id) ON DELETE CASCADE,
  author_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  parent_comment_id UUID REFERENCES comments(id),
  is_private BOOLEAN DEFAULT FALSE,
  mentioned_users UUID[],
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Reactions
CREATE TABLE reactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  target_type VARCHAR(50) NOT NULL CHECK (target_type IN ('post', 'comment')),
  target_id UUID NOT NULL,
  reaction_type VARCHAR(50) NOT NULL CHECK (reaction_type IN ('like', 'love', 'fire', 'strong')),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, target_type, target_id, reaction_type)
);
```

---

## Business Logic & Workflows

### 1. User Registration & Approval Workflow

#### Athlete Registration

1. User submits registration form with basic information
2. System validates email uniqueness and password strength
3. Email verification link sent to user
4. Upon email verification, account is activated
5. User can immediately access platform features

#### Trainer Registration

1. Trainer submits detailed registration form including qualifications
2. System validates information and uploads documents
3. Application enters "pending_approval" status
4. Admin reviews application within 2-3 business days
5. If approved: trainer account activated, welcome email sent
6. If rejected: rejection email with reasons sent, can reapply

### 2. Program Creation & Publishing Workflow

#### Program Development

1. Trainer creates program draft with basic information
2. Trainer builds program structure (splits → weeks → days → sessions → exercises)
3. Trainer can save progress and continue editing
4. System auto-saves changes every 30 seconds

#### Program Publishing

1. Trainer submits program for review
2. System validates program completeness and structure
3. Program enters "pending_approval" status
4. Admin reviews program content and quality
5. If approved: program published, followers notified
6. If rejected: feedback provided, trainer can revise and resubmit

### 3. Payment & Revenue Distribution

#### Purchase Processing

1. User selects program/splits to purchase
2. System calculates total with any applicable discounts
3. Payment processed through Stripe
4. Upon successful payment:
   - User granted immediate access to content
   - Transaction recorded with revenue split
   - Trainer earnings updated
   - Purchase confirmation sent

#### Revenue Distribution

- Platform fee: 30% of gross revenue
- Trainer earning: 70% of gross revenue
- Processing fees deducted from platform portion
- Trainer payouts processed weekly (minimum $50)

### 4. Content Moderation Workflow

#### Automated Checks

- Profanity filtering on all user-generated content
- Image/video content scanning for inappropriate material
- Spam detection on comments and posts

#### Manual Review Process

1. Flagged content enters review queue
2. Admin reviews within 24 hours
3. Actions: approve, reject, request changes, or escalate
4. Users notified of moderation decisions
5. Appeals process available for rejected content

### 5. Refund Processing Workflow

#### Refund Eligibility

- 7-day refund window from purchase date
- Must have completed less than 25% of program
- No previous refunds for same program

#### Processing Steps

1. User submits refund request with reason
2. System checks eligibility automatically
3. If eligible: refund processed within 2-3 business days
4. If ineligible: user notified with explanation
5. Disputed cases escalated to admin review

### 6. Notification System

#### Real-time Notifications

- New comments on user's posts
- Reactions to user's content
- Direct messages received
- Program updates from followed trainers

#### Email Notifications

- Weekly digest of activity
- Program purchase confirmations
- Account security alerts
- Marketing communications (opt-in)

#### Push Notifications

- Immediate alerts for high-priority events
- Workout reminders
- Social interactions
- System announcements

---

## Implementation Guidelines

### 1. Security Requirements

#### Authentication & Authorization

- JWT tokens with 1-hour expiration
- Refresh tokens with 30-day expiration
- Role-based access control (RBAC)
- Rate limiting on authentication endpoints
- Password requirements: 8+ characters, mixed case, numbers, symbols

#### Data Protection

- All sensitive data encrypted at rest
- PII data anonymization for analytics
- GDPR compliance for EU users
- Regular security audits and penetration testing

### 2. Performance Requirements

#### API Response Times

- Authentication: < 200ms
- Content retrieval: < 500ms
- Search queries: < 1s
- File uploads: Progress tracking required

#### Scalability

- Horizontal scaling capability
- Database read replicas for performance
- CDN for media content delivery
- Caching strategy for frequently accessed data

### 3. Integration Requirements

#### Payment Processing

- Stripe integration for card payments
- PayPal integration for alternative payments
- Apple Pay and Google Pay support
- PCI DSS compliance

#### Media Storage

- AWS S3 or similar for file storage
- Video transcoding for multiple resolutions
- Image optimization and resizing
- CDN integration for global delivery

#### Communication

- SendGrid for email delivery
- Firebase for push notifications
- Twilio for SMS (optional)
- WebSocket for real-time features

### 4. Monitoring & Analytics

#### Application Monitoring

- Error tracking and alerting
- Performance monitoring
- User behavior analytics
- Business metrics tracking

#### Logging

- Structured logging for all API requests
- Audit trails for sensitive operations
- Error logs with stack traces
- Performance metrics logging

This comprehensive specification provides the backend team with all necessary information to build a robust, scalable fitness platform that supports the complete user journey from registration to program completion, with proper monetization, social features, and administrative oversight.
