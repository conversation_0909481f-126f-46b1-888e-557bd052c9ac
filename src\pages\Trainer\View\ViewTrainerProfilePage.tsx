import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Container } from "@/components/Container";
import { useTheme } from "@/hooks/useTheme";
import { useContexts } from "@/hooks/useContexts";
import { useProfile } from "@/hooks/useProfile";
import { useSDK } from "@/hooks/useSDK";
import { THEME_COLORS } from "@/context/Theme";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { LazyLoad } from "@/components/LazyLoad";
import { Modal } from "@/components/Modal";
import { InteractiveButton } from "@/components/InteractiveButton";
import { ToastStatusEnum } from "@/utils/Enums";
import { AccountSecurity } from "@/components/Profile";
import UpdatePasswordModal from "@/components/UserProfile/UpdatePasswordModal";
import { TwoFactorAuthModal } from "@/components/Profile";
import ExerciseLibraryModal from "@/components/Trainer/ExerciseLibraryModal";
import VideoLibraryModal from "@/components/Trainer/VideoLibraryModal";
import { useUpdateModelMutation } from "@/query/shared";
import { Models } from "@/utils/baas";
import { useTrainerStatus, disableActionForDeactivatedTrainer } from "@/hooks/useTrainerStatus";
import { DeactivationWarning } from "@/components/DeactivationWarning";

// Options for dropdowns and checkboxes
const yearsOfExperienceOptions = [
  "Less than 1 year",
  "1-2 years",
  "3-5 years",
  "6-10 years",
  "10+ years",
];
const genderOptions = [
  "Man",
  "Woman",
  "Non-Binary",
  "Transgender Woman",
  "Transgender Man",
  "Prefer not to say",
  "Other",
];
const qualificationsOptions = [
  "Certified Personal Trainer",
  "Group Fitness Instructor Certification",
  "Nutrition and Dietetics Certification",
  "Sports Medicine",
  "Corrective Exercise Specialist",
  "Youth Fitness",
  "Senior Fitness",
  "Functional Movement",
];
const specializationsOptions = [
  "Body Building",
  "Endurance Training",
  "Strength Training",
  "Functional Fitness",
  "Cross Fit",
  "Yoga",
  "Calisthenics",
  "Pilates",
];

// Plus Icon Component
const PlusIcon = () => {
  return (
    <svg
      width={15}
      height={16}
      viewBox="0 0 15 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.5 1.5V14.5M1 8H14"
        stroke="currentColor"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

// Validation schema
const schema = yup.object().shape({
  full_name: yup.string().required("Full name is required"),
  email: yup
    .string()
    .email("Please enter a valid email")
    .required("Email is required"),
  gender: yup.string().required("Gender is required"),
  bio: yup
    .string()
    .max(500, "Bio cannot exceed 500 characters")
    .required("Bio is required"),
    years_of_experience: yup.string().required("Experience level is required"),
  qualifications: yup
    .array()
    .of(yup.string())
    .min(1, "Select at least one qualification"),
  specializations: yup
    .array()
    .of(yup.string())
    .min(1, "Select at least one specialization"),
  email_notifications: yup.boolean(),
  in_app_notifications: yup.boolean(),
});

type FormData = yup.InferType<typeof schema>;

const ViewTrainerProfilePage = () => {
  const { state } = useTheme();
  const { globalDispatch, showToast, tokenExpireError } = useContexts();
  const { mutateAsync: updateModel, isPending: isUpdating } = useUpdateModelMutation(Models.USER);
  const { profile, getProfile } = useProfile();
  const { sdk } = useSDK();
  const mode = state?.theme;
  const { isDeactivated, canPerformActions, statusMessage } = useTrainerStatus();

  // Loading state
  const [loading, setLoading] = useState({
    fetching: true,
    submitting: false,
  });

  // Modal state for adding custom qualifications/specializations
  const [modal, setModal] = useState({
    show: false,
    target: "",
    title: "",
    value: "",
  });

  // Content state for custom qualifications/specializations
  const [content, setContent] = useState<{
    qualifications: string[];
    specializations: string[];
  }>({
    qualifications: [],
    specializations: [],
  });

  // State management for password and 2FA modals
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [show2FAModal, setShow2FAModal] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);

  // State management for library modals
  const [showExerciseModal, setShowExerciseModal] = useState(false);
  const [showVideoModal, setShowVideoModal] = useState(false);

  // React Hook Form setup
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      full_name: "",
      email: "",
      gender: "",
      bio: "",
      years_of_experience: "",
      qualifications: [],
      specializations: [],
      email_notifications: false,
      in_app_notifications: true,
    },
  });

  // Watch form values for controlled components
  const formValues = watch();

  // Add custom qualification or specialization
  const addContent = () => {
    setContent((prev) => ({
      ...prev,
      [modal.target as keyof typeof prev]: [
        ...prev[modal.target as keyof typeof prev],
        modal.value,
      ],
    }));

    setModal({ show: false, target: "", title: "", value: "" });
  };
  const updateProfile = async () => {
    const profile = await getProfile();
    if (profile?.data) {
      const profileData = profile.data;
      // Set form values from profile data
      setValue("full_name", profileData?.full_name || profile?.full_name || "");
      setValue("email", profile?.email || "");
      setValue("gender", profileData?.gender || "");
      setValue("bio", profileData?.bio || "");
      setValue("years_of_experience", profileData?.years_of_experience || "");
      setValue("qualifications", profileData?.qualifications || []);
      setValue("specializations", profileData?.specializations || []);
      setValue(
        "email_notifications",
        profileData?.email_notifications ?? false
      );
      setValue(
        "in_app_notifications",
        profileData?.in_app_notifications ?? true
      );

      setLoading((prev) => ({ ...prev, fetching: false }));
    }
  }
  // Handle form submission
  const onSubmit = async (data: FormData) => {
    // Prevent updates for deactivated trainers
    if (!canPerformActions) {
      showToast("Your account has been deactivated. You cannot update your profile.", 4000, ToastStatusEnum.ERROR);
      return;
    }

    try {
      setLoading((prev) => ({ ...prev, submitting: true }));


      const profileData = {
        data: JSON.stringify({
          ...profile?.data,
          full_name: data.full_name,
          first_name: data.full_name.split(" ")[0] || "",
          last_name: data.full_name.split(" ")[1] || "",
          bio: data.bio,
          gender: data.gender,
          specializations: data.specializations,
          years_of_experience: data.years_of_experience,
          qualifications: data.qualifications,
          email_notifications: data.email_notifications,
          in_app_notifications: data.in_app_notifications,
          profile_update: true,
        }),
        profile_update: true,
      }
      const result = await updateModel({
        id: profile?.id,
        payload: profileData
      });

      if (!result.error) {
        showToast(
          "Profile updated successfully",
          3000,
          ToastStatusEnum.SUCCESS
        );
        updateProfile();
      } else {
        showToast(
          result.message || "Failed to update profile",
          3000,
          ToastStatusEnum.ERROR
        );
      }
    } catch (error: any) {
      const message =
        error?.response?.data?.message || error?.message || "An error occurred";
      console.error("Error updating profile:", error);
      showToast(message, 3000, ToastStatusEnum.ERROR);
      tokenExpireError(message);
    } finally {
      setLoading((prev) => ({ ...prev, submitting: false }));
    }
  };

  const handleCancel = () => {
    // Reset form to original values
    if (profile?.data) {
      const profileData = profile.data;
      let parsedData: any = profileData;
      if (typeof profileData === "string") {
        try {
          parsedData = JSON.parse(profileData);
        } catch (e) {
          console.warn("Could not parse profile data:", e);
        }
      }

      reset({
        full_name: parsedData?.full_name || profile?.full_name || "",
        email: profile?.email || "",
        gender: parsedData?.gender || "",
        bio: parsedData?.bio || "",
        years_of_experience: parsedData?.years_of_experience || "",
        qualifications: parsedData?.qualifications || [],
        specializations: parsedData?.specializations || [],
        email_notifications: parsedData?.email_notifications ?? false,
        in_app_notifications: parsedData?.in_app_notifications ?? true,
      });
    }
  };

  // Handle password change
  const handleChangePassword = () => {
    disableActionForDeactivatedTrainer(
      () => setShowPasswordModal(true),
      showToast,
      { isDeactivated, isVerified: false, isActive: false, canPerformActions, statusMessage }
    );
  };

  // Handle 2FA toggle
  const handleTwoFactorAuth = () => {
    disableActionForDeactivatedTrainer(
      () => setShow2FAModal(true),
      showToast,
      { isDeactivated, isVerified: false, isActive: false, canPerformActions, statusMessage }
    );
  };

  // Handle 2FA modal success
  const handle2FASuccess = (enabled: boolean) => {
    setTwoFactorEnabled(enabled);
    // Refresh profile data to get updated 2FA status
    getProfile();
  };

  // Handle exercise library
  const handleExerciseLibrary = () => {
    disableActionForDeactivatedTrainer(
      () => setShowExerciseModal(true),
      showToast,
      { isDeactivated, isVerified: false, isActive: false, canPerformActions, statusMessage }
    );
  };

  // Handle video library
  const handleVideoLibrary = () => {
    disableActionForDeactivatedTrainer(
      () => setShowVideoModal(true),
      showToast,
      { isDeactivated, isVerified: false, isActive: false, canPerformActions, statusMessage }
    );
  };

  // Set the path in global state for navigation highlighting
  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "profile",
      },
    });
  }, [globalDispatch]);

  useEffect(() => {
    updateProfile();
  }, []);

  // Check 2FA status when component loads
  useEffect(() => {
    const check2FAStatus = async () => {
      try {
        const result = await sdk.get2FAStatus();
        if (!result.error) {
          setTwoFactorEnabled(result.enabled || false);
        }
      } catch (error) {
        console.error("Error checking 2FA status:", error);
      }
    };

    if (profile) {
      check2FAStatus();
    }
  }, [profile, sdk]);

  // Show loading state while fetching profile data
  if (loading.fetching) {
    return (
      <Container>
        <div className="p-6 space-y-6 w-full max-w-7xl mx-auto">
          <header className="mb-8">
            <h1 className="text-2xl font-bold text-text">Profile</h1>
          </header>
          <div className="flex justify-center items-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-text">Loading profile...</p>
            </div>
          </div>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <div className="p-6 space-y-6 w-full max-w-7xl mx-auto">
        {/* Header */}
        <header className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-text">Profile</h1>
        </header>


        {/* Main Content Card */}
        <form onSubmit={handleSubmit(onSubmit, (errors) => {
          console.log(errors);
        })}>
          <div
            className="rounded-md shadow-sm border p-6 space-y-5 transition-colors duration-200"
            style={{
              backgroundColor: THEME_COLORS[mode].BACKGROUND,
              borderColor: THEME_COLORS[mode].BORDER,
            }}
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Profile Management & Professional */}
              <div className="space-y-8">
                {/* Profile Management Section */}
                <section className="space-y-6">
                  <h2 className="text-lg font-semibold text-text">
                    Profile Management
                  </h2>

                  <div className="space-y-6">
                    {/* Full Name */}
                    <LazyLoad>
                      <MkdInputV2
                        name="full_name"
                        type="text"
                        register={register}
                        errors={errors}
                        required
                        disabled={isDeactivated}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>Full Name</MkdInputV2.Label>
                          <MkdInputV2.Field placeholder="Enter your full name" />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>
                    </LazyLoad>

                    {/* Email */}
                    <LazyLoad>
                      <MkdInputV2
                        name="email"
                        type="email"
                        register={register}
                        errors={errors}
                        required
                        disabled={isDeactivated}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>Email</MkdInputV2.Label>
                          <MkdInputV2.Field placeholder="Enter your email" />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>
                    </LazyLoad>

                    {/* Gender */}
                    <LazyLoad>
                      <MkdInputV2
                        name="gender"
                        type="select"
                        register={register}
                        errors={errors}
                        options={genderOptions}
                        required
                        disabled={isDeactivated}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>Gender</MkdInputV2.Label>
                          <MkdInputV2.Field placeholder="Select Gender" />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>
                    </LazyLoad>

                    {/* Bio */}
                    <LazyLoad>
                      <MkdInputV2
                        name="bio"
                        type="textarea"
                        register={register}
                        errors={errors}
                        required
                        disabled={isDeactivated}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>Bio</MkdInputV2.Label>
                          <MkdInputV2.Field
                            placeholder="Write something about yourself"
                            rows={"4"}
                          />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>
                    </LazyLoad>
                  </div>
                </section>
                {/* Professional Section */}
                <section className="space-y-6">
                  <h2 className="text-lg font-semibold text-text">
                    Professional
                  </h2>

                  <div className="space-y-6">
                    {/* Experience */}
                    <LazyLoad>
                      <MkdInputV2
                        name="years_of_experience"
                        type="select"
                        register={register}
                        errors={errors}
                        options={yearsOfExperienceOptions}
                        required
                        disabled={isDeactivated}
                      >
                        <MkdInputV2.Container>
                          <MkdInputV2.Label>Experience</MkdInputV2.Label>
                          <MkdInputV2.Field placeholder="Select Experience Level" />
                          <MkdInputV2.Error />
                        </MkdInputV2.Container>
                      </MkdInputV2>
                    </LazyLoad>
                  </div>
                </section>
              </div>
              {/* Right Column - Notifications, Security, Library */}
              <div className="space-y-8">
                {/* Notifications Preferences */}
                <section className="space-y-6">
                  <h2 className="text-lg font-semibold text-text">
                    Notifications Preferences
                  </h2>

                  <div className="space-y-4">
                    {/* Email Notifications */}
                    <LazyLoad>
                      <MkdInputV2
                        name="email_notifications"
                        type="checkbox"
                        register={register}
                        errors={errors}
                        disabled={isDeactivated}
                      >
                        <MkdInputV2.Container className="flex items-center gap-3">
                          <MkdInputV2.Field />
                          <MkdInputV2.Label>
                            Email Notifications
                          </MkdInputV2.Label>
                        </MkdInputV2.Container>
                      </MkdInputV2>
                    </LazyLoad>

                    {/* In-app Notifications */}
                    <LazyLoad>
                      <MkdInputV2
                        name="in_app_notifications"
                        type="checkbox"
                        register={register}
                        errors={errors}
                        disabled={isDeactivated}
                      >
                        <MkdInputV2.Container className="flex items-center gap-3">
                          <MkdInputV2.Field />
                          <MkdInputV2.Label>
                            In-app Notifications
                          </MkdInputV2.Label>
                        </MkdInputV2.Container>
                      </MkdInputV2>
                    </LazyLoad>
                  </div>
                </section>

                {/* Account Security */}
                <section className="space-y-6">
                  <h2 className="text-lg font-semibold text-text">
                    Account Security
                  </h2>

                  <div className="space-y-4 flex flex-col">
                  {!profile?.login_type && <button
                      type="button"
                      className="w-full sm:w-56 h-10 rounded border border-primary text-primary text-sm font-semibold hover:bg-primary hover:text-white transition-colors duration-200"
                      style={{
                        borderColor: THEME_COLORS[mode].PRIMARY,
                        color: THEME_COLORS[mode].PRIMARY,
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor =
                          THEME_COLORS[mode].PRIMARY;
                        e.currentTarget.style.color = "white";
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = "transparent";
                        e.currentTarget.style.color =
                          THEME_COLORS[mode].PRIMARY;
                      }}
                      onClick={handleChangePassword}
                    >
                      Change Password
                    </button>}

                    <button
                      type="button"
                      className="w-full sm:w-56 h-10 rounded border border-primary text-primary text-sm font-semibold hover:bg-primary hover:text-white transition-colors duration-200"
                      style={{
                        borderColor: THEME_COLORS[mode].PRIMARY,
                        color: THEME_COLORS[mode].PRIMARY,
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor =
                          THEME_COLORS[mode].PRIMARY;
                        e.currentTarget.style.color = "white";
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = "transparent";
                        e.currentTarget.style.color =
                          THEME_COLORS[mode].PRIMARY;
                      }}
                      onClick={handleTwoFactorAuth}
                    >
                      Two-Factor Authentication
                    </button>
                  </div>
                </section>

                {/* Library */}
                <section className="space-y-6">
                  <h2 className="text-lg font-semibold text-text">Library</h2>

                  <div className="space-y-4 flex flex-col">
                    <button
                      type="button"
                      className="w-full sm:w-56 h-10 rounded border border-primary text-primary text-sm font-semibold hover:bg-primary hover:text-white transition-colors duration-200"
                      style={{
                        borderColor: THEME_COLORS[mode].PRIMARY,
                        color: THEME_COLORS[mode].PRIMARY,
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor =
                          THEME_COLORS[mode].PRIMARY;
                        e.currentTarget.style.color = "white";
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = "transparent";
                        e.currentTarget.style.color =
                          THEME_COLORS[mode].PRIMARY;
                      }}
                      onClick={handleExerciseLibrary}
                    >
                      Exercise Library
                    </button>

                    <button
                      type="button"
                      className="w-full hidden sm:w-56 h-10 rounded border border-primary text-primary text-sm font-semibold hover:bg-primary hover:text-white transition-colors duration-200"
                      style={{
                        borderColor: THEME_COLORS[mode].PRIMARY,
                        color: THEME_COLORS[mode].PRIMARY,
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor =
                          THEME_COLORS[mode].PRIMARY;
                        e.currentTarget.style.color = "white";
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = "transparent";
                        e.currentTarget.style.color =
                          THEME_COLORS[mode].PRIMARY;
                      }}
                      onClick={handleVideoLibrary}
                    >
                      Video Library
                    </button>
                  </div>
                </section>
              </div>
            </div>

            {/* Qualifications */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-text">
                Qualifications
              </label>
              <div className="flex md:flex-row flex-col gap-3">
                <div className="flex-1">
                  <div
                    className="grid grid-cols-1 md:grid-cols-2 gap-2 p-4 rounded-md border"
                    style={{
                      borderColor: THEME_COLORS[mode].BORDER,
                      backgroundColor: THEME_COLORS[mode].BACKGROUND,
                    }}
                  >
                    {[...qualificationsOptions, ...content.qualifications].map(
                      (qualification) => (
                        <LazyLoad key={qualification}>
                          <MkdInputV2
                            name="qualifications"
                            type="checkbox"
                            register={register}
                            errors={errors}
                            value={qualification}
                            required
                            disabled={isDeactivated}
                          >
                            <MkdInputV2.Container className="flex items-center gap-3">
                              <MkdInputV2.Field />
                              <MkdInputV2.Label className="text-[0.875rem] font-normal text-wrap">
                                {qualification}
                              </MkdInputV2.Label>
                            </MkdInputV2.Container>
                          </MkdInputV2>
                        </LazyLoad>
                      )
                    )}
                  </div>
                  {errors.qualifications && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.qualifications.message as string}
                    </p>
                  )}
                </div>
                <div className="flex justify-center items-start">
                  <InteractiveButton
                    type="button"
                    onClick={() =>
                      setModal((prev) => ({
                        ...prev,
                        show: true,
                        target: "qualifications",
                        title: "Add Qualification",
                      }))
                    }
                    className="w-full md:!min-w-fit md:w-fit px-2 border text-primary border-green-400 bg-transparent hover:bg-green-50 h-[2.875rem] flex items-center justify-center gap-2"
                    style={{
                      borderColor: THEME_COLORS[mode].PRIMARY,
                      color: THEME_COLORS[mode].PRIMARY,
                    }}
                  >
                    <PlusIcon />
                    Add New
                  </InteractiveButton>
                </div>
              </div>
            </div>

            {/* Specializations */}
            <div className="space-y-2 mt-2">
              <label className="text-sm font-medium text-text">
                Specializations
              </label>
              <div className="flex gap-3 flex-col md:flex-row">
                <div className="flex-1">
                  <div
                    className="grid grid-cols-1 md:grid-cols-2 gap-2 p-4 rounded-md border"
                    style={{
                      borderColor: THEME_COLORS[mode].BORDER,
                      backgroundColor: THEME_COLORS[mode].BACKGROUND,
                    }}
                  >
                    {[
                      ...specializationsOptions,
                      ...content.specializations,
                    ].map((specialization) => (
                      <LazyLoad key={specialization}>
                        <MkdInputV2
                          name="specializations"
                          type="checkbox"
                          register={register}
                          errors={errors}
                          value={specialization}
                          required
                          disabled={isDeactivated}
                        >
                          <MkdInputV2.Container className="flex items-center gap-3">
                            <MkdInputV2.Field />
                            <MkdInputV2.Label className="text-[0.875rem] font-normal text-wrap">
                              {specialization}
                            </MkdInputV2.Label>
                          </MkdInputV2.Container>
                        </MkdInputV2>
                      </LazyLoad>
                    ))}
                  </div>
                  {errors.specializations && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.specializations.message as string}
                    </p>
                  )}
                </div>
                <div className="flex justify-center items-start">
                  <InteractiveButton
                    type="button"
                    onClick={() =>
                      setModal((prev) => ({
                        ...prev,
                        show: true,
                        target: "specializations",
                        title: "Add Specialization",
                      }))
                    }
                    className="w-full md:!min-w-fit md:w-fit px-2 border text-primary border-green-400 bg-transparent hover:bg-green-50 h-[2.875rem] flex items-center justify-center gap-2"
                    style={{
                      borderColor: THEME_COLORS[mode].PRIMARY,
                      color: THEME_COLORS[mode].PRIMARY,
                    }}
                  >
                    <PlusIcon />
                    Add New
                  </InteractiveButton>
                </div>
              </div>
            </div>
            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row justify-end gap-4 pt-6 border-t border-border">
              <button
                type="button"
                onClick={handleCancel}
                disabled={loading.submitting}
                className="px-6 py-3 rounded border border-border text-text font-semibold hover:bg-background-hover transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  borderColor: THEME_COLORS[mode].BORDER,
                  color: THEME_COLORS[mode].TEXT,
                }}
                onMouseEnter={(e) => {
                  if (!loading.submitting) {
                    e.currentTarget.style.backgroundColor =
                      THEME_COLORS[mode].BACKGROUND_HOVER;
                  }
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "transparent";
                }}
              >
                Cancel
              </button>

              <InteractiveButton
                type="submit"
                disabled={loading.submitting || isDeactivated}
                loading={loading.submitting}
                className="px-6 py-3 rounded text-white font-semibold hover:opacity-90 transition-opacity duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  backgroundColor: THEME_COLORS[mode].PRIMARY,
                }}
                onMouseEnter={(e) => {
                  if (!loading.submitting && !isDeactivated) {
                    e.currentTarget.style.opacity = "0.9";
                  }
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.opacity = "1";
                }}
              >
                {loading.submitting ? "Saving..." : isDeactivated ? "Account Deactivated" : "Save Changes"}
              </InteractiveButton>
            </div>
          </div>
        </form>
      </div>

      {/* Modal for adding custom qualifications/specializations */}
      <Modal
        isOpen={modal.show}
        title={modal.title}
        modalCloseClick={() => {
          setModal({ show: false, target: "", title: "", value: "" });
        }}
        modalHeader={true}
        classes={{
          modal: "w-full  ",
          modalContent: "border-b border-gray-200 dark:border-[#3a3a3a]",
          modalDialog: "max-w-xl",
        }}
        modalFooter={
          <div className="flex justify-end pt-5 px-5 gap-2 w-full">
            <InteractiveButton
              type="button"
              onClick={() => {
                setModal({ show: false, target: "", title: "", value: "" });
              }}
              className="bg-transparent text-text px-4 py-2 rounded-md"
            >
              Cancel
            </InteractiveButton>

            <InteractiveButton
              type="button"
              onClick={() => {
                addContent();
              }}
              className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/80"
            >
              Add
            </InteractiveButton>
          </div>
        }
      >
        <div className="py-5">
          <MkdInputV2
            name={modal.target}
            onChange={(e) => {
              setModal((prev) => ({
                ...prev,
                value: e.target.value,
              }));
            }}
            value={modal.value}
            required
          >
            <MkdInputV2.Container>
              <MkdInputV2.Label className="font-normal capitalize text-text">
                {modal.target}
              </MkdInputV2.Label>
              <MkdInputV2.Field />
              <MkdInputV2.Error />
            </MkdInputV2.Container>
          </MkdInputV2>
        </div>
      </Modal>

      {/* Password Change Modal */}
      {!profile?.login_type && <UpdatePasswordModal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
      />}

      {/* Two-Factor Authentication Modal */}
      <TwoFactorAuthModal
        isOpen={show2FAModal}
        onClose={() => setShow2FAModal(false)}
        onSuccess={handle2FASuccess}
        currentlyEnabled={twoFactorEnabled}
      />

      {/* Exercise Library Modal */}
      <ExerciseLibraryModal
        isOpen={showExerciseModal}
        onClose={() => setShowExerciseModal(false)}
      />

      {/* Video Library Modal */}
      {/* <VideoLibraryModal
        isOpen={showVideoModal}
        onClose={() => setShowVideoModal(false)}
      /> */}
    </Container>
  );
};

export default ViewTrainerProfilePage;
