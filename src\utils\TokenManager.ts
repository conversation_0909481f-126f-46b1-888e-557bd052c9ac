import { baseUrl } from "./config";
import MkdSDK from "./MkdSDK";

export interface TokenRefreshResult {
  success: boolean;
  newToken?: string;
  newRefreshToken?: string;
  error?: string;
}

export class TokenManager {
  private static instance: TokenManager;
  private refreshPromise: Promise<TokenRefreshResult> | null = null;
  private refreshTimer: number | null = null;
  private sdk: MkdSDK;

  private constructor() {
    this.sdk = new MkdSDK({ project_id: "kanglink", baseurl: baseUrl });
    this.setupAutoRefresh();
  }

  public static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  /**
   * Check if current token is expired by decoding JWT
   * @param token JWT token to check
   * @returns boolean indicating if token is expired
   */
  private isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split(".")[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      // Add 30 second buffer to refresh before actual expiration
      return payload.exp < currentTime + 30;
    } catch (error) {
      console.error("Error checking token expiration:", error);
      return true; // Assume expired if we can't decode
    }
  }

  /**
   * Get time until token expires in seconds
   * @param token JWT token to check
   * @returns seconds until expiration, or 0 if expired/invalid
   */
  private getTokenExpirationTime(token: string): number {
    try {
      const payload = JSON.parse(atob(token.split(".")[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      return Math.max(0, payload.exp - currentTime);
    } catch (error) {
      console.error("Error getting token expiration time:", error);
      return 0;
    }
  }

  /**
   * Check if remember me is enabled and refresh token exists
   */
  private canRefreshToken(): boolean {
    const refreshToken = localStorage.getItem("refresh_token");
    const rememberMe = localStorage.getItem("remember_me");
    return !!(refreshToken && rememberMe === "true");
  }

  /**
   * Silently refresh token using stored refresh token
   * @returns Promise<TokenRefreshResult> indicating success and new tokens
   */
  async silentTokenRefresh(): Promise<TokenRefreshResult> {
    // If there's already a refresh in progress, return that promise
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this.performTokenRefresh();
    const result = await this.refreshPromise;
    this.refreshPromise = null;

    return result;
  }

  private async performTokenRefresh(): Promise<TokenRefreshResult> {
    try {
      const refreshToken = localStorage.getItem("refresh_token");

      if (!this.canRefreshToken()) {
        return {
          success: false,
          error: "No refresh token available or remember me not enabled",
        };
      }

      console.log("Attempting silent token refresh...");
      const response = await this.sdk.refreshToken(refreshToken!);

      if (response.error || !response?.access_token) {
        console.error("Silent token refresh failed:", response.message);
        return {
          success: false,
          error: response.message || "Token refresh failed",
        };
      }

      // Update localStorage with new tokens
      const newToken = response.access_token;
      const newRefreshToken = response.refresh_token;

      localStorage.setItem("token", newToken);
      if (newRefreshToken) {
        localStorage.setItem("refresh_token", newRefreshToken);
      }

      console.log("Token refreshed silently");

      // Setup next auto refresh
      this.setupAutoRefresh();

      return {
        success: true,
        newToken,
        newRefreshToken,
      };
    } catch (error) {
      console.error("Silent token refresh error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Setup automatic token refresh based on token expiration
   */
  private setupAutoRefresh(): void {
    // Clear existing timer
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }

    const token = localStorage.getItem("token");

    if (!token || !this.canRefreshToken()) {
      return;
    }

    const timeUntilExpiration = this.getTokenExpirationTime(token);

    if (timeUntilExpiration <= 0) {
      // Token is already expired, refresh immediately
      this.silentTokenRefresh();
      return;
    }

    // Schedule refresh 5 minutes before expiration (or halfway if token expires in less than 10 minutes)
    const refreshTime = Math.min(
      timeUntilExpiration - 300,
      timeUntilExpiration / 2
    );
    const refreshTimeMs = Math.max(refreshTime * 1000, 60000); // At least 1 minute

    console.log(
      `Token will be refreshed in ${Math.round(refreshTime / 60)} minutes`
    );

    this.refreshTimer = setTimeout(() => {
      this.silentTokenRefresh();
    }, refreshTimeMs);
  }

  /**
   * Check if token needs refresh and refresh if necessary
   * @returns Promise<boolean> indicating if token is valid (after potential refresh)
   */
  async ensureValidToken(): Promise<boolean> {
    const token = localStorage.getItem("token");

    if (!token) {
      return false;
    }

    if (this.isTokenExpired(token)) {
      if (!this.canRefreshToken()) {
        return false;
      }

      const result = await this.silentTokenRefresh();
      return result.success;
    }

    return true;
  }

  /**
   * Initialize token manager (call this when user logs in)
   */
  public initialize(): void {
    this.setupAutoRefresh();
  }

  /**
   * Cleanup token manager (call this when user logs out)
   */
  public cleanup(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
    this.refreshPromise = null;
  }

  /**
   * Get current token status
   */
  public getTokenStatus(): {
    hasToken: boolean;
    isExpired: boolean;
    canRefresh: boolean;
    expiresIn: number;
  } {
    const token = localStorage.getItem("token");

    if (!token) {
      return {
        hasToken: false,
        isExpired: true,
        canRefresh: this.canRefreshToken(),
        expiresIn: 0,
      };
    }

    return {
      hasToken: true,
      isExpired: this.isTokenExpired(token),
      canRefresh: this.canRefreshToken(),
      expiresIn: this.getTokenExpirationTime(token),
    };
  }
}

// Export singleton instance
export const tokenManager = TokenManager.getInstance();
