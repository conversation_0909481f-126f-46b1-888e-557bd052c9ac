import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

interface TrainerAboutProps {
  trainer: {
    about: string[];
    certifications: string[];
  };
}

const TrainerAbout = ({ trainer }: TrainerAboutProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <div className="w-full py-6 lg:py-8 xl:py-12">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 xl:gap-16 2xl:gap-20">
        {/* About Trainer Section */}
        <div className="space-y-4 lg:space-y-6">
          <h2
            className="text-lg sm:text-xl lg:text-2xl font-bold transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            About Trainer
          </h2>

          <div className="space-y-3 lg:space-y-4">
            {trainer.about.map((paragraph, index) => (
              <p
                key={index}
                className="text-sm sm:text-base lg:text-sm xl:text-base leading-relaxed transition-colors duration-200"
                style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
              >
                {paragraph}
              </p>
            ))}
          </div>
        </div>

        {/* Certification Section */}
        <div className="space-y-4 lg:space-y-6">
          <h2
            className="text-lg sm:text-xl lg:text-2xl font-bold transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            Certification
          </h2>

          <div className="space-y-3 lg:space-y-4">
            {trainer.certifications.map((paragraph, index) => (
              <p
                key={index}
                className="text-sm sm:text-base lg:text-sm xl:text-base leading-relaxed transition-colors duration-200"
                style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
              >
                {paragraph}
              </p>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrainerAbout;
