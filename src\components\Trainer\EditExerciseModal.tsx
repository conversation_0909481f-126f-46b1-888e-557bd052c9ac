import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Modal } from "@/components/Modal";
import { InteractiveButton } from "@/components/InteractiveButton";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { Exercise } from "@/interfaces";

// Yup validation schema
const exerciseSchema = yup.object({
  exerciseName: yup.string().trim().required("Exercise name is required").max(150, "Exercise name must be less than 150 characters"),
  videoUrl: yup
    .string()
    .trim()
    .max(255, "Video URL must be less than 255 characters")
    .required("Video URL is required")
    .test("is-valid-url", "Please enter a valid URL", (value) => {
      if (!value) return false;
      try {
        const url = new URL(value);
        return ['http:', 'https:'].includes(url.protocol);
      } catch {
        return false;
      }
    })
    .test("is-valid-url", "Please enter a valid URL", (value) => {
      if (!value) return false;
      try {
        const url = new URL(value);
        return ['http:', 'https:'].includes(url.protocol);
      } catch {
        return false;
      }
    }),
});

type ExerciseFormData = yup.InferType<typeof exerciseSchema>;

interface EditExerciseModalProps {
  isOpen: boolean;
  onClose: () => void;
  exercise: Exercise | null;
  onUpdateExercise: (exerciseId: number, exerciseName: string, videoUrl?: string) => void;
  isLoading?: boolean;
}

const EditExerciseModal: React.FC<EditExerciseModalProps> = ({
  isOpen,
  onClose,
  exercise,
  onUpdateExercise,
  isLoading = false,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<ExerciseFormData>({
    resolver: yupResolver(exerciseSchema),
  });

  // Update form when exercise changes
  useEffect(() => {
    if (exercise) {
      setValue("exerciseName", exercise.name || "");
      setValue("videoUrl", exercise.video_url || "");
    }
  }, [exercise, setValue]);

  const onSubmit = (data: ExerciseFormData) => {
    if (exercise?.id) {
      onUpdateExercise(exercise.id as number, data.exerciseName, data.videoUrl);
      reset();
      onClose();
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={handleClose}
      title="Edit Exercise"
      modalHeader={true}
      classes={{
        modalDialog: "!w-full md:!w-[25rem] !h-fit",
        modalContent: "!px-5 !pt-5",
        modal: "h-full",
      }}
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Exercise Name Input */}
        <MkdInputV2
          type="text"
          name="exerciseName"
          register={register}
          errors={errors}
          required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label>Exercise Name</MkdInputV2.Label>
            <MkdInputV2.Field placeholder="Enter exercise name" />
            <MkdInputV2.Error />
          </MkdInputV2.Container>
        </MkdInputV2>

        {/* Video URL Input */}
        <MkdInputV2
          type="text"
          name="videoUrl"
          register={register}
          errors={errors}
          required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label>Video URL</MkdInputV2.Label>
            <MkdInputV2.Field placeholder="Enter video URL (e.g., https://example.com/video.mp4 or https://youtube.com/watch?v=...)" />
            <MkdInputV2.Error />
          </MkdInputV2.Container>
        </MkdInputV2>

        {/* Footer Buttons */}
        <div className="flex justify-end gap-3 pt-4">
          <InteractiveButton
            type="button"
            onClick={handleClose}
            disabled={isLoading}
            className="px-4 py-2 border border-border rounded text-text hover:bg-input disabled:opacity-50"
          >
            Cancel
          </InteractiveButton>
          <InteractiveButton
            type="submit"
            disabled={isLoading}
            loading={isLoading}
            className={`px-4 py-2 rounded text-white ${"bg-primary hover:bg-primary-hover"
              // : "bg-gray-400 cursor-not-allowed"
              }`}
          >
            {isLoading ? "Updating..." : "Update Exercise"}
          </InteractiveButton>
        </div>
      </form>
    </Modal>
  );
};

export default EditExerciseModal; 