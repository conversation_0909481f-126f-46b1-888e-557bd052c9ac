import React, { useState } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface FilterOption {
  id: string;
  label: string;
  checked: boolean;
}

interface FiltersSidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  sortOptions?: FilterOption[];
  genderOptions?: FilterOption[];
  experienceOptions?: FilterOption[];
  onSortChange?: (optionId: string) => void;
  onGenderChange?: (optionId: string) => void;
  onExperienceChange?: (optionId: string) => void;
}

const FiltersSidebar: React.FC<FiltersSidebarProps> = ({
  isOpen = true,
  onClose,
  sortOptions = [
    { id: "relevance", label: "Relevance", checked: true },
    { id: "price", label: "Price", checked: false },
    { id: "bestseller", label: "Bestseller", checked: false },
  ],
  genderOptions = [
    { id: "female", label: "Female", checked: false },
    { id: "male", label: "Male", checked: false },
  ],
  experienceOptions = [
    { id: "beginner", label: "Beginner", checked: false },
    { id: "intermediate", label: "Intermediate", checked: false },
    { id: "advanced", label: "Advanced", checked: false },
  ],
  onSortChange,
  onGenderChange,
  onExperienceChange,
}) => {
  const [localSortOptions, setLocalSortOptions] = useState(sortOptions);
  const [localGenderOptions, setLocalGenderOptions] = useState(genderOptions);
  const [localExperienceOptions, setLocalExperienceOptions] = useState(experienceOptions);

  const handleSortChange = (optionId: string) => {
    setLocalSortOptions(prev => 
      prev.map(option => ({
        ...option,
        checked: option.id === optionId
      }))
    );
    onSortChange?.(optionId);
  };

  const handleGenderChange = (optionId: string) => {
    setLocalGenderOptions(prev => 
      prev.map(option => ({
        ...option,
        checked: option.id === optionId ? !option.checked : option.checked
      }))
    );
    onGenderChange?.(optionId);
  };

  const handleExperienceChange = (optionId: string) => {
    setLocalExperienceOptions(prev => 
      prev.map(option => ({
        ...option,
        checked: option.id === optionId ? !option.checked : option.checked
      }))
    );
    onExperienceChange?.(optionId);
  };

  if (!isOpen) return null;

  return (
    <div className="w-64 h-full bg-background-secondary shadow-lg border-l border-border">
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-bold text-text">
            Filters
          </h2>
          {onClose && (
            <button
              onClick={onClose}
              className="p-1 hover:bg-background-hover rounded transition-colors duration-200 lg:hidden"
            >
              <XMarkIcon className="h-5 w-5 text-text" />
            </button>
          )}
        </div>

        {/* Sort Section */}
        <div className="mb-8">
          <h3 className="text-base font-medium text-text mb-4">Sort</h3>
          <div className="space-y-3">
            {localSortOptions.map((option) => (
              <label key={option.id} className="flex items-center gap-3 cursor-pointer">
                <input
                  type="radio"
                  name="sort"
                  checked={option.checked}
                  onChange={() => handleSortChange(option.id)}
                  className="w-3 h-3 text-primary border-border focus:ring-primary"
                />
                <span className="text-base text-text-secondary">
                  {option.label}
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Gender Section */}
        <div className="mb-8">
          <h3 className="text-base font-medium text-text mb-4">Gender</h3>
          <div className="space-y-3">
            {localGenderOptions.map((option) => (
              <label key={option.id} className="flex items-center gap-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={option.checked}
                  onChange={() => handleGenderChange(option.id)}
                  className="w-3 h-3 text-primary border-border focus:ring-primary rounded"
                />
                <span className="text-base text-text-secondary">
                  {option.label}
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Experience Section */}
        <div className="mb-8">
          <h3 className="text-base font-medium text-text mb-4">Experience</h3>
          <div className="space-y-3">
            {localExperienceOptions.map((option) => (
              <label key={option.id} className="flex items-center gap-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={option.checked}
                  onChange={() => handleExperienceChange(option.id)}
                  className="w-3 h-3 text-primary border-border focus:ring-primary rounded"
                />
                <span className="text-base text-text-secondary">
                  {option.label}
                </span>
              </label>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FiltersSidebar;
