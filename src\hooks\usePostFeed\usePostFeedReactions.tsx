import { usePostFeedActions } from "./usePostFeedActions";

interface UsePostFeedReactionsProps {
  onReactionToggled?: (reactionData: any) => void;
}

export const usePostFeedReactions = ({
  onReactionToggled,
}: UsePostFeedReactionsProps = {}) => {
  const {
    toggleReaction,
    fetchReactions,
    reactions,
    userReaction,
    loading,
    setReactions,
    setUserReaction,
  } = usePostFeedActions({
    onReactionToggled,
  });

  return {
    // Actions
    toggleReaction,
    fetchReactions,

    // State
    reactions,
    userReaction,
    setReactions,
    setUserReaction,

    // Loading states
    isTogglingReaction: loading.isTogglingReaction,
    isFetchingReactions: loading.isFetchingReactions,
  };
}; 