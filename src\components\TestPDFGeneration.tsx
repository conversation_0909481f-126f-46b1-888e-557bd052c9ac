import React from 'react';
import { generateTransactionsPDF } from '@/utils/pdfGenerator';
import { Transaction } from '@/interfaces/model.interface';

const TestPDFGeneration: React.FC = () => {
  const testTransactions: Transaction[] = [
    {
      id: 1,
      trainer_name: '<PERSON>',
      created_at: '2024-01-15T10:30:00Z',
      payment_status: 'paid',
      total_amount: 199.99,
      coach_revenue: 139.99,
      platform_earning: 59.99,
      currency: 'USD',
    },
    {
      id: 2,
      trainer_name: '<PERSON>',
      created_at: '2024-01-16T14:20:00Z',
      payment_status: 'pending',
      total_amount: 299.99,
      coach_revenue: 209.99,
      platform_earning: 89.99,
      currency: 'USD',
    },
    {
      id: 3,
      trainer_name: '<PERSON>',
      created_at: '2024-01-17T09:15:00Z',
      payment_status: 'refunded',
      total_amount: 149.99,
      coach_revenue: 104.99,
      platform_earning: 44.99,
      currency: 'USD',
    },
  ];

  const handleTestPDF = () => {
    try {
      generateTransactionsPDF(testTransactions, {
        title: 'Test Transaction Report',
        subtitle: 'PDF Generation Test',
        includeFilters: {
          searchTerm: 'test search',
          dateFilter: 'Today',
          statusFilter: 'Show All',
        },
      });
      console.log('PDF generated successfully!');
    } catch (error) {
      console.error('Error generating PDF:', error);
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>PDF Generation Test</h2>
      <button
        onClick={handleTestPDF}
        style={{
          padding: '10px 20px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
        }}
      >
        Generate Test PDF
      </button>
    </div>
  );
};

export default TestPDFGeneration;
