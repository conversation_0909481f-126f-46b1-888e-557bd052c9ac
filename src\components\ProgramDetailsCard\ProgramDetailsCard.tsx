import React from "react";

interface ProgramDetail {
  label: string;
  value: string;
}

interface ProgramDetailsCardProps {
  details: ProgramDetail[];
}

const ProgramDetailsCard: React.FC<ProgramDetailsCardProps> = ({ details }) => {
  const renderValue = (detail: ProgramDetail) => {
    // Special handling for Split field
    if (detail.label === "Split") {
      // Handle "X splits: ..." format
      if (detail.value.includes(" splits: ")) {
        return (
          <span className="text-sm lg:text-base text-text-secondary font-normal break-words">
            {detail.value}
          </span>
        );
      }

      // Handle multiple splits with long text
      if (detail.value.length > 50 && detail.value.includes(", ")) {
        const splits = detail.value.split(", ");
        if (splits.length > 1) {
          return (
            <div className="text-sm lg:text-base text-text-secondary font-normal">
              <div className="space-y-1">
                {splits.map((split, idx) => (
                  <div key={idx} className="break-words">
                    • {split.trim()}
                  </div>
                ))}
              </div>
            </div>
          );
        }
      }
    }

    return (
      <span className="text-sm lg:text-base text-text-secondary font-normal break-words">
        {detail.value}
      </span>
    );
  };

  return (
    <div className="w-full bg-input rounded-lg shadow-md border border-border">
      <div className="p-4 lg:p-6">
        <div className="space-y-4">
          {details.map((detail, index) => {
            const isLongSplit =
              detail.label === "Split" &&
              detail.value.length > 50 &&
              detail.value.includes(", ") &&
              !detail.value.includes(" splits: ");

            return (
              <div
                key={index}
                className={`${
                  isLongSplit
                    ? "flex flex-col gap-2"
                    : "flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1"
                } pb-4 ${
                  index < details.length - 1 ? "border-b border-border" : ""
                }`}
              >
                <span className="text-sm lg:text-base font-medium text-text">
                  {detail.label}
                </span>
                {renderValue(detail)}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ProgramDetailsCard;
