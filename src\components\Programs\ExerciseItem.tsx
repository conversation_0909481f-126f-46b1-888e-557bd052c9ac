import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { PlayIcon, ChevronDownIcon } from "@heroicons/react/24/solid";
import { useState } from "react";
import VideoPlayerModal from "@/components/Library/VideoPlayerModal";

interface ExerciseItemProps {
  exerciseLetter: string;
  exerciseName: string;
  reps: string;
  videoUrl?: string;
  thumbnailUrl?: string;
  isLast?: boolean;
}

const ExerciseItem = ({
  exerciseLetter,
  exerciseName,
  reps,
  videoUrl = "",
  thumbnailUrl = "https://placehold.co/128x80",
  isLast = false,
}: ExerciseItemProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  // Helper functions for video URL handling (copied from ExerciseStatsGrid)
  const isYouTube =
    videoUrl?.includes("youtube.com") || videoUrl?.includes("youtu.be");
  const isVimeo = videoUrl?.includes("vimeo.com");
  const isExternalVideo = isYouTube || isVimeo;

  const getYouTubeVideoId = (url: string) => {
    return url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)?.[1];
  };

  const getVimeoVideoId = (url: string) => {
    return url.match(/vimeo\.com\/(\d+)/)?.[1];
  };

  const getYouTubeThumbnail = (videoId: string) => {
    return `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;
  };

  const getVimeoThumbnail = (videoId: string) => {
    // Use Vimeo's thumbnail API - this will work for most public videos
    return `https://vumbnail.com/${videoId}.jpg`;
  };

  const getVideoThumbnail = () => {
    if (!videoUrl) return thumbnailUrl;

    if (isYouTube) {
      const videoId = getYouTubeVideoId(videoUrl);
      return videoId ? getYouTubeThumbnail(videoId) : thumbnailUrl;
    } else if (isVimeo) {
      const videoId = getVimeoVideoId(videoUrl);
      return videoId ? getVimeoThumbnail(videoId) : thumbnailUrl;
    }

    return thumbnailUrl || videoUrl;
  };

  const handleVideoClick = () => {
    if (videoUrl) {
      setIsVideoModalOpen(true);
    }
  };

  // Create video object for modal
  const videoObject = {
    id: 0,
    name: exerciseName,
    video_url: videoUrl,
    video_type: isYouTube ? "youtube" : isVimeo ? "vimeo" : "direct",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  return (
    <>
      <div
        className={`flex items-center justify-between py-4 px-1 ${!isLast ? "border-b" : ""} transition-colors duration-200`}
        style={{
          borderColor: !isLast ? THEME_COLORS[mode].BORDER : "transparent",
        }}
      >
        {/* Left side - Exercise info */}
        <div className="flex items-center space-x-3">
          {/* Exercise letter circle */}
          <div
            className="w-4 h-4 rounded-full flex items-center justify-center transition-colors duration-200"
            style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY }}
          >
            <span
              className="text-xs font-normal font-inter"
              style={{ color: THEME_COLORS[mode].PRIMARY }}
            >
              {exerciseLetter}
            </span>
          </div>

          {/* Exercise details */}
          <div className="space-y-1">
            <p
              className="text-xs font-medium font-inter leading-none transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              {exerciseName}
            </p>
            <p
              className="text-xs font-normal font-inter leading-none transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
            >
              {reps}
            </p>
          </div>
        </div>

        {/* Right side - Thumbnail and actions */}
        <div className="flex items-center space-x-2">
          {/* Exercise thumbnail with play button */}
          <div
            className="relative w-11 h-9 rounded-md overflow-hidden bg-gradient-to-r from-green-400 to-amber-400 cursor-pointer hover:opacity-80 transition-opacity duration-200"
            onClick={handleVideoClick}
          >
            {videoUrl ? (
              <>
                {isExternalVideo ? (
                  /* External video thumbnail */
                  <img
                    src={getVideoThumbnail()}
                    alt={`${exerciseName} video preview`}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      // Fallback to placeholder if thumbnail fails to load
                      e.currentTarget.src = thumbnailUrl;
                    }}
                  />
                ) : (
                  /* Direct video file */
                  <video
                    src={videoUrl}
                    className="w-full h-full object-cover"
                    preload="metadata"
                    muted
                    onError={() => {
                      // Video will show first frame or poster
                    }}
                  />
                )}
                {/* Play button overlay */}
                <div className="absolute inset-0 bg-black/30 rounded-md flex items-center justify-center">
                  <div
                    className="w-6 h-6 rounded-full flex items-center justify-center transition-colors duration-200"
                    style={{ backgroundColor: "rgba(255, 255, 255, 0.9)" }}
                  >
                    <PlayIcon
                      className="w-2 h-3 ml-0.5"
                      style={{ color: THEME_COLORS[mode].PRIMARY }}
                    />
                  </div>
                </div>
              </>
            ) : (
              /* No video - show placeholder */
              <div className="w-full h-full flex items-center justify-center">
                <PlayIcon
                  className="w-2 h-3 text-text-secondary"
                  style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                />
              </div>
            )}
          </div>

          {/* Dropdown arrow */}
          <ChevronDownIcon
            className="w-3 h-3 transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
          />
        </div>
      </div>

      {/* Video Player Modal */}
      <VideoPlayerModal
        isOpen={isVideoModalOpen}
        onClose={() => setIsVideoModalOpen(false)}
        data={videoUrl ? videoObject : null}
      />
    </>
  );
};

export default ExerciseItem;
