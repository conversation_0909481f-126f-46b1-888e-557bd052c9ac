import React, { useState, useEffect } from "react";
import { Modal } from "@/components/Modal";
import { InteractiveButton } from "@/components/InteractiveButton";
import { MkdInputV2 } from "@/components/MkdInputV2";

interface EditVideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  video: any | null;
  onUpdateVideo: (videoId: number, videoName: string, videoUrl: string) => void;
  isLoading?: boolean;
}

const EditVideoModal: React.FC<EditVideoModalProps> = ({
  isOpen,
  onClose,
  video,
  onUpdateVideo,
  isLoading = false,
}) => {
  const [videoName, setVideoName] = useState("");
  const [videoUrl, setVideoUrl] = useState("");

  // Update form when video changes
  useEffect(() => {
    if (video) {
      setVideoName(video.name || "");
      setVideoUrl(video.url || "");
    }
  }, [video]);

  const handleSubmit = () => {
    if (videoName.trim() && videoUrl.trim() && video?.id) {
      onUpdateVideo(video.id as number, videoName.trim(), videoUrl.trim());
      onClose();
    }
  };

  const handleClose = () => {
    // Reset form on close
    setVideoName("");
    setVideoUrl("");
    onClose();
  };

  const isValid = videoName.trim() && videoUrl.trim() && !isLoading;

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={handleClose}
      title="Edit Video"
      modalHeader={true}
      classes={{
        modalDialog: "!w-full md:!w-[25rem] !h-fit",
        modalContent: "!px-5 !pt-5",
        modal: "h-full",
      }}
    >
      <div className="space-y-6">
        {/* Video Name Input */}
        <MkdInputV2
          name="videoName"
          type="text"
          value={videoName}
          onChange={(e) => setVideoName(e.target.value)}
          required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label>Video Name</MkdInputV2.Label>
            <MkdInputV2.Field placeholder="Enter video name" />
          </MkdInputV2.Container>
        </MkdInputV2>

        {/* Video URL Input */}
        <MkdInputV2
          name="videoUrl"
          type="url"
          value={videoUrl}
          onChange={(e) => setVideoUrl(e.target.value)}
          required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label>Video URL</MkdInputV2.Label>
            <MkdInputV2.Field placeholder="Enter video URL (YouTube, Vimeo, etc.)" />
          </MkdInputV2.Container>
        </MkdInputV2>

        {/* Footer Buttons */}
        <div className="flex justify-end gap-3 pt-4">
          <InteractiveButton
            type="button"
            onClick={handleClose}
            disabled={isLoading}
            className="px-4 py-2 border border-border rounded text-text hover:bg-input disabled:opacity-50"
          >
            Cancel
          </InteractiveButton>
          <InteractiveButton
            type="button"
            onClick={handleSubmit}
            disabled={!isValid}
            loading={isLoading}
            className={`px-4 py-2 rounded text-white ${
              isValid
                ? "bg-primary hover:bg-primary-hover"
                : "bg-gray-400 cursor-not-allowed"
            }`}
          >
            {isLoading ? "Updating..." : "Update Video"}
          </InteractiveButton>
        </div>
      </div>
    </Modal>
  );
};

export default EditVideoModal;
