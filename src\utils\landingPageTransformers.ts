import { LandingProgram, LandingTrainer } from "@/hooks/useLandingPageData";

// Interface for component-expected program data
export interface ComponentProgram {
  id: string;
  name: string;
  description: string;
  price: number;
  rating: number;
  image: string;
  isFavorite?: boolean;
}

// Interface for component-expected trainer data
export interface ComponentTrainer {
  id: string;
  name: string;
  description: string;
  image: string;
  rating: number;
  startingPrice: number;
  isFavorite?: boolean;
}

// Transform API program data to component format
export const transformProgramData = (
  apiProgram: LandingProgram
): ComponentProgram => {
  return {
    id: apiProgram.id.toString(),
    name: apiProgram.program_name,
    description: apiProgram.program_description,
    price: apiProgram.price || 0, // Price will need to be calculated from splits or set as default
    rating: parseFloat(apiProgram.rating) || 0,
    image: apiProgram.image || "",
    isFavorite: false, // This would need to be determined from user's favorites
  };
};

// Transform API trainer data to component format
export const transformTrainerData = (
  apiTrainer: LandingTrainer
): ComponentTrainer => {
  return {
    id: apiTrainer.id.toString(),
    name: apiTrainer.full_name,
    description: apiTrainer.bio,
    image: apiTrainer.photo || "",
    rating: parseFloat(apiTrainer.rating) || 0,
    startingPrice: 50, // Default starting price, would need to be calculated from trainer's programs
    isFavorite: false, // This would need to be determined from user's favorites
  };
};

// Transform array of API programs to component format
export const transformProgramsArray = (
  apiPrograms: LandingProgram[]
): ComponentProgram[] => {
  return apiPrograms.map(transformProgramData);
};

// Transform array of API trainers to component format
export const transformTrainersArray = (
  apiTrainers: LandingTrainer[]
): ComponentTrainer[] => {
  return apiTrainers.map(transformTrainerData);
};
