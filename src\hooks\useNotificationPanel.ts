import { useState, useCallback } from 'react';
import { useNotifications } from '../context/NotificationContext';

interface UseNotificationPanelReturn {
  isOpen: boolean;
  openPanel: () => void;
  closePanel: () => void;
  togglePanel: () => void;
}

export const useNotificationPanel = (): UseNotificationPanelReturn => {
  const [isOpen, setIsOpen] = useState(false);
  const { refreshNotifications } = useNotifications();

  const openPanel = useCallback(() => {
    setIsOpen(true);
    refreshNotifications();
  }, [refreshNotifications]);

  const closePanel = useCallback(() => {
    setIsOpen(false);
  }, []);

  const togglePanel = useCallback(() => {
    setIsOpen(prev => !prev);
    if (!isOpen) {
      refreshNotifications();
    }
  }, [isOpen, refreshNotifications]);

  return {
    isOpen,
    openPanel,
    closePanel,
    togglePanel,
  };
};

export default useNotificationPanel; 