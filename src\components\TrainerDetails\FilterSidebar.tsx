import { useState } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface FilterSidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  onFiltersChange?: (filters: FilterState) => void;
}

interface FilterState {
  sort: string;
  gender: string[];
  experience: string[];
}

const FilterSidebar = ({
  isOpen = true,
  onClose,
  onFiltersChange,
}: FilterSidebarProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const [filters, setFilters] = useState<FilterState>({
    sort: "relevance",
    gender: [],
    experience: [],
  });

  const handleSortChange = (value: string) => {
    const newFilters = { ...filters, sort: value };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const handleGenderChange = (value: string, checked: boolean) => {
    const newGender = checked
      ? [...filters.gender, value]
      : filters.gender.filter((g) => g !== value);
    const newFilters = { ...filters, gender: newGender };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const handleExperienceChange = (value: string, checked: boolean) => {
    const newExperience = checked
      ? [...filters.experience, value]
      : filters.experience.filter((e) => e !== value);
    const newFilters = { ...filters, experience: newExperience };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const sidebarClasses = `
    fixed lg:relative top-0 right-0 h-full lg:h-auto w-72 sm:w-80 md:w-72 lg:w-64 xl:w-72 2xl:w-80
    transform transition-transform duration-300 ease-in-out z-50
    ${isOpen ? "translate-x-0" : "translate-x-full lg:translate-x-0"}
  `;

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={sidebarClasses}
        style={{
          backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
          borderColor: THEME_COLORS[mode].BORDER,
        }}
      >
        <div className="h-full border-l lg:border-l-0 lg:border-r p-4 sm:p-6 lg:p-4 xl:p-6 overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3
              className="text-lg font-bold transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              Filters
            </h3>
            {onClose && (
              <button
                onClick={onClose}
                className="lg:hidden p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                <XMarkIcon
                  className="w-5 h-5 transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                />
              </button>
            )}
          </div>

          {/* Sort Section */}
          <div className="mb-8">
            <h4
              className="text-base font-medium mb-4 transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              Sort
            </h4>
            <div className="space-y-3">
              {[
                { value: "relevance", label: "Relevance" },
                { value: "price", label: "Price" },
                { value: "bestseller", label: "Bestseller" },
              ].map((option) => (
                <label
                  key={option.value}
                  className="flex items-center cursor-pointer"
                >
                  <input
                    type="radio"
                    name="sort"
                    value={option.value}
                    checked={filters.sort === option.value}
                    onChange={(e) => handleSortChange(e.target.value)}
                    className="w-4 h-4 sm:w-5 sm:h-5 lg:w-4 lg:h-4 border border-gray-400 rounded-full mr-3 lg:mr-4 focus:ring-2 focus:ring-blue-500"
                  />
                  <span
                    className="text-sm sm:text-base lg:text-sm xl:text-base transition-colors duration-200"
                    style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                  >
                    {option.label}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Gender Section */}
          <div className="mb-8">
            <h4
              className="text-base font-medium mb-4 transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              Gender
            </h4>
            <div className="space-y-3">
              {[
                { value: "female", label: "Female" },
                { value: "male", label: "Male" },
              ].map((option) => (
                <label
                  key={option.value}
                  className="flex items-center cursor-pointer"
                >
                  <input
                    type="checkbox"
                    value={option.value}
                    checked={filters.gender.includes(option.value)}
                    onChange={(e) =>
                      handleGenderChange(option.value, e.target.checked)
                    }
                    className="w-3 h-3 border border-gray-400 rounded mr-3 focus:ring-2 focus:ring-blue-500"
                  />
                  <span
                    className="text-sm transition-colors duration-200"
                    style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                  >
                    {option.label}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Experience Section */}
          <div className="mb-8">
            <h4
              className="text-base font-medium mb-4 transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              Experience
            </h4>
            <div className="space-y-3">
              {[
                { value: "beginner", label: "Beginner" },
                { value: "intermediate", label: "Intermediate" },
                { value: "advanced", label: "Advanced" },
              ].map((option) => (
                <label
                  key={option.value}
                  className="flex items-center cursor-pointer"
                >
                  <input
                    type="checkbox"
                    value={option.value}
                    checked={filters.experience.includes(option.value)}
                    onChange={(e) =>
                      handleExperienceChange(option.value, e.target.checked)
                    }
                    className="w-3 h-3 border border-gray-400 rounded mr-3 focus:ring-2 focus:ring-blue-500"
                  />
                  <span
                    className="text-sm transition-colors duration-200"
                    style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                  >
                    {option.label}
                  </span>
                </label>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default FilterSidebar;
