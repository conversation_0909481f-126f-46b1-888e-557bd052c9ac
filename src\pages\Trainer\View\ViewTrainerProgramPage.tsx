import { useEffect, useState, useRef } from "react";
import { StepOneFormData } from "@/components/CreateProgramStepOne";
import { CreateProgramPreview } from "@/components/CreateProgramPreview";
import {
  MkdWizardContainer,
  MkdWizardContainerRef,
} from "@/components/MkdWizardContainer";
import { useContexts } from "@/hooks/useContexts";
import { useParams } from "react-router";
import { useCustomModelQuery } from "@/query/shared";
import { ProgramPreviewSkeleton } from "@/components/Skeleton";

const ViewTrainerProgramPage = () => {
  const { globalDispatch } = useContexts();
  const [stepOneData, setStepOneData] = useState<StepOneFormData | null>(null);
  const [stepTwoData, setStepTwoData] = useState<any>(null);
  const wizardRef = useRef<MkdWizardContainerRef>(null);
  const params = useParams();
  const programId = params.programId;

  const { mutateAsync: fetchProgramData, isPending } = useCustomModelQuery();

  const fetchProgram = async () => {
    try {
      const response = await fetchProgramData({
        endpoint: `/v2/api/kanglink/custom/trainer/programs/${programId}`,
        method: "GET",
      });
      const programData = response?.data;

      setStepOneData(programData?.stepOneData);
      setStepTwoData(programData?.stepTwoData);
    } catch (error) {
      console.error("Error fetching program data:", error);
    }
  };

  const handDone = () => {
    // This is now the "Done" action from preview - navigate back to previous page
    window.history.back();
  };

  useEffect(() => {
    if (programId) {
      fetchProgram();
    }
  }, [fetchProgramData, programId]);

  // Set the path in global state for navigation highlighting
  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "program",
      },
    });
  }, [globalDispatch]);

  // Show skeleton loader while data is loading
  if (isPending || !stepOneData || !stepTwoData) {
    return (
      <div className="min-h-screen bg-background-secondary py-6">
        <ProgramPreviewSkeleton />
      </div>
    );
  }

  return (
    <MkdWizardContainer ref={wizardRef} showButton={false}>
      <CreateProgramPreview
        componentId={1}
        onSubmit={handDone}
        onCancel={handDone}
        stepOneData={stepOneData}
        stepTwoData={stepTwoData}
      />
    </MkdWizardContainer>
  );
};

export default ViewTrainerProgramPage;
