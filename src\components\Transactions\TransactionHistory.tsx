import React, { useState } from "react";
import { useTransactionHistory } from "@/hooks/useTrainerTransactions";
import { ChevronLeft, ChevronRight, Filter, Download } from "lucide-react";

interface TransactionHistoryProps {
  onRefresh?: () => void;
}

const TransactionHistory: React.FC<TransactionHistoryProps> = ({ onRefresh }) => {
  const { history, loading, error, fetchHistory } = useTransactionHistory();
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    type: "all",
    status: "",
    start_date: "",
    end_date: "",
  });
  const [showFilters, setShowFilters] = useState(false);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchHistory({ ...filters, page, limit: 20 });
  };

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters);
    setCurrentPage(1);
    fetchHistory({ ...newFilters, page: 1, limit: 20 });
  };

  const handleRefresh = () => {
    fetchHistory({ ...filters, page: currentPage, limit: 20 });
    onRefresh?.();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400", label: "Pending" },
      processed: { color: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400", label: "Processed" },
      failed: { color: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400", label: "Failed" },
      cancelled: { color: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400", label: "Cancelled" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  if (loading && !history) {
    return (
      <div className="rounded-lg shadow-sm border border-border bg-secondary dark:bg-neutral-800 dark:border-[#3a3a3a]">
        <div className="p-6">
          <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-40 mb-4 animate-pulse"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="h-16 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg shadow-sm border border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800 p-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-red-600 dark:text-red-400">
            Transaction History
          </h3>
          <button
            onClick={handleRefresh}
            className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 font-medium"
          >
            Retry
          </button>
        </div>
        <p className="mt-2 text-sm text-red-600 dark:text-red-400">
          Failed to load transaction history
        </p>
      </div>
    );
  }

  return (
    <div className="rounded-lg shadow-sm border border-border bg-secondary dark:bg-neutral-800 dark:border-[#3a3a3a]">
      {/* Header */}
      <div className="p-6 border-b border-border dark:border-[#3a3a3a]">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-text dark:text-gray-100">
            Transaction History
          </h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center px-3 py-2 text-sm font-medium text-text-secondary bg-background border border-border rounded-md hover:bg-background-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </button>
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="flex items-center px-3 py-2 text-sm font-medium text-text-secondary bg-background border border-border rounded-md hover:bg-background-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </button>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-background dark:bg-neutral-900 rounded-lg">
            <div>
              <label className="block text-sm font-medium text-text mb-1">Type</label>
              <select
                value={filters.type}
                onChange={(e) => handleFilterChange({ ...filters, type: e.target.value })}
                className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text"
              >
                <option value="all">All Types</option>
                <option value="earnings">Earnings</option>
                <option value="withdrawals">Withdrawals</option>
                <option value="refunds">Refunds</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-text mb-1">Status</label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange({ ...filters, status: e.target.value })}
                className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text"
              >
                <option value="">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="processed">Processed</option>
                <option value="failed">Failed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-text mb-1">Start Date</label>
              <input
                type="date"
                value={filters.start_date}
                onChange={(e) => handleFilterChange({ ...filters, start_date: e.target.value })}
                className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-text mb-1">End Date</label>
              <input
                type="date"
                value={filters.end_date}
                onChange={(e) => handleFilterChange({ ...filters, end_date: e.target.value })}
                className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text"
              />
            </div>
          </div>
        )}
      </div>

      {/* Transaction List */}
      <div className="divide-y divide-border dark:divide-[#3a3a3a]">
        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-sm text-text-secondary">Loading transactions...</p>
          </div>
        ) : !history?.transactions.length ? (
          <div className="p-12 text-center">
            <div className="text-text-secondary">
              <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-sm">No transactions found</p>
            </div>
          </div>
        ) : (
          history.transactions.map((transaction) => (
            <div key={transaction.commission_id} className="p-6 hover:bg-background-hover transition-colors duration-200">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-text">
                      {transaction.program.title}
                    </h4>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(transaction.status)}
                      <span className="text-sm font-medium text-text">
                        {formatCurrency(transaction.amount.trainer_amount, transaction.amount.currency)}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-sm text-text-secondary">
                    <div>
                      <span>{transaction.split.title}</span>
                      <span className="mx-2">•</span>
                      <span>{transaction.athlete.name}</span>
                    </div>
                    <span>{formatDate(transaction.dates.created)}</span>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {history && history.pagination.total_pages > 1 && (
        <div className="p-6 border-t border-border dark:border-[#3a3a3a]">
          <div className="flex items-center justify-between">
            <div className="text-sm text-text-secondary">
              Showing {((history.pagination.current_page - 1) * history.pagination.per_page) + 1} to{" "}
              {Math.min(history.pagination.current_page * history.pagination.per_page, history.pagination.total_records)} of{" "}
              {history.pagination.total_records} transactions
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={!history.pagination.has_prev || loading}
                className="flex items-center px-3 py-2 text-sm font-medium text-text-secondary bg-background border border-border rounded-md hover:bg-background-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                Previous
              </button>
              <span className="text-sm text-text-secondary">
                Page {history.pagination.current_page} of {history.pagination.total_pages}
              </span>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={!history.pagination.has_next || loading}
                className="flex items-center px-3 py-2 text-sm font-medium text-text-secondary bg-background border border-border rounded-md hover:bg-background-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
                <ChevronRight className="w-4 h-4 ml-1" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TransactionHistory;
