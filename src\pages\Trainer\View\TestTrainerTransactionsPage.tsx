import React, { useState } from "react";
import { ThemeStyles } from "@/components/ThemeStyles";
import { useContexts } from "@/hooks/useContexts";
import {
  TransactionStatsCards,
  EarningsGraph,
  StripeConnectSetup,
  WithdrawalModal,
} from "@/components/Transactions";

/**
 * Test page for trainer transactions components with test data
 * Access this page by adding ?test=true to the URL or setting REACT_APP_USE_TEST_DATA=true
 */
const TestTrainerTransactionsPage = () => {
  const { globalDispatch } = useContexts();
  const [isWithdrawalModalOpen, setIsWithdrawalModalOpen] = useState(false);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "transaction",
      },
    });
  }, [globalDispatch]);

  const handleRefresh = () => {
    // This will trigger refresh in child components
    console.log("Refreshing components...");
  };

  const handleWithdrawFunds = () => {
    setIsWithdrawalModalOpen(true);
  };

  const handleWithdrawalSuccess = () => {
    handleRefresh();
  };

  return (
    <>
      <ThemeStyles />
      <div className="relative flex flex-col gap-6 px-4 py-6 w-full max-w-[1200px] mx-auto min-h-screen bg-background text-text">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="font-bold text-2xl sm:text-3xl text-text dark:text-gray-100">
              Trainer Transactions (Test Mode)
            </h1>
            <p className="text-sm text-text-secondary mt-1">
              This page uses test data for development and demonstration purposes
            </p>
          </div>
          <button
            onClick={handleWithdrawFunds}
            className="flex w-full sm:w-auto justify-center items-center px-6 py-3 rounded bg-green-400 hover:bg-green-500 text-white font-semibold text-base transition-colors duration-200"
          >
            Withdraw Funds
          </button>
        </div>

        {/* Test Data Info */}
        <div className="rounded-lg shadow-sm border border-blue-200 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800 p-4">
          <div className="flex items-start">
            <div className="text-blue-600 dark:text-blue-400">
              <svg className="w-5 h-5 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                Test Data Active
              </h3>
              <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
                <ul className="list-disc list-inside space-y-1">
                  <li>All API calls are mocked with realistic test data</li>
                  <li>Stripe Connect status cycles through different states</li>
                  <li>Withdrawal requests simulate processing delays</li>
                  <li>No real payments or transactions are processed</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <TransactionStatsCards onRefresh={handleRefresh} />

        {/* Main Content - Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Payment Settings Section */}
          <div className="rounded-lg shadow-sm border border-border bg-secondary p-6 dark:bg-neutral-800 dark:border-[#3a3a3a]">
            <StripeConnectSetup onRefresh={handleRefresh} />
          </div>

          {/* Earning Graph Section */}
          <EarningsGraph onRefresh={handleRefresh} />
        </div>

        {/* Test Controls */}
        <div className="rounded-lg shadow-sm border border-border bg-secondary p-6 dark:bg-neutral-800 dark:border-[#3a3a3a]">
          <h3 className="text-lg font-medium text-text dark:text-gray-100 mb-4">
            Test Controls
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <button
              onClick={handleRefresh}
              className="px-4 py-2 text-sm font-medium text-text-secondary bg-background border border-border rounded-md hover:bg-background-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              Refresh All Data
            </button>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 text-sm font-medium text-text-secondary bg-background border border-border rounded-md hover:bg-background-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              Reload Page
            </button>
            <button
              onClick={() => {
                const url = new URL(window.location.href);
                url.searchParams.delete("test");
                window.location.href = url.toString();
              }}
              className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
            >
              Exit Test Mode
            </button>
          </div>
        </div>

        {/* Withdrawal Modal */}
        <WithdrawalModal
          isOpen={isWithdrawalModalOpen}
          onClose={() => setIsWithdrawalModalOpen(false)}
          onSuccess={handleWithdrawalSuccess}
        />
      </div>
    </>
  );
};

export default TestTrainerTransactionsPage;
