import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useTheme } from "@/hooks/useTheme";
import { useContexts } from "@/hooks/useContexts";
import { useProfile } from "@/hooks/useProfile";
import { useSDK } from "@/hooks/useSDK";
import { ToastStatusEnum } from "@/utils/Enums";
import UpdatePasswordModal from "@/components/UserProfile/UpdatePasswordModal";
import { TwoFactorAuthModal } from "@/components/Profile";
import {
  ProfileManagementCard,
  NotificationPreferencesCard,
  AccountSecurityCard,
} from "@/components/Athlete";
// new purchases. updates to old purchases.

// Validation schema
const schema = yup.object().shape({
  fullName: yup.string().required("Full Name is required"),
  email: yup.string().email("Invalid email").required("Email is required"),
  fitnessGoals: yup.array().required("Please select your fitness goals").min(1, "Please select at least one fitness goal"),
  dateOfBirth: yup.string().required("Date of Birth is required"),
  level: yup.string().required("Please select your level"),
  emailNotifications: yup.boolean(),
  inAppNotifications: yup.boolean(),
  photo: yup.string().optional(),
});

type FormData = yup.InferType<typeof schema>;

const ViewAthleteProfilePage = () => {
  const { state } = useTheme();
  const { globalDispatch, showToast, tokenExpireError } = useContexts();
  const { profile, getProfile } = useProfile();
  const { sdk } = useSDK();
  const mode = state?.theme;

  // Loading state
  const [loading, setLoading] = useState({
    fetching: true,
    submitting: false,
  });
  const [profileImage, setProfileImage] = useState<any>(null);

  // State management for password and 2FA modals
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [show2FAModal, setShow2FAModal] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);

  // React Hook Form setup
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      fullName: "",
      email: "",
      fitnessGoals: [],
      dateOfBirth: "",
      level: "Beginner",
      emailNotifications: false,
      inAppNotifications: true,
      photo: "",
    },
  });

  const formValues = watch();
  // Handle form submission
  const onSubmit = async (data: FormData) => {
    try {
      setLoading((prev) => ({ ...prev, submitting: true }));
      // if profileiamge  and profile?.photo is not the same, then send it
      if (profileImage && profileImage !== profile?.photo) {
        const formData = new FormData();
        formData.append("file", profileImage);

        const result = await sdk?.uploadImage(formData);
        if (result && !result.error && result.url) {
          data.photo = result.url;
        }
      }

      // Prepare payload
      const payload: any = {
        fitness_goals: data.fitnessGoals,
        date_of_birth: data.dateOfBirth,
        level: data.level,
        email_notifications: data.emailNotifications,
        in_app_notifications: data.inAppNotifications,
        full_name: data.fullName,
      };

      // Only include photo if it's been changed (not empty and different from current)
      if (data.photo && data.photo !== profile?.photo) {
        payload.photo = data.photo;
      }

      const result = await sdk.updateProfile({
        full_name: data.fullName,
        email: data.email,
        payload,
      });

      if (!result.error) {
        showToast(
          "Profile updated successfully",
          3000,
          ToastStatusEnum.SUCCESS
        );
        getProfile(); // Refresh profile data
      } else {
        showToast(
          result.message || "Failed to update profile",
          3000,
          ToastStatusEnum.ERROR
        );
      }
    } catch (error: any) {
      const message =
        error?.response?.data?.message || error?.message || "An error occurred";
      console.error("Error updating profile:", error);
      showToast(message, 3000, ToastStatusEnum.ERROR);
      tokenExpireError(message);
    } finally {
      setLoading((prev) => ({ ...prev, submitting: false }));
    }
  };

  const handleCancel = () => {
    // Reset form to original values
    if (profile?.data) {
      const profileData = profile.data;
      let parsedData: any = profileData;
      if (typeof profileData === "string") {
        try {
          parsedData = JSON.parse(profileData);
        } catch (e) {
          console.warn("Could not parse profile data:", e);
        }
      }

      reset({
        fullName: parsedData?.full_name || profile?.full_name || "",
        email: profile?.email || "",
        fitnessGoals: parsedData?.fitness_goals || [],
        dateOfBirth: parsedData?.date_of_birth || "",
        level: parsedData?.level || "Beginner",
        emailNotifications: parsedData?.email_notifications ?? false,
        inAppNotifications: parsedData?.in_app_notifications ?? true,
        photo: profile?.photo || "",
      });
    }
  };

  // Handle password change
  const handleChangePassword = () => {
    setShowPasswordModal(true);
  };

  // Handle 2FA toggle
  const handleTwoFactorAuth = () => {
    setShow2FAModal(true);
  };

  // Handle 2FA modal success
  const handle2FASuccess = (enabled: boolean) => {
    setTwoFactorEnabled(enabled);
    // Refresh profile data to get updated 2FA status
    getProfile();
  };

  // Set the path in global state for navigation highlighting
  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "profile",
      },
    });
  }, [globalDispatch]);

  useEffect(() => {
    if (profile?.data) {
      const profileData = profile.data;
      // Set form values from profile data
      setValue("fullName", profileData?.full_name || profile?.full_name || "");
      setValue("email", profile?.email || "");
      setValue("fitnessGoals", profileData?.fitness_goals || []);
      setValue("dateOfBirth", profileData?.date_of_birth || "");
      setValue("level", profileData?.level || "Beginner");
      setValue(
        "emailNotifications",
        profileData?.email_notifications ?? false
      );
      setValue(
        "inAppNotifications",
        profileData?.in_app_notifications ?? true
      );

      setValue("photo", profile?.photo || "");

      setLoading((prev) => ({ ...prev, fetching: false }));
    }
  }, [profile?.data, setValue]);

  // Check 2FA status when component loads
  useEffect(() => {
    const check2FAStatus = async () => {
      try {
        const result = await sdk.get2FAStatus();
        if (!result.error) {
          setTwoFactorEnabled(result.enabled || false);
        }
      } catch (error) {
        console.error("Error checking 2FA status:", error);
      }
    };

    if (profile) {
      check2FAStatus();
    }
  }, [profile, sdk]);

  // Show loading state while fetching profile data
  if (loading.fetching) {
    return (
      <div className="bg-background text-text p-4 md:p-6">
        {/* Page Header */}
        <div className="mb-6 max-w-7xl mx-auto">
          <h1 className="text-2xl font-bold text-text">Profile</h1>
        </div>
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-text">Loading profile...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background text-text p-4 md:p-6">
      {/* Page Header */}
      <div className="mb-6 max-w-7xl mx-auto">
        <h1 className="text-2xl font-bold text-text">Profile</h1>
      </div>

      {/* Main Content Grid */}
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 max-w-7xl mx-auto">
          {/* Left Column - Profile Management */}
          <div className="w-full">
            <ProfileManagementCard
              register={register}
              errors={errors}
              setValue={setValue}
              watch={watch}
              profileImage={formValues.photo}
              setProfileImage={(file: any) => {
                setProfileImage(file);
              }}
            />
          </div>

          {/* Right Column - Notifications & Security */}
          <div className="w-full space-y-6">
            <NotificationPreferencesCard
              register={register}
              errors={errors}
              setValue={setValue}
              watch={watch}
            />
            <AccountSecurityCard
              onChangePassword={handleChangePassword}
              onTwoFactorAuth={handleTwoFactorAuth}
              authType={profile?.login_type ? "oauth" : "email"}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-end gap-4 pt-6 mt-6 max-w-7xl mx-auto">
          <button
            type="button"
            onClick={handleCancel}
            disabled={loading.submitting}
            className="px-6 py-3 rounded border border-border text-text font-semibold hover:bg-background-hover transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>

          <button
            type="submit"
            disabled={loading.submitting}
            className="px-6 py-3 rounded bg-primary text-white font-semibold hover:opacity-90 transition-opacity duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading.submitting ? "Saving..." : "Save Changes"}
          </button>
        </div>
      </form>

      {/* Password Change Modal */}
      {!profile?.login_type && <UpdatePasswordModal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
      />}

      {/* Two-Factor Authentication Modal */}
      <TwoFactorAuthModal
        isOpen={show2FAModal}
        onClose={() => setShow2FAModal(false)}
        onSuccess={handle2FASuccess}
        currentlyEnabled={twoFactorEnabled}
      />
    </div>
  );
};

export default ViewAthleteProfilePage;
