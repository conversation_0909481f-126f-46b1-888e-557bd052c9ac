import { useState, useEffect } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, PostComposer, FeedPost } from "@/components/Athlete";
import { useCustomModelQuery } from "@/query/shared/customModel";

interface Enrollment {
  id: string;
  program_id: string;
  split_id: string;
  program_name: string;
  split_title: string;
  program: {
    id: string;
    program_name: string;
    type_of_program: string;
    allow_comments: boolean;
  };
  split: {
    id: string;
    title: string;
  };
  trainer: {
    id: string;
    email: string;
    data: any;
  };
}

interface ApiPost {
  id: string;
  user_id: string;
  program_id: string;
  split_id: string;
  post_type: string;
  content: string;
  rating?: number;
  attachments?: any;
  is_private: boolean;
  is_anonymous?: boolean;
  visibility_scope: string;
  is_pinned: boolean;
  is_edited: boolean;
  is_flagged: boolean;
  reaction_count: number;
  comment_count: number;
  created_at: string;
  updated_at: string;
  user: {
    id: string;
    email: string;
    data: any;
  };
  program: {
    id: string;
    program_name: string;
    type_of_program: string;
  };
  split?: {
    id: string;
    title: string;
  };
}

interface Author {
  id: string;
  name: string;
  avatar: string;
  isTrainer?: boolean;
}

interface Post {
  id: string;
  author: Author;
  content: string;
  timestamp: string;
  isPrivate?: boolean;
  reactions: Array<{
    type: "like" | "love" | "comment" | "share";
    count: number;
    isActive?: boolean;
  }>;
  comments: Array<{
    id: string;
    author: Author;
    content: string;
    timestamp: string;
    isPrivate?: boolean;
    reactions: Array<{
      type: "like" | "love" | "comment" | "share";
      count: number;
      isActive?: boolean;
    }>;
    replies?: any[];
  }>;
}

const ViewAthleteFeedPage = () => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [posts, setPosts] = useState<Post[]>([]);
  const [selectedEnrollment, setSelectedEnrollment] =
    useState<Enrollment | null>(null);

  // Use custom model queries
  const postsQuery = useCustomModelQuery({
    role: "member",
    showToast: false,
  });

  const createPostQuery = useCustomModelQuery({
    role: "member",
    showToast: true,
  });

  // Transform API post to FeedPost format
  const transformApiPostToPost = (apiPost: ApiPost): Post => {
    // Extract user data - handle anonymous posts
    const userData = (() => {
      try {
        return apiPost.user.data ? JSON.parse(apiPost.user.data) : {};
      } catch (error) {
        console.error("Error parsing user data:", error);
        return apiPost.user.data;
      }
    })();
    const isAnonymous = apiPost.is_anonymous;

    const userName = isAnonymous
      ? "Anonymous User"
      : userData?.full_name ||
        (userData.first_name && userData.last_name
          ? `${userData.first_name} ${userData.last_name}`
          : "") ||
        apiPost.user.email;

    const author: Author = {
      id: isAnonymous ? "anonymous" : apiPost.user.id,
      name: userName,
      avatar: isAnonymous
        ? "https://placehold.co/48x48"
        : userData.avatar || "https://placehold.co/48x48",
      isTrainer:
        !isAnonymous && apiPost.user_id === selectedEnrollment?.trainer.id,
    };

    return {
      id: apiPost.id,
      author,
      content: apiPost.content,
      timestamp: apiPost.created_at,
      isPrivate: apiPost.is_private,
      reactions: [
        {
          type: "like" as const,
          count: apiPost.reaction_count,
          isActive: false,
        },
        { type: "love" as const, count: 0, isActive: false },
        {
          type: "comment" as const,
          count: apiPost.comment_count,
          isActive: false,
        },
        { type: "share" as const, count: 0, isActive: false },
      ],
      comments: [], // Comments will be loaded separately if needed
    };
  };

  // Fetch posts for the selected program
  const fetchPosts = async (programId: string, searchQuery?: string) => {
    try {
      const params = new URLSearchParams({
        program_id: programId,
        page: "1",
        limit: "50",
        ...(searchQuery && { search: searchQuery }),
      });

      const response = await postsQuery.mutateAsync({
        endpoint: `/v2/api/kanglink/custom/trainer/feed?${params.toString()}`,
        method: "GET",
      });
      console.log(response);
      if (!response.error && response.data) {
        const transformedPosts = response.data.map(transformApiPostToPost);
        setPosts(transformedPosts);
      }
    } catch (error) {
      console.error("Error fetching posts:", error);
    }
  };

  const handleSearch = (query: string) => {
    console.log("Search query:", query);
    if (selectedEnrollment) {
      fetchPosts(selectedEnrollment.program_id, query);
    }
  };

  const handleProgramSelect = (enrollment: Enrollment | null) => {
    setSelectedEnrollment(enrollment);
    if (enrollment) {
      fetchPosts(enrollment.program_id);
    } else {
      setPosts([]);
    }
  };

  const handlePost = (
    content: string,
    isPrivate: boolean,
    isAnonymous: boolean,
    attachments?: string[]
  ) => {
    console.log("New post:", { content, isPrivate, isAnonymous, attachments });
    // Implement post creation
    if (selectedEnrollment) {
      // Create post for the selected program
      createPost(content, isPrivate, isAnonymous, attachments);
    }
  };

  const createPost = async (
    content: string,
    isPrivate: boolean,
    isAnonymous: boolean,
    attachments?: string[]
  ) => {
    try {
      const response = await createPostQuery.mutateAsync({
        endpoint: "/v2/api/kanglink/custom/trainer/feed",
        method: "POST",
        body: {
          program_id: selectedEnrollment?.program_id,
          split_id: selectedEnrollment?.split_id,
          post_type: "update",
          content,
          is_private: isPrivate,
          is_anonymous: isAnonymous,
          attachments:
            attachments && attachments.length > 0
              ? JSON.stringify(attachments)
              : null,
          visibility_scope: isPrivate ? "private" : "program_members",
        },
      });

      if (!response.error && response.data) {
        // Refresh posts after creating new post
        if (selectedEnrollment) {
          fetchPosts(selectedEnrollment.program_id);
        }
      }
    } catch (error) {
      console.error("Error creating post:", error);
    }
  };

  const handleReactionClick = (
    postId: string,
    reactionType: string,
    isActive: boolean
  ) => {
    console.log("Reaction:", { postId, reactionType, isActive });
    // Implement reaction handling
  };

  const handleAddComment = (
    postId: string,
    content: string,
    isAnonymous: boolean
  ) => {
    console.log("New comment:", { postId, content, isAnonymous });
    // Implement comment creation
  };

  const handleReplyToComment = (
    postId: string,
    commentId: string,
    content: string,
    isAnonymous: boolean
  ) => {
    console.log("Reply to comment:", {
      postId,
      commentId,
      content,
      isAnonymous,
    });
    // Implement reply creation
  };

  return (
    <div
      className="min-h-screen transition-colors duration-200"
      style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY }}
    >
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <FeedHeader
          onSearch={handleSearch}
          onProgramSelect={handleProgramSelect}
          selectedEnrollment={selectedEnrollment}
        />

        {selectedEnrollment && (
          <>
            <PostComposer onPost={handlePost} />

            <div className="space-y-6">
              {postsQuery.isPending ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-text-secondary">Loading posts...</p>
                </div>
              ) : posts.length > 0 ? (
                posts.map((post) => (
                  <FeedPost
                    key={post.id}
                    post={post}
                    onReactionClick={handleReactionClick}
                    onAddComment={handleAddComment}
                    onReplyToComment={handleReplyToComment}
                  />
                ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-text-secondary">
                    No posts found for this program.
                  </p>
                </div>
              )}
            </div>
          </>
        )}

        {!selectedEnrollment && (
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <h3 className="text-lg font-semibold text-text mb-2">
                Select a Program
              </h3>
              <p className="text-text-secondary">
                Choose a program from the dropdown above to view and interact
                with its feed.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ViewAthleteFeedPage;
