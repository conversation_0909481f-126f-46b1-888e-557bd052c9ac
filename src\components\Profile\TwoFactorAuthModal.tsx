import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Modal } from "@/components/Modal";
import { InteractiveButton } from "@/components/InteractiveButton";
import MkdInputV2 from "@/components/MkdInputV2";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";

interface TwoFactorAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (enabled: boolean) => void;
  currentlyEnabled: boolean;
}

// Validation schema for 2FA verification
const verificationSchema = yup.object().shape({
  verification_code: yup
    .string()
    .required("Verification code is required")
    .length(6, "Verification code must be 6 digits")
    .matches(/^\d{6}$/, "Verification code must contain only numbers"),
});

type VerificationFormData = yup.InferType<typeof verificationSchema>;

const TwoFactorAuthModal: React.FC<TwoFactorAuthModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  currentlyEnabled,
}) => {
  const { sdk } = useSDK();
  const { showToast, tokenExpireError } = useContexts();

  // State management
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<"setup" | "verify" | "disable">("setup");
  const [qrCodeUrl, setQrCodeUrl] = useState<string>("");
  const [secretKey, setSecretKey] = useState<string>("");
  const [backupCodes, setBackupCodes] = useState<string[]>([]);

  // React Hook Form for verification
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<VerificationFormData>({
    resolver: yupResolver(verificationSchema),
  });

  // Initialize modal state based on current 2FA status
  useEffect(() => {
    if (isOpen) {
      if (currentlyEnabled) {
        setStep("disable");
      } else {
        setStep("setup");
        initiate2FASetup()
      }
    } else {
      // Reset state when modal closes
      setStep("setup");
      setQrCodeUrl("");
      setSecretKey("");
      setBackupCodes([]);
      reset();
    }
  }, [isOpen, currentlyEnabled, reset]);

  // Initiate 2FA setup process
  const initiate2FASetup = async () => {
    try {
      setLoading(true);
      const result = await sdk.enable2FA();

      if (!result.error) {
        // Assuming the API returns QR code URL and secret key
        setQrCodeUrl(result.data?.qr_code || result.qr_code || "");
        setSecretKey(result.data?.secret_key || result.secret_key || "");
        setBackupCodes(result.data?.backup_codes || result.backup_codes || []);
      } else {
        showToast(
          result.message || "Failed to initiate 2FA setup",
          5000,
          ToastStatusEnum.ERROR
        );
        onClose();
      }
    } catch (error: any) {
      const message =
        error?.response?.data?.message || error?.message || "An error occurred";
      showToast(message, 5000, ToastStatusEnum.ERROR);
      tokenExpireError(message);
      onClose();
    } finally {
      setLoading(false);
    }
  };

  // Verify 2FA setup
  const onVerifySubmit = async (data: VerificationFormData) => {
    try {
      setLoading(true);
      const result = await sdk.verify2FA(data.verification_code);

      if (!result.error && result.valid) {
        showToast("2FA enabled successfully!", 3000, ToastStatusEnum.SUCCESS);
        onClose();
        onSuccess(true);
      } else {
        showToast(
          result.message || "Invalid verification code",
          3000,
          ToastStatusEnum.ERROR
        );
      }
    } catch (error: any) {
      const message =
        error?.response?.data?.message || error?.message || "An error occurred";
      showToast(message, 5000, ToastStatusEnum.ERROR);
    } finally {
      setLoading(false);
    }
  };

  // Disable 2FA
  const disable2FA = async () => {
    try {
      setLoading(true);
      const result = await sdk.disable2FA();

      if (!result.error) {
        showToast("2FA disabled successfully!", 3000, ToastStatusEnum.SUCCESS);
        onClose();
        onSuccess(false);
      } else {
        showToast(
          result.message || "Failed to disable 2FA",
          3000,
          ToastStatusEnum.ERROR
        );
      }
    } catch (error: any) {
      const message =
        error?.response?.data?.message || error?.message || "An error occurred";
      showToast(message, 5000, ToastStatusEnum.ERROR);
    } finally {
      setLoading(false);
    }
  };

  // Copy text to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      showToast("Copied to clipboard", 2000, ToastStatusEnum.SUCCESS);
    });
  };

  const renderSetupStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-text mb-2">
          Set up Two-Factor Authentication
        </h3>
        <p className="text-text-secondary text-sm">
          Scan the QR code with your authenticator app or enter the secret key
          manually.
        </p>
      </div>

      {/* QR Code */}
      {qrCodeUrl && (
        <div className="flex justify-center">
          <div className="bg-white p-4 rounded-lg border">
            <img src={qrCodeUrl} alt="2FA QR Code" className="w-48 h-48" />
          </div>
        </div>
      )}

      {/* Secret Key */}
      {secretKey && (
        <div className="space-y-2">
          <label className="text-sm font-medium text-text">
            Or enter this key manually:
          </label>
          <div className="flex items-center space-x-2">
            <code className="flex-1 p-2 bg-background-secondary border border-border rounded text-sm font-mono break-all">
              {secretKey}
            </code>
            <InteractiveButton
              type="button"
              onClick={() => copyToClipboard(secretKey)}
              className="px-3 py-2 text-sm bg-transparent border border-border text-text hover:bg-background-secondary"
            >
              Copy
            </InteractiveButton>
          </div>
        </div>
      )}

      {/* Next Step Button */}
      <div className="flex justify-end">
        <InteractiveButton
          type="button"
          onClick={() => setStep("verify")}
          disabled={loading}
          className="px-6 py-2 bg-primary text-white hover:bg-primary/80"
        >
          I've Added the Account
        </InteractiveButton>
      </div>
    </div>
  );

  const renderVerifyStep = () => (
    <form onSubmit={handleSubmit(onVerifySubmit)} className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-text mb-2">
          Verify Your Setup
        </h3>
        <p className="text-text-secondary text-sm">
          Enter the 6-digit code from your authenticator app to complete setup.
        </p>
      </div>

      {/* Verification Code Input */}
      <div className="space-y-2">
        <MkdInputV2
          name="verification_code"
          type="text"
          register={register}
          errors={errors}
          required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label className="text-sm font-medium text-text">
              Verification Code
            </MkdInputV2.Label>
            <MkdInputV2.Field
              placeholder="000000"
              className="w-full h-11 px-3 py-2 bg-background border border-border rounded-md text-text text-center text-lg font-mono tracking-widest placeholder-text-secondary focus:border-primary focus:ring-0 transition-colors duration-200"
            />
            <MkdInputV2.Error />
          </MkdInputV2.Container>
        </MkdInputV2>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <InteractiveButton
          type="button"
          onClick={() => setStep("setup")}
          disabled={loading}
          className="px-4 py-2 bg-transparent border border-border text-text hover:bg-background-secondary"
        >
          Back
        </InteractiveButton>
        <InteractiveButton
          type="submit"
          loading={loading}
          disabled={loading}
          className="px-6 py-2 bg-primary text-white hover:bg-primary/80"
        >
          Verify & Enable
        </InteractiveButton>
      </div>
    </form>
  );

  const renderDisableStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-text mb-2">
          Disable Two-Factor Authentication
        </h3>
        <p className="text-text-secondary text-sm">
          Are you sure you want to disable two-factor authentication? This will
          make your account less secure.
        </p>
      </div>

      {/* Warning */}
      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-yellow-400"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              Security Warning
            </h3>
            <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
              <p>
                Disabling 2FA will remove an important security layer from your
                account.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3">
        <InteractiveButton
          type="button"
          onClick={onClose}
          disabled={loading}
          className="px-4 py-2 bg-transparent border border-border text-text hover:bg-background-secondary"
        >
          Cancel
        </InteractiveButton>
        <InteractiveButton
          type="button"
          onClick={disable2FA}
          loading={loading}
          disabled={loading}
          className="px-6 py-2 bg-red-600 text-white hover:bg-red-700"
        >
          Disable 2FA
        </InteractiveButton>
      </div>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title={
        currentlyEnabled
          ? "Two-Factor Authentication"
          : "Enable Two-Factor Authentication"
      }
      modalHeader
      classes={{
        modalDialog: "!w-full !px-0 md:!w-[32rem] !h-fit",
        modalContent: "!z-10 !px-0 overflow-hidden !pt-0 !mt-0",
        modal: "h-full",
      }}
    >
      <div className="p-6">
        {step === "setup" && renderSetupStep()}
        {step === "verify" && renderVerifyStep()}
        {step === "disable" && renderDisableStep()}
      </div>
    </Modal>
  );
};

export default TwoFactorAuthModal;
