import { PostFeed } from "@/interfaces";

export interface Reply {
  id: number;
  author_name: string;
  author_avatar: string;
  content: string;
  timestamp: string;
  is_trainer?: boolean;
}

export interface FeedPostProps extends PostFeed {
  id: number;
  author_name: string;
  author_avatar: string;
  content: string;
  timestamp: string;
  is_private?: boolean;
  replies?: Reply[];
  author_id?: string | number; // For ownership checks
  user_reaction?: string | null; // Current user's reaction type
  can_delete?: boolean; // Whether current user can delete this post
}

export interface PostActionsProps {
  likes: number;
  comments: number;
  current_reaction?: string | null;
  on_reaction_toggle: (
    reaction_type: "like" | "love" | "fire" | "strong"
  ) => void;
  on_toggle_comments: () => void;
  is_loading_reaction?: boolean;
}

export interface CommentInputProps {
  user_avatar?: string;
  value: string;
  on_change: (value: string) => void;
  on_submit: () => void;
  placeholder?: string;
  is_loading?: boolean;
}

export interface ReplySectionProps {
  replies: Reply[];
  is_visible: boolean;
}

export interface EmptyFeedStateProps {
  title?: string;
  description?: string;
}

export interface TrainerFeedHeaderProps {
  title?: string;
  subtitle?: string;
  selected_program?: Program | null;
  on_program_select: (program: Program | null) => void;
}

export interface Program {
  id: number | string;
  program_name: string;
  status: string;
  image?: string;
  type_of_program?: string;
  created_at?: string;
}

export interface TrainerPostComposerProps {
  profile: {
    photo: string;
    full_name: string;
    [key: string]: any;
  };
  content: string;
  on_content_change: (value: string) => void;
  on_submit: () => void;
  post_type: string;
  on_post_type_change: (type: string) => void;
  selected_program?: Program | null;
  on_post_created?: (post: any) => void;
}
