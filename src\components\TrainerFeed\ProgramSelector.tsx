import React, { useState, useEffect, useRef } from "react";
import { useGetPaginateQuery } from "@/query/shared/listModel";
import { useProfile } from "@/hooks/useProfile";
import { Models } from "@/utils/baas";
import { Program } from "./types";

interface ProgramSelectorProps {
  selected_program?: Program | null;
  on_program_select: (program: Program | null) => void;
  placeholder?: string;
}

const ProgramSelector: React.FC<ProgramSelectorProps> = ({
  selected_program,
  on_program_select,
  placeholder = "Search and select a program...",
}) => {
  const [search_term, setSearchTerm] = useState("");
  const [is_open, setIsOpen] = useState(false);
  const [debounced_search, setDebouncedSearch] = useState("");
  const dropdown_ref = useRef<HTMLDivElement>(null);
  const { profile } = useProfile();

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search_term);
    }, 300);

    return () => clearTimeout(timer);
  }, [search_term]);

  // Build filter array for search
  const buildFilters = () => {
    const filters: string[] = [];

    if (profile?.id) {
      filters.push(`user_id,eq,${profile.id}`);
    }

    // Only show published/live programs
    filters.push(`status,in,'live','published'`);

    if (debounced_search) {
      filters.push(`program_name,cs,${debounced_search}`);
    }

    return filters.length > 0 ? filters : undefined;
  };

  // Fetch programs
  const {
    data: programs_response,
    isLoading,
    isError,
  } = useGetPaginateQuery(
    Models.PROGRAM,
    {
      page: 1,
      size: 200000,
      filter: buildFilters(),
      order: "program_name",
      direction: "asc",
    },
    {
      enabled: !!profile?.id && is_open,
      keepPreviousData: true,
    }
  );

  const programs = programs_response?.data || [];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdown_ref.current &&
        !dropdown_ref.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleProgramSelect = (program: Program) => {
    on_program_select(program);
    setIsOpen(false);
    setSearchTerm("");
  };

  const handleClearSelection = () => {
    on_program_select(null);
    setSearchTerm("");
  };

  const display_text = selected_program
    ? selected_program.program_name
    : search_term || placeholder;

  return (
    <div className="relative w-full" ref={dropdown_ref}>
      <div className="relative">
        <input
          type="text"
          value={
            is_open
              ? search_term
              : selected_program
                ? selected_program.program_name
                : ""
          }
          onChange={(e) => setSearchTerm(e.target.value)}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          className="w-full pl-12 pr-10 py-3 border border-border rounded-xl bg-background text-text placeholder-text-disabled focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 text-sm"
        />

        {/* Search Icon */}
        <svg
          className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-disabled"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>

        {/* Clear/Dropdown Icon */}
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
          {selected_program && (
            <button
              onClick={handleClearSelection}
              className="p-1 hover:bg-background-secondary rounded-full transition-colors"
              type="button"
            >
              <svg
                className="w-4 h-4 text-text-disabled"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          )}
          <svg
            className={`w-4 h-4 text-text-disabled transition-transform ${is_open ? "rotate-180" : ""}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </div>
      </div>

      {/* Dropdown */}
      {is_open && (
        <div className="absolute z-50 w-full mt-1 bg-background border border-border rounded-xl shadow-lg max-h-60 overflow-y-auto">
          {isLoading && (
            <div className="p-4 text-center text-text-disabled">
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                Loading programs...
              </div>
            </div>
          )}

          {isError && (
            <div className="p-4 text-center text-red-500">
              Error loading programs
            </div>
          )}

          {!isLoading && !isError && programs.length === 0 && (
            <div className="p-4 text-center text-text-disabled">
              {debounced_search ? "No programs found" : "No programs available"}
            </div>
          )}

          {!isLoading && !isError && programs.length > 0 && (
            <div className="py-2">
              {programs.map((program: Program) => (
                <button
                  key={program.id}
                  onClick={() => handleProgramSelect(program)}
                  className="w-full px-4 py-3 text-left hover:bg-background-secondary transition-colors flex items-center gap-3"
                  type="button"
                >
                  {program.image && (
                    <img
                      src={program.image}
                      alt={program.program_name}
                      className="w-8 h-8 rounded-lg object-cover flex-shrink-0"
                    />
                  )}
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-text truncate">
                      {program.program_name}
                    </div>
                    {program.type_of_program && (
                      <div className="text-sm text-text-disabled truncate">
                        {program.type_of_program}
                      </div>
                    )}
                  </div>
                  <div className="flex-shrink-0">
                    <span
                      className={`px-2 py-1 text-xs rounded-full ${
                        program.status === "live" ||
                        program.status === "published"
                          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                          : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
                      }`}
                    >
                      {program.status}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProgramSelector;
