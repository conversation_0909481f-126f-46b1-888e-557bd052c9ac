# Fresh Data Fix for View Pages

## Problem
When users navigate away from detail pages (like ViewAthleteProgramPage or ViewAthleteTrainerDetailsPage) and then return, they were seeing stale cached data instead of the latest information. This happened because React Query was serving cached data with a 5-minute stale time.

## Root Cause
The React Query hooks in these pages had:
```typescript
staleTime: 5 * 60 * 1000, // 5 minutes cache
```

This meant that React Query would serve cached data for 5 minutes without refetching, even when users navigated back to the page.

## Solution
Updated the query configurations to always fetch fresh data:

```typescript
staleTime: 0, // Always fetch fresh data
refetchOnMount: true, // Refetch when component mounts
refetchOnWindowFocus: true, // Refetch when window gains focus
```

## Files Modified

### 1. ViewAthleteProgramPage.tsx
- **useProgramDetails hook**: Updated to fetch fresh program data
- **useProgramReviews hook**: Updated to fetch fresh review data

### 2. ViewAthleteTrainerDetailsPage.tsx  
- **useTrainerDetails hook**: Updated to fetch fresh trainer data
- **useTrainerPrograms hook**: Updated to fetch fresh trainer programs data

## Benefits

1. **Always Fresh Data**: Users see the latest information when navigating back to pages
2. **Real-time Updates**: Changes made elsewhere are immediately visible
3. **Better UX**: No need to manually refresh the page to see updates
4. **Window Focus Refetch**: Data refreshes when switching back to the browser tab

## Trade-offs

1. **More Network Requests**: Pages will make API calls more frequently
2. **Slightly Higher Server Load**: More requests to the backend
3. **Potential Loading States**: Users might see loading indicators more often

## When This Helps

- User views a program, navigates away, program gets updated, user returns → sees fresh data
- User views trainer details, trainer updates profile, user returns → sees updated profile
- User switches between tabs and returns → gets latest data
- Multiple users viewing same content → all see consistent, up-to-date information

## Alternative Approaches Considered

1. **Shorter staleTime**: Could use 30 seconds instead of 0, but still risk stale data
2. **Manual invalidation**: Could invalidate queries on specific actions, but complex to manage
3. **WebSocket updates**: Real-time updates, but adds complexity
4. **Background refetch**: Periodic updates, but still allows stale data windows

## Recommendation

The current approach (staleTime: 0) is best for detail pages where data freshness is critical. For list pages or less critical data, longer stale times may still be appropriate for performance.

## Testing

To test the fix:
1. Navigate to a program detail page
2. Open the same program in another tab/window and make changes
3. Return to the original tab
4. Verify that fresh data is loaded without manual refresh

## Future Considerations

- Monitor API call frequency and server load
- Consider implementing smart caching strategies based on data type
- Add loading states that are user-friendly for frequent refetches
- Consider WebSocket integration for real-time updates if needed
