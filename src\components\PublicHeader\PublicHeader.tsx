import { Link, useLocation } from "react-router-dom";
// import { BrandLogo } from "@/assets/images";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { ThemeToggle } from "@/components/ThemeToggle";
import { LazyLoad } from "@/components/LazyLoad";
import OfflineIndicator from "../OfflineIndicator";

export const PublicHeader = ({ role }: { role?: string }) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const location = useLocation();
  const isAthlete = location.pathname.includes("athlete/signup");
  const isTrainer = location.pathname.includes("trainer/signup");

  const headerStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderBottomColor: THEME_COLORS[mode].BORDER,
  };

  const logoStyles = {
    color: THEME_COLORS[mode].PRIMARY,
  };

  // const supportButtonStyles = {
  //   backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
  //   borderColor: THEME_COLORS[mode].BORDER,
  //   color: THEME_COLORS[mode].TEXT
  // };

  // Responsive, theme-aware header using Tailwind classes
  return (
    <div className="w-full">
      <nav className="flex min-h-[50px] items-center justify-between border-b border-border bg-background px-4 py-2 md:px-8 transition-colors duration-200">
        <Link
          to="/"
          className={
            "flex w-fit h-full items-center hover:opacity-80 transition-opacity duration-200"
          }
        >
          {/* <img className={"h-[70%] object-contain "} src={BrandLogo} /> */}
          <h1
            className="text-xl md:text-2xl font-bold font-inter transition-colors duration-200"
            style={logoStyles}
          >
            Kanga Sportlink
          </h1>
        </Link>

        {/* Responsive: stack on mobile, horizontal on md+ */}
        <div className="flex items-center gap-2 md:gap-3">
          {/* Show signup as athlete if not on athlete signup page */}
          {["admin", "super_admin", "pending"].includes(role || "") ? null : (
            <>
              {!isAthlete && (
                <Link
                  to="/athlete/signup"
                  className="hidden xs:flex justify-center items-center px-4 py-2 rounded border border-border bg-background text-text text-center font-sans text-sm font-medium leading-normal transition-colors duration-200 hover:bg-background-hover hover:border-primary focus:outline-none focus:ring-2 focus:ring-primary/30 w-auto min-w-[8rem]"
                >
                  Signup as Athlete
                </Link>
              )}
              {/* Show signup as trainer if not on trainer signup page */}
              {!isTrainer && (
                <Link
                  to="/trainer/signup"
                  className="hidden xs:flex justify-center items-center px-4 py-2 rounded border border-border bg-background text-text text-center font-sans text-sm font-medium leading-normal transition-colors duration-200 hover:bg-background-hover hover:border-primary focus:outline-none focus:ring-2 focus:ring-primary/30 w-auto min-w-[8rem]"
                >
                  Signup as Trainer
                </Link>
              )}
              {/* Show login if on signup pages */}
              {(isAthlete || isTrainer) && (
                <Link
                  to="/login"
                  className="hidden xs:flex justify-center items-center px-4 py-2 rounded border border-border bg-background text-text text-center font-sans text-sm font-medium leading-normal transition-colors duration-200 hover:bg-background-hover hover:border-primary focus:outline-none focus:ring-2 focus:ring-primary/30 w-auto min-w-[8rem]"
                >
                  Login
                </Link>
              )}
            </>
          )}
          {/* Mobile: show condensed menu (if needed, can add hamburger here) */}
          {/* Always show OfflineIndicator and ThemeToggle */}
          {/* <OfflineIndicator showWhenOnline={true} /> */}
          <LazyLoad>
            <ThemeToggle className="transition-all duration-200 hover:scale-105" />
          </LazyLoad>
          {/* <div
            className="flex cursor-pointer items-center rounded-md border px-3 py-2 shadow-sm transition-all duration-200 hover:scale-95 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2"
            style={supportButtonStyles}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor =
                THEME_COLORS[mode].BACKGROUND_HOVER;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor =
                THEME_COLORS[mode].BACKGROUND_SECONDARY;
            }}
            onFocus={(e) => {
              e.currentTarget.style.boxShadow = `0 0 0 2px ${THEME_COLORS[mode].PRIMARY}40`;
            }}
            onBlur={(e) => {
              e.currentTarget.style.boxShadow = "";
            }}
            tabIndex={0}
            role="button"
            aria-label="Support"
          >
            Support
          </div> */}
        </div>
      </nav>
    </div>
  );
};

export default PublicHeader;
