import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useContexts } from "@/hooks/useContexts";
import { useProfile } from "@/hooks/useProfile";
import { Models } from "@/utils/baas/models";
import { useGetListQuery } from "@/query/shared/listModel";
import { useUpdateModelMutation } from "@/query/shared/updateModel";
import { useDeleteModelMutation } from "@/query/shared/deleteModel";

export interface UserReview {
  id: string;
  program_id: string;
  split_id?: string;
  content: string;
  rating: number;
  created_at: string;
  updated_at: string;
  is_edited: boolean;
}

export interface UpdateReviewData {
  content: string;
  rating: number;
}

export const useUserReview = (programId: string | number) => {
  const { authState } = useContexts();
  const { profile } = useProfile();
  const queryClient = useQueryClient();

  // Check if user is logged in
  const isLoggedIn = authState.isAuthenticated;

  // Get user's existing review for this program
  const {
    data: userReview,
    isLoading: isLoadingUserReview,
    error: userReviewError,
    refetch: refetchUserReview,
  } = useGetListQuery(
    Models.POST_FEED,
    {
      page: 1,
      size: 1,
      filter: [
        `program_id,eq,${programId.toString()}`,
        `post_type,eq,review`,
        `user_id,eq,${profile?.id}`,
      ],
    },
    undefined,
    {
      enabled: isLoggedIn && !!programId && !!profile?.id,
      queryKey: ["user-review", programId, profile?.id],
    }
  );

  // Update review mutation
  const { mutateAsync: updateReview, isPending: isUpdatingReview } =
    useUpdateModelMutation(Models.POST_FEED, {
      showToast: true,
    });

  // Delete review mutation
  const { mutateAsync: deleteReview, isPending: isDeletingReview } =
    useDeleteModelMutation(Models.POST_FEED, {
      showToast: true,
    });

  const existingReview: UserReview | null = userReview?.data?.[0] || null;

  const updateUserReview = async (
    reviewId: string,
    reviewData: UpdateReviewData
  ) => {
    if (!isLoggedIn) {
      throw new Error("User must be logged in to update a review");
    }

    // Validate rating
    if (reviewData.rating < 1 || reviewData.rating > 5) {
      throw new Error("Rating must be between 1 and 5 stars");
    }

    // Validate content
    if (!reviewData.content.trim()) {
      throw new Error("Review content cannot be empty");
    }

    if (reviewData.content.trim().length < 10) {
      throw new Error("Review must be at least 10 characters long");
    }

    if (reviewData.content.trim().length > 1000) {
      throw new Error("Review cannot exceed 1000 characters");
    }

    const payload = {
      content: reviewData.content.trim(),
      rating: reviewData.rating,
      is_edited: true,
    };

    const response = await updateReview({
      id: reviewId,
      payload,
    });

    if (response?.error) {
      throw new Error(response.message || "Failed to update review");
    }

    // Invalidate and refetch program reviews
    queryClient.invalidateQueries({
      queryKey: ["program-reviews", programId.toString()],
    });

    // Invalidate user review
    queryClient.invalidateQueries({
      queryKey: ["user-review", programId, profile?.id],
    });

    // Invalidate program details to update review count
    queryClient.invalidateQueries({
      queryKey: ["program-details", programId.toString()],
    });

    return response;
  };

  const deleteUserReview = async (reviewId: string) => {
    if (!isLoggedIn) {
      throw new Error("User must be logged in to delete a review");
    }

    const response = await deleteReview(reviewId);

    if (response?.error) {
      throw new Error(response.message || "Failed to delete review");
    }

    // Invalidate and refetch program reviews
    queryClient.invalidateQueries({
      queryKey: ["program-reviews", programId.toString()],
    });

    // Invalidate user review
    queryClient.invalidateQueries({
      queryKey: ["user-review", programId, profile?.id],
    });

    // Invalidate program details to update review count
    queryClient.invalidateQueries({
      queryKey: ["program-details", programId.toString()],
    });

    return response;
  };

  return {
    existingReview,
    isLoadingUserReview,
    userReviewError,
    updateUserReview,
    deleteUserReview,
    isUpdatingReview,
    isDeletingReview,
    isLoggedIn,
    refetchUserReview,
  };
};

export default useUserReview; 