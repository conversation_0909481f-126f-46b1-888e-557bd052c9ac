import { useState, useRef } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { InteractiveButton } from "@/components/InteractiveButton";
import { Link, useSearchParams, useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/useToast";
import { baseUrl } from "@/utils/config";
import { useCustomModelQuery } from "@/query/shared";
import { RestAPIMethodEnum } from "@/utils/Enums";

const ForgotPassword = () => {
  const { state } = useTheme();
  const mode = state?.theme;
  const navigate = useNavigate();
  const toast = useToast();
  const [email, setEmail] = useState("");
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<"email" | "otp">("email");
  const [tempToken, setTempToken] = useState("");
  const otpRefs = useRef<(HTMLInputElement | null)[]>([]);
  const [searchParams] = useSearchParams();
  const role = searchParams.get("role");

  const { mutateAsync: customQuery } = useCustomModelQuery();

  // Theme styles
  const containerStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
  };

  const cardStyles = {
    backgroundColor: THEME_COLORS[mode].CARD_BG,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  const inputStyles = {
    backgroundColor: THEME_COLORS[mode].INPUT,
    borderColor: THEME_COLORS[mode].BORDER,
    color: THEME_COLORS[mode].TEXT,
  };

  const otpInputStyles = {
    backgroundColor: THEME_COLORS[mode].INPUT,
    borderColor: THEME_COLORS[mode].BORDER,
    color: THEME_COLORS[mode].TEXT,
  };

  // Handle OTP input
  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) return; // Only allow single digit

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      otpRefs.current[index + 1]?.focus();
    }
  };

  // Handle backspace in OTP
  const handleOtpKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      otpRefs.current[index - 1]?.focus();
    }
  };

  // Handle email submission
  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const res = await customQuery({
        endpoint: `/v2/api/kanglink/custom/forgot-password?role=${role}`,
        method: RestAPIMethodEnum.POST,
        body: {
          email: email,
        },
      });

      if (res.error) {
        toast.error(res.message || "Failed to send OTP");
        return;
      }

      // Store the temporary token for OTP verification
      setTempToken(res.data.temp_token);
      setStep("otp");
      toast.success("OTP sent to your email address");
    } catch (error: any) {
      console.error("Forgot password error:", error);
      const message = error?.response?.data?.message || error?.message;
      toast.error(message || "Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP submission
  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const otpString = otp.join("");

      const res = await customQuery({
        endpoint: `/v2/api/kanglink/custom/verify-otp`,
        method: RestAPIMethodEnum.POST,
        body: {
          otp: otpString,
          temp_token: tempToken,
        },
      });

      if (res.error) {
        toast.error(res.message || "Invalid OTP");
        return;
      }

      // Store the reset token in localStorage and navigate to change password
      localStorage.setItem("reset_token", res.data.reset_token);
      toast.success("OTP verified successfully");
      navigate(`/change-password?role=${role}`);
    } catch (error: any) {
      console.error("OTP verification error:", error);
      const message = error?.response?.data?.message || error?.message;
      toast.error(message || "Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="min-h-screen bg-background-secondary transition-colors duration-200"
      style={containerStyles}
    >
      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
        <div
          className="w-full max-w-md bg-card-bg border border-border rounded-lg shadow-lg p-8 transition-colors duration-200"
          style={cardStyles}
        >
          {/* Title */}
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-text font-inter">
              Forgot Password
            </h2>
          </div>

          {/* Email Step */}
          {step === "email" && (
            <form onSubmit={handleEmailSubmit} className="space-y-6">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-text mb-2"
                >
                  Enter Email to Receive OTP
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter Email Address"
                  required
                  style={inputStyles}
                  className="w-full h-12 px-3 py-2 border rounded-md text-base font-inter placeholder-text-secondary focus:outline-none focus:ring-0 focus:border-primary hover:border-border-hover transition-colors duration-200"
                />
              </div>

              <InteractiveButton
                type="submit"
                loading={isLoading}
                disabled={!email || isLoading}
                className="w-full bg-primary hover:bg-primary-hover text-white py-3 px-4 rounded-md font-semibold"
              >
                Send OTP
              </InteractiveButton>
            </form>
          )}

          {/* OTP Step */}
          {step === "otp" && (
            <form onSubmit={handleOtpSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  Enter OTP
                </label>

                {/* OTP Input Grid */}
                <div className="flex justify-center gap-2 sm:gap-3 mb-4">
                  {otp.map((digit, index) => (
                    <input
                      key={index}
                      ref={(el) => (otpRefs.current[index] = el)}
                      type="text"
                      inputMode="numeric"
                      pattern="[0-9]*"
                      maxLength={1}
                      value={digit}
                      onChange={(e) => handleOtpChange(index, e.target.value)}
                      onKeyDown={(e) => handleOtpKeyDown(index, e)}
                      style={otpInputStyles}
                      className="w-10 h-10 sm:w-12 sm:h-12 text-center text-lg font-mono border rounded-md focus:outline-none focus:ring-0 focus:border-primary hover:border-border-hover transition-colors duration-200"
                    />
                  ))}
                </div>

                <p className="text-center text-xs text-text-secondary">
                  OTP valid for 5 min only
                </p>
              </div>

              <InteractiveButton
                type="submit"
                loading={isLoading}
                disabled={otp.some((digit) => !digit) || isLoading}
                className="w-full bg-primary hover:bg-primary-hover text-white py-3 px-4 rounded-md font-semibold"
              >
                Reset Password
              </InteractiveButton>

              {/* Back to Email */}
              <button
                type="button"
                onClick={() => {
                  // reset opt
                  setOtp(["", "", "", "", "", ""]);
                  setStep("email");
                }}
                className="w-full text-sm text-text-secondary hover:text-text transition-colors duration-200"
              >
                Back to Email
              </button>
            </form>
          )}

          {/* Login Link */}
          <div className="mt-6 text-center">
            <Link
              to="/login"
              className="text-sm text-primary hover:text-primary-hover transition-colors duration-200"
            >
              Remember your password? Sign in
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ForgotPassword;
