import { useNavigate, useParams } from "react-router-dom";
import { useState } from "react";
import { ProgramHeader } from "@/components/ProgramHeader";
import { ProgramDescription } from "@/components/ProgramDescription";
import { ProgramDetailsCard } from "@/components/ProgramDetailsCard";
import { BillingSection } from "@/components/BillingSection";
import { ReviewsSection } from "@/components/ReviewsSection";
import { WriteReviewModal } from "@/components/WriteReviewModal";
import { useProgramDetails } from "@/hooks/useProgramDetails";
import { useProgramReviews } from "@/hooks/useProgramReviews";
import { useProgramPageTitle } from "@/hooks/usePageTitle";
import { useEnrollment } from "@/hooks/useEnrollment";
import { useContexts } from "@/hooks/useContexts";
import { TransformedProgramData } from "@/interfaces";
import { ToastStatusEnum } from "@/utils/Enums";

// Transformed data interfaces for components

interface TransformedReviewData {
  id: string;
  athleteName: string;
  reviewText: string;
  rating: number;
  user: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
    full_name: string;
    photo: string | null;
  };
  created_at: string;
  is_edited: boolean;
}

const ViewAthleteProgramPage = () => {
  const { programId } = useParams<{ programId: string }>();
  const navigate = useNavigate();
  const { showToast } = useContexts();
  const { isLoggedIn, useCheckProgramSubscription } = useEnrollment();

  // Modal state
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);

  // Check subscription status
  const subscriptionStatus = useCheckProgramSubscription(programId || "");

  // Fetch program details and reviews
  const {
    data: programData,
    isLoading: isLoadingProgram,
    error: programError,
  } = useProgramDetails(programId);

  const {
    data: reviewsResponse,
    isLoading: isLoadingReviews,
    error: reviewsError,
  } = useProgramReviews(programId, {
    page: 1,
    limit: 20,
    sort_by: "created_at",
    sort_order: "desc",
  });

  // Update page title when program data is loaded
  useProgramPageTitle(programData?.program_name);

  // Transform API data to match component expectations
  const transformedProgramData: TransformedProgramData | null = programData
    ? {
        ...programData,
        id: programData.id.toString(),
        courseName: programData.program_name,
        trainerName: programData.trainer.full_name,
        rating: programData.rating,
        description: programData.program_description
          ? [programData.program_description]
          : [],
        details: [
          { label: "Duration", value: programData.duration || "Not specified" },
          {
            label: "Level",
            value: programData.target_levels.join(", ") || "Not specified",
          },
          {
            label: "Split",
            value: (() => {
              if (!programData.splits || programData.splits.length === 0) {
                return "Not specified";
              }

              const splitNames = programData.splits.map((s) => s.title);

              // If there's only one split, show it directly
              if (splitNames.length === 1) {
                return splitNames[0];
              }

              // If there are multiple splits but the combined length is reasonable, join them
              if (splitNames.join(", ").length <= 60) {
                return splitNames.join(", ");
              }

              // For many splits or very long names, show count + first split
              if (splitNames.length > 3) {
                return `${splitNames.length} splits: ${splitNames[0]}, ...`;
              }

              // For 2-3 long splits, return them all (ProgramDetailsCard will format nicely)
              return splitNames.join(", ");
            })(),
          },
          {
            label: "Category",
            value: programData.type_of_program || "Not specified",
          },
          {
            label: "Communication",
            value: programData.allow_private_messages
              ? "Private threads allowed"
              : "No private threads",
          },
          {
            label: "Trainer Tracks progress",
            value: programData.track_progress ? "Yes" : "No",
          },
          {
            label: "Tracking",
            value: programData.allow_comments ? "Allowed" : "Not allowed",
          },
        ],
        splits: programData.splits.map((split) => ({
          id: split.id.toString(),
          name: split.title,
          description: split.equipment_required
            ? `Equipment required: ${split.equipment_required}`
            : "No equipment specified",
          subscriptionPrice: split.subscription || 0,
          buyPrice: split.full_price || 0,
        })),
        program_discount: programData.program_discount,
        discount: programData.discount,
        coupon: programData.coupon,
        coupon_usage_stats: programData.coupon_usage_stats,
        reviews: [], // Will be populated from reviews API
        trainer: programData.trainer,
        currency: programData.currency,
        image: programData.image,
        review_count: programData.review_count,
        payment_plan: programData.payment_plan,
      }
    : null;

  // Transform reviews data
  const transformedReviews: TransformedReviewData[] | undefined =
    reviewsResponse?.data?.map((review) => ({
      id: review.id.toString(),
      athleteName: review.user.full_name,
      reviewText: review.content,
      rating: review.rating,
      user: review.user,
      created_at: review.created_at,
      is_edited: review.is_edited,
    })) || [];

  // Loading state
  if (isLoadingProgram) {
    return (
      <div className="bg-background min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-foreground">Loading program details...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (programError || !transformedProgramData) {
    return (
      <div className="bg-background min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-foreground">
            {programError?.message || "Program not found"}
          </p>
        </div>
      </div>
    );
  }

  const handleWriteReview = () => {
    if (!isLoggedIn) {
      showToast("Please log in to write a review", 5000, ToastStatusEnum.ERROR);
      return;
    }

    if (subscriptionStatus.isLoading) {
      showToast("Checking subscription status...", 3000, ToastStatusEnum.INFO);
      return;
    }

    if (!subscriptionStatus.isSubscribed) {
      showToast(
        "You must be subscribed to this program to write a review",
        5000,
        ToastStatusEnum.ERROR
      );
      return;
    }

    setIsReviewModalOpen(true);
  };

  const handleReviewSubmitted = () => {
    // Refresh reviews after successful submission
    // The hook will automatically invalidate the queries and show success toast
    // Additional actions can be added here if needed
  };

  const handleViewProfile = () => {
    console.log("View profile clicked");
    navigate(
      `/athlete/trainer-details?id=${transformedProgramData.trainer.id}`
    );
  };

  const handleSubscribe = (splitId: string) => {
    console.log("Subscribe to split:", splitId);
  };

  const handleBuy = (splitId: string) => {
    console.log("Buy split:", splitId);
  };

  const handleReply = (reviewId: string, reply: string) => {
    console.log("Reply to review:", reviewId, reply);
  };

  return (
    <div className="bg-background">
      {/* <div className="flex-1 lg:mr-64"> */}
      <div className="max-w-7xl mx-auto">
        {/* Program Header */}
        <ProgramHeader
          courseName={transformedProgramData.courseName}
          trainerName={transformedProgramData.trainerName}
          rating={transformedProgramData.rating}
          programId={programId}
          onWriteReview={handleWriteReview}
          onViewProfile={handleViewProfile}
        />

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-4 lg:p-6">
          {/* Left Column - Description */}
          <div className="lg:col-span-2">
            <ProgramDescription
              description={transformedProgramData.description}
            />
          </div>

          {/* Right Column - Program Details */}
          <div className="lg:col-span-1">
            <ProgramDetailsCard details={transformedProgramData.details} />
          </div>
        </div>

        {/* Billing Section */}
        <BillingSection
          programId={transformedProgramData.id}
          splits={transformedProgramData.splits}
          onSubscribe={handleSubscribe}
          onBuy={handleBuy}
          paymentPlan={transformedProgramData.payment_plan}
          program={transformedProgramData}
        />

        {/* Reviews Section */}
        {isLoadingReviews ? (
          <div className="p-4 lg:p-6">
            <h3 className="text-xl font-semibold mb-4">Reviews</h3>
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-foreground">Loading reviews...</p>
            </div>
          </div>
        ) : reviewsError ? (
          <div className="p-4 lg:p-6">
            <h3 className="text-xl font-semibold mb-4">Reviews</h3>
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                Unable to load reviews at this time.
              </p>
            </div>
          </div>
        ) : (
          <ReviewsSection reviews={transformedReviews} onReply={handleReply} />
        )}

        {/* Write Review Modal */}
        <WriteReviewModal
          isOpen={isReviewModalOpen}
          onClose={() => setIsReviewModalOpen(false)}
          programId={programId || ""}
          programName={transformedProgramData.courseName}
          onReviewSubmitted={handleReviewSubmitted}
        />
      </div>
    </div>
  );
};

export default ViewAthleteProgramPage;
