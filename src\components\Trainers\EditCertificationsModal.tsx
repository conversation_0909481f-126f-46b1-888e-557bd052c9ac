import React, { useState } from "react";
import { Modal } from "@/components/Modal/Modal";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useGetListQuery } from "@/query/shared/listModel";
import { useCreateModelMutation } from "@/query/shared/createModel";
import { useUpdateModelMutation } from "@/query/shared/updateModel";
import { useDeleteModelMutation } from "@/query/shared/deleteModel";
import { Models } from "@/utils/baas/models";
import { Qualification } from "@/interfaces/model.interface";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import { EditIcon, TrashIcon } from "@/assets/svgs";
import { PlusIcon } from "lucide-react";
import { useProfile } from "@/hooks/useProfile";

interface EditCertificationsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface AddEditModalState {
  show: boolean;
  mode: "add" | "edit";
  title: string;
  value: string;
  editingId?: number;
}

const EditCertificationsModal: React.FC<EditCertificationsModalProps> = ({
  isOpen,
  onClose,
}) => {
  const { showToast } = useContexts();
  const { profile } = useProfile();

  // Modal state for add/edit operations
  const [addEditModal, setAddEditModal] = useState<AddEditModalState>({
    show: false,
    mode: "add",
    title: "",
    value: "",
  });

  // Fetch qualifications
  const {
    data: qualifications,
    isLoading,
    refetch,
  } = useGetListQuery(Models.QUALIFICATION, {}, undefined, {
    enabled: isOpen,
  });

  // Mutations
  const { mutateAsync: createQualification, isPending: isCreating } =
    useCreateModelMutation(Models.QUALIFICATION);
  const { mutateAsync: updateQualification, isPending: isUpdating } =
    useUpdateModelMutation(Models.QUALIFICATION);
  const { mutateAsync: deleteQualification, isPending: isDeleting } =
    useDeleteModelMutation(Models.QUALIFICATION);

  // Handle add new qualification
  const handleAdd = () => {
    setAddEditModal({
      show: true,
      mode: "add",
      title: "Add Certification",
      value: "",
    });
  };

  // Handle edit qualification
  const handleEdit = (qualification: Qualification) => {
    setAddEditModal({
      show: true,
      mode: "edit",
      title: "Edit Certification",
      value: qualification.name || "",
      editingId: qualification.id as number,
    });
  };

  // Handle delete qualification
  const handleDelete = async (id: number) => {
    if (window.confirm("Are you sure you want to delete this certification?")) {
      try {
        await deleteQualification(id);
        showToast(
          "Certification deleted successfully",
          5000,
          ToastStatusEnum.SUCCESS
        );
        refetch();
      } catch {
        showToast(
          "Failed to delete certification",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    }
  };

  // Handle save (add or edit)
  const handleSave = async () => {
    if (!addEditModal.value.trim()) {
      showToast(
        "Please enter a certification name",
        5000,
        ToastStatusEnum.ERROR
      );
      return;
    }

    try {
      if (addEditModal.mode === "add") {
        await createQualification({
          name: addEditModal.value.trim(),
          user_id: profile?.id,
          type: 1, // Default type
        });
        showToast(
          "Certification added successfully",
          5000,
          ToastStatusEnum.SUCCESS
        );
      } else {
        await updateQualification({
          id: addEditModal.editingId!,
          payload: {
            name: addEditModal.value.trim(),
            user_id: profile?.id,
            type: 1, // Default type
          },
        });
        showToast(
          "Certification updated successfully",
          5000,
          ToastStatusEnum.SUCCESS
        );
      }

      setAddEditModal({
        show: false,
        mode: "add",
        title: "",
        value: "",
      });
      refetch();
    } catch {
      showToast(
        `Failed to ${addEditModal.mode} certification`,
        5000,
        ToastStatusEnum.ERROR
      );
    }
  };

  // Close add/edit modal
  const closeAddEditModal = () => {
    setAddEditModal({
      show: false,
      mode: "add",
      title: "",
      value: "",
    });
  };

  const isLoading_operations = isCreating || isUpdating || isDeleting;

  return (
    <>
      <Modal
        isOpen={isOpen}
        title="Manage Certifications (Qualifications)"
        modalCloseClick={onClose}
        modalHeader={true}
        classes={{
          modal: "w-full",
          modalContent: "w-full border-b border-gray-200 dark:border-[#3a3a3a]",
          modalDialog: "max-w-2xl w-full max-h-full",
        }}
        modalFooter={
          <div className="flex justify-between items-center pt-5 px-5 w-full">
            <InteractiveButton
              type="button"
              onClick={handleAdd}
              className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/80 flex items-center gap-2"
              disabled={isLoading_operations}
            >
              <PlusIcon size={16} />
              Add New
            </InteractiveButton>
            <InteractiveButton
              type="button"
              onClick={onClose}
              className="bg-transparent text-text px-4 py-2 rounded-md border border-border hover:bg-gray-50"
            >
              Close
            </InteractiveButton>
          </div>
        }
      >
        <div className="py-5">
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <div className="text-text">Loading certifications...</div>
            </div>
          ) : (
            <div className="space-y-3">
              {qualifications?.data?.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No certifications found. Add your first certification!
                </div>
              ) : (
                qualifications?.data?.map((qualification: Qualification) => (
                  <div
                    key={qualification.id}
                    className="flex items-center justify-between p-3 border border-border rounded-md bg-background hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                  >
                    <span className="text-text font-medium">
                      {qualification.name}
                    </span>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleEdit(qualification)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                        disabled={isLoading_operations}
                        title="Edit"
                      >
                        <EditIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(qualification.id as number)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                        disabled={isLoading_operations}
                        title="Delete"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      </Modal>

      {/* Add/Edit Modal */}
      <Modal
        isOpen={addEditModal.show}
        title={addEditModal.title}
        modalCloseClick={closeAddEditModal}
        modalHeader={true}
        classes={{
          modal: "w-full",
          modalContent: "w-full border-b border-gray-200 dark:border-[#3a3a3a]",
          modalDialog: "max-w-md w-full",
        }}
        modalFooter={
          <div className="flex justify-end pt-5 px-5 gap-2 w-full">
            <InteractiveButton
              type="button"
              onClick={closeAddEditModal}
              className="bg-transparent text-text px-4 py-2 rounded-md"
              disabled={isLoading_operations}
            >
              Cancel
            </InteractiveButton>
            <InteractiveButton
              type="button"
              onClick={handleSave}
              className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/80"
              disabled={isLoading_operations || !addEditModal.value.trim()}
            >
              {isLoading_operations ? "Saving..." : "Save"}
            </InteractiveButton>
          </div>
        }
      >
        <div className="py-5">
          <MkdInputV2
            name="qualification_name"
            onChange={(e) => {
              setAddEditModal((prev) => ({
                ...prev,
                value: e.target.value,
              }));
            }}
            value={addEditModal.value}
            required
            placeholder="Enter certification name..."
          >
            <MkdInputV2.Container>
              <MkdInputV2.Label className="font-normal text-text">
                Certification Name
              </MkdInputV2.Label>
              <MkdInputV2.Field className="text-text" />
              <MkdInputV2.Error />
            </MkdInputV2.Container>
          </MkdInputV2>
        </div>
      </Modal>
    </>
  );
};

export default EditCertificationsModal;
