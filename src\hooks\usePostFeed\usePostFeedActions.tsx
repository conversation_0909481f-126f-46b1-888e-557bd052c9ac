import { useState, useCallback, useRef } from "react";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import { useCustomModelQuery } from "@/query/shared/customModel";

interface Comment {
  id: string;
  user_id: string;
  post_id: string;
  parent_comment_id?: string;
  content: string;
  attachments?: any;
  is_private: boolean;
  is_edited: boolean;
  is_flagged: boolean;
  flag_reason?: string;
  mentioned_users?: any;
  reaction_count: number;
  created_at: string;
  updated_at: string;
  user: {
    id: string;
    email: string;
    data: any;
  };
}

interface Reaction {
  id: string;
  user_id: string;
  target_type: "post" | "comment";
  target_id: string;
  reaction_type: "like" | "love" | "fire" | "strong";
  created_at: string;
  updated_at: string;
  user: {
    id: string;
    email: string;
    data: any;
  };
}

interface UsePostFeedActionsProps {
  postId?: string;
  onCommentAdded?: (comment: Comment) => void;
  onCommentDeleted?: (commentId: string) => void;
  onReactionToggled?: (reactionData: any) => void;
}

export const usePostFeedActions = ({
  postId,
  onCommentAdded,
  onCommentDeleted,
  onReactionToggled,
}: UsePostFeedActionsProps = {}) => {
  const { showToast } = useContexts();
  const { mutateAsync: customRequest } = useCustomModelQuery({ showToast: false });

  const [isAddingComment, setIsAddingComment] = useState(false);
  const [isDeletingComment, setIsDeletingComment] = useState(false);
  const [isTogglingReaction, setIsTogglingReaction] = useState(false);
  const [isFetchingComments, setIsFetchingComments] = useState(false);
  const [isFetchingReactions, setIsFetchingReactions] = useState(false);

  const [comments, setComments] = useState<Comment[]>([]);
  const [reactions, setReactions] = useState<Reaction[]>([]);
  const [userReaction, setUserReaction] = useState<string | null>(null);
  
  // Use refs to store latest callback values to prevent dependency issues
  const onCommentAddedRef = useRef(onCommentAdded);
  const onCommentDeletedRef = useRef(onCommentDeleted);
  const onReactionToggledRef = useRef(onReactionToggled);
  
  // Update refs when values change
  onCommentAddedRef.current = onCommentAdded;
  onCommentDeletedRef.current = onCommentDeleted;
  onReactionToggledRef.current = onReactionToggled;

  // Add comment to a post
  const addComment = useCallback(
    async (content: string, isAnonymous: boolean = false, parentCommentId?: string) => {
      if (!postId || !content.trim()) return;

      setIsAddingComment(true);
      try {
        const result = await customRequest({
          endpoint: `/v2/api/kanglink/custom/trainer/feed/${postId}/comments`,
          method: "POST",
          body: {
            content: content.trim(),
            parent_comment_id: parentCommentId || null,
            is_private: isAnonymous,
            attachments: null,
            mentioned_users: null,
          },
        });

        if (result.error) {
          throw new Error(result.message);
        }

        const newComment = result.data;
        setComments((prev) => [...prev, newComment]);
        onCommentAddedRef.current?.(newComment);
        
        showToast("Comment added successfully", 3000, ToastStatusEnum.SUCCESS);
      } catch (error: any) {
        const message = error?.message || "Failed to add comment";
        showToast(message, 4000, ToastStatusEnum.ERROR);
        throw error;
      } finally {
        setIsAddingComment(false);
      }
    },
    [postId, customRequest, showToast]
  );

  // Delete a comment
  const deleteComment = useCallback(
    async (commentId: string) => {
      if (!commentId) return;

      setIsDeletingComment(true);
      try {
        const result = await customRequest({
          endpoint: `/v2/api/kanglink/custom/trainer/feed/comments/${commentId}`,
          method: "DELETE",
        });

        if (result.error) {
          throw new Error(result.message);
        }

        setComments((prev) => prev.filter((comment) => comment.id !== commentId));
        onCommentDeletedRef.current?.(commentId);
        
        showToast("Comment deleted successfully", 3000, ToastStatusEnum.SUCCESS);
      } catch (error: any) {
        const message = error?.message || "Failed to delete comment";
        showToast(message, 4000, ToastStatusEnum.ERROR);
        throw error;
      } finally {
        setIsDeletingComment(false);
      }
    },
    [customRequest, showToast]
  );

  // Toggle reaction on a post or comment
  const toggleReaction = useCallback(
    async (
      targetType: "post" | "comment",
      targetId: string,
      reactionType: "like" | "love" | "fire" | "strong"
    ) => {
      if (!targetId) return;

      setIsTogglingReaction(true);
      try {
        const result = await customRequest({
          endpoint: `/v2/api/kanglink/custom/trainer/feed/${targetType}/${targetId}/reactions`,
          method: "POST",
          body: {
            reaction_type: reactionType,
          },
        });

        if (result.error) {
          throw new Error(result.message);
        }

        const reactionData = result.data;
        
        // Update user reaction state
        if (reactionData.action === "removed") {
          setUserReaction(null);
        } else {
          setUserReaction(reactionData.reaction_type);
        }

        onReactionToggledRef.current?.(reactionData);
        
        // Don't show toast for reactions to keep UI smooth
      } catch (error: any) {
        const message = error?.message || "Failed to toggle reaction";
        showToast(message, 3000, ToastStatusEnum.ERROR);
        throw error;
      } finally {
        setIsTogglingReaction(false);
      }
    },
    [customRequest, showToast]
  );

  // Fetch comments for a post
  const fetchComments = useCallback(
    async (postIdParam?: string, includeReplies: boolean = true) => {
      const targetPostId = postIdParam || postId;
      if (!targetPostId) return;

      setIsFetchingComments(true);
      try {
        const result = await customRequest({
          endpoint: `/v2/api/kanglink/custom/trainer/feed/${targetPostId}/comments?include_replies=${includeReplies}&limit=50`,
          method: "GET",
        });

        if (result.error) {
          throw new Error(result.message);
        }

        setComments(result.data || []);
        return result.data;
      } catch (error: any) {
        const message = error?.message || "Failed to fetch comments";
        showToast(message, 3000, ToastStatusEnum.ERROR);
        throw error;
      } finally {
        setIsFetchingComments(false);
      }
    },
    [postId, customRequest, showToast]
  );

  // Fetch reactions for a post or comment
  const fetchReactions = useCallback(
    async (
      targetType: "post" | "comment",
      targetId: string,
      reactionType?: "like" | "love" | "fire" | "strong"
    ) => {
      if (!targetId) return;

      setIsFetchingReactions(true);
      try {
        let endpoint = `/v2/api/kanglink/custom/trainer/feed/${targetType}/${targetId}/reactions?limit=100`;
        if (reactionType) {
          endpoint += `&reaction_type=${reactionType}`;
        }

        const result = await customRequest({
          endpoint,
          method: "GET",
        });

        if (result.error) {
          throw new Error(result.message);
        }

        setReactions(result.data || []);
        return result.data;
      } catch (error: any) {
        const message = error?.message || "Failed to fetch reactions";
        showToast(message, 3000, ToastStatusEnum.ERROR);
        throw error;
      } finally {
        setIsFetchingReactions(false);
      }
    },
    [customRequest, showToast]
  );

  return {
    // Comment actions
    addComment,
    deleteComment,
    fetchComments,

    // Reaction actions
    toggleReaction,
    fetchReactions,

    // State
    comments,
    reactions,
    userReaction,

    // Loading states
    loading: {
      isAddingComment,
      isDeletingComment,
      isTogglingReaction,
      isFetchingComments,
      isFetchingReactions,
    },

    // Utilities
    setComments,
    setReactions,
    setUserReaction,
  };
}; 