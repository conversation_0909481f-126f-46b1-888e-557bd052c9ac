import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { IconDefinition } from "@fortawesome/free-solid-svg-icons";

interface DashboardStatCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  icon: IconDefinition;
}

const DashboardStatCard = ({ title, value, subtitle, icon }: DashboardStatCardProps) => {
  return (
    <div className="rounded-lg shadow-sm border border-border bg-secondary p-6 flex flex-col gap-4 dark:bg-neutral-800 dark:border-[#3a3a3a]">
      <div className="flex items-center justify-between">
        <span className="text-lg font-medium text-text dark:text-gray-100">
          {title}
        </span>
        <FontAwesomeIcon
          icon={icon}
          className="w-5 h-5 text-primary"
        />
      </div>
      <span className="text-3xl font-bold text-text dark:text-gray-100">
        {value}
      </span>
      <span className="text-sm text-gray-500 dark:text-gray-400">
        {subtitle}
      </span>
    </div>
  );
};

export default DashboardStatCard;
