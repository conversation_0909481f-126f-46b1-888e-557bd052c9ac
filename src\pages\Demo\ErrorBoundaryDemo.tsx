import { useState } from 'react';
import { ThemeProvider } from '@/context/Theme';
import { useTheme } from '@/hooks/useTheme';
import { THEME_COLORS } from '@/context/Theme';
import ErrorBoundary from '@/components/ErrorBoundary/ErrorBoundary';
import { ThemeToggle } from '@/components/ThemeToggle';
import { InteractiveButton } from '@/components/InteractiveButton';

// Component that can throw errors for testing
const ErrorThrower = ({ shouldThrow, errorMessage }: { shouldThrow: boolean; errorMessage?: string }) => {
  if (shouldThrow) {
    throw new Error(errorMessage || 'This is a test error for demonstration purposes');
  }
  return (
    <div className="p-6 rounded-lg border transition-colors duration-200">
      <h3 className="text-lg font-semibold mb-2">Working Component</h3>
      <p>This component is working normally. No errors here!</p>
    </div>
  );
};

const DemoContent = () => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [triggerError, setTriggerError] = useState(false);
  const [errorType, setErrorType] = useState<'simple' | 'complex'>('simple');

  const contentStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    color: THEME_COLORS[mode].TEXT,
    minHeight: '100vh',
  };

  const cardStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  const buttonStyles = {
    backgroundColor: THEME_COLORS[mode].PRIMARY,
    color: THEME_COLORS[mode].BACKGROUND,
  };

  const errorMessages = {
    simple: 'A simple error occurred',
    complex: 'A complex error with detailed information about what went wrong in the application'
  };

  return (
    <div style={contentStyles} className="transition-colors duration-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center space-y-8">
          <div>
            <h1 className="text-4xl font-bold mb-4" style={{ color: THEME_COLORS[mode].TEXT }}>
              ErrorBoundary Demo
            </h1>
            <p className="text-lg" style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}>
              This demo showcases the ErrorBoundary component with theme support and proper error handling.
            </p>
          </div>

          <div className="flex justify-center">
            <ThemeToggle className="mb-8" />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Controls */}
            <div 
              className="p-6 rounded-lg border transition-colors duration-200"
              style={cardStyles}
            >
              <h2 className="text-2xl font-semibold mb-4" style={{ color: THEME_COLORS[mode].TEXT }}>
                Error Controls
              </h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: THEME_COLORS[mode].TEXT }}>
                    Error Type:
                  </label>
                  <select
                    value={errorType}
                    onChange={(e) => setErrorType(e.target.value as 'simple' | 'complex')}
                    className="w-full p-2 border rounded transition-colors duration-200"
                    style={{
                      backgroundColor: THEME_COLORS[mode].INPUT,
                      borderColor: THEME_COLORS[mode].BORDER,
                      color: THEME_COLORS[mode].TEXT,
                    }}
                  >
                    <option value="simple">Simple Error</option>
                    <option value="complex">Complex Error</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <InteractiveButton
                    onClick={() => setTriggerError(true)}
                    className="w-full transition-colors duration-200"
                    style={{
                      backgroundColor: '#DC2626',
                      color: 'white',
                    }}
                  >
                    Trigger Error
                  </InteractiveButton>

                  <InteractiveButton
                    onClick={() => setTriggerError(false)}
                    className="w-full transition-colors duration-200"
                    style={buttonStyles}
                  >
                    Reset Component
                  </InteractiveButton>
                </div>
              </div>
            </div>

            {/* Error Boundary Demo */}
            <div 
              className="p-6 rounded-lg border transition-colors duration-200"
              style={cardStyles}
            >
              <h2 className="text-2xl font-semibold mb-4" style={{ color: THEME_COLORS[mode].TEXT }}>
                Component with ErrorBoundary
              </h2>
              
              <ErrorBoundary fallbackMessage={errorMessages[errorType]}>
                <ErrorThrower 
                  shouldThrow={triggerError} 
                  errorMessage={errorMessages[errorType]}
                />
              </ErrorBoundary>
            </div>
          </div>

          <div className="mt-12">
            <h2 className="text-2xl font-bold mb-4" style={{ color: THEME_COLORS[mode].TEXT }}>
              ErrorBoundary Features
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div 
                className="p-6 rounded-lg border transition-colors duration-200"
                style={cardStyles}
              >
                <h3 className="text-xl font-semibold mb-3" style={{ color: THEME_COLORS[mode].TEXT }}>
                  Theme Support
                </h3>
                <p style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}>
                  Automatically adapts to light and dark themes with proper color schemes:
                </p>
                <ul className="mt-2 text-sm" style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}>
                  <li>• Light: border-red-200 bg-red-50 text-red-800</li>
                  <li>• Dark: border-red-800 bg-red-900 text-red-200</li>
                </ul>
              </div>

              <div 
                className="p-6 rounded-lg border transition-colors duration-200"
                style={cardStyles}
              >
                <h3 className="text-xl font-semibold mb-3" style={{ color: THEME_COLORS[mode].TEXT }}>
                  Development Mode
                </h3>
                <p style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}>
                  Shows detailed error information in development mode including stack traces and component hierarchy.
                </p>
              </div>

              <div 
                className="p-6 rounded-lg border transition-colors duration-200"
                style={cardStyles}
              >
                <h3 className="text-xl font-semibold mb-3" style={{ color: THEME_COLORS[mode].TEXT }}>
                  Error Recovery
                </h3>
                <p style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}>
                  Provides a "Try Again" button that resets the error state and attempts to re-render the component.
                </p>
              </div>
            </div>
          </div>

          <div className="mt-12 p-6 rounded-lg border" 
               style={cardStyles}>
            <h3 className="text-xl font-semibold mb-3" style={{ color: THEME_COLORS[mode].TEXT }}>
              Usage Instructions
            </h3>
            <div className="text-left max-w-2xl mx-auto">
              <ol className="space-y-2" style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}>
                <li>1. Toggle between light and dark themes to see the color adaptation</li>
                <li>2. Select an error type (simple or complex)</li>
                <li>3. Click "Trigger Error" to see the ErrorBoundary in action</li>
                <li>4. Click "Try Again" in the error UI to reset the component</li>
                <li>5. Use "Reset Component" to return to the normal state</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const ErrorBoundaryDemo = () => {
  return (
    <ThemeProvider>
      <DemoContent />
    </ThemeProvider>
  );
};

export default ErrorBoundaryDemo;
