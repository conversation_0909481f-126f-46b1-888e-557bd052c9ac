import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { StarIcon } from "@heroicons/react/24/solid";

interface TrainerHeaderProps {
  trainer: {
    id: string;
    name: string;
    image: string;
    yearsOfExperience: number;
    areasOfExpertise: string[];
    rating: number;
  };
}

const TrainerHeader = ({ trainer }: TrainerHeaderProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <StarIcon
        key={index}
        className={`w-5 h-5 ${
          index < rating ? "text-green-400" : "text-gray-300 dark:text-gray-600"
        }`}
      />
    ));
  };

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 md:gap-8 lg:gap-8 xl:gap-12">
        {/* Profile Image */}
        <div className="flex-shrink-0 mx-auto sm:mx-0">
          <div className="w-32 h-32 sm:w-40 sm:h-40 md:w-44 md:h-44 lg:w-48 lg:h-48 xl:w-56 xl:h-56 rounded-full overflow-hidden shadow-lg border-4 border-white dark:border-gray-700">
            <img
              src={trainer.image}
              alt={trainer.name}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        {/* Trainer Info */}
        <div className="flex-1 flex flex-col justify-center">
          <div className="flex flex-col md:flex-row md:items-start md:justify-between xl:flex-row xl:items-start xl:justify-between gap-4 md:gap-6 xl:gap-8">
            {/* Left side - Name, Experience, Tags */}
            <div className="flex-1">
              <div className="text-center sm:!text-left">
                <h1
                  className="text-xl sm:text-2xl md:text-3xl lg:text-3xl xl:text-4xl font-bold mb-2 md:mb-3 lg:mb-3 transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  {trainer.name}
                </h1>

                <p
                  className="text-sm md:text-base lg:text-base mb-4 md:mb-5 lg:mb-6 transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                >
                  Years of Experience: {trainer.yearsOfExperience} Years
                </p>
              </div>

              {/* Areas of Expertise Tags */}
              <div className="flex flex-wrap gap-2 md:gap-3 lg:gap-3 justify-center sm:justify-start">
                {trainer.areasOfExpertise.map((area, index) => (
                  <span
                    key={index}
                    className="px-3 py-1.5 md:px-4 md:py-2 lg:px-4 lg:py-2 rounded-lg text-xs md:text-sm lg:text-sm font-medium transition-colors duration-200"
                    style={{
                      backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
                      color: THEME_COLORS[mode].TEXT,
                      border: `1px solid ${THEME_COLORS[mode].BORDER}`,
                    }}
                  >
                    {area}
                  </span>
                ))}
              </div>
            </div>

            {/* Right side - Rating */}
            <div className="flex items-center justify-center sm:justify-start md:justify-end xl:justify-end gap-1 md:mt-0 xl:mt-0">
              {renderStars(trainer.rating)}
            </div>
          </div>
        </div>
      </div>

      {/* Divider */}
      <div
        className="w-full h-px mt-6 lg:mt-8 xl:mt-12 transition-colors duration-200"
        style={{ backgroundColor: THEME_COLORS[mode].BORDER }}
      />
    </div>
  );
};

export default TrainerHeader;
