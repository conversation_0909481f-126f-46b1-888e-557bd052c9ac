import { useMemo, useEffect } from "react";
import { baseUrl, operations } from "@/utils";
import MkdSDK from "@/utils/MkdSDK";
import TreeSDK from "@/utils/TreeSDK";
import { OfflineAwareMkdSDK } from "@/utils/offline/OfflineAwareMkdSDK";
import { OfflineAwareTreeSDK } from "@/utils/offline/OfflineAwareTreeSDK";
import { useOffline } from "@/hooks/useOffline";

interface SdkConfig {
  baseurl?: string | null;
  fe_baseurl?: string | null;
  project_id?: string | null;
  secret?: string | null;
  table?: string | null;
  GOOGLE_CAPTCHA_SITEKEY?: string | null;
  enableOfflineMode?: boolean;
  role?: string | null;
}

interface UseSDKReturnType {
  sdk: OfflineAwareMkdSDK | MkdSDK;
  tdk: OfflineAwareTreeSDK | TreeSDK;
  projectId: string;
  operations: typeof operations;
  isOfflineMode: boolean;
}

const useSDK = (config: SdkConfig = {}): UseSDKReturnType => {
  // Try to get offline context, but don't require it
  // Note: useOffline must be called unconditionally to follow Rules of Hooks

  const offlineContext = useOffline();

  const enableOfflineMode = config.enableOfflineMode ?? true;
  const hasOfflineService = offlineContext && enableOfflineMode;
  const offlineService = offlineContext?.offlineService;

  const sdk = useMemo(() => {
    if (hasOfflineService) {
      return new OfflineAwareMkdSDK(
        { ...config, project_id: "kanglink", baseurl: baseUrl },
        offlineService
      );
    }
    return new MkdSDK({
      ...config,
      project_id: "kanglink",
      baseurl: baseUrl,
    });
  }, [config, hasOfflineService]);

  const tdk = useMemo(() => {
    if (hasOfflineService) {
      return new OfflineAwareTreeSDK(
        { ...config, project_id: "kanglink", baseurl: baseUrl },
        offlineService
      );
    }
    return new TreeSDK({
      ...config,
      project_id: "kanglink",
      baseurl: baseUrl,
    });
  }, [config, hasOfflineService]);

  // Configure offline-aware SDKs with offline mode settings
  useEffect(() => {
    if (hasOfflineService && offlineContext) {
      // Set offline mode based on network status
      const isOffline = !offlineContext.state.networkStatus.isOnline;

      if (sdk instanceof OfflineAwareMkdSDK) {
        sdk.setOfflineMode(isOffline);
      }
      if (tdk instanceof OfflineAwareTreeSDK) {
        tdk.setOfflineMode(isOffline);
      }
    }
  }, [
    hasOfflineService,
    offlineContext,
    sdk,
    tdk,
    offlineContext?.state.networkStatus.isOnline,
  ]);

  const projectId = sdk.getProjectId();

  return {
    sdk,
    tdk,
    projectId,
    operations,
    isOfflineMode: hasOfflineService,
  };
};

export default useSDK;
