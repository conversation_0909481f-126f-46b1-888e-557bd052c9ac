# AI Assistant Workflow Guide

**⚠️ CRITICAL: This file must be attached in every session for proper context engineering.**

## 📋 Context File Roles

### Primary Reference Files
- **`implementation.md`** → Step-by-step task execution plan. Follow this order strictly.
- **`ui_ux.md`** → Design system, layout rules, and visual guidelines. Consult for all frontend decisions.
- **`project_structure.md`** → File organization and folder hierarchy. Verify all paths here.
- **`bug_tracking.md`** → Known issues and debugging protocols. Check before troubleshooting.
- **`examples/`** → Standard code patterns and templates. Copy these structures exactly.

### Supporting Documentation
- **`examples/README.md`** → Pattern usage guide and conventions
- **`roadmap.md`** → Feature priorities and future plans
- **`progress.md`** → Current development status

## 🎯 Workflow Process

### 1. Task Planning
1. **Read `implementation.md`** → Identify current task and dependencies
2. **Check `examples/README.md`** → Understand available patterns
3. **Review `ui_ux.md`** → Confirm design requirements
4. **Verify `project_structure.md`** → Validate file paths and organization

### 2. Implementation
1. **Reference `examples/`** → Find matching pattern (component, hook, route, test, etc.)
2. **Copy structure exactly** → Maintain consistent architecture
3. **Follow `ui_ux.md`** → Apply design system and styling rules
4. **Use `project_structure.md`** → Place files in correct locations

### 3. Quality Assurance
1. **Check `bug_tracking.md`** → Avoid known issues
2. **Validate against examples** → Ensure pattern compliance
3. **Test implementation** → Verify functionality works

## ✅ DO - Required Actions

### Always Reference Context Files
- **Start with `implementation.md`** for task order and requirements
- **Use `examples/` patterns** for all code structure and logic
- **Follow `ui_ux.md`** for styling, layout, and user experience
- **Verify paths** in `project_structure.md` before creating files
- **Check `bug_tracking.md`** before debugging or fixing issues

### Code Quality Standards
- **Copy example patterns exactly** → Don't modify established structures
- **Use TypeScript interfaces** as shown in examples
- **Implement error handling** following example patterns
- **Add loading states** and user feedback as demonstrated
- **Follow naming conventions** from examples
- **Maintain folder structure** per `project_structure.md`

### Communication
- **Explain which examples** you're referencing
- **Cite specific files** from context when making decisions
- **Ask for clarification** if context files conflict
- **Update progress** in relevant tracking files

## ❌ DON'T - Avoid These Actions

### Never Guess or Assume
- **Don't create new patterns** → Use existing examples
- **Don't modify file structure** → Follow `project_structure.md`
- **Don't ignore design rules** → Always check `ui_ux.md`
- **Don't skip implementation steps** → Follow `implementation.md` order
- **Don't debug without checking** → Review `bug_tracking.md` first

### Code Anti-Patterns
- **Don't hallucinate APIs** → Use patterns from examples
- **Don't create inconsistent components** → Copy example structures
- **Don't skip error handling** → Follow example patterns
- **Don't ignore TypeScript** → Maintain type safety from examples
- **Don't break existing patterns** → Preserve architectural consistency

### Process Violations
- **Don't work without context** → Always attach required files
- **Don't skip file verification** → Check paths in `project_structure.md`
- **Don't ignore known bugs** → Review `bug_tracking.md`
- **Don't deviate from examples** → Maintain pattern consistency

## 🔄 Example Usage Pattern

### For New Components
1. Check `examples/reusable-component.example.tsx`
2. Copy folder structure: `ComponentName/index.ts`, `ComponentName.tsx`, `ComponentName.module.css`
3. Follow TypeScript interfaces and props patterns
4. Apply styling from `ui_ux.md`
5. Place in correct location per `project_structure.md`

### For API Integration
1. Reference `examples/api-query.example.tsx`
2. Use React Query patterns with offline support
3. Follow error handling and loading state patterns
4. Implement toast notifications as shown

### For Authentication
1. Use `examples/auth-context.example.tsx`
2. Follow `examples/protected-route.example.tsx` for route protection
3. Maintain role-based access patterns

### For Testing
1. Copy `examples/e2e-test.example.ts` structure
2. Use Page Object Model patterns
3. Follow assertion and waiting strategies

## 🚨 Critical Reminders

1. **Context Files Are Truth** → Never contradict established patterns
2. **Examples Are Templates** → Copy, don't modify
3. **Consistency Is Key** → Maintain architectural patterns
4. **Documentation Drives Development** → Follow the written plan
5. **This Workflow Must Always Be Available** → Attach in every session

## 📝 Session Checklist

Before starting any work, confirm:
- [ ] `workflow.md` (this file) is attached
- [ ] `implementation.md` is available for task planning
- [ ] `examples/` folder is accessible for patterns
- [ ] `ui_ux.md` is available for design decisions
- [ ] `project_structure.md` is available for file organization
- [ ] `bug_tracking.md` is available for issue awareness

**Remember: These context files contain the project's DNA. Preserve and follow them exactly.**
