import React from "react";
import { EmptyFeedStateProps } from "./types";

const EmptyFeedState: React.FC<EmptyFeedStateProps> = ({
  title = "No posts yet",
  description = "Be the first to share something with your athletes! Create your first post to start engaging with your community.",
}) => {
  return (
    <div className="text-center py-16">
      <div className="w-20 h-20 bg-background-hover rounded-full flex items-center justify-center mx-auto mb-6">
        <svg
          className="w-10 h-10 text-text-disabled"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
          />
        </svg>
      </div>
      <h3 className="text-xl font-semibold text-text mb-3">{title}</h3>
      <p className="text-text-disabled max-w-md mx-auto">{description}</p>
    </div>
  );
};

export default EmptyFeedState;
