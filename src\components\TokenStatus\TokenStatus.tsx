import React from 'react';
import { useTokenRefresh } from '@/hooks/useTokenRefresh';

/**
 * Component that displays current token status and provides manual refresh option
 * Useful for debugging and monitoring token state
 */
export const TokenStatus: React.FC = () => {
  const { tokenStatus, refreshToken, isRefreshing } = useTokenRefresh();

  const formatTime = (seconds: number): string => {
    if (seconds <= 0) return 'Expired';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const getStatusColor = (): string => {
    if (!tokenStatus.hasToken) return 'text-gray-500';
    if (tokenStatus.isExpired) return 'text-red-500';
    if (tokenStatus.expiresIn < 300) return 'text-yellow-500'; // Less than 5 minutes
    return 'text-green-500';
  };

  const getStatusText = (): string => {
    if (!tokenStatus.hasToken) return 'No Token';
    if (tokenStatus.isExpired) return 'Expired';
    return 'Valid';
  };

  return (
    <div className="p-4 border rounded-lg bg-white dark:bg-gray-800 shadow-sm">
      <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
        Token Status
      </h3>
      
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600 dark:text-gray-300">Status:</span>
          <span className={`text-sm font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
        
        {tokenStatus.hasToken && (
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600 dark:text-gray-300">Expires in:</span>
            <span className={`text-sm font-medium ${getStatusColor()}`}>
              {formatTime(tokenStatus.expiresIn)}
            </span>
          </div>
        )}
        
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600 dark:text-gray-300">Can Refresh:</span>
          <span className={`text-sm font-medium ${tokenStatus.canRefresh ? 'text-green-500' : 'text-red-500'}`}>
            {tokenStatus.canRefresh ? 'Yes' : 'No'}
          </span>
        </div>
        
        {tokenStatus.canRefresh && (
          <div className="mt-3">
            <button
              onClick={refreshToken}
              disabled={isRefreshing}
              className="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRefreshing ? 'Refreshing...' : 'Manual Refresh'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TokenStatus;
