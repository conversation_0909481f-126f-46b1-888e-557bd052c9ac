import React from "react";
import { useTheme } from "@/hooks/useTheme";

interface AffiliateLinkSectionProps {
  value?: string;
  onChange?: (value: string) => void;
}

const AffiliateLinkSection = ({
  value = "www.affiliate-link.com",
  onChange,
}: AffiliateLinkSectionProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <div className="bg-background border border-border rounded-md p-6 shadow-sm">
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-text mb-2">
            Affiliate Link
          </label>
          <div className="relative">
            <input
              type="text"
              value={value}
              onChange={(e) => onChange?.(e.target.value)}
              className="w-full pl-3 px-10 py-2 border border-border rounded-md bg-input text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="www.affiliate-link.com"
              disabled
            />
            <div
              onClick={() => {
                navigator.clipboard.writeText(value);
              }}
              className="absolute cursor-pointer right-3 top-1/2 transform -translate-y-1/2"
            >
              <svg
                width={16}
                height={16}
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clipPath="url(#clip0_92_146)">
                  <path
                    d="M8.5 0H12.3781C12.775 0 13.1562 0.159375 13.4375 0.440625L15.5594 2.5625C15.8406 2.84375 16 3.225 16 3.62188V10.5C16 11.3281 15.3281 12 14.5 12H8.5C7.67188 12 7 11.3281 7 10.5V1.5C7 0.671875 7.67188 0 8.5 0ZM1.5 4H6V6H2V14H8V13H10V14.5C10 15.3281 9.32812 16 8.5 16H1.5C0.671875 16 0 15.3281 0 14.5V5.5C0 4.67188 0.671875 4 1.5 4Z"
                    fill="#4CBF6D"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_92_146">
                    <path d="M0 0H16V16H0V0Z" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AffiliateLinkSection;
