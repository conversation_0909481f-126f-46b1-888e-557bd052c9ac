import { useContexts } from "@/hooks/useContexts";
import { useEffect, Suspense } from "react";
import { Link } from "react-router-dom";
import {
  faChartLine,
  faUsers,
  faDollarSign,
} from "@fortawesome/free-solid-svg-icons";
import {
  DashboardStatCard,
  DashboardTabContainer,
} from "@/components/TrainerDashboard";
import { useTrainerDashboard } from "@/hooks/useTrainerDashboard";
import { useTrainerStatus } from "@/hooks/useTrainerStatus";
import { DeactivationWarning } from "@/components/DeactivationWarning";

// Helper function to get month name
const getMonthName = (month: number): string => {
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];
  return months[month - 1] || "Unknown";
};

const TrainerDashboard = () => {
  const { globalDispatch } = useContexts();
  const { isDeactivated } = useTrainerStatus();

  // Fetch dashboard data
  const {
    stats,
    notifications,
    activities,
    isStatsLoading,
    isNotificationsLoading,
    isActivitiesLoading,
    error,
  } = useTrainerDashboard({
    enabled: true,
    refreshInterval: 5 * 60 * 1000, // Refresh every 5 minutes
  });

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "dashboard",
      },
    });
  }, [globalDispatch]);

  // Show error state if there's an error
  if (error) {
    return (
      <div className="relative flex flex-col gap-6 px-4 py-6 w-full max-w-[1200px] mx-auto min-h-screen bg-background text-text">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-red-600 dark:text-red-400 mb-2">
              Error Loading Dashboard
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-hover transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="relative flex flex-col gap-6 px-4 py-6 w-full max-w-[1200px] mx-auto min-h-screen bg-background text-text">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h1 className="font-bold text-2xl sm:text-3xl text-text dark:text-gray-100">
            Dashboard
          </h1>
          <div className="flex gap-3 w-full sm:w-auto">
            <Link
              to={"/trainer/feed"}
              className="flex-1 sm:flex-none px-6 py-2 rounded border border-border bg-background text-text font-semibold text-sm hover:bg-background-hover focus:outline-none transition-colors dark:bg-neutral-800 dark:text-gray-100 dark:border-[#3a3a3a]"
            >
              Feed
            </Link>
           {!isDeactivated && <Link
              to={"/trainer/add-program"}
              className="flex-1 sm:flex-none px-6 py-2 rounded bg-primary text-white font-semibold text-sm hover:bg-primary-hover focus:outline-none transition-colors"
            >
              + Create New Program
            </Link>}
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Suspense
            fallback={
              <div className="animate-pulse bg-gray-200 dark:bg-gray-700 rounded-lg h-32"></div>
            }
          >
            <DashboardStatCard
              title="Active Programs"
              value={
                isStatsLoading ? "..." : (stats?.active_programs_count ?? 0)
              }
              subtitle="Currently active programs"
              icon={faChartLine}
            />
          </Suspense>
          <Suspense
            fallback={
              <div className="animate-pulse bg-gray-200 dark:bg-gray-700 rounded-lg h-32"></div>
            }
          >
            <DashboardStatCard
              title="Active Athletes"
              value={
                isStatsLoading ? "..." : (stats?.active_athletes_count ?? 0)
              }
              subtitle="Currently enrolled"
              icon={faUsers}
            />
          </Suspense>
          <Suspense
            fallback={
              <div className="animate-pulse bg-gray-200 dark:bg-gray-700 rounded-lg h-32"></div>
            }
          >
            <DashboardStatCard
              title="Revenue Generated"
              value={
                isStatsLoading
                  ? "..."
                  : `$${stats?.monthly_revenue?.toLocaleString() ?? "0"}`
              }
              subtitle={
                stats?.period
                  ? `${getMonthName(stats.period.month)} ${stats.period.year}`
                  : "This month"
              }
              icon={faDollarSign}
            />
          </Suspense>
        </div>

        {/* Notifications & Activity */}
        <Suspense
          fallback={
            <div className="animate-pulse bg-gray-200 dark:bg-gray-700 rounded-lg h-64 mt-6"></div>
          }
        >
          <DashboardTabContainer
            notifications={notifications}
            activities={activities}
            isNotificationsLoading={isNotificationsLoading}
            isActivitiesLoading={isActivitiesLoading}
          />
        </Suspense>
      </div>
    </>
  );
};

export default TrainerDashboard;
