import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import ProgramGridCard from "@/components/ProgramGridCard/ProgramGridCard";

interface Program {
  id: string;
  name: string;
  description: string;
  price: number;
  rating: number;
  image: string;
  isFavorite?: boolean;
}

interface ProgramsProps {
  programs: Program[];
  onFavoriteToggle?: (programId: string, isFavorite: boolean) => void;
  onProgramClick?: (programId: string) => void;
}

const Programs = ({
  programs,
  onFavoriteToggle,
  onProgramClick,
}: ProgramsProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <div className="w-full">
      {/* Section Title */}
      <h2
        className="text-lg sm:text-xl lg:text-2xl font-bold mb-6 lg:mb-8 transition-colors duration-200"
        style={{ color: THEME_COLORS[mode].TEXT }}
      >
        All Programs
      </h2>

      {/* Programs Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-6">
        {programs.map((program, index: any) => (
          <ProgramGridCard
            key={`${program.id}-${index}`}
            program={program}
            onFavoriteToggle={onFavoriteToggle}
            onProgramClick={onProgramClick}
          />
        ))}
      </div>
    </div>
  );
};
export default Programs;
