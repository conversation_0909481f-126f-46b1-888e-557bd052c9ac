<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="successGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="warningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="infoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>

    <!-- Arrow marker -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7"
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151" />
    </marker>

    <!-- Dotted arrow marker -->
    <marker id="dottedArrow" markerWidth="10" markerHeight="7"
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#9CA3AF" />
    </marker>
  </defs>

  <!-- Background -->
  <rect width="1200" height="800" fill="#F9FAFB"/>

  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#111827">
    Trainer Discount Management - Data Flow Diagram
  </text>

  <!-- Page Load Flow Section -->
  <text x="50" y="80" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#374151">
    1. Page Load Flow
  </text>

  <!-- Page Load Components -->
  <rect x="50" y="100" width="120" height="60" rx="8" fill="url(#primaryGradient)" stroke="#4F46E5" stroke-width="2"/>
  <text x="110" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Discount Page</text>
  <text x="110" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Loads</text>

  <rect x="220" y="100" width="120" height="60" rx="8" fill="url(#infoGradient)" stroke="#3B82F6" stroke-width="2"/>
  <text x="280" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">GET Program</text>
  <text x="280" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Pricing</text>

  <rect x="390" y="100" width="120" height="60" rx="8" fill="url(#infoGradient)" stroke="#3B82F6" stroke-width="2"/>
  <text x="450" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">GET Discount</text>
  <text x="450" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Settings</text>

  <rect x="560" y="100" width="120" height="60" rx="8" fill="url(#successGradient)" stroke="#10B981" stroke-width="2"/>
  <text x="620" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Populate</text>
  <text x="620" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">UI Forms</text>

  <!-- Page Load Arrows -->
  <line x1="170" y1="130" x2="210" y2="130" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="340" y1="130" x2="380" y2="130" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="510" y1="130" x2="550" y2="130" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- User Interaction Flow Section -->
  <text x="50" y="220" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#374151">
    2. User Interaction Flow
  </text>

  <!-- User Interaction Components -->
  <rect x="50" y="240" width="120" height="60" rx="8" fill="url(#warningGradient)" stroke="#F59E0B" stroke-width="2"/>
  <text x="110" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">User Modifies</text>
  <text x="110" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Discounts</text>

  <rect x="220" y="240" width="120" height="60" rx="8" fill="url(#infoGradient)" stroke="#3B82F6" stroke-width="2"/>
  <text x="280" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">POST Preview</text>
  <text x="280" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Calculation</text>

  <rect x="390" y="240" width="120" height="60" rx="8" fill="url(#successGradient)" stroke="#10B981" stroke-width="2"/>
  <text x="450" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Real-time</text>
  <text x="450" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">UI Update</text>

  <!-- User Interaction Arrows -->
  <line x1="170" y1="270" x2="210" y2="270" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="340" y1="270" x2="380" y2="270" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- Apply Flow Section -->
  <text x="50" y="360" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#374151">
    3. Apply Changes Flow
  </text>

  <!-- Apply Flow Components -->
  <rect x="50" y="380" width="100" height="60" rx="8" fill="url(#primaryGradient)" stroke="#4F46E5" stroke-width="2"/>
  <text x="100" y="400" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Apply</text>
  <text x="100" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Button</text>

  <rect x="180" y="380" width="100" height="60" rx="8" fill="url(#infoGradient)" stroke="#3B82F6" stroke-width="2"/>
  <text x="230" y="400" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Validate</text>
  <text x="230" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Promo Code</text>

  <rect x="310" y="380" width="100" height="60" rx="8" fill="url(#infoGradient)" stroke="#3B82F6" stroke-width="2"/>
  <text x="360" y="400" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">PUT Update</text>
  <text x="360" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Settings</text>

  <rect x="440" y="380" width="100" height="60" rx="8" fill="url(#successGradient)" stroke="#10B981" stroke-width="2"/>
  <text x="490" y="400" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Success</text>
  <text x="490" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Response</text>

  <rect x="570" y="380" width="100" height="60" rx="8" fill="url(#successGradient)" stroke="#10B981" stroke-width="2"/>
  <text x="620" y="400" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Refresh</text>
  <text x="620" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">UI Data</text>

  <!-- Apply Flow Arrows -->
  <line x1="150" y1="410" x2="170" y2="410" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="280" y1="410" x2="300" y2="410" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="410" y1="410" x2="430" y2="410" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="540" y1="410" x2="560" y2="410" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- Database Layer -->
  <text x="750" y="80" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#374151">
    Database Layer
  </text>

  <!-- Database Components -->
  <rect x="750" y="100" width="180" height="80" rx="8" fill="#E5E7EB" stroke="#9CA3AF" stroke-width="2"/>
  <text x="840" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#374151">Program Pricing Tiers</text>
  <text x="840" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#374151">• Subscription Tiers</text>
  <text x="840" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#374151">• Full Payment Tiers</text>

  <rect x="750" y="200" width="180" height="80" rx="8" fill="#E5E7EB" stroke="#9CA3AF" stroke-width="2"/>
  <text x="840" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#374151">Discount Settings</text>
  <text x="840" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#374151">• Sale Discounts</text>
  <text x="840" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#374151">• Individual Discounts</text>

  <rect x="750" y="300" width="180" height="80" rx="8" fill="#E5E7EB" stroke="#9CA3AF" stroke-width="2"/>
  <text x="840" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#374151">Promo Codes</text>
  <text x="840" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#374151">• Code Validation</text>
  <text x="840" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#374151">• Usage Tracking</text>

  <rect x="750" y="400" width="180" height="80" rx="8" fill="#E5E7EB" stroke="#9CA3AF" stroke-width="2"/>
  <text x="840" y="420" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#374151">Audit Log</text>
  <text x="840" y="440" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#374151">• Change Tracking</text>
  <text x="840" y="455" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#374151">• User Actions</text>

  <!-- Stripe Integration -->
  <text x="980" y="80" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#374151">
    Stripe Integration
  </text>

  <rect x="980" y="100" width="160" height="60" rx="8" fill="#6366F1" stroke="#4F46E5" stroke-width="2"/>
  <text x="1060" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Create/Update</text>
  <text x="1060" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Products &amp; Prices</text>

  <rect x="980" y="180" width="160" height="60" rx="8" fill="#6366F1" stroke="#4F46E5" stroke-width="2"/>
  <text x="1060" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
    <tspan x="1060" dy="0">Create/Manage</tspan>
    <tspan x="1060" dy="15">Coupons</tspan>
  </text>

  <rect x="980" y="260" width="160" height="60" rx="8" fill="#6366F1" stroke="#4F46E5" stroke-width="2"/>
  <text x="1060" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
    <tspan x="1060" dy="0">Sync Discount</tspan>
    <tspan x="1060" dy="15">Changes</tspan>
  </text>

  <!-- Database to Stripe connections -->
  <line x1="930" y1="140" x2="970" y2="130" stroke="#9CA3AF" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#dottedArrow)"/>
  <line x1="930" y1="240" x2="970" y2="210" stroke="#9CA3AF" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#dottedArrow)"/>
  <line x1="930" y1="340" x2="970" y2="290" stroke="#9CA3AF" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#dottedArrow)"/>

  <!-- API to Database connections -->
  <line x1="680" y1="130" x2="740" y2="140" stroke="#9CA3AF" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#dottedArrow)"/>
  <line x1="510" y1="130" x2="740" y2="240" stroke="#9CA3AF" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#dottedArrow)"/>
  <line x1="360" y1="410" x2="740" y2="340" stroke="#9CA3AF" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#dottedArrow)"/>
  <line x1="490" y1="410" x2="740" y2="440" stroke="#9CA3AF" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#dottedArrow)"/>

  <!-- Error Handling Flow -->
  <text x="50" y="520" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#374151">
    4. Error Handling Flow
  </text>

  <rect x="50" y="540" width="120" height="60" rx="8" fill="#EF4444" stroke="#DC2626" stroke-width="2"/>
  <text x="110" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
    <tspan x="110" dy="0">Network/</tspan>
    <tspan x="110" dy="15">Validation Error</tspan>
  </text>

  <rect x="220" y="540" width="120" height="60" rx="8" fill="#F59E0B" stroke="#D97706" stroke-width="2"/>
  <text x="280" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
    <tspan x="280" dy="0">Show Error</tspan>
    <tspan x="280" dy="15">Message</tspan>
  </text>

  <rect x="390" y="540" width="120" height="60" rx="8" fill="#3B82F6" stroke="#1D4ED8" stroke-width="2"/>
  <text x="450" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">
    <tspan x="450" dy="0">Retry Option/</tspan>
    <tspan x="450" dy="15">Field Highlight</tspan>
  </text>

  <!-- Error Flow Arrows -->
  <line x1="170" y1="570" x2="210" y2="570" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="340" y1="570" x2="380" y2="570" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- Data Types Legend -->
  <text x="50" y="660" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#374151">
    Data Types &amp; Calculations
  </text>

  <rect x="50" y="680" width="280" height="100" rx="8" fill="#F3F4F6" stroke="#D1D5DB" stroke-width="1"/>
  <text x="60" y="700" font-family="Arial, sans-serif" font-size="12" fill="#374151">• Discount Types: Fixed ($) | Percentage (%)</text>
  <text x="60" y="720" font-family="Arial, sans-serif" font-size="12" fill="#374151">• Revenue Impact: Original vs Discounted</text>
  <text x="60" y="740" font-family="Arial, sans-serif" font-size="12" fill="#374151">• Real-time Calculations: Preview before save</text>
  <text x="60" y="760" font-family="Arial, sans-serif" font-size="12" fill="#374151">• Promo Code Validation: Uniqueness &amp; conflicts</text>
  <text x="60" y="780" font-family="Arial, sans-serif" font-size="12" fill="#374151">• Stripe Sync: Products, Prices, Coupons</text>

  <!-- Legend -->
  <text x="400" y="660" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#374151">
    Legend
  </text>

  <rect x="400" y="680" width="20" height="15" fill="url(#primaryGradient)"/>
  <text x="430" y="692" font-family="Arial, sans-serif" font-size="12" fill="#374151">User Actions</text>

  <rect x="400" y="705" width="20" height="15" fill="url(#infoGradient)"/>
  <text x="430" y="717" font-family="Arial, sans-serif" font-size="12" fill="#374151">API Calls</text>

  <rect x="400" y="730" width="20" height="15" fill="url(#successGradient)"/>
  <text x="430" y="742" font-family="Arial, sans-serif" font-size="12" fill="#374151">Success States</text>

  <rect x="400" y="755" width="20" height="15" fill="#EF4444"/>
  <text x="430" y="767" font-family="Arial, sans-serif" font-size="12" fill="#374151">Error States</text>

  <line x1="550" y1="690" x2="580" y2="690" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="590" y="695" font-family="Arial, sans-serif" font-size="12" fill="#374151">Data Flow</text>

  <line x1="550" y1="710" x2="580" y2="710" stroke="#9CA3AF" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#dottedArrow)"/>
  <text x="590" y="715" font-family="Arial, sans-serif" font-size="12" fill="#374151">Database/Stripe</text>
</svg>
