import { useMemo, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useGetListQuery } from "@/query/shared/listModel";
import { Models } from "@/utils/baas/models";
import { queryKeys } from "@/query/queryKeys";
import { useSDK } from "@/hooks/useSDK";
import { RoleEnum } from "@/utils/Enums";
import { 
  useAdminAlertStats, 
  usePendingApprovalPrograms, 
  useRefundRequests,
  AdminAlertStats,
  PendingProgram,
  RefundRequest
} from "@/hooks/useAdminAlerts";

// Admin-specific dashboard stats interface
export interface AdminDashboardStats {
  total_athletes: number;
  total_trainers: number;
  pending_program_approval: number;
  pending_refund_requests: number;
  // Admin alerts stats
  recent_alerts_count: number;
  unread_alerts_count: number;
  pending_approval_programs_count: number;
  pending_refund_requests_count: number;
}

// Extended interfaces for detailed data
export interface AdminDashboardDetailedData {
  pendingPrograms: PendingProgram[];
  pendingRefunds: RefundRequest[];
  alertStats: AdminAlertStats | null;
}

// Admin-specific alert interface
export interface AdminAlert {
  id: string;
  title: string;
  message: string;
  count?: number;
}

export interface UseAdminDashboardProps {
  enabled?: boolean;
  refreshInterval?: number; // in milliseconds
}

export interface UseAdminDashboardReturn {
  stats: AdminDashboardStats | null;
  alerts: AdminAlert[];
  detailedData: AdminDashboardDetailedData;
  isLoading: boolean;
  isStatsLoading: boolean;
  isAlertsLoading: boolean;
  error: string | null;
  refetch: () => void;
  refetchStats: () => void;
}

export const useAdminDashboard = ({
  enabled = true,
  refreshInterval,
}: UseAdminDashboardProps = {}): UseAdminDashboardReturn => {
  const queryClient = useQueryClient();
  const { operations } = useSDK();

  // Admin alerts hooks
  const { data: alertStats, isLoading: isAlertStatsLoading } = useAdminAlertStats();
  const { data: pendingProgramsData, isLoading: isPendingProgramsLoading } = usePendingApprovalPrograms();
  const { data: refundRequestsData, isLoading: isRefundRequestsLoading } = useRefundRequests({ status: "pending" });

  // Fetch athletes (users with role 'member')
  const {
    data: athletesResponse,
    isLoading: isAthletesLoading,
    error: athletesError,
    refetch: refetchAthletes,
  } = useGetListQuery(
    Models.USER,
    {
      filter: [`role_id,${operations.EQUAL},member`],
      size: 1000000, // Get all to count them
    },
    RoleEnum.SUPER_ADMIN,
    {
      enabled: enabled,
      refetchInterval: refreshInterval,
      staleTime: 2 * 60 * 1000, // 2 minutes
      cacheTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  // Fetch trainers (users with role 'trainer')
  const {
    data: trainersResponse,
    isLoading: isTrainersLoading,
    error: trainersError,
    refetch: refetchTrainers,
  } = useGetListQuery(
    Models.USER,
    {
      filter: [`role_id,${operations.EQUAL},trainer`],
      size: 1000000, // Get all to count them
    },
    RoleEnum.SUPER_ADMIN,
    {
      enabled: enabled,
      refetchInterval: refreshInterval,
      staleTime: 2 * 60 * 1000, // 2 minutes
      cacheTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  // Fetch programs pending approval
  // const {
  //   data: pendingProgramsResponse,
  //   isLoading: isPendingProgramsLoading,
  //   error: pendingProgramsError,
  //   refetch: refetchPendingPrograms,
  // } = useGetListQuery(
  //   Models.PROGRAM,
  //   {
  //     filter: [`status,${operations.CONTAINS},pending_approval`],
  //     size: 1000000, // Get all to count them
  //   },
  //   RoleEnum.SUPER_ADMIN,
  //   {
  //     enabled: enabled,
  //     refetchInterval: refreshInterval,
  //     staleTime: 2 * 60 * 1000, // 2 minutes
  //     cacheTime: 5 * 60 * 1000, // 5 minutes
  //   }
  // );

  // Fetch Refund requests pending approval - commented out as we now use the admin alerts API
  // const {
  //   data: pendingRefundRequestsResponse,
  //   isLoading: isPendingRefundRequestsLoading,
  //   error: pendingRefundRequestsError,
  //   refetch: refetchPendingRefundRequests,
  // } = useGetListQuery(
  //   Models.ENROLLMENT,
  //   {
  //     filter: [
  //       `status,${operations.CONTAINS},refund`,
  //       `payment_status,${operations.NOT_CONTAINS},refunded`,
  //     ],
  //     size: 1000000, // Get all to count them
  //   },
  //   RoleEnum.SUPER_ADMIN,
  //   {
  //     enabled: enabled,
  //     refetchInterval: refreshInterval,
  //     staleTime: 2 * 60 * 1000, // 2 minutes
  //     cacheTime: 5 * 60 * 1000, // 5 minutes
  //   }
  // );

  // Calculate stats from the fetched data
  const stats = useMemo((): AdminDashboardStats | null => {
    if (
      !athletesResponse?.data ||
      !trainersResponse?.data 
    ) {
      return null;
    }

    return {
      total_athletes: athletesResponse.data.length || 0,
      total_trainers: trainersResponse.data.length || 0,
      pending_program_approval: alertStats?.pending_approval_programs_count || pendingProgramsData?.count || 0,
      pending_refund_requests: alertStats?.pending_refund_requests_count || refundRequestsData?.count || 0,
      // Admin alerts stats
      recent_alerts_count: alertStats?.recent_alerts_count || 0,
      unread_alerts_count: alertStats?.unread_alerts_count || 0,
      pending_approval_programs_count: alertStats?.pending_approval_programs_count || pendingProgramsData?.count || 0,
      pending_refund_requests_count: alertStats?.pending_refund_requests_count || refundRequestsData?.count || 0,
    };
  }, [
    athletesResponse?.data,
    trainersResponse?.data,
    alertStats,
    pendingProgramsData?.count,
    refundRequestsData?.count,
  ]);

  // Generate alerts based on stats
  const alerts = useMemo((): AdminAlert[] => {
    if (!stats) return [];

    const alertsList: AdminAlert[] = [];

    if (stats.pending_program_approval > 0) {
      alertsList.push({
        id: "program_approval",
        title: "Program Approval Pending",
        message: `${stats.pending_program_approval} new program approval pending`,
        count: stats.pending_program_approval,
      });
    }

    if (stats.pending_refund_requests > 0) {
      alertsList.push({
        id: "refund_requests",
        title: "Refund Requests",
        message: `${stats.pending_refund_requests} pending refund requests`,
        count: stats.pending_refund_requests,
      });
    }

    return alertsList;
  }, [stats]);

  // Create detailed data object
  const detailedData = useMemo((): AdminDashboardDetailedData => {
    return {
      pendingPrograms: pendingProgramsData?.programs || [],
      pendingRefunds: refundRequestsData?.refund_requests || [],
      alertStats: alertStats || null,
    };
  }, [pendingProgramsData?.programs, refundRequestsData?.refund_requests, alertStats]);

  // Determine loading states
  const isStatsLoading =
    isAthletesLoading ||
    isTrainersLoading ||
    isAlertStatsLoading ||
    isPendingProgramsLoading ||
    isRefundRequestsLoading;
  const isLoading = isStatsLoading;
  const isAlertsLoading = isStatsLoading; // Alerts depend on stats

    // Determine error state
  const error =
    athletesError ||
    trainersError
    ? String(
        athletesError ||
        trainersError
      )
    : null;

  // Refetch function to invalidate and refetch all queries
  const refetch = () => {
    queryClient.invalidateQueries({
      queryKey: [queryKeys.user?.list],
    });
    queryClient.invalidateQueries({
      queryKey: [queryKeys.program?.list],
    });
    refetchAthletes();
    refetchTrainers();
  };

  const refetchStats = () => {
    refetchAthletes();
    refetchTrainers();
  };

  // Set up interval to invalidate queries if refreshInterval is provided
  useEffect(() => {
    if (!refreshInterval || !enabled) return;

    const interval = setInterval(() => {
      queryClient.invalidateQueries({
        queryKey: [queryKeys.user?.list],
      });
      queryClient.invalidateQueries({
        queryKey: [queryKeys.program?.list],
      });
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval, enabled, queryClient]);

  return {
    stats,
    alerts,
    detailedData,
    isLoading,
    isStatsLoading,
    isAlertsLoading,
    error,
    refetch,
    refetchStats,
  };
};

export default useAdminDashboard;
