import { useState } from "react";
import DashboardNotifications from "./DashboardNotifications";
import DashboardActivity from "./DashboardActivity";
import { Notification, Activity } from "@/interfaces/model.interface";

type TabType = "notifications" | "activity";

interface DashboardTabContainerProps {
  className?: string;
  notifications?: Notification[];
  activities?: Activity[];
  isNotificationsLoading?: boolean;
  isActivitiesLoading?: boolean;
}

const DashboardTabContainer = ({
  className = "",
  notifications,
  activities,
  isNotificationsLoading = false,
  isActivitiesLoading = false,
}: DashboardTabContainerProps) => {
  const [activeTab, setActiveTab] = useState<TabType>("notifications");

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
  };

  return (
    <div
      className={`rounded-lg shadow-sm border border-border bg-secondary p-0 flex flex-col mt-6 dark:bg-neutral-800 dark:border-[#3a3a3a] ${className}`}
    >
      {/* Tab Headers */}
      <div className="flex">
        <button
          onClick={() => handleTabChange("notifications")}
          className={`flex-1 py-3 rounded-tl-lg font-semibold text-sm focus:outline-none transition-colors ${
            activeTab === "notifications"
              ? "bg-primary text-white hover:bg-primary-hover"
              : "bg-background text-text border-l border-border hover:bg-background-hover dark:bg-neutral-800 dark:text-gray-100 dark:border-[#3a3a3a]"
          }`}
        >
          Notifications
        </button>
        <button
          onClick={() => handleTabChange("activity")}
          className={`flex-1 py-3 rounded-tr-lg font-semibold text-sm border-l border-border focus:outline-none transition-colors ${
            activeTab === "activity"
              ? "bg-primary text-white hover:bg-primary-hover"
              : "bg-background text-text hover:bg-background-hover dark:bg-neutral-800 dark:text-gray-100 dark:border-[#3a3a3a]"
          }`}
        >
          Activity
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === "notifications" && (
        <DashboardNotifications
          notifications={notifications}
          isLoading={isNotificationsLoading}
        />
      )}
      {activeTab === "activity" && (
        <DashboardActivity
          activities={activities}
          isLoading={isActivitiesLoading}
          showFilters={true}
        />
      )}
    </div>
  );
};

export default DashboardTabContainer;
