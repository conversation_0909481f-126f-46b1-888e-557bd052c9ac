export const TrainerEndpoints = {
  // Authentication Endpoints
  TRAINER_AUTH: {
    FORGOT_PASSWORD: {
      url: "/v2/api/kanglink/custom/trainer/forgot-password",
      method: "POST",
      body: {
        email: "string",
      },
    },
    CHANGE_PASSWORD: {
      url: "/v2/api/kanglink/custom/trainer/change-password",
      method: "POST",
      body: {
        currentPassword: "string",
        newPassword: "string",
        confirmPassword: "string",
      },
    },
    VERIFY_OTP: {
      url: "/v2/api/kanglink/custom/trainer/verify-otp",
      method: "POST",
      body: {
        email: "string",
        otp: "string",
      },
    },
  },

  // Profile Management Endpoints
  TRAINER_PROFILE: {
    GET: {
      url: "/v2/api/kanglink/custom/trainer/profile",
      method: "GET",
    },
    UPDATE: {
      url: "/v2/api/kanglink/custom/trainer/profile",
      method: "PUT",
      body: {
        fullName: "string",
        email: "string",
        gender: "string",
        bio: "string",
        yearsOfExperience: "number",
        qualifications: "array",
        specializations: "array",
        phone: "string",
        profilePicture: "file",
      },
    },
    UPLOAD_PICTURE: {
      url: "/v2/api/kanglink/custom/trainer/profile/upload-picture",
      method: "POST",
      body: "FormData with image file",
    },
  },

  // Program Management Endpoints
  TRAINER_PROGRAMS: {
    CREATE: {
      url: "/v2/api/kanglink/custom/trainer/programs",
      method: "POST",
      body: {
        programName: "string",
        description: "string",
        category: "string",
        difficulty: "string",
        duration: "number",
        price: "number",
        exercises: "array",
        weeks: "array",
        sessions: "array",
      },
    },
    GET_ALL: {
      url: "/v2/api/kanglink/custom/trainer/programs",
      method: "GET",
      query: {
        page: "number",
        limit: "number",
        status: "string",
        search: "string",
      },
    },
    GET_BY_ID: {
      url: "/v2/api/kanglink/custom/trainer/programs/{id}",
      method: "GET",
      params: {
        id: "string|number",
      },
    },
    UPDATE: {
      url: "/v2/api/kanglink/custom/trainer/programs/{id}",
      method: "PUT",
      params: {
        id: "string|number",
      },
      body: {
        programName: "string",
        description: "string",
        category: "string",
        difficulty: "string",
        duration: "number",
        price: "number",
        exercises: "array",
        weeks: "array",
        sessions: "array",
      },
    },
    DELETE: {
      url: "/v2/api/kanglink/custom/trainer/programs/{id}",
      method: "DELETE",
      params: {
        id: "string|number",
      },
    },
    PUBLISH: {
      url: "/v2/api/kanglink/custom/trainer/programs/{id}/publish",
      method: "POST",
      params: {
        id: "string|number",
      },
    },
    SAVE_DRAFT: {
      url: "/v2/api/kanglink/custom/trainer/programs/{id}/draft",
      method: "POST",
      params: {
        id: "string|number",
      },
    },
    SEARCH: {
      url: "/v2/api/kanglink/custom/trainer/programs/search",
      method: "GET",
      query: {
        q: "string",
        status: "string",
        category: "string",
      },
    },
  },

  // Athlete Management Endpoints
  TRAINER_ATHLETES: {
    GET_ALL: {
      url: "/v2/api/kanglink/custom/trainer/athletes",
      method: "GET",
      query: {
        page: "number",
        limit: "number",
        program: "string",
        purchaseType: "string",
        search: "string",
      },
    },
    GET_PROGRESS: {
      url: "/v2/api/kanglink/custom/trainer/athletes/{id}/progress",
      method: "GET",
      params: {
        id: "string|number",
      },
    },
    UPDATE_STATUS: {
      url: "/v2/api/kanglink/custom/trainer/athletes/{id}/status",
      method: "PUT",
      params: {
        id: "string|number",
      },
      body: {
        status: "string",
        notes: "string",
      },
    },
    SEARCH: {
      url: "/v2/api/kanglink/custom/trainer/athletes/search",
      method: "GET",
      query: {
        q: "string",
        program: "string",
        status: "string",
      },
    },
  },

  // Dashboard & Analytics Endpoints
  TRAINER_DASHBOARD: {
    GET_STATS: {
      url: "/v2/api/kanglink/custom/trainer/dashboard/stats",
      method: "GET",
    },
    GET_ACTIVITIES: {
      url: "/v2/api/kanglink/custom/trainer/dashboard/activities",
      method: "GET",
      query: {
        page: "number",
        limit: "number",
      },
    },
  },

  // Feed & Social Endpoints
  TRAINER_FEED: {
    GET_POSTS: {
      url: "/v2/api/kanglink/custom/trainer/feed",
      method: "GET",
      query: {
        page: "number",
        limit: "number",
      },
    },
    CREATE_POST: {
      url: "/v2/api/kanglink/custom/trainer/feed",
      method: "POST",
      body: {
        content: "string",
        isPrivate: "boolean",
        attachments: "array",
      },
    },
    LIKE_POST: {
      url: "/v2/api/kanglink/custom/trainer/feed/{id}/like",
      method: "POST",
      params: {
        id: "string|number",
      },
    },
    ADD_COMMENT: {
      url: "/v2/api/kanglink/custom/trainer/feed/{id}/comments",
      method: "POST",
      params: {
        id: "string|number",
      },
      body: {
        content: "string",
      },
    },
  },

  // Financial & Transaction Endpoints
  TRAINER_TRANSACTIONS: {
    GET_STATS: {
      url: "/v2/api/kanglink/custom/trainer/transactions/stats",
      method: "GET",
    },
    GET_HISTORY: {
      url: "/v2/api/kanglink/custom/trainer/transactions",
      method: "GET",
      query: {
        page: "number",
        limit: "number",
        startDate: "string",
        endDate: "string",
      },
    },
    GET_PAYMENT_METHODS: {
      url: "/v2/api/kanglink/custom/trainer/payment-methods",
      method: "GET",
    },
    ADD_PAYMENT_METHOD: {
      url: "/v2/api/kanglink/custom/trainer/payment-methods",
      method: "POST",
      body: {
        type: "string",
        cardNumber: "string",
        expiryDate: "string",
        cvv: "string",
        holderName: "string",
      },
    },
    REQUEST_WITHDRAWAL: {
      url: "/v2/api/kanglink/custom/trainer/withdrawals",
      method: "POST",
      body: {
        amount: "number",
        paymentMethodId: "string",
      },
    },
  },

  // Discount & Pricing Endpoints
  TRAINER_DISCOUNTS: {
    GET: {
      url: "/v2/api/kanglink/custom/trainer/discounts",
      method: "GET",
    },
    UPDATE: {
      url: "/v2/api/kanglink/custom/trainer/discounts",
      method: "PUT",
      body: {
        affiliateLink: "string",
        promoCode: "object",
        saleDiscount: "object",
        subscriptionDiscounts: "array",
        fullPriceDiscounts: "array",
      },
    },
    CREATE_PROMO_CODE: {
      url: "/v2/api/kanglink/custom/trainer/promo-codes",
      method: "POST",
      body: {
        code: "string",
        discountType: "string",
        discountValue: "number",
        appliesTo: "string",
        expiryDate: "string",
      },
    },
  },

  // File Upload Endpoints
  TRAINER_UPLOADS: {
    VIDEO: {
      url: "/v2/api/kanglink/custom/trainer/upload/video",
      method: "POST",
      body: "FormData with video file",
    },
    IMAGE: {
      url: "/v2/api/kanglink/custom/trainer/upload/image",
      method: "POST",
      body: "FormData with image file",
    },
  },

  // Notification Endpoints
  TRAINER_NOTIFICATIONS: {
    GET: {
      url: "/v2/api/kanglink/custom/trainer/notifications",
      method: "GET",
      query: {
        page: "number",
        limit: "number",
        unread: "boolean",
      },
    },
    MARK_READ: {
      url: "/v2/api/kanglink/custom/trainer/notifications/{id}/read",
      method: "PUT",
      params: {
        id: "string|number",
      },
    },
  },
};
