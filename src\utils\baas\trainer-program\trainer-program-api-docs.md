# Trainer Program Management API Documentation

## Overview

This document provides comprehensive API specifications for the trainer program creation and management system. The system supports a multi-step program creation process with draft/publish functionality, exercise library integration, and video management.

## Base URL

```
https://api.kanglink.com/v2/api/kanglink/custom/trainer
```

## Authentication

All endpoints require Bearer token authentication:

```
Authorization: Bearer {jwt_token}
```

---

## Program Creation Flow

The program creation process involves three main steps:

1. **Step One**: Basic program information, payment plans, settings, target levels, splits, and pricing
2. **Step Two**: Program structure with weeks, days, sessions, and exercises
3. **Preview**: Final review before saving as draft or publishing

---

## API Endpoints

### 1. Create Program (Save as Draft)

**Endpoint:** `POST /programs/draft`

**Purpose:** Save a program as draft during the creation process

**Request Body:**

```json
{
  "stepOneData": {
    "programName": "Advanced Fitness Program",
    "typeOfProgram": "Body building",
    "programDescription": "A comprehensive fitness program for advanced users",
    "paymentPlan": ["oneTime", "monthly"],
    "trackProgress": true,
    "allowComments": true,
    "allowPrivateMessages": false,
    "targetLevels": ["intermediate", "expert"],
    "splitProgram": 2,
    "splits": [
      {
        "split_id": "split-1",
        "title": "Upper Body",
        "fullPrice": 99.99,
        "subscription": 19.99
      },
      {
        "split_id": "split-2",
        "title": "Lower Body",
        "fullPrice": 89.99,
        "subscription": 17.99
      }
    ],
    "currency": "USD",
    "daysForPreview": 7
  },
  "stepTwoData": {
    "programSplit": "split-1",
    "description": "Detailed program description",
    "equipmentRequired": "Dumbbells, Resistance bands",
    "splitConfigurations": {
      "split-1": [
        {
          "id": "week-1",
          "name": "Week 1",
          "isCollapsed": false,
          "days": [
            {
              "id": "day-1",
              "name": "Day 1",
              "isRestDay": false,
              "isCollapsed": false,
              "sessions": [
                {
                  "id": "session-1",
                  "name": "Morning Workout",
                  "sessionLetter": "A",
                  "sessionNumber": 1,
                  "isCollapsed": false,
                  "exercises": [
                    {
                      "id": "exercise-1",
                      "name": "Push-ups",
                      "sets": "3",
                      "repsOrTime": "15",
                      "repsTimeType": "reps",
                      "videoUrl": "https://example.com/pushup.mp4",
                      "exerciseDetails": "Keep your core tight",
                      "restDuration": {
                        "minutes": 1,
                        "seconds": 30
                      },
                      "linkedExerciseId": null,
                      "isLinked": false
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    "status": "draft"
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Program saved as draft successfully",
  "data": {
    "programId": 123,
    "status": "draft",
    "createdAt": "2024-01-15T10:30:00Z",
    "validationErrors": null
  }
}
```

---

### 2. Publish Program

**Endpoint:** `POST /programs/publish`

**Purpose:** Publish a completed program

**Request Body:** Same as Create Program Draft, but with `status: "published"`

**Response:**

```json
{
  "success": true,
  "message": "Program published successfully",
  "data": {
    "programId": 123,
    "status": "published",
    "publishedAt": "2024-01-15T10:30:00Z",
    "programUrl": "https://app.kanglink.com/programs/123",
    "validationErrors": null
  }
}
```

---

### 3. Update Existing Program

**Endpoint:** `PUT /programs/{programId}`

**Purpose:** Update an existing program (draft or published)

**URL Parameters:**

- `programId` (number, required) - The ID of the program to update

**Request Body:** Same structure as Create Program

**Response:**

```json
{
  "success": true,
  "message": "Program updated successfully",
  "data": {
    "programId": 123,
    "status": "published",
    "updatedAt": "2024-01-15T10:30:00Z",
    "validationErrors": null
  }
}
```

---

### 4. Get Program by ID

**Endpoint:** `GET /programs/{programId}`

**Purpose:** Retrieve a program for editing

**URL Parameters:**

- `programId` (number, required) - The ID of the program

**Response:**

```json
{
  "success": true,
  "data": {
    "program": {
      "id": 123,
      "trainerId": "trainer_456",
      "programName": "Advanced Fitness Program",
      "typeOfProgram": "Body building",
      "programDescription": "A comprehensive fitness program",
      "paymentPlan": ["oneTime", "monthly"],
      "trackProgress": true,
      "allowComments": true,
      "allowPrivateMessages": false,
      "targetLevels": ["intermediate", "expert"],
      "splitProgram": 2,
      "splits": [...],
      "currency": "USD",
      "daysForPreview": 7,
      "programSplit": "split-1",
      "description": "Detailed program description",
      "equipmentRequired": "Dumbbells, Resistance bands",
      "splitConfigurations": {...},
      "status": "published",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    },
    "lastModified": "2024-01-15T10:30:00Z",
    "canEdit": true
  }
}
```

---

### 5. Get Exercise Library

**Endpoint:** `GET /exercises`

**Purpose:** Retrieve available exercises for program creation

**Query Parameters:**

- `search` (string, optional) - Search term for exercise names
- `category` (string, optional) - Filter by exercise category
- `muscleGroup` (string, optional) - Filter by muscle group
- `page` (number, optional, default: 1) - Page number for pagination
- `limit` (number, optional, default: 50) - Number of items per page

**Response:**

```json
{
  "success": true,
  "data": {
    "exercises": [
      {
        "id": "ex-1",
        "name": "Push-ups",
        "createdBy": "admin",
        "category": "Bodyweight",
        "muscleGroups": ["Chest", "Triceps", "Shoulders"],
        "equipment": ["None"]
      },
      {
        "id": "ex-2",
        "name": "Custom Exercise",
        "createdBy": "trainer",
        "trainerId": "trainer_456",
        "category": "Strength",
        "muscleGroups": ["Back"],
        "equipment": ["Dumbbells"]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 150,
      "totalPages": 3
    }
  }
}
```

---

### 6. Add Custom Exercise

**Endpoint:** `POST /exercises`

**Purpose:** Add a new custom exercise to the library

**Request Body:**

```json
{
  "name": "Custom Squat Variation",
  "category": "Strength",
  "muscleGroups": ["Quadriceps", "Glutes"],
  "equipment": ["Barbell"],
  "saveToLibrary": true
}
```

**Response:**

```json
{
  "success": true,
  "message": "Exercise added successfully",
  "data": {
    "exercise": {
      "id": "ex-new-123",
      "name": "Custom Squat Variation",
      "createdBy": "trainer",
      "trainerId": "trainer_456",
      "category": "Strength",
      "muscleGroups": ["Quadriceps", "Glutes"],
      "equipment": ["Barbell"]
    },
    "savedToLibrary": true
  }
}
```

---

### 7. Get Video Library

**Endpoint:** `GET /videos`

**Purpose:** Retrieve available videos for exercises

**Query Parameters:**

- `search` (string, optional) - Search term for video names
- `page` (number, optional, default: 1) - Page number for pagination
- `limit` (number, optional, default: 50) - Number of items per page

**Response:**

```json
{
  "success": true,
  "data": {
    "videos": [
      {
        "id": "vid-1",
        "name": "Push-up Tutorial",
        "url": "https://example.com/pushup.mp4",
        "createdBy": "admin",
        "duration": 120,
        "thumbnailUrl": "https://example.com/pushup-thumb.jpg"
      },
      {
        "id": "vid-2",
        "name": "Custom Workout Video",
        "url": "https://example.com/custom.mp4",
        "createdBy": "trainer",
        "trainerId": "trainer_456",
        "duration": 180,
        "thumbnailUrl": "https://example.com/custom-thumb.jpg"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 75,
      "totalPages": 2
    }
  }
}
```

---

### 8. Upload Video

**Endpoint:** `POST /videos/upload`

**Purpose:** Upload a video file for exercises

**Request Body:** (multipart/form-data)

- `file` (File) - The video file to upload
- `name` (string) - Name for the video
- `saveToLibrary` (boolean) - Whether to save to trainer's video library

**Response:**

```json
{
  "success": true,
  "message": "Video uploaded successfully",
  "data": {
    "video": {
      "id": "vid-new-123",
      "name": "My Custom Video",
      "url": "https://cdn.kanglink.com/videos/vid-new-123.mp4",
      "createdBy": "trainer",
      "trainerId": "trainer_456",
      "duration": 240,
      "thumbnailUrl": "https://cdn.kanglink.com/thumbnails/vid-new-123.jpg"
    },
    "uploadUrl": "https://cdn.kanglink.com/videos/vid-new-123.mp4",
    "savedToLibrary": true
  }
}
```

---

### 9. Add Video by URL

**Endpoint:** `POST /videos`

**Purpose:** Add a video by providing a URL

**Request Body:**

```json
{
  "name": "External Video Tutorial",
  "url": "https://youtube.com/watch?v=example",
  "saveToLibrary": false
}
```

**Response:**

```json
{
  "success": true,
  "message": "Video added successfully",
  "data": {
    "video": {
      "id": "vid-url-123",
      "name": "External Video Tutorial",
      "url": "https://youtube.com/watch?v=example",
      "createdBy": "trainer",
      "trainerId": "trainer_456"
    },
    "savedToLibrary": false
  }
}
```

---

### 10. Validate Program Data

**Endpoint:** `POST /programs/validate`

**Purpose:** Validate program data before saving or publishing

**Request Body:**

```json
{
  "stepOneData": {
    /* StepOneFormData */
  },
  "stepTwoData": {
    /* StepTwoFormData */
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "isValid": false,
    "errors": [
      "Split 'Upper Body' must have at least one week",
      "Exercise 'Push-ups' in Week 1, Day 1 is missing sets information"
    ],
    "warnings": [
      "Consider adding more exercises to Session A for better workout variety"
    ]
  }
}
```

---

### 11. Get Program Types

**Endpoint:** `GET /program-types`

**Purpose:** Get available program types/categories

**Response:**

```json
{
  "success": true,
  "data": {
    "programTypes": [
      {
        "value": "Body building",
        "label": "Body Building",
        "description": "Muscle building and strength training programs"
      },
      {
        "value": "High Jump",
        "label": "High Jump",
        "description": "Athletic training for high jump performance"
      },
      {
        "value": "Cross fit",
        "label": "CrossFit",
        "description": "High-intensity functional fitness programs"
      }
    ]
  }
}
```

---

### 12. Get Currencies

**Endpoint:** `GET /currencies`

**Purpose:** Get available currencies for program pricing

**Response:**

```json
{
  "success": true,
  "data": {
    "currencies": [
      {
        "code": "USD",
        "name": "US Dollar",
        "symbol": "$"
      },
      {
        "code": "EUR",
        "name": "Euro",
        "symbol": "€"
      },
      {
        "code": "GBP",
        "name": "British Pound",
        "symbol": "£"
      }
    ]
  }
}
```

---

### 13. Delete Program

**Endpoint:** `DELETE /programs/{programId}`

**Purpose:** Delete a program

**URL Parameters:**

- `programId` (number, required) - The ID of the program to delete

**Response:**

```json
{
  "success": true,
  "message": "Program deleted successfully",
  "data": {
    "programId": 123,
    "deletedAt": "2024-01-15T10:30:00Z"
  }
}
```

---

### 14. Duplicate Program

**Endpoint:** `POST /programs/{programId}/duplicate`

**Purpose:** Create a copy of an existing program

**URL Parameters:**

- `programId` (number, required) - The ID of the program to duplicate

**Request Body:**

```json
{
  "newProgramName": "Copy of Advanced Fitness Program",
  "copyPricing": true,
  "copyStructure": true
}
```

**Response:**

```json
{
  "success": true,
  "message": "Program duplicated successfully",
  "data": {
    "originalProgramId": 123,
    "newProgramId": 456,
    "newProgramName": "Copy of Advanced Fitness Program",
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

---

## Error Handling

### Common Error Codes:

- `400` - Bad Request (Invalid input data)
- `401` - Unauthorized (Invalid or missing token)
- `403` - Forbidden (Insufficient permissions)
- `404` - Not Found (Program or resource not found)
- `409` - Conflict (Program name already exists)
- `422` - Unprocessable Entity (Validation errors)
- `500` - Internal Server Error

### Error Response Format:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Program validation failed",
    "details": {
      "programName": "Program name is required",
      "splits": "At least one split must be configured"
    }
  }
}
```

---

## Business Logic Notes

### Program Structure Validation:

- Each program must have at least one split
- Each split must have at least one week
- Each week must have at least one day
- Non-rest days must have at least one session
- Each session must have at least one exercise

### Payment Plan Requirements:

- If "oneTime" is selected, fullPrice must be provided for all splits
- If "monthly" is selected, subscription price must be provided for all splits
- Currency must be a valid ISO currency code

### Exercise and Video Management:

- Exercises can be from admin library or trainer's custom library
- Videos can be uploaded files or external URLs
- Custom exercises/videos can optionally be saved to trainer's library for reuse
