import { useEffect, useMemo, useState } from "react";
import { Container } from "@/components/Container";
import { useContexts } from "@/hooks/useContexts";
import { useProfile } from "@/hooks/useProfile";
import { LazyLoad } from "@/components/LazyLoad";
import {
  TrainerFeedHeader,
  TrainerPostComposer,
  TrainerFeedPost,
  EmptyFeedState,
} from "@/components/TrainerFeed";
import type { FeedPostProps, Program } from "@/components/TrainerFeed/types";
import { useFeedPosts } from "@/hooks/useFeedPosts";
import { PostFeed } from "@/interfaces/model.interface";

const TrainerFeedPage = () => {
  const { globalDispatch } = useContexts();
  const { profile } = useProfile();
  const profileData = {
    ...profile?.data,
    id: profile?.id,
    email: profile?.email,
    role: profile?.role,
    status: profile?.status,
  };

  const [selected_program, setSelectedProgram] = useState<Program | null>(null);
  const [newPostContent, setNewPostContent] = useState("");
  const [postType, setPostType] = useState("Announcement");

  // Use the feed posts hook
  const {
    posts: feedPosts,
    isLoading: isLoadingPosts,
    error: postsError,
    refetch: refetchPosts,
    addPost,
  } = useFeedPosts({
    program_id: selected_program?.id,
    enabled: !!selected_program?.id,
  });

  const handleCreatePost = () => {
    if (newPostContent.trim()) {
      // TODO: Implement post creation logic with selected_program.id
      console.log("Creating post for program:", selected_program?.id);
      setNewPostContent("");
    }
  };

  const handlePostCreated = (new_post: PostFeed) => {
    // Add the new post to the beginning of the feed using the hook
    addPost(new_post);
    console.log("New post created:", new_post);

    // Optionally refetch to get the latest data from server
    refetchPosts();
  };

  // Convert PostFeed to FeedPostProps format
  const convertPostFeedToFeedPost = (
    post: PostFeed
  ): Omit<FeedPostProps, "replies" | "likes" | "comments"> => {
    return {
      id: post.id as number,
      author_name: post.user?.data?.full_name || "Unknown User",
      author_avatar: post.user?.data?.photo || "https://placehold.co/48x48",
      post_type: post.post_type || "update",
      content: post.content || "",
      timestamp: post.created_at
        ? new Date(post.created_at).toLocaleString()
        : "Unknown time",
      is_private: post.is_private || false,
      author_id: post.user_id,
      can_delete: profile?.id === post.user_id,
    };
  };

  // Filter and convert posts based on selected program, useMemo
  const filteredPosts = useMemo(() => {
    return selected_program ? feedPosts.map(convertPostFeedToFeedPost) : [];
  }, [selected_program, feedPosts]);

  // Set the path in global state for navigation highlighting
  useEffect(() => {
    refetchPosts();
  }, [selected_program?.id]);

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "feed",
      },
    });
  }, [globalDispatch]);

  return (
    <>
      <Container>
        <div className="p-[1.5rem] space-y-5 w-full mx-auto">
          {/* Header */}
          <LazyLoad>
            <TrainerFeedHeader
              title="Feed"
              subtitle='Welcome to "Selected Program"'
              selected_program={selected_program}
              on_program_select={setSelectedProgram}
            />
          </LazyLoad>

          {/* Create Post Section */}
          {selected_program && (
            <LazyLoad>
              <TrainerPostComposer
                content={newPostContent}
                on_content_change={setNewPostContent}
                profile={profileData}
                on_submit={handleCreatePost}
                post_type={postType}
                on_post_type_change={setPostType}
                selected_program={selected_program}
                on_post_created={handlePostCreated}
              />
            </LazyLoad>
          )}

          {/* Feed Posts */}
          <section className="space-y-8">
            {!selected_program ? (
              <LazyLoad>
                <EmptyFeedState
                  title="No Program Selected"
                  description="Please select a program from the dropdown above to view its feed and posts."
                />
              </LazyLoad>
            ) : isLoadingPosts ? (
              <LazyLoad>
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <span className="ml-2 text-text-disabled">
                    Loading posts...
                  </span>
                </div>
              </LazyLoad>
            ) : postsError ? (
              <LazyLoad>
                <EmptyFeedState
                  title="Error Loading Posts"
                  description={postsError}
                />
              </LazyLoad>
            ) : filteredPosts.length > 0 ? (
              filteredPosts.map((post) => (
                <LazyLoad key={post.id}>
                  <TrainerFeedPost {...post} />
                </LazyLoad>
              ))
            ) : (
              <LazyLoad>
                <EmptyFeedState
                  title="No Posts Yet"
                  description={`No posts found for "${selected_program.program_name}". Create the first post to get started!`}
                />
              </LazyLoad>
            )}
          </section>
        </div>
      </Container>
    </>
  );
};

export default TrainerFeedPage;
