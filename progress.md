# General Preferences

- User prefers implementations to include full screen responsiveness and both light and dark mode support.
- User emphasized that button styling must match the team's design system and theme requirements, suggesting there are specific design standards to follow.
- User prefers breaking down complex components into smaller, reusable components for better maintainability and code organization.
- User prefers breaking down components in CreateProgramStepTwo into smaller, manageable files for better maintainability.
- User prefers breaking down components in CreateProgramStepOne into smaller, manageable files for better maintainability.

# UI Element Preferences

- User prefers Apply Filter buttons with specific styling: width 7.49613rem, height 2.625rem, specific padding values, white background with #D9DCE0 border for light mode, and requests corresponding dark mode styling.
- For trainer profile pages, UI should match design files perfectly and be responsive on all screens with theme support.
- For trainer program pages, UI should match design files perfectly and be responsive on all screens with theme support.
- For trainer program interfaces: weeks/days/sessions should be collapsible, multiple sessions per day are allowed, sessions can be linked (A+B becomes A1+A2), and components should use full width layout.
- For wizard forms, each step component should have its own continue button that advances to the next step internally, with the final step having a submit button for the entire form.
- For trainer program sessions: multiple sessions per day with multiple exercises per session, exercises (not sessions) can be linked with link icon on left, linked exercises show joined lines, action buttons appear after every session, link connector styling needs refinement, and 'Add Exercise' button should appear between two unlinked exercises rather than at the end.
- For trainer program exercises: labels should start from A-Z, linked exercises should use same letter with numbers (A1, A2, A3), and all exercise labels must maintain consistency after linking/unlinking operations.
- Payment plans should use checkboxes (not radio buttons) to allow selecting one or both options, with conditional form inputs: one-time payment shows full price only, monthly subscription shows subscription price only, and both selected shows both price inputs in split details.

# Form Validation and Requirements

- For program creation forms: require program name/type/description, validate character limits, enforce at least one target level selection, allow optional split titles, validate positive pricing, limit preview days to 7 max, enforce single payment method selection, and implement comprehensive form validation with disabled proceed button until all required fields are valid.
- For program creation forms: number of splits in step one should create equivalent number of title/price sections, and step two should have dropdown to select/configure individual splits defined in step one.
- For program creation forms, validate that each split has at least one week, one day, one session, and one exercise before allowing submission.

# Data Handling

- Exercise and video dropdowns should be populated from database with both trainer-created and admin-created content, and when users add new exercises/videos with save checked, they should be added to database and update the dropdown list.

# Navigation

- Navigation path should be added to global state following the pattern used in ListTrainerProgramPage.tsx and TrainerHeader.tsx components.
- For trainer profile pages, navigation path should be added to global state following the pattern used in ViewTrainerFeedPage.tsx and TrainerHeader.tsx components.

# Wizard Forms Specifics

- For wizard forms, data should be preserved when navigating between steps (like CreateProgramStepTwo to CreateProgramPreview and back) using initialData patterns.
- For wizard forms like CreateProgramStepTwo: action buttons should work relative to selected split, and description fields should auto-populate from previous steps.
- For CreateProgramStepTwo exercises, plus button should open modal allowing users to add new exercises from admin-created library or upload their own videos.
- For wizard forms like CreateProgramStepTwo: should be the saving step with 'Save as Draft' and 'Publish' buttons, CreateProgramPreview should be final preview step with 'Back' (returns to step two) and 'Done' (returns to previous page) buttons.
  `

# Guides

---

description:
globs:
alwaysApply: true

---

# These Rules outline the main architectural flow for this project

## Base Rules

## Styling

- Always use Tailwind classes for styles

## Core Components

Use these pre-built components instead of creating new ones:

### Form Components

- [InteractiveButton.tsx](mdc:src/components/InteractiveButton/InteractiveButton.tsx) - For all button implementations
- [MkdInputV2.tsx](mdc:src/components/MkdInputV2/MkdInputV2.tsx) - For all input fields including custom types like mapping dropdown
- [MkdPasswordInput.tsx](mdc:src/components/MkdPasswordInput/MkdPasswordInput.tsx) - For password fields with toggle view
- [MkdTabContainer.tsx](mdc:src/components/MkdTabContainer/MkdTabContainer.tsx) - For tabbed interfaces
- [MkdWizardContainer.tsx](mdc:src/components/MkdWizardContainer/MkdWizardContainer.tsx) - For wizard/multi-step forms

### Layout Components

- @ViewWrapper.tsx - For page layouts
- @SimpleViewWrapper.tsx - For simple page layouts
- [Container.tsx](mdc:src/components/Container/Container.tsx) - For all page wrap - this should be should inside the page as the main parent

### Page Route Wrapper

#### Used only in the routes to wrap each page [Routes.tsx](mdc:src/routes/Routes.tsx), should not be used direct in th page

- [AdminWrapper.tsx](mdc:src/components/AdminWrapper/AdminWrapper.tsx) - For admin page layouts
- [PublicWrapper.tsx](mdc:src/components/PublicWrapper/PublicWrapper.tsx) - For public page layouts
- other in the format [Role]Wrapper.tsx could exist for other role wrappers

### Data Display Components

- [V3Wrapper.tsx](mdc:src/components/MkdListTable/V3Wrapper/V3Wrapper.tsx) - For data tables - it implements [MkdListTable.v3.tsx](mdc:src/components/MkdListTable/V3/MkdListTable.v3.tsx)
- [MkdSimpleTable.tsx](mdc:src/components/MkdSimpleTable/MkdSimpleTable.tsx) - For simple data tables
- [MkdFileTable.tsx](mdc:src/components/MkdFileTable/MkdFileTable.tsx) - For file uploading and parsing CSV or exel file and show the list
- [PaginationBar.tsx](mdc:src/components/PaginationBar/PaginationBar.tsx) - For pagination

### UI Components

- [Modal.tsx](mdc:src/components/Modal/Modal.tsx) - For modal dialogs
- [ModalSidebar.tsx](mdc:src/components/ModalSidebar/ModalSidebar.tsx) - For side panels
- [ActionConfirmationModal.tsx](mdc:src/components/ActionConfirmationModal/ActionConfirmationModal.tsx) for confirmation dialogs
- [SnackBar.tsx](mdc:src/components/SnackBar/SnackBar.tsx) - For notifications
- [MkdLoader.tsx](mdc:src/components/MkdLoader/MkdLoader.tsx) - For loading states
- [Skeleton.tsx](mdc:src/components/Skeleton/Skeleton.tsx) - For loading placeholders

- use [ThemeStyles.tsx](mdc:src/components/ThemeStyles/ThemeStyles.tsx), [ThemeConstants.ts](mdc:src/context/Theme/ThemeConstants.ts), [tailwind.config.ts](mdc:tailwind.config.ts) for tailwind classes that map to the theme tokens
- use [useTheme.tsx](mdc:src/hooks/useTheme/useTheme.tsx) if classes can not be used

## Project Structure

### Pages

- Create pages in `src/pages` following the structure:
  ```
  pages/
    [Role]/
      [Add|Edit|View|Auth|List|Custom]/
        **.tsx
  ```
- All pages must be exported via [LazyLoad.ts](mdc:src/routes/LazyLoad.ts) file and imported into [Routes.tsx](mdc:src/routes/Routes.tsx)
- Use appropriate wrapper components based on page type (AdminWrapper, PublicWrapper, etc.)

### Hooks

- Create hooks in `src/hooks/[hookName]` with:
  - `index.ts` - Export the hook
  - `[hookName].tsx` - Hook implementation
- Available core hooks:
  - `useContexts` - For auth and global context
  - `useProfile` - For user profile management
  - `useSDK` - For direct SDK interactions

### Queries

- Use the query system in `src/query` for data fetching and mutations
- Follow the existing query structure and patterns

### Components

- All components must be wrapped with `LazyLoad`
- Export components from their respective `index.ts` files
- Follow the component structure:
  ```
  components/
    [ComponentName]/
      index.ts
      [ComponentName].tsx
      [ComponentName].types.ts (if needed)
  ```

### TypeScript

- Use interfaces from `src/interfaces`
- Use enums from `src/utils/Enums`
- Properly type all components, hooks, and functions

## Form Handling

- Use react-hook-form with yup and yupResolver
- Integrate with [MkdInputV2.tsx](mdc:src/components/MkdInputV2/MkdInputV2.tsx) components
- Follow form validation patterns

## Code Organization

- Keep UI code separate from functionality using hooks
- Create reusable components for repeated patterns
- Break down complex components into smaller, manageable pieces
- Use proper TypeScript types and interfaces

- Follow the established project structure and patterns
