import React from "react";
import { useTheme } from "@/hooks/useTheme";
import { ChevronDown } from "lucide-react";
import { Split } from "@/interfaces/model.interface";

interface DiscountItem {
  tier_id: number | string;
  discount_type: "fixed" | "percentage";
  discount_value: number;
}

interface DiscountOnSubscriptionSectionProps {
  discounts?: DiscountItem[];
  splits?: Split[];
  onChange?: (discounts: DiscountItem[]) => void;
}

const DiscountOnSubscriptionSection = ({
  discounts = [],
  splits = [],
  onChange,
}: DiscountOnSubscriptionSectionProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const updateDiscount = (
    tier_id: number | string,
    field: keyof DiscountItem,
    value: any
  ) => {
    const updatedDiscounts = discounts.map((discount) =>
      discount.tier_id === tier_id ? { ...discount, [field]: value } : discount
    );

    // If discount doesn't exist, create it
    if (!discounts.find((d) => d.tier_id === tier_id)) {
      updatedDiscounts.push({
        tier_id,
        discount_type: field === "discount_type" ? value : "percentage",
        discount_value: field === "discount_value" ? value : 0,
      });
    }

    onChange?.(updatedDiscounts);
  };

  return (
    <div className="bg-background border border-border rounded-md p-6 shadow-sm">
      <div className="space-y-4">
        <h3 className="text-sm font-medium text-text">
          Discount On Subscription
        </h3>

        {splits.length === 0 ? (
          <p className="text-sm text-text-secondary">
            No subscription splits available
          </p>
        ) : (
          <div className="space-y-3">
            {splits.map((split) => {
              const discount = discounts.find((d) => d.tier_id === split.id);
              return (
                <div
                  key={split.id}
                  className="flex items-center gap-4 p-3 border border-border rounded-md"
                >
                  <div className="flex-1">
                    <p className="text-sm font-medium text-text">
                      {split.title}
                    </p>
                    <p className="text-xs text-text-secondary">
                      Original: ${split.subscription}
                    </p>
                  </div>

                  <div className="flex items-center gap-2">
                    <select
                      value={discount?.discount_type || "percentage"}
                      onChange={(e) =>
                        updateDiscount(
                          split.id!,
                          "discount_type",
                          e.target.value
                        )
                      }
                      className="px-2 bg-none py-1 text-sm border border-border rounded bg-input text-text"
                    >
                      <option value="percentage">%</option>
                      <option value="fixed">$</option>
                    </select>

                    <input
                      type="number"
                      value={discount?.discount_value || 0}
                      onChange={(e) =>
                        updateDiscount(
                          split.id!,
                          "discount_value",
                          Number(e.target.value)
                        )
                      }
                      className="w-20 px-2 py-1 text-sm border border-border rounded bg-input text-text"
                      placeholder="0"
                    />
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default DiscountOnSubscriptionSection;
