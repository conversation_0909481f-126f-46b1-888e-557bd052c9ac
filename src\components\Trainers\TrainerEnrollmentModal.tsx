import React, { useMemo } from "react";
import { Modal } from "@/components/Modal/Modal";
import { useGetListQuery } from "@/query/shared/listModel";
import { Models } from "@/utils/baas/models";
import { Enrollment } from "@/interfaces/model.interface";
import {
  User,
  Calendar,
  CreditCard,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  BookOpen,
  Target,
} from "lucide-react";
import { useSDK } from "@/hooks/useSDK";

interface TrainerEnrollmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  trainerId: number;
  trainerName: string;
}

interface EnrollmentWithDetails extends Enrollment {
  athlete?: {
    id: number;
    full_name: string;
    email: string;
    photo?: string;
  };
  trainer?: {
    id: number;
    full_name: string;
    email: string;
    photo?: string;
  };
  program?: {
    id: number;
    program_name: string;
    type_of_program: string;
    program_description?: string;
    image?: string;
  };
  split?: {
    id: number;
    title: string;
    full_price: number;
    subscription: number;
  };
}

const TrainerEnrollmentModal: React.FC<TrainerEnrollmentModalProps> = ({
  isOpen,
  onClose,
  trainerId,
  trainerName,
}) => {
  // Build query options for fetching enrollments with program, split, athlete, and trainer details
  const { projectId } = useSDK();

  const queryOptions = useMemo(() => {
    if (!trainerId) return undefined;

    return {
      filter: [
        `trainer_id,eq,${trainerId}`,
        `${projectId}_enrollment.status,eq,active`,
        `${projectId}_enrollment.payment_status,eq,paid`,
      ],
      join: ["program", "split|split_id", "user|athlete_id"],
    };
  }, [trainerId]);

  // Fetch enrollment details using useGetListQuery
  const {
    data: enrollmentData,
    isLoading: loading,
    error,
  } = useGetListQuery(Models.ENROLLMENT, queryOptions, undefined, {
    enabled: isOpen && !!queryOptions,
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });

  // Process and enrich enrollment data
  const enrichedEnrollments: EnrollmentWithDetails[] = useMemo(() => {
    if (!enrollmentData?.data) return [];

    return enrollmentData.data.map((enrollment: any) => {
      // Handle joined data - the API might return joined data differently
      const athleteData = enrollment.athlete || enrollment.user;
      const trainerData = enrollment.trainer;

      const athleteInfo = athleteData
        ? {
            id: athleteData.id as number,
            full_name:
              JSON.parse(athleteData.data || "{}")?.full_name ||
              `${athleteData.first_name || ""} ${athleteData.last_name || ""}`.trim() ||
              "Unknown Athlete",
            email: athleteData.email || "<EMAIL>",
            photo: athleteData.photo,
          }
        : {
            id: enrollment.athlete_id as number,
            full_name: "Unknown Athlete",
            email: "<EMAIL>",
          };

      const trainerInfo = trainerData
        ? {
            id: trainerData.id as number,
            full_name:
              JSON.parse(trainerData.data || "{}")?.full_name ||
              `${trainerData.first_name || ""} ${trainerData.last_name || ""}`.trim() ||
              "Unknown Trainer",
            email: trainerData.email || "<EMAIL>",
            photo: trainerData.photo,
          }
        : {
            id: enrollment.trainer_id as number,
            full_name: "Unknown Trainer",
            email: "<EMAIL>",
          };

      const programInfo = enrollment.program
        ? {
            id: enrollment.program.id as number,
            program_name: enrollment.program.program_name || "Unknown Program",
            type_of_program:
              enrollment.program.type_of_program || "Unknown Type",
            program_description: enrollment.program.program_description,
            image: enrollment.program.image,
          }
        : {
            id: enrollment.program_id as number,
            program_name: "Unknown Program",
            type_of_program: "Unknown Type",
          };

      const splitInfo = enrollment.split
        ? {
            id: enrollment.split.id as number,
            title: enrollment.split.title || "Unknown Split",
            full_price: enrollment.split.full_price || 0,
            subscription: enrollment.split.subscription || 0,
          }
        : {
            id: enrollment.split_id as number,
            title: "Unknown Split",
            full_price: 0,
            subscription: 0,
          };

      return {
        ...enrollment,
        athlete: athleteInfo,
        trainer: trainerInfo,
        program: programInfo,
        split: splitInfo,
      } as EnrollmentWithDetails;
    });
  }, [enrollmentData]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case "expired":
        return <XCircle className="w-5 h-5 text-red-500" />;
      case "cancelled":
        return <XCircle className="w-5 h-5 text-gray-500" />;
      case "pending":
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "text-green-600 bg-green-50 border-green-200";
      case "pending":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "failed":
        return "text-red-600 bg-red-50 border-red-200";
      case "refunded":
        return "text-gray-600 bg-gray-50 border-gray-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch {
      return "Invalid Date";
    }
  };

  const formatCurrency = (amount: number, currency: string = "USD") => {
    if (typeof amount !== "number") return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      modalHeader={true}
      title={`${trainerName}'s Enrollments`}
      classes={{
        modal: "h-full w-full",
        modalDialog: "h-[90%] max-w-5xl w-full",
        modalContent: "max-h-[calc(90vh-120px)] w-full overflow-y-auto",
      }}
    >
      <div className="space-y-6">
        {loading && (
          <div className="flex items-center justify-center py-8">
            <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-3 text-text">Loading enrollments...</span>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">
              {error?.message || "Failed to load enrollment details"}
            </p>
          </div>
        )}

        {!loading && !error && enrichedEnrollments.length === 0 && (
          <div className="text-center py-8">
            <BookOpen className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <p className="text-text-secondary">
              No enrollments found for this trainer.
            </p>
          </div>
        )}

        {!loading && !error && enrichedEnrollments.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-text">
                Enrollment Details ({enrichedEnrollments.length})
              </h3>
            </div>

            <div className="grid gap-6">
              {enrichedEnrollments.map((enrollment) => (
                <div
                  key={enrollment.id}
                  className="bg-background border border-border rounded-lg p-6 hover:shadow-md transition-shadow duration-200"
                >
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Program Information */}
                    <div className="lg:col-span-2 space-y-4">
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                          <BookOpen className="w-6 h-6 text-primary" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-text text-lg">
                            {enrollment.program?.program_name ||
                              "Unknown Program"}
                          </h4>
                          <p className="text-sm text-text-secondary mb-2">
                            {enrollment.program?.type_of_program}
                          </p>
                          {enrollment.program?.program_description && (
                            <p className="text-sm text-text-secondary line-clamp-2">
                              {enrollment.program.program_description}
                            </p>
                          )}
                        </div>
                      </div>

                      {/* Athlete Information */}
                      <div className="flex items-center space-x-3 p-3 bg-background-secondary rounded-lg">
                        <User className="w-5 h-5 text-text-secondary" />
                        <div>
                          <p className="font-medium text-text">
                            {enrollment.athlete?.full_name}
                          </p>
                          <p className="text-sm text-text-secondary">
                            {enrollment.athlete?.email}
                          </p>
                        </div>
                      </div>

                      {/* Split Information */}
                      <div className="flex items-center space-x-3 p-3 bg-background-secondary rounded-lg">
                        <Target className="w-5 h-5 text-text-secondary" />
                        <div>
                          <p className="font-medium text-text">
                            {enrollment.split?.title}
                          </p>
                          <p className="text-sm text-text-secondary">
                            Full Price:{" "}
                            {formatCurrency(enrollment.split?.full_price || 0)}{" "}
                            | Subscription:{" "}
                            {formatCurrency(
                              enrollment.split?.subscription || 0
                            )}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Enrollment Details */}
                    <div className="space-y-4">
                      {/* Status */}
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(enrollment.status || "pending")}
                        <span className="font-medium text-text capitalize">
                          {enrollment.status || "pending"}
                        </span>
                      </div>

                      {/* Payment Information */}
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <CreditCard className="w-4 h-4 text-text-secondary" />
                          <span className="text-sm text-text">
                            {formatCurrency(
                              enrollment.amount || 0,
                              enrollment.currency
                            )}
                          </span>
                        </div>
                        <div
                          className={`inline-flex px-2 py-1 rounded-full text-xs font-medium border ${getPaymentStatusColor(enrollment.payment_status || "pending")}`}
                        >
                          {enrollment.payment_status || "pending"}
                        </div>
                      </div>

                      {/* Dates */}
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Calendar className="w-4 h-4 text-text-secondary" />
                          <div className="text-sm">
                            <p className="text-text">
                              Enrolled:{" "}
                              {formatDate(enrollment.enrollment_date || "")}
                            </p>
                            {enrollment.expiry_date && (
                              <p className="text-text-secondary">
                                Expires: {formatDate(enrollment.expiry_date)}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Payment Type */}
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4 text-text-secondary" />
                        <span className="text-sm text-text capitalize">
                          {enrollment.payment_type || "one_time"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default TrainerEnrollmentModal;
