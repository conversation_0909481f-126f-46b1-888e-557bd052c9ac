import React, { useState, useEffect } from "react";
import { CardElement, useStripe, useElements } from "@stripe/react-stripe-js";
import { MkdButton } from "@/components/MkdButton";
import {
  useEnrollment,
  SplitPricing,
  PaymentAuthenticationResponse,
  DiscountPreviewResponse,
} from "@/hooks/useEnrollment";
import { useCustomerCards, StripeCard } from "@/hooks/useCustomerCards";
import { useToast } from "@/hooks/useToast";
import { useNavigate } from "react-router-dom";
import { TransformedProgramData } from "@/interfaces";

// Initialize Stripe (you'll need to add your publishable key)
// const stripePromise = loadStripe(
//   process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || "pk_test_your_stripe_key"
// );

interface StripePaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  splitId: number | string;
  programId: number | string;
  pricing: SplitPricing;
  paymentType: "subscription" | "one_time";
  existingCard?: StripeCard | null;
  forceNewCard?: boolean;
  affiliateCode?: string | null;
  onBackToCardSelection?: () => void;
  programData?: TransformedProgramData | null;
  initialCouponCode?: string;
  // Centralized discount preview props
  discountPreview?: DiscountPreviewResponse["data"] | null;
  isLoadingDiscount?: boolean;
  couponError?: string | null;
  onCouponCodeChange?: (code: string) => void;
}

const PaymentForm: React.FC<{
  splitId: number | string;
  programId: number | string;
  pricing: SplitPricing;
  paymentType: "subscription" | "one_time";
  existingCard?: StripeCard | null;
  forceNewCard?: boolean;
  affiliateCode?: string | null;
  onSuccess: (enrollmentId: number | string) => void;
  onError: (error: string) => void;
  onBackToCardSelection?: () => void;
  programData?: TransformedProgramData | null;
  initialCouponCode?: string;
  // Centralized discount preview props
  discountPreview?: DiscountPreviewResponse["data"] | null;
  isLoadingDiscount?: boolean;
  couponError?: string | null;
  onCouponCodeChange?: (code: string) => void;
}> = ({
  splitId,
  programId: _programId,
  pricing,
  paymentType,
  existingCard,
  forceNewCard,
  affiliateCode,
  onSuccess,
  onError,
  onBackToCardSelection,
  programData: _programData,
  initialCouponCode,
  // Centralized discount preview props
  discountPreview,
  isLoadingDiscount = false,
  couponError,
  onCouponCodeChange,
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [authenticationMessage, setAuthenticationMessage] = useState("");
  const [couponCode, setCouponCode] = useState(initialCouponCode || "");
  const enrollmentHooks = useEnrollment();
  const customerCardHooks = useCustomerCards();

  const createEnrollment = enrollmentHooks.useCreateEnrollment();
  const checkEnrollmentStatus = enrollmentHooks.useCheckEnrollmentStatus();
  const attachPaymentMethod = customerCardHooks.useAttachPaymentMethod();

  // Note: Coupon input is now always shown for maximum flexibility

  // Handle coupon code input changes (now delegated to parent)
  const handleCouponCodeChange = (code: string) => {
    setCouponCode(code);
    if (onCouponCodeChange) {
      onCouponCodeChange(code);
    }
  };

  // Handle initial coupon code if provided
  useEffect(() => {
    if (initialCouponCode && initialCouponCode.trim()) {
      setCouponCode(initialCouponCode.trim());
    }
  }, [initialCouponCode]);

  // Handle payment authentication
  const handlePaymentAuthentication = async (
    authResponse: PaymentAuthenticationResponse
  ) => {
    if (!stripe) {
      throw new Error("Stripe not initialized");
    }

    setIsAuthenticating(true);
    setAuthenticationMessage("Authenticating payment...");

    try {
      // Use Stripe.js to handle authentication
      const { error, paymentIntent } = await stripe.confirmCardPayment(
        authResponse.payment_intent.client_secret
      );

      if (error) {
        throw new Error(`Authentication failed: ${error.message}`);
      }

      if (paymentIntent.status === "succeeded") {
        setAuthenticationMessage("Completing enrollment...");

        // Wait for webhook processing if subscription_id is provided
        if (authResponse.subscription_id) {
          await waitForEnrollmentActivation(authResponse.subscription_id);
        }

        // Redirect to program page
        navigate(`/athlete/library`);
        // onSuccess();
      }
    } catch (error: any) {
      onError(error.message || "Authentication error occurred");
    } finally {
      setIsAuthenticating(false);
      setAuthenticationMessage("");
    }
  };

  // Wait for enrollment activation after authentication
  const waitForEnrollmentActivation = async (
    subscriptionId: string,
    maxAttempts = 10
  ) => {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      await new Promise((resolve) => setTimeout(resolve, 2000));

      try {
        const result = await checkEnrollmentStatus.mutateAsync(subscriptionId);

        if (result.status === "active") {
          return true;
        } else if (result.status === "payment_failed") {
          throw new Error("Payment failed after authentication");
        }
      } catch (_error) {
        // Continue polling on error, but throw on last attempt
        if (attempt === maxAttempts - 1) {
          throw new Error("Enrollment activation timeout");
        }
      }
    }
    throw new Error("Enrollment activation timeout");
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    try {
      let paymentMethodId: string;

      // If using existing card, use its ID directly
      if (existingCard && !forceNewCard) {
        paymentMethodId = existingCard.id;
      } else {
        // Create new payment method from card element
        const cardElement = elements.getElement(CardElement);
        if (!cardElement) {
          throw new Error("Card element not found");
        }

        const { error: pmError, paymentMethod } =
          await stripe.createPaymentMethod({
            type: "card",
            card: cardElement,
          });

        if (pmError) {
          throw new Error(pmError.message);
        }

        paymentMethodId = paymentMethod.id;

        // For new PaymentMethods, attach them to the customer first
        // This is required by the backend before using the PaymentMethod
        try {
          await attachPaymentMethod.mutateAsync({
            payment_method_id: paymentMethod.id,
          });
        } catch (attachError: any) {
          throw new Error(
            `Failed to attach payment method: ${attachError.message}`
          );
        }
      }

      // Create enrollment with payment method, affiliate code, and coupon code
      const enrollmentData: any = {
        split_id: splitId,
        payment_type: paymentType,
        payment_method_id: paymentMethodId,
      };

      if (affiliateCode) {
        enrollmentData.affiliate_code = affiliateCode;
      }

      if (couponCode.trim()) {
        enrollmentData.coupon_code = couponCode.trim();
      }

      const result = await createEnrollment.mutateAsync(enrollmentData);

      // Check if authentication is required
      if ((result as any).requires_action) {
        await handlePaymentAuthentication(
          result as PaymentAuthenticationResponse
        );
      } else {
        // Successful enrollment without authentication
        onSuccess(result?.data?.enrollment_id);
      }
    } catch (error: any) {
      // Handle specific error messages for affiliate and coupon codes
      let errorMessage = error.message || "Payment failed";

      if (errorMessage.includes("affiliate code")) {
        errorMessage = "Invalid affiliate code. Please check and try again.";
      } else if (errorMessage.includes("coupon")) {
        errorMessage =
          "Invalid or expired coupon code. Please check and try again.";
      }

      onError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const originalAmount = pricing.pricing[paymentType].amount;
  const finalAmount = discountPreview
    ? discountPreview.final_amount
    : originalAmount;
  const description = pricing.pricing[paymentType].description;

  // Debug logging (removed to prevent re-render issues)
  // console.log("Debug - Discount Preview:", {
  //   discountPreview,
  //   couponCode,
  //   couponError,
  //   originalAmount,
  //   finalAmount,
  //   isLoadingDiscount,
  // });

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-text mb-2">
          {paymentType === "subscription"
            ? "Monthly Subscription"
            : "One-time Purchase"}
        </h3>
        <p className="text-sm text-text-secondary mb-4">{description}</p>

        {/* Debug Info - Removed to prevent re-renders */}

        {/* Pricing Display */}
        <div className="mb-4">
          {discountPreview &&
          discountPreview.total_discount_amount > 0 &&
          !couponError ? (
            <div>
              <div className="text-lg text-text-secondary line-through">
                ${originalAmount} {pricing.currency}
                {paymentType === "subscription" && (
                  <span className="text-sm font-normal">/month</span>
                )}
              </div>
              <div className="text-2xl font-bold text-primary">
                ${finalAmount} {pricing.currency}
                {paymentType === "subscription" && (
                  <span className="text-sm font-normal">/month</span>
                )}
              </div>
              <div className="text-sm text-green-600 dark:text-green-400">
                You save ${discountPreview.total_discount_amount}!
              </div>
            </div>
          ) : discountPreview &&
            discountPreview.total_discount_amount > 0 &&
            couponError ? (
            <div>
              <div className="text-lg text-text-secondary line-through">
                ${originalAmount} {pricing.currency}
                {paymentType === "subscription" && (
                  <span className="text-sm font-normal">/month</span>
                )}
              </div>
              <div className="text-2xl font-bold text-primary">
                ${finalAmount} {pricing.currency}
                {paymentType === "subscription" && (
                  <span className="text-sm font-normal">/month</span>
                )}
              </div>
              <div className="text-sm text-green-600 dark:text-green-400">
                You save ${discountPreview.total_discount_amount}! (from other
                discounts)
              </div>
              <div className="text-sm text-amber-600 dark:text-amber-400">
                Note: Coupon code is invalid, but other discounts still apply
              </div>
            </div>
          ) : (
            <div className="text-2xl font-bold text-primary">
              ${finalAmount} {pricing.currency}
              {paymentType === "subscription" && (
                <span className="text-sm font-normal">/month</span>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Payment Method Display */}
      {existingCard && !forceNewCard ? (
        <div className="p-4 border border-border rounded-md bg-input">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-text font-medium">
                💳 •••• {existingCard.card.last4}
              </div>
              <div className="text-sm text-text-secondary capitalize">
                {existingCard.card.brand} • Expires{" "}
                {existingCard.card.exp_month}/{existingCard.card.exp_year}
              </div>
            </div>
            {/* Note: is_default is not provided by the new API, we'll handle this differently */}
            {false && (
              <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                Default
              </span>
            )}
          </div>
        </div>
      ) : (
        <div className="p-4 border border-border rounded-md bg-input">
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: "16px",
                  color: "var(--text)",
                  "::placeholder": {
                    color: "var(--text-secondary)",
                  },
                },
              },
              hidePostalCode: true,
            }}
          />
        </div>
      )}

      {/* Back to Card Selection Button */}
      {forceNewCard && onBackToCardSelection && (
        <div className="flex justify-start">
          <button
            type="button"
            onClick={onBackToCardSelection}
            className="text-sm text-primary hover:text-primary-hover underline"
            disabled={isProcessing || isAuthenticating}
          >
            ← Use existing card instead
          </button>
        </div>
      )}

      {/* Coupon Code Input - Always show for flexibility */}
      <div>
        <label
          htmlFor="coupon-code"
          className="block text-sm font-medium text-text mb-2"
        >
          Coupon Code (Optional)
        </label>
        <input
          id="coupon-code"
          type="text"
          value={couponCode}
          onChange={(e) => handleCouponCodeChange(e.target.value)}
          placeholder="Enter coupon code"
          className="w-full uppercase px-3 py-2 border border-border rounded-md bg-input text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          disabled={isProcessing || isAuthenticating}
        />
        {isLoadingDiscount && (
          <div className="mt-2 text-sm text-text-secondary">
            Checking discount...
          </div>
        )}
        {couponError && (
          <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <div className="text-sm text-red-800 dark:text-red-200">
              ✗ {couponError}
            </div>
          </div>
        )}
        {!couponError &&
          couponCode.trim() &&
          discountPreview &&
          discountPreview.coupon_validation &&
          discountPreview.coupon_validation.valid && (
            <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
              <div className="text-sm text-green-800 dark:text-green-200">
                ✓ Coupon code "{couponCode}" applied successfully!
              </div>
            </div>
          )}
        {discountPreview &&
          discountPreview.applied_discounts &&
          discountPreview.applied_discounts.length > 0 && (
            <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
              <div className="text-sm text-green-800 dark:text-green-200">
                {discountPreview.applied_discounts
                  .filter((discount: any) => {
                    // If there's a coupon error and this is a coupon discount, don't show it
                    if (couponError && discount.type === "coupon") {
                      return false;
                    }
                    return true;
                  })
                  .map((discount: any, index: number) => (
                    <div key={index}>
                      ✓ {discount.type === "coupon" ? "Coupon" : "Discount"}{" "}
                      applied: ${discount.discount_amount} off
                      {discount.source && (
                        <span className="text-xs opacity-75">
                          {" "}
                          ({discount.source})
                        </span>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          )}
      </div>

      <MkdButton
        type="submit"
        disabled={!stripe || isProcessing || isAuthenticating}
        className="w-full"
      >
        {isAuthenticating
          ? authenticationMessage || "Authenticating..."
          : isProcessing
            ? existingCard && !forceNewCard
              ? "Processing payment..."
              : paymentType === "subscription"
                ? "Adding card & subscribing..."
                : "Processing..."
            : `Pay $${finalAmount} ${pricing.currency}`}
      </MkdButton>

      {/* Authentication status */}
      {isAuthenticating && (
        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            {authenticationMessage}
          </p>
          <p className="text-xs text-blue-600 dark:text-blue-300 mt-1">
            Please complete any authentication steps in the popup window.
          </p>
        </div>
      )}
    </form>
  );
};

const StripePaymentModal: React.FC<StripePaymentModalProps> = ({
  isOpen,
  onClose,
  splitId,
  programId,
  pricing,
  paymentType,
  existingCard,
  forceNewCard,
  affiliateCode,
  onBackToCardSelection,
  programData,
  initialCouponCode,
  // Centralized discount preview props
  discountPreview,
  isLoadingDiscount = false,
  couponError,
  onCouponCodeChange,
}) => {
  const { showToast } = useToast();
  const navigate = useNavigate();

  const handleSuccess = (_enrollmentId?: number | string) => {
    showToast("Payment successful! You now have access to the program.", 5000);
    onClose();
    // Note: Redirect is handled in the PaymentForm component for authenticated payments
    // For immediate successful payments, redirect here
    setTimeout(() => {
      navigate(`/athlete/library`);
    }, 1000);
  };

  const handleError = (error: string) => {
    showToast(error, 5000);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-background rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            {onBackToCardSelection && existingCard && (
              <button
                onClick={onBackToCardSelection}
                className="mr-3 p-1 text-text-secondary hover:text-text transition-colors"
                title="Back to card selection"
              >
                ←
              </button>
            )}
            <h2 className="text-xl font-semibold text-text">
              Enroll in {pricing.split_title}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-text-secondary hover:text-text"
          >
            ✕
          </button>
        </div>

        <PaymentForm
          splitId={splitId}
          programId={programId}
          pricing={pricing}
          paymentType={paymentType}
          existingCard={existingCard}
          forceNewCard={forceNewCard}
          affiliateCode={affiliateCode}
          onSuccess={handleSuccess}
          onError={handleError}
          onBackToCardSelection={onBackToCardSelection}
          programData={programData}
          initialCouponCode={initialCouponCode}
          // Centralized discount preview props
          discountPreview={discountPreview}
          isLoadingDiscount={isLoadingDiscount}
          couponError={couponError}
          onCouponCodeChange={onCouponCodeChange}
        />
      </div>
    </div>
  );
};

export default StripePaymentModal;
