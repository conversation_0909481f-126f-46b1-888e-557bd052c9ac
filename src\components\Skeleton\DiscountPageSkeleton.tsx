import React from "react";
import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

const DiscountPageSkeleton: React.FC = () => {
  const { state } = useTheme();
  const mode = state?.theme || "light";

  return (
    <div className="min-h-screen bg-background-secondary py-6 px-2 sm:px-4 md:px-8">
      <SkeletonTheme
        baseColor={THEME_COLORS[mode].TEXT_DISABLED}
        highlightColor={THEME_COLORS[mode].BACKGROUND_HOVER}
      >
        <div className="max-w-7xl mx-auto">
          {/* Page Header Skeleton */}
          <div className="mb-6">
            <Skeleton height={28} width="35%" className="mb-2" />
            <Skeleton height={14} width="45%" />
          </div>

          {/* Main Content Container Skeleton */}
          <div className="bg-background rounded-lg shadow-sm border border-border p-4 md:p-6">
            {/* Two Column Layout */}
            <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
              {/* Left Column */}
              <div className="flex-1 flex flex-col gap-4">
                {/* Affiliate Link Section Skeleton */}
                <div className="space-y-3">
                  <Skeleton height={20} width="25%" />
                  <div className="space-y-2">
                    <Skeleton height={14} width="18%" />
                    <Skeleton height={36} width="100%" />
                  </div>
                </div>

                {/* Promo Code Section Skeleton */}
                <div className="space-y-3">
                  <Skeleton height={20} width="30%" />

                  {/* Promo Code Input */}
                  <div className="space-y-2">
                    <Skeleton height={14} width="20%" />
                    <Skeleton height={36} width="100%" />
                  </div>

                  {/* Discount Type and Value */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <Skeleton height={14} width="25%" />
                      <Skeleton height={36} width="100%" />
                    </div>
                    <div className="space-y-2">
                      <Skeleton height={14} width="20%" />
                      <Skeleton height={36} width="100%" />
                    </div>
                  </div>

                  {/* Applies To and Expiry */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <Skeleton height={14} width="18%" />
                      <Skeleton height={36} width="100%" />
                    </div>
                    <div className="space-y-2">
                      <Skeleton height={14} width="22%" />
                      <Skeleton height={36} width="100%" />
                    </div>
                  </div>

                  {/* Create Button */}
                  <Skeleton height={36} width="25%" />
                </div>
              </div>

              {/* Right Column */}
              <div className="flex-1 flex flex-col gap-4">
                {/* Sale Discount Section Skeleton */}
                <div className="space-y-3">
                  <Skeleton height={20} width="25%" />

                  {/* Discount Type and Value */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <Skeleton height={14} width="25%" />
                      <Skeleton height={36} width="100%" />
                    </div>
                    <div className="space-y-2">
                      <Skeleton height={14} width="20%" />
                      <Skeleton height={36} width="100%" />
                    </div>
                  </div>

                  {/* Apply to All Checkbox */}
                  <div className="flex items-center space-x-2">
                    <Skeleton height={14} width={14} />
                    <Skeleton height={14} width="35%" />
                  </div>
                </div>

                {/* Subscription Discounts Section Skeleton */}
                <div className="space-y-3">
                  <Skeleton height={20} width="40%" />

                  {/* Single Discount Item */}
                  <div className="border border-border rounded-lg p-3 space-y-2">
                    <div className="flex justify-between items-center">
                      <Skeleton height={16} width="20%" />
                      <Skeleton height={28} width="15%" />
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-1">
                        <Skeleton height={12} width="25%" />
                        <Skeleton height={32} width="100%" />
                      </div>
                      <div className="space-y-1">
                        <Skeleton height={12} width="20%" />
                        <Skeleton height={32} width="100%" />
                      </div>
                    </div>
                  </div>

                  {/* Add Discount Button */}
                  <Skeleton height={32} width="30%" />
                </div>

                {/* Full Price Discounts Section Skeleton */}
                <div className="space-y-3">
                  <Skeleton height={20} width="35%" />

                  {/* Single Discount Item */}
                  <div className="border border-border rounded-lg p-3 space-y-2">
                    <div className="flex justify-between items-center">
                      <Skeleton height={16} width="20%" />
                      <Skeleton height={28} width="15%" />
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-1">
                        <Skeleton height={12} width="25%" />
                        <Skeleton height={32} width="100%" />
                      </div>
                      <div className="space-y-1">
                        <Skeleton height={12} width="20%" />
                        <Skeleton height={32} width="100%" />
                      </div>
                    </div>
                  </div>

                  {/* Add Discount Button */}
                  <Skeleton height={32} width="30%" />
                </div>
              </div>
            </div>

            {/* Action Buttons Skeleton */}
            <div className="mt-6 flex justify-end space-x-3">
              <Skeleton height={36} width="15%" />
              <Skeleton height={36} width="12%" />
            </div>
          </div>
        </div>
      </SkeletonTheme>
    </div>
  );
};

export default DiscountPageSkeleton;
