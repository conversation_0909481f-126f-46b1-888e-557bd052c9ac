import { useState, useEffect } from "react";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import {
  USE_TEST_DATA,
  simulateApiDelay,
  mockTransactionStats,
  mockEarningsGraphData,
  mockStripeConnectStatusComplete,
  mockStripeConnectStatusNotSetup,
  mockStripeConnectStatusIncomplete,
  mockTransactionHistory,
  mockWithdrawalResponse,
  mockStripeConnectSetupResponse,
  mockStripeConnectDeleteResponse,
} from "@/assets/data/trainer_transactions_test_data";

// Types for API responses
export interface TransactionStats {
  total_earnings: number;
  pending_payouts: number;
  available_to_withdraw: number;
  withdrawn: number;
  currency: string;
  payout_time_hours: number;
}

export interface EarningsGraphData {
  earnings_by_month: Array<{
    month: string;
    month_name: string;
    year: number;
    month_number: number;
    earnings: number;
  }>;
  total_months: number;
  currency: string;
}

export interface StripeConnectStatus {
  has_stripe_connect: boolean;
  stripe_connect_account_id?: string;
  onboarded: boolean;
  details_submitted: boolean;
  charges_enabled: boolean;
  payouts_enabled: boolean;
  requirements?: any;
  message: string;
}

export interface WithdrawalRequest {
  amount: number;
  currency: string;
}

export interface WithdrawalResponse {
  withdrawal_id: string;
  amount: number;
  currency: string;
  status: string;
  transfer_details: {
    stripe_transfer_id: string;
    destination_account: string;
    created: number;
  };
  message: string;
}

export interface TransactionHistoryItem {
  commission_id: number;
  enrollment_id: number;
  type: string;
  commission_type: string;
  amount: {
    total: number;
    original: number;
    discount: number;
    trainer_amount: number;
    company_amount: number;
    currency: string;
  };
  status: string;
  dates: {
    created: string;
    scheduled_payout: string;
    processed_payout?: string;
    enrollment_date: string;
  };
  program: {
    id: number;
    title: string;
    description: string;
  };
  split: {
    id: number;
    title: string;
    full_price: number;
    subscription_price: number;
  };
  athlete: {
    email: string;
    name: string;
  };
  enrollment: {
    payment_type: string;
    status: string;
    payment_status: string;
    stripe_subscription_id?: string;
    stripe_payment_intent_id?: string;
  };
}

export interface TransactionHistoryResponse {
  transactions: TransactionHistoryItem[];
  pagination: {
    current_page: number;
    per_page: number;
    total_records: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  filters: {
    type: string;
    status?: string;
    start_date?: string;
    end_date?: string;
  };
}

// Hook for transaction stats
export const useTransactionStats = () => {
  const { mutateAsync: customModelQuery } = useCustomModelQuery();
  const { showToast } = useContexts();
  const [stats, setStats] = useState<TransactionStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    setLoading(true);
    setError(null);
    try {
      if (USE_TEST_DATA) {
        // Use test data in development
        await simulateApiDelay(800);
        setStats(mockTransactionStats);
        return;
      }

      const response = await customModelQuery({
        endpoint: "/v2/api/kanglink/custom/trainer/transactions/stats",
        method: "GET",
      });

      if (response && response.data) {
        setStats(response.data);
      }
    } catch (err: any) {
      const errorMessage =
        err?.response?.data?.message || "Failed to fetch transaction stats";
      setError(errorMessage);
      showToast(errorMessage, 5000, ToastStatusEnum.ERROR);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return {
    stats,
    loading,
    error,
    refetch: fetchStats,
  };
};

// Hook for earnings graph data
export const useEarningsGraph = () => {
  const { mutateAsync: customModelQuery } = useCustomModelQuery();
  const { showToast } = useContexts();
  const [graphData, setGraphData] = useState<EarningsGraphData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchGraphData = async () => {
    setLoading(true);
    setError(null);
    try {
      if (USE_TEST_DATA) {
        // Use test data in development
        await simulateApiDelay(1200);
        setGraphData(mockEarningsGraphData);
        return;
      }

      const response = await customModelQuery({
        endpoint: "/v2/api/kanglink/custom/trainer/transactions/earnings-graph",
        method: "GET",
      });

      if (response && response.data) {
        setGraphData(response.data);
      }
    } catch (err: any) {
      const errorMessage =
        err?.response?.data?.message || "Failed to fetch earnings graph data";
      setError(errorMessage);
      showToast(errorMessage, 5000, ToastStatusEnum.ERROR);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchGraphData();
  }, []);

  return {
    graphData,
    loading,
    error,
    refetch: fetchGraphData,
  };
};

// Hook for Stripe Connect status
export const useStripeConnectStatus = () => {
  const { mutateAsync: customModelQuery } = useCustomModelQuery();
  const { showToast } = useContexts();
  const [status, setStatus] = useState<StripeConnectStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = async () => {
    setLoading(true);
    setError(null);
    try {
      if (USE_TEST_DATA) {
        // Use test data in development - cycle through different states
        await simulateApiDelay(600);
        const testStates = [
          mockStripeConnectStatusNotSetup,
          mockStripeConnectStatusIncomplete,
          mockStripeConnectStatusComplete,
        ];
        // Use a simple hash of current time to pick a state
        const stateIndex = Math.floor(Date.now() / 10000) % testStates.length;
        setStatus(testStates[stateIndex]);
        return;
      }

      const response = await customModelQuery({
        endpoint:
          "/v2/api/kanglink/custom/trainer/transactions/stripe-connect-status",
        method: "GET",
      });

      if (response && response.data) {
        setStatus(response.data);
      }
    } catch (err: any) {
      const errorMessage =
        err?.response?.data?.message || "Failed to fetch Stripe Connect status";
      setError(errorMessage);
      showToast(errorMessage, 5000, ToastStatusEnum.ERROR);
    } finally {
      setLoading(false);
    }
  };

  const setupStripeConnect = async (data: {
    country: string;
    business_type: string;
    return_url: string;
    refresh_url: string;
  }) => {
    setLoading(true);
    setError(null);
    try {
      if (USE_TEST_DATA) {
        // Use test data in development
        await simulateApiDelay(1500);
        showToast(
          "Stripe Connect setup initiated (Test Mode)",
          3000,
          ToastStatusEnum.SUCCESS
        );
        return mockStripeConnectSetupResponse;
      }

      const response = await customModelQuery({
        endpoint:
          "/v2/api/kanglink/custom/trainer/transactions/setup-stripe-connect",
        method: "POST",
        body: data,
      });

      if (response && response.data) {
        showToast(
          "Stripe Connect setup initiated",
          3000,
          ToastStatusEnum.SUCCESS
        );
        return response.data;
      }
    } catch (err: any) {
      const errorMessage =
        err?.response?.data?.message || "Failed to setup Stripe Connect";
      setError(errorMessage);
      showToast(errorMessage, 5000, ToastStatusEnum.ERROR);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteStripeConnect = async () => {
    setLoading(true);
    setError(null);
    try {
      if (USE_TEST_DATA) {
        // Use test data in development
        await simulateApiDelay(1000);
        showToast(
          "Stripe Connect account deleted (Test Mode)",
          3000,
          ToastStatusEnum.SUCCESS
        );
        setStatus(mockStripeConnectStatusNotSetup);
        return;
      }

      const response = await customModelQuery({
        endpoint: "/v2/api/kanglink/custom/trainer/transactions/stripe-connect",
        method: "DELETE",
      });

      if (response) {
        showToast(
          "Stripe Connect account deleted successfully",
          3000,
          ToastStatusEnum.SUCCESS
        );
        // Refresh status after deletion
        fetchStatus();
      }
    } catch (err: any) {
      const errorMessage =
        err?.response?.data?.message ||
        "Failed to delete Stripe Connect account";
      setError(errorMessage);
      showToast(errorMessage, 5000, ToastStatusEnum.ERROR);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
  }, []);

  return {
    status,
    loading,
    error,
    refetch: fetchStatus,
    setupStripeConnect,
    deleteStripeConnect,
  };
};

// Hook for withdrawal requests
export const useWithdrawal = () => {
  const { mutateAsync: customModelQuery } = useCustomModelQuery();
  const { showToast } = useContexts();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const requestWithdrawal = async (
    data: WithdrawalRequest
  ): Promise<WithdrawalResponse | null> => {
    setLoading(true);
    setError(null);
    try {
      if (USE_TEST_DATA) {
        // Use test data in development
        await simulateApiDelay(2000);
        showToast(
          "Withdrawal processed successfully (Test Mode)",
          3000,
          ToastStatusEnum.SUCCESS
        );
        return {
          ...mockWithdrawalResponse,
          amount: data.amount,
          currency: data.currency,
        };
      }

      const response = await customModelQuery({
        endpoint: "/v2/api/kanglink/custom/trainer/transactions/withdraw",
        method: "POST",
        body: data,
      });

      if (response && response.data) {
        showToast(
          "Withdrawal processed successfully",
          3000,
          ToastStatusEnum.SUCCESS
        );
        return response.data;
      }
      return null;
    } catch (err: any) {
      const errorMessage =
        err?.response?.data?.message || "Failed to process withdrawal";
      setError(errorMessage);
      showToast(errorMessage, 5000, ToastStatusEnum.ERROR);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    requestWithdrawal,
  };
};

// Hook for transaction history
export const useTransactionHistory = () => {
  const { mutateAsync: customModelQuery } = useCustomModelQuery();
  const { showToast } = useContexts();
  const [history, setHistory] = useState<TransactionHistoryResponse | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchHistory = async (params?: {
    page?: number;
    limit?: number;
    type?: string;
    status?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    setLoading(true);
    setError(null);
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.type) queryParams.append("type", params.type);
      if (params?.status) queryParams.append("status", params.status);
      if (params?.start_date)
        queryParams.append("start_date", params.start_date);
      if (params?.end_date) queryParams.append("end_date", params.end_date);

      const endpoint = `/v2/api/kanglink/custom/trainer/transactions/history${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`;

      if (USE_TEST_DATA) {
        // Use test data in development
        await simulateApiDelay(1000);
        setHistory(mockTransactionHistory);
        return;
      }

      const response = await customModelQuery({
        endpoint,
        method: "GET",
      });

      if (response && response.data) {
        setHistory(response.data);
      }
    } catch (err: any) {
      const errorMessage =
        err?.response?.data?.message || "Failed to fetch transaction history";
      setError(errorMessage);
      showToast(errorMessage, 5000, ToastStatusEnum.ERROR);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHistory({ page: 1, limit: 20 });
  }, []);

  return {
    history,
    loading,
    error,
    fetchHistory,
    refetch: () => fetchHistory({ page: 1, limit: 20 }),
  };
};
