-- MySQL Schema for Complete Program Structure
-- Based on FormData interface from src/components/CreateProgramStepOne/types.ts
-- and ProgramFormData interface from src/components/CreateProgramStepTwo/types.ts

-- Create programs table
CREATE TABLE kanglink_program (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    program_name VARCHAR(255) NOT NULL,
    type_of_program VARCHAR(100) NOT NULL,
    program_description TEXT,
    payment_plan JSON, -- Store array of payment plans as JSON
    track_progress BOOLEAN DEFAULT FALSE,
    allow_comments BOOLEAN DEFAULT FALSE,
    allow_private_messages BOOLEAN DEFAULT FALSE,
    target_levels JSON, -- Store array of target levels as <PERSON><PERSON><PERSON>
    split_program INT DEFAULT 0,
    currency VARCHAR(10) DEFAULT 'USD',
    days_for_preview INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
);

-- <PERSON><PERSON> splits table
CREATE TABLE kanglink_split (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    program_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    full_price DECIMAL(10, 2) NULL, -- Optional field
    subscription DECIMAL(10, 2) NULL, -- Optional field
    program_split VARCHAR(255), -- From ProgramFormData
    description TEXT, -- From ProgramFormData
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
);

-- Seed data for kanglink_split table
-- Creating splits for programs from program_data.csv (program IDs 13-24, user_id 11)

INSERT INTO kanglink_split (
    user_id,
    program_id,
    title,
    full_price,
    subscription,
    program_split,
    description
) VALUES
-- Ultimate Strength Builder (Program ID: 13, 3 splits)
(11, 13, 'Foundation Phase', 149.99, 29.99, 'Phase 1', 'Build your strength foundation with compound movements and proper form development.'),
(11, 13, 'Strength Building Phase', 149.99, 29.99, 'Phase 2', 'Increase intensity and volume to build maximum strength and muscle mass.'),
(11, 13, 'Peak Performance Phase', 149.99, 29.99, 'Phase 3', 'Peak your strength with advanced techniques and competition-level training.'),

-- Beginner Strength Foundation (Program ID: 14, 2 splits)
(11, 14, 'Learning Phase', 79.99, 19.99, 'Beginner Phase 1', 'Learn proper form and basic movement patterns with light weights.'),
(11, 14, 'Progression Phase', 79.99, 19.99, 'Beginner Phase 2', 'Progress to heavier weights and more complex exercises with confidence.'),

-- HIIT Fat Burner Challenge (Program ID: 15, 1 split)
(11, 15, 'HIIT Challenge', 59.99, 14.99, 'Complete Program', 'High-intensity interval training for maximum fat burn and cardiovascular fitness.'),

-- Marathon Training Program (Program ID: 16, 4 splits)
(11, 16, 'Base Building Phase', 199.99, NULL, 'Weeks 1-4', 'Build your aerobic base with easy runs and gradual mileage increase.'),
(11, 16, 'Build-Up Phase', 199.99, NULL, 'Weeks 5-8', 'Increase weekly mileage and introduce tempo runs and hill training.'),
(11, 16, 'Peak Training Phase', 199.99, NULL, 'Weeks 9-12', 'Peak training with long runs, speed work, and race pace practice.'),
(11, 16, 'Taper & Race Phase', 199.99, NULL, 'Weeks 13-16', 'Taper training volume and prepare for race day with final preparations.'),

-- Daily Mobility & Flexibility (Program ID: 17, 1 split)
(11, 17, 'Daily Routine', 39.99, 9.99, 'Complete Program', 'Comprehensive daily mobility and flexibility routine for all fitness levels.'),

-- Complete Body Transformation (Program ID: 18, 4 splits)
(11, 18, 'Foundation Phase', 299.99, 49.99, 'Weeks 1-4', 'Establish healthy habits, basic strength training, and nutrition fundamentals.'),
(11, 18, 'Building Phase', 299.99, 49.99, 'Weeks 5-8', 'Increase training intensity, build muscle, and refine nutrition plan.'),
(11, 18, 'Transformation Phase', 299.99, 49.99, 'Weeks 9-12', 'Advanced training techniques, body recomposition, and lifestyle optimization.'),
(11, 18, 'Mastery Phase', 299.99, 49.99, 'Weeks 13-16', 'Master your transformation with advanced protocols and long-term maintenance.'),

-- Functional Fitness Bootcamp (Program ID: 19, 3 splits)
(11, 19, 'Movement Foundation', 119.99, 24.99, 'Weeks 1-3', 'Master functional movement patterns and build movement quality.'),
(11, 19, 'Strength & Conditioning', 119.99, 24.99, 'Weeks 4-7', 'Build functional strength and cardiovascular conditioning.'),
(11, 19, 'Performance Phase', 119.99, 24.99, 'Weeks 8-10', 'Peak functional fitness with complex movements and high-intensity training.'),

-- Powerlifting Prep Program (Program ID: 20, 3 splits)
(11, 20, 'Technique Phase', 179.99, NULL, 'Weeks 1-4', 'Perfect your squat, bench press, and deadlift technique with moderate weights.'),
(11, 20, 'Strength Phase', 179.99, NULL, 'Weeks 5-8', 'Build maximum strength with heavy training and accessory work.'),
(11, 20, 'Peaking Phase', 179.99, NULL, 'Weeks 9-12', 'Peak for competition with opener, second, and third attempt preparation.'),

-- Bodyweight Mastery (Program ID: 21, 2 splits)
(11, 21, 'Foundation Skills', 89.99, 19.99, 'Weeks 1-6', 'Build basic bodyweight strength and learn fundamental movement patterns.'),
(11, 21, 'Advanced Skills', 89.99, 19.99, 'Weeks 7-12', 'Master advanced bodyweight movements like handstands and muscle-ups.'),

-- Senior Fitness & Wellness (Program ID: 22, 2 splits)
(11, 22, 'Gentle Introduction', 69.99, 19.99, 'Weeks 1-4', 'Safe introduction to exercise with focus on balance and basic strength.'),
(11, 22, 'Active Living', 69.99, 19.99, 'Weeks 5-8', 'Build confidence and strength for independent, active daily living.'),

-- 30-Day Abs Challenge (Program ID: 23, 1 split)
(11, 23, 'Abs Challenge', 29.99, NULL, 'Complete Program', 'Intensive 30-day core strengthening program with progressive difficulty.'),

-- Quick Morning Energizer (Program ID: 24, 1 split)
(11, 24, 'Morning Routine', NULL, 9.99, 'Complete Program', 'Energizing 15-minute morning workouts to start your day right.');


-- Create weeks table
CREATE TABLE kanglink_week (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    split_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    equipment_required VARCHAR(255),
    is_collapsed BOOLEAN DEFAULT FALSE,
    week_order INT NOT NULL, -- To maintain order of weeks
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
);

-- Seed data for kanglink_week table
-- Creating weeks for splits from real_data.csv (split IDs 1-27, user_id 11)

INSERT INTO kanglink_week (
    user_id,
    split_id,
    name,
    equipment_required,
    is_collapsed,
    week_order
) VALUES
-- Foundation Phase (Split ID: 1) - 4 weeks
(11, 1, 'Week 1: Movement Basics', 'Barbell, Dumbbells, Bench', false, 1),
(11, 1, 'Week 2: Form Development', 'Barbell, Dumbbells, Bench', false, 2),
(11, 1, 'Week 3: Load Introduction', 'Barbell, Dumbbells, Bench, Plates', false, 3),
(11, 1, 'Week 4: Foundation Assessment', 'Barbell, Dumbbells, Bench, Plates', false, 4),

-- Strength Building Phase (Split ID: 2) - 4 weeks
(11, 2, 'Week 1: Volume Increase', 'Barbell, Dumbbells, Bench, Plates', false, 1),
(11, 2, 'Week 2: Intensity Focus', 'Barbell, Dumbbells, Bench, Plates', false, 2),
(11, 2, 'Week 3: Progressive Overload', 'Barbell, Dumbbells, Bench, Plates', false, 3),
(11, 2, 'Week 4: Strength Testing', 'Barbell, Dumbbells, Bench, Plates', false, 4),

-- Peak Performance Phase (Split ID: 3) - 4 weeks
(11, 3, 'Week 1: Advanced Techniques', 'Barbell, Dumbbells, Bench, Plates, Chains', false, 1),
(11, 3, 'Week 2: Peak Intensity', 'Barbell, Dumbbells, Bench, Plates, Chains', false, 2),
(11, 3, 'Week 3: Competition Prep', 'Barbell, Dumbbells, Bench, Plates', false, 3),
(11, 3, 'Week 4: Peak Performance', 'Barbell, Dumbbells, Bench, Plates', false, 4),

-- Learning Phase (Split ID: 4) - 4 weeks
(11, 4, 'Week 1: Introduction to Weights', 'Light Dumbbells, Bodyweight', false, 1),
(11, 4, 'Week 2: Basic Movements', 'Light Dumbbells, Resistance Bands', false, 2),
(11, 4, 'Week 3: Form Mastery', 'Dumbbells, Barbell (empty)', false, 3),
(11, 4, 'Week 4: Confidence Building', 'Dumbbells, Light Barbell', false, 4),

-- Progression Phase (Split ID: 5) - 4 weeks
(11, 5, 'Week 1: Weight Progression', 'Dumbbells, Barbell, Light Plates', false, 1),
(11, 5, 'Week 2: Complex Movements', 'Dumbbells, Barbell, Plates', false, 2),
(11, 5, 'Week 3: Strength Building', 'Dumbbells, Barbell, Plates', false, 3),
(11, 5, 'Week 4: Progress Assessment', 'Dumbbells, Barbell, Plates', false, 4),

-- HIIT Challenge (Split ID: 6) - 6 weeks
(11, 6, 'Week 1: HIIT Introduction', 'Bodyweight, Timer', false, 1),
(11, 6, 'Week 2: Intensity Increase', 'Bodyweight, Timer', false, 2),
(11, 6, 'Week 3: Advanced Intervals', 'Bodyweight, Timer, Light Weights', false, 3),
(11, 6, 'Week 4: Peak Intensity', 'Bodyweight, Timer, Light Weights', false, 4),
(11, 6, 'Week 5: Challenge Week', 'Bodyweight, Timer, Light Weights', false, 5),
(11, 6, 'Week 6: Final Push', 'Bodyweight, Timer, Light Weights', false, 6),

-- Base Building Phase (Split ID: 7) - 4 weeks
(11, 7, 'Week 1: Easy Base Building', 'Running Shoes, Heart Rate Monitor', false, 1),
(11, 7, 'Week 2: Aerobic Development', 'Running Shoes, Heart Rate Monitor', false, 2),
(11, 7, 'Week 3: Mileage Increase', 'Running Shoes, Heart Rate Monitor', false, 3),
(11, 7, 'Week 4: Base Consolidation', 'Running Shoes, Heart Rate Monitor', false, 4),

-- Build-Up Phase (Split ID: 8) - 4 weeks
(11, 8, 'Week 1: Tempo Introduction', 'Running Shoes, Heart Rate Monitor', false, 1),
(11, 8, 'Week 2: Hill Training', 'Running Shoes, Heart Rate Monitor', false, 2),
(11, 8, 'Week 3: Speed Development', 'Running Shoes, Heart Rate Monitor, Track', false, 3),
(11, 8, 'Week 4: Build-Up Assessment', 'Running Shoes, Heart Rate Monitor', false, 4),

-- Peak Training Phase (Split ID: 9) - 4 weeks
(11, 9, 'Week 1: Long Run Focus', 'Running Shoes, Heart Rate Monitor, Hydration', false, 1),
(11, 9, 'Week 2: Race Pace Practice', 'Running Shoes, Heart Rate Monitor, Track', false, 2),
(11, 9, 'Week 3: Peak Volume', 'Running Shoes, Heart Rate Monitor, Hydration', false, 3),
(11, 9, 'Week 4: Speed Sharpening', 'Running Shoes, Heart Rate Monitor, Track', false, 4),

-- Taper & Race Phase (Split ID: 10) - 4 weeks
(11, 10, 'Week 1: Volume Reduction', 'Running Shoes, Heart Rate Monitor', false, 1),
(11, 10, 'Week 2: Taper Continues', 'Running Shoes, Heart Rate Monitor', false, 2),
(11, 10, 'Week 3: Race Preparation', 'Running Shoes, Heart Rate Monitor, Race Gear', false, 3),
(11, 10, 'Week 4: Race Week', 'Running Shoes, Heart Rate Monitor, Race Gear', false, 4),

-- Daily Routine (Split ID: 11) - 4 weeks (repeating cycle)
(11, 11, 'Week 1: Mobility Foundation', 'Yoga Mat, Foam Roller', false, 1),
(11, 11, 'Week 2: Flexibility Focus', 'Yoga Mat, Foam Roller, Resistance Bands', false, 2),
(11, 11, 'Week 3: Advanced Stretching', 'Yoga Mat, Foam Roller, Resistance Bands', false, 3),
(11, 11, 'Week 4: Full Body Integration', 'Yoga Mat, Foam Roller, Resistance Bands', false, 4),

-- Foundation Phase (Split ID: 12) - 4 weeks
(11, 12, 'Week 1: Habit Formation', 'Basic Equipment, Food Scale', false, 1),
(11, 12, 'Week 2: Exercise Introduction', 'Dumbbells, Resistance Bands, Food Scale', false, 2),
(11, 12, 'Week 3: Routine Building', 'Dumbbells, Resistance Bands, Food Scale', false, 3),
(11, 12, 'Week 4: Foundation Assessment', 'Dumbbells, Resistance Bands, Food Scale', false, 4),

-- Building Phase (Split ID: 13) - 4 weeks
(11, 13, 'Week 1: Intensity Increase', 'Dumbbells, Barbell, Cardio Equipment', false, 1),
(11, 13, 'Week 2: Muscle Building', 'Dumbbells, Barbell, Cardio Equipment', false, 2),
(11, 13, 'Week 3: Nutrition Optimization', 'Dumbbells, Barbell, Cardio Equipment, Meal Prep', false, 3),
(11, 13, 'Week 4: Progress Evaluation', 'Dumbbells, Barbell, Cardio Equipment', false, 4),

-- Transformation Phase (Split ID: 14) - 4 weeks
(11, 14, 'Week 1: Advanced Training', 'Full Gym Equipment, Supplements', false, 1),
(11, 14, 'Week 2: Body Recomposition', 'Full Gym Equipment, Supplements', false, 2),
(11, 14, 'Week 3: Lifestyle Integration', 'Full Gym Equipment, Supplements', false, 3),
(11, 14, 'Week 4: Transformation Check', 'Full Gym Equipment, Supplements', false, 4),

-- Mastery Phase (Split ID: 15) - 4 weeks
(11, 15, 'Week 1: Advanced Protocols', 'Full Gym Equipment, Advanced Tools', false, 1),
(11, 15, 'Week 2: Peak Performance', 'Full Gym Equipment, Advanced Tools', false, 2),
(11, 15, 'Week 3: Maintenance Planning', 'Full Gym Equipment, Advanced Tools', false, 3),
(11, 15, 'Week 4: Mastery Achievement', 'Full Gym Equipment, Advanced Tools', false, 4),

-- Movement Foundation (Split ID: 16) - 3 weeks
(11, 16, 'Week 1: Basic Patterns', 'Bodyweight, Light Weights', false, 1),
(11, 16, 'Week 2: Movement Quality', 'Bodyweight, Light Weights, Bands', false, 2),
(11, 16, 'Week 3: Pattern Mastery', 'Bodyweight, Light Weights, Bands', false, 3),

-- Strength & Conditioning (Split ID: 17) - 4 weeks
(11, 17, 'Week 1: Functional Strength', 'Kettlebells, Medicine Ball, Ropes', false, 1),
(11, 17, 'Week 2: Conditioning Focus', 'Kettlebells, Medicine Ball, Ropes, Cardio', false, 2),
(11, 17, 'Week 3: Power Development', 'Kettlebells, Medicine Ball, Ropes, Plyometric', false, 3),
(11, 17, 'Week 4: Strength Assessment', 'Kettlebells, Medicine Ball, Ropes', false, 4),

-- Performance Phase (Split ID: 18) - 3 weeks
(11, 18, 'Week 1: Complex Movements', 'Full Functional Equipment', false, 1),
(11, 18, 'Week 2: High Intensity', 'Full Functional Equipment', false, 2),
(11, 18, 'Week 3: Peak Performance', 'Full Functional Equipment', false, 3),

-- Technique Phase (Split ID: 19) - 4 weeks
(11, 19, 'Week 1: Squat Technique', 'Barbell, Squat Rack, Light Plates', false, 1),
(11, 19, 'Week 2: Bench Press Technique', 'Barbell, Bench, Light Plates', false, 2),
(11, 19, 'Week 3: Deadlift Technique', 'Barbell, Platform, Light Plates', false, 3),
(11, 19, 'Week 4: Technique Integration', 'Barbell, Full Setup, Light Plates', false, 4),

-- Strength Phase (Split ID: 20) - 4 weeks
(11, 20, 'Week 1: Heavy Training Introduction', 'Barbell, Full Setup, Heavy Plates', false, 1),
(11, 20, 'Week 2: Accessory Work', 'Barbell, Full Setup, Heavy Plates, Accessories', false, 2),
(11, 20, 'Week 3: Maximum Strength', 'Barbell, Full Setup, Heavy Plates', false, 3),
(11, 20, 'Week 4: Strength Testing', 'Barbell, Full Setup, Heavy Plates', false, 4),

-- Peaking Phase (Split ID: 21) - 4 weeks
(11, 21, 'Week 1: Opener Practice', 'Competition Equipment', false, 1),
(11, 21, 'Week 2: Second Attempt', 'Competition Equipment', false, 2),
(11, 21, 'Week 3: Third Attempt Prep', 'Competition Equipment', false, 3),
(11, 21, 'Week 4: Competition Week', 'Competition Equipment', false, 4),

-- Foundation Skills (Split ID: 22) - 6 weeks
(11, 22, 'Week 1: Basic Strength', 'Bodyweight, Pull-up Bar', false, 1),
(11, 22, 'Week 2: Core Development', 'Bodyweight, Pull-up Bar, Mat', false, 2),
(11, 22, 'Week 3: Upper Body Focus', 'Bodyweight, Pull-up Bar, Parallettes', false, 3),
(11, 22, 'Week 4: Balance Training', 'Bodyweight, Pull-up Bar, Balance Board', false, 4),
(11, 22, 'Week 5: Movement Flow', 'Bodyweight, Pull-up Bar, Mat', false, 5),
(11, 22, 'Week 6: Skill Integration', 'Bodyweight, Pull-up Bar, Mat', false, 6),

-- Advanced Skills (Split ID: 23) - 6 weeks
(11, 23, 'Week 1: Handstand Progression', 'Wall Space, Parallettes', false, 1),
(11, 23, 'Week 2: Muscle-up Training', 'Pull-up Bar, Resistance Bands', false, 2),
(11, 23, 'Week 3: Pistol Squat Development', 'Bodyweight, Assistance', false, 3),
(11, 23, 'Week 4: Advanced Combinations', 'Full Bodyweight Setup', false, 4),
(11, 23, 'Week 5: Skill Refinement', 'Full Bodyweight Setup', false, 5),
(11, 23, 'Week 6: Mastery Testing', 'Full Bodyweight Setup', false, 6),

-- Gentle Introduction (Split ID: 24) - 4 weeks
(11, 24, 'Week 1: Safety First', 'Chair, Light Weights, Mat', false, 1),
(11, 24, 'Week 2: Balance Focus', 'Chair, Light Weights, Mat, Balance Aids', false, 2),
(11, 24, 'Week 3: Strength Building', 'Chair, Light Weights, Mat, Resistance Bands', false, 3),
(11, 24, 'Week 4: Confidence Building', 'Chair, Light Weights, Mat, Resistance Bands', false, 4),

-- Active Living (Split ID: 25) - 4 weeks
(11, 25, 'Week 1: Daily Activities', 'Functional Equipment, Light Weights', false, 1),
(11, 25, 'Week 2: Independence Focus', 'Functional Equipment, Light Weights', false, 2),
(11, 25, 'Week 3: Strength for Life', 'Functional Equipment, Light Weights', false, 3),
(11, 25, 'Week 4: Active Lifestyle', 'Functional Equipment, Light Weights', false, 4),

-- Abs Challenge (Split ID: 26) - 4 weeks (30 days)
(11, 26, 'Week 1: Core Activation', 'Mat, Bodyweight', false, 1),
(11, 26, 'Week 2: Strength Building', 'Mat, Bodyweight, Light Weights', false, 2),
(11, 26, 'Week 3: Intensity Increase', 'Mat, Bodyweight, Light Weights', false, 3),
(11, 26, 'Week 4: Challenge Completion', 'Mat, Bodyweight, Light Weights', false, 4),

-- Morning Routine (Split ID: 27) - 4 weeks (repeating cycle)
(11, 27, 'Week 1: Energy Activation', 'Bodyweight, Mat', false, 1),
(11, 27, 'Week 2: Strength & Mobility', 'Bodyweight, Mat, Light Weights', false, 2),
(11, 27, 'Week 3: Dynamic Movement', 'Bodyweight, Mat, Light Weights', false, 3),
(11, 27, 'Week 4: Complete Routine', 'Bodyweight, Mat, Light Weights', false, 4);

-- Create days table
CREATE TABLE kanglink_day (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    week_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    is_rest_day BOOLEAN DEFAULT FALSE,
    is_collapsed BOOLEAN DEFAULT FALSE,
    day_order INT NOT NULL, -- To maintain order of days
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
);

-- Seed data for kanglink_day table
-- Creating days for weeks from real_data.csv (week IDs 1-42, user_id 11)
-- Using typical training schedules based on program type

INSERT INTO kanglink_day (
    user_id,
    week_id,
    name,
    is_rest_day,
    is_collapsed,
    day_order
) VALUES
-- Strength Training Program Days (Weeks 1-20) - 4 days per week
-- Week 1: Movement Basics
(11, 1, 'Monday - Upper Body Foundation', false, false, 1),
(11, 1, 'Tuesday - Rest Day', true, false, 2),
(11, 1, 'Wednesday - Lower Body Foundation', false, false, 3),
(11, 1, 'Thursday - Rest Day', true, false, 4),
(11, 1, 'Friday - Full Body Practice', false, false, 5),
(11, 1, 'Saturday - Active Recovery', true, false, 6),
(11, 1, 'Sunday - Rest Day', true, false, 7),

-- Week 2: Form Development
(11, 2, 'Monday - Upper Body Form', false, false, 1),
(11, 2, 'Tuesday - Rest Day', true, false, 2),
(11, 2, 'Wednesday - Lower Body Form', false, false, 3),
(11, 2, 'Thursday - Rest Day', true, false, 4),
(11, 2, 'Friday - Technique Practice', false, false, 5),
(11, 2, 'Saturday - Active Recovery', true, false, 6),
(11, 2, 'Sunday - Rest Day', true, false, 7),

-- Week 3: Load Introduction
(11, 3, 'Monday - Upper Body Load', false, false, 1),
(11, 3, 'Tuesday - Rest Day', true, false, 2),
(11, 3, 'Wednesday - Lower Body Load', false, false, 3),
(11, 3, 'Thursday - Rest Day', true, false, 4),
(11, 3, 'Friday - Progressive Training', false, false, 5),
(11, 3, 'Saturday - Active Recovery', true, false, 6),
(11, 3, 'Sunday - Rest Day', true, false, 7),

-- Week 4: Foundation Assessment
(11, 4, 'Monday - Upper Body Assessment', false, false, 1),
(11, 4, 'Tuesday - Rest Day', true, false, 2),
(11, 4, 'Wednesday - Lower Body Assessment', false, false, 3),
(11, 4, 'Thursday - Rest Day', true, false, 4),
(11, 4, 'Friday - Full Assessment', false, false, 5),
(11, 4, 'Saturday - Active Recovery', true, false, 6),
(11, 4, 'Sunday - Rest Day', true, false, 7),

-- Week 5: Volume Increase
(11, 5, 'Monday - Upper Body Volume', false, false, 1),
(11, 5, 'Tuesday - Rest Day', true, false, 2),
(11, 5, 'Wednesday - Lower Body Volume', false, false, 3),
(11, 5, 'Thursday - Rest Day', true, false, 4),
(11, 5, 'Friday - High Volume Training', false, false, 5),
(11, 5, 'Saturday - Active Recovery', true, false, 6),
(11, 5, 'Sunday - Rest Day', true, false, 7),

-- Week 6: Intensity Focus
(11, 6, 'Monday - Upper Body Intensity', false, false, 1),
(11, 6, 'Tuesday - Rest Day', true, false, 2),
(11, 6, 'Wednesday - Lower Body Intensity', false, false, 3),
(11, 6, 'Thursday - Rest Day', true, false, 4),
(11, 6, 'Friday - High Intensity Training', false, false, 5),
(11, 6, 'Saturday - Active Recovery', true, false, 6),
(11, 6, 'Sunday - Rest Day', true, false, 7),

-- Week 7: Progressive Overload
(11, 7, 'Monday - Upper Body Overload', false, false, 1),
(11, 7, 'Tuesday - Rest Day', true, false, 2),
(11, 7, 'Wednesday - Lower Body Overload', false, false, 3),
(11, 7, 'Thursday - Rest Day', true, false, 4),
(11, 7, 'Friday - Progressive Overload', false, false, 5),
(11, 7, 'Saturday - Active Recovery', true, false, 6),
(11, 7, 'Sunday - Rest Day', true, false, 7),

-- Week 8: Strength Testing
(11, 8, 'Monday - Upper Body Testing', false, false, 1),
(11, 8, 'Tuesday - Rest Day', true, false, 2),
(11, 8, 'Wednesday - Lower Body Testing', false, false, 3),
(11, 8, 'Thursday - Rest Day', true, false, 4),
(11, 8, 'Friday - Strength Assessment', false, false, 5),
(11, 8, 'Saturday - Active Recovery', true, false, 6),
(11, 8, 'Sunday - Rest Day', true, false, 7),

-- Week 9: Advanced Techniques
(11, 9, 'Monday - Advanced Upper Body', false, false, 1),
(11, 9, 'Tuesday - Rest Day', true, false, 2),
(11, 9, 'Wednesday - Advanced Lower Body', false, false, 3),
(11, 9, 'Thursday - Rest Day', true, false, 4),
(11, 9, 'Friday - Advanced Techniques', false, false, 5),
(11, 9, 'Saturday - Active Recovery', true, false, 6),
(11, 9, 'Sunday - Rest Day', true, false, 7),

-- Week 10: Peak Intensity
(11, 10, 'Monday - Peak Upper Body', false, false, 1),
(11, 10, 'Tuesday - Rest Day', true, false, 2),
(11, 10, 'Wednesday - Peak Lower Body', false, false, 3),
(11, 10, 'Thursday - Rest Day', true, false, 4),
(11, 10, 'Friday - Peak Training', false, false, 5),
(11, 10, 'Saturday - Active Recovery', true, false, 6),
(11, 10, 'Sunday - Rest Day', true, false, 7),

-- Week 11: Competition Prep
(11, 11, 'Monday - Competition Upper', false, false, 1),
(11, 11, 'Tuesday - Rest Day', true, false, 2),
(11, 11, 'Wednesday - Competition Lower', false, false, 3),
(11, 11, 'Thursday - Rest Day', true, false, 4),
(11, 11, 'Friday - Competition Prep', false, false, 5),
(11, 11, 'Saturday - Active Recovery', true, false, 6),
(11, 11, 'Sunday - Rest Day', true, false, 7),

-- Week 12: Peak Performance
(11, 12, 'Monday - Peak Upper Performance', false, false, 1),
(11, 12, 'Tuesday - Rest Day', true, false, 2),
(11, 12, 'Wednesday - Peak Lower Performance', false, false, 3),
(11, 12, 'Thursday - Rest Day', true, false, 4),
(11, 12, 'Friday - Peak Performance', false, false, 5),
(11, 12, 'Saturday - Active Recovery', true, false, 6),
(11, 12, 'Sunday - Rest Day', true, false, 7),

-- Beginner Program Days (Weeks 13-20)
-- Week 13: Introduction to Weights
(11, 13, 'Monday - Basic Upper Body', false, false, 1),
(11, 13, 'Tuesday - Rest Day', true, false, 2),
(11, 13, 'Wednesday - Basic Lower Body', false, false, 3),
(11, 13, 'Thursday - Rest Day', true, false, 4),
(11, 13, 'Friday - Full Body Basics', false, false, 5),
(11, 13, 'Saturday - Rest Day', true, false, 6),
(11, 13, 'Sunday - Rest Day', true, false, 7),

-- Week 14: Basic Movements
(11, 14, 'Monday - Movement Patterns', false, false, 1),
(11, 14, 'Tuesday - Rest Day', true, false, 2),
(11, 14, 'Wednesday - Basic Strength', false, false, 3),
(11, 14, 'Thursday - Rest Day', true, false, 4),
(11, 14, 'Friday - Movement Practice', false, false, 5),
(11, 14, 'Saturday - Rest Day', true, false, 6),
(11, 14, 'Sunday - Rest Day', true, false, 7),

-- Week 15: Form Mastery
(11, 15, 'Monday - Form Focus Upper', false, false, 1),
(11, 15, 'Tuesday - Rest Day', true, false, 2),
(11, 15, 'Wednesday - Form Focus Lower', false, false, 3),
(11, 15, 'Thursday - Rest Day', true, false, 4),
(11, 15, 'Friday - Form Mastery', false, false, 5),
(11, 15, 'Saturday - Rest Day', true, false, 6),
(11, 15, 'Sunday - Rest Day', true, false, 7),

-- Week 16: Confidence Building
(11, 16, 'Monday - Confidence Upper', false, false, 1),
(11, 16, 'Tuesday - Rest Day', true, false, 2),
(11, 16, 'Wednesday - Confidence Lower', false, false, 3),
(11, 16, 'Thursday - Rest Day', true, false, 4),
(11, 16, 'Friday - Confidence Building', false, false, 5),
(11, 16, 'Saturday - Rest Day', true, false, 6),
(11, 16, 'Sunday - Rest Day', true, false, 7),

-- Week 17: Weight Progression
(11, 17, 'Monday - Progressive Upper', false, false, 1),
(11, 17, 'Tuesday - Rest Day', true, false, 2),
(11, 17, 'Wednesday - Progressive Lower', false, false, 3),
(11, 17, 'Thursday - Rest Day', true, false, 4),
(11, 17, 'Friday - Weight Progression', false, false, 5),
(11, 17, 'Saturday - Rest Day', true, false, 6),
(11, 17, 'Sunday - Rest Day', true, false, 7),

-- Week 18: Complex Movements
(11, 18, 'Monday - Complex Upper', false, false, 1),
(11, 18, 'Tuesday - Rest Day', true, false, 2),
(11, 18, 'Wednesday - Complex Lower', false, false, 3),
(11, 18, 'Thursday - Rest Day', true, false, 4),
(11, 18, 'Friday - Complex Movements', false, false, 5),
(11, 18, 'Saturday - Rest Day', true, false, 6),
(11, 18, 'Sunday - Rest Day', true, false, 7),

-- Week 19: Strength Building
(11, 19, 'Monday - Strength Upper', false, false, 1),
(11, 19, 'Tuesday - Rest Day', true, false, 2),
(11, 19, 'Wednesday - Strength Lower', false, false, 3),
(11, 19, 'Thursday - Rest Day', true, false, 4),
(11, 19, 'Friday - Strength Building', false, false, 5),
(11, 19, 'Saturday - Rest Day', true, false, 6),
(11, 19, 'Sunday - Rest Day', true, false, 7),

-- Week 20: Progress Assessment
(11, 20, 'Monday - Assessment Upper', false, false, 1),
(11, 20, 'Tuesday - Rest Day', true, false, 2),
(11, 20, 'Wednesday - Assessment Lower', false, false, 3),
(11, 20, 'Thursday - Rest Day', true, false, 4),
(11, 20, 'Friday - Progress Assessment', false, false, 5),
(11, 20, 'Saturday - Rest Day', true, false, 6),
(11, 20, 'Sunday - Rest Day', true, false, 7),

-- HIIT Program Days (Weeks 21-26) - 5 days per week
-- Week 21: HIIT Introduction
(11, 21, 'Monday - HIIT Basics', false, false, 1),
(11, 21, 'Tuesday - Active Recovery', true, false, 2),
(11, 21, 'Wednesday - HIIT Cardio', false, false, 3),
(11, 21, 'Thursday - Rest Day', true, false, 4),
(11, 21, 'Friday - HIIT Strength', false, false, 5),
(11, 21, 'Saturday - HIIT Circuit', false, false, 6),
(11, 21, 'Sunday - Rest Day', true, false, 7),

-- Week 22: Intensity Increase
(11, 22, 'Monday - Intense HIIT', false, false, 1),
(11, 22, 'Tuesday - Active Recovery', true, false, 2),
(11, 22, 'Wednesday - Cardio HIIT', false, false, 3),
(11, 22, 'Thursday - Rest Day', true, false, 4),
(11, 22, 'Friday - Strength HIIT', false, false, 5),
(11, 22, 'Saturday - Circuit Training', false, false, 6),
(11, 22, 'Sunday - Rest Day', true, false, 7),

-- Week 23: Advanced Intervals
(11, 23, 'Monday - Advanced HIIT', false, false, 1),
(11, 23, 'Tuesday - Active Recovery', true, false, 2),
(11, 23, 'Wednesday - Interval Training', false, false, 3),
(11, 23, 'Thursday - Rest Day', true, false, 4),
(11, 23, 'Friday - Power HIIT', false, false, 5),
(11, 23, 'Saturday - Advanced Circuit', false, false, 6),
(11, 23, 'Sunday - Rest Day', true, false, 7),

-- Week 24: Peak Intensity
(11, 24, 'Monday - Peak HIIT', false, false, 1),
(11, 24, 'Tuesday - Active Recovery', true, false, 2),
(11, 24, 'Wednesday - Maximum Intensity', false, false, 3),
(11, 24, 'Thursday - Rest Day', true, false, 4),
(11, 24, 'Friday - Peak Training', false, false, 5),
(11, 24, 'Saturday - Challenge Circuit', false, false, 6),
(11, 24, 'Sunday - Rest Day', true, false, 7),

-- Week 25: Challenge Week
(11, 25, 'Monday - Challenge Day 1', false, false, 1),
(11, 25, 'Tuesday - Active Recovery', true, false, 2),
(11, 25, 'Wednesday - Challenge Day 2', false, false, 3),
(11, 25, 'Thursday - Rest Day', true, false, 4),
(11, 25, 'Friday - Challenge Day 3', false, false, 5),
(11, 25, 'Saturday - Final Challenge', false, false, 6),
(11, 25, 'Sunday - Rest Day', true, false, 7),

-- Week 26: Final Push
(11, 26, 'Monday - Final HIIT', false, false, 1),
(11, 26, 'Tuesday - Active Recovery', true, false, 2),
(11, 26, 'Wednesday - Ultimate Challenge', false, false, 3),
(11, 26, 'Thursday - Rest Day', true, false, 4),
(11, 26, 'Friday - Final Push', false, false, 5),
(11, 26, 'Saturday - Completion Test', false, false, 6),
(11, 26, 'Sunday - Rest Day', true, false, 7),

-- Marathon Training Program Days (Weeks 27-42) - 6 days per week
-- Week 27: Easy Base Building
(11, 27, 'Monday - Easy Run', false, false, 1),
(11, 27, 'Tuesday - Cross Training', false, false, 2),
(11, 27, 'Wednesday - Easy Run', false, false, 3),
(11, 27, 'Thursday - Rest Day', true, false, 4),
(11, 27, 'Friday - Easy Run', false, false, 5),
(11, 27, 'Saturday - Long Run', false, false, 6),
(11, 27, 'Sunday - Recovery Run', false, false, 7),

-- Week 28: Aerobic Development
(11, 28, 'Monday - Aerobic Run', false, false, 1),
(11, 28, 'Tuesday - Cross Training', false, false, 2),
(11, 28, 'Wednesday - Aerobic Run', false, false, 3),
(11, 28, 'Thursday - Rest Day', true, false, 4),
(11, 28, 'Friday - Aerobic Run', false, false, 5),
(11, 28, 'Saturday - Long Run', false, false, 6),
(11, 28, 'Sunday - Recovery Run', false, false, 7),

-- Week 29: Mileage Increase
(11, 29, 'Monday - Base Run', false, false, 1),
(11, 29, 'Tuesday - Cross Training', false, false, 2),
(11, 29, 'Wednesday - Base Run', false, false, 3),
(11, 29, 'Thursday - Rest Day', true, false, 4),
(11, 29, 'Friday - Base Run', false, false, 5),
(11, 29, 'Saturday - Long Run', false, false, 6),
(11, 29, 'Sunday - Recovery Run', false, false, 7),

-- Week 30: Base Consolidation
(11, 30, 'Monday - Steady Run', false, false, 1),
(11, 30, 'Tuesday - Cross Training', false, false, 2),
(11, 30, 'Wednesday - Steady Run', false, false, 3),
(11, 30, 'Thursday - Rest Day', true, false, 4),
(11, 30, 'Friday - Steady Run', false, false, 5),
(11, 30, 'Saturday - Long Run', false, false, 6),
(11, 30, 'Sunday - Recovery Run', false, false, 7);