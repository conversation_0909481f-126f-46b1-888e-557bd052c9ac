import React, { useState } from 'react';
import { NotificationProvider, useNotifications } from '../../context/NotificationContext';
import { NotificationBell, NotificationPanel, SystemAlertForm } from './index';
import useNotificationPanel from '../../hooks/useNotificationPanel';
import MkdSDK from '../../utils/MkdSDK';

// Example header component with notification integration
const HeaderWithNotifications: React.FC = () => {
  const { unreadCount } = useNotifications();
  const { isOpen, openPanel, closePanel } = useNotificationPanel();
  const [showSystemAlertForm, setShowSystemAlertForm] = useState(false);

  // Get user role from SDK (this would be available in your actual app)
  const sdk = new MkdSDK();
  const userRole = sdk.getRole();
  const isSuperAdmin = userRole === 'super_admin';

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo/Brand */}
          <div className="flex items-center">
            <h1 className="text-xl font-semibold text-gray-900">KSL Platform</h1>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex space-x-8">
            <a href="#" className="text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
              Dashboard
            </a>
            <a href="#" className="text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
              Programs
            </a>
            <a href="#" className="text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
              Athletes
            </a>
          </nav>

          {/* Right side actions */}
          <div className="flex items-center space-x-4">
            {/* Notification Bell */}
            <NotificationBell
              onOpenNotifications={openPanel}
              className="relative"
            />

            {/* System Alert Button (Super Admin only) */}
            {isSuperAdmin && (
              <button
                onClick={() => setShowSystemAlertForm(true)}
                className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                title="Create System Alert"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" />
                  <line x1="12" y1="9" x2="12" y2="13" />
                  <line x1="12" y1="17" x2="12.01" y2="17" />
                </svg>
              </button>
            )}

            {/* User Profile */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
              <span className="text-sm text-gray-700">User Name</span>
            </div>
          </div>
        </div>
      </div>

      {/* Notification Panel */}
      <NotificationPanel
        isOpen={isOpen}
        onClose={closePanel}
        position="right"
      />

      {/* System Alert Form */}
      {showSystemAlertForm && (
        <SystemAlertForm
          onClose={() => setShowSystemAlertForm(false)}
          onSuccess={() => {
            console.log('System alert created successfully');
            // You could show a success toast here
          }}
        />
      )}
    </header>
  );
};

// Wrapper component that provides the notification context
const NotificationIntegrationExample: React.FC = () => {
  return (
    <NotificationProvider>
      <div className="min-h-screen bg-gray-50">
        <HeaderWithNotifications />
        
        {/* Main content */}
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
              <div className="text-center">
                <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                  Notification System Integration
                </h2>
                <p className="text-gray-600 mb-4">
                  This example shows how to integrate the notification system into your app.
                </p>
                <div className="space-y-2 text-sm text-gray-500">
                  <p>• Click the notification bell to see notifications</p>
                  <p>• Super admins can create system alerts</p>
                  <p>• Notifications are automatically updated</p>
                  <p>• Real-time unread count display</p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </NotificationProvider>
  );
};

export default NotificationIntegrationExample; 