// ============================================================================
// ADMIN DASHBOARD API SPECIFICATIONS
// ============================================================================

// Data Models
export interface DashboardStats {
  athletes: {
    total: number;
    newThisMonth: number;
    activeThisMonth: number;
    growthPercentage: number;
  };
  trainers: {
    total: number;
    newThisMonth: number;
    activeThisMonth: number;
    growthPercentage: number;
    pendingApproval: number;
  };
  programs: {
    total: number;
    pendingApproval: number;
    publishedThisMonth: number;
    rejectedThisMonth: number;
  };
  refunds: {
    pendingRequests: number;
    processedThisMonth: number;
    totalAmountPending: number;
    averageProcessingTime: string;
  };
  revenue: {
    totalThisMonth: number;
    totalLastMonth: number;
    growthPercentage: number;
    currency: string;
  };
  lastUpdated: string;
}

export interface Alert {
  id: string;
  type: "user_flagged" | "program_approval" | "refund_request" | "system";
  priority: "high" | "medium" | "low";
  title: string;
  message: string;
  count: number;
  actionUrl: string;
  createdAt: string;
  isRead: boolean;
}

export interface AdminUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  profileImage?: string;
  status: "active" | "inactive" | "suspended" | "banned" | "pending";
  joinedAt: string;
  lastActiveAt: string;
  verificationStatus: "verified" | "pending" | "rejected";
  flaggedReports: number;
  // Athlete specific
  programsEnrolled?: number;
  totalSpent?: number;
  // Trainer specific
  programsCreated?: number;
  totalEarnings?: number;
  averageRating?: number;
  totalReviews?: number;
  specializations?: string[];
}

export interface AdminProgram {
  id: string;
  name: string;
  description: string;
  trainer: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  status: "pending" | "approved" | "rejected" | "published";
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  category: string;
  duration: string;
  difficulty: string;
  price: {
    oneTime?: number;
    monthly?: number;
  };
  flaggedContent: string[];
  moderationNotes: string;
}

export interface RefundRequest {
  id: string;
  athlete: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  program: {
    id: string;
    name: string;
    trainer: {
      id: string;
      firstName: string;
      lastName: string;
    };
  };
  transaction: {
    id: string;
    amount: number;
    currency: string;
    paymentMethod: string;
    processedAt: string;
  };
  refundAmount: number;
  reason: string;
  status: "pending" | "approved" | "rejected" | "processed";
  requestedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  adminNotes: string;
  eligibilityCheck: {
    isEligible: boolean;
    daysFromPurchase: number;
    refundPolicy: string;
    programProgress: string;
  };
}

export interface PaginationResponse {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// API Response Types
export interface DashboardStatsResponse {
  success: boolean;
  data: DashboardStats;
}

export interface AlertsResponse {
  success: boolean;
  data: {
    alerts: Alert[];
    totalUnread: number;
    lastChecked: string;
  };
}

export interface UsersListResponse {
  success: boolean;
  data: {
    athletes?: AdminUser[];
    trainers?: AdminUser[];
    pagination: PaginationResponse;
  };
}

export interface ProgramsListResponse {
  success: boolean;
  data: {
    programs: AdminProgram[];
    pagination: PaginationResponse;
  };
}

export interface RefundsListResponse {
  success: boolean;
  data: {
    refunds: RefundRequest[];
    pagination: PaginationResponse;
    summary: {
      totalPendingAmount: number;
      averageProcessingTime: string;
      approvalRate: number;
    };
  };
}

// Request Types
export interface ProgramReviewRequest {
  action: "approve" | "reject";
  notes: string;
  flaggedContent?: string[];
  requiresChanges?: boolean;
  publicationDate?: string;
}

export interface RefundProcessRequest {
  action: "approve" | "reject";
  refundAmount: number;
  adminNotes: string;
  refundMethod: "original_payment" | "store_credit";
  processingFee?: number;
}

export interface UserStatusUpdateRequest {
  status: "active" | "suspended" | "banned";
  reason: string;
  duration?: string;
  notifyUser: boolean;
  adminNotes: string;
}

// ============================================================================
// API ENDPOINT SPECIFICATIONS
// ============================================================================

export const AdminDashboardAPIs = {
  // 1. GET DASHBOARD STATISTICS
  GET_DASHBOARD_STATS: {
    url: "/v2/api/kanglink/custom/admin/dashboard/stats",
    method: "GET",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    queryParams: {
      period: "string (optional, default: '30d')",
      timezone: "string (optional)",
    },
    response: "DashboardStatsResponse",
  },

  // 2. GET DASHBOARD ALERTS
  GET_DASHBOARD_ALERTS: {
    url: "/v2/api/kanglink/custom/admin/dashboard/alerts",
    method: "GET",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    queryParams: {
      priority: "string (optional)",
      type: "string (optional)",
      limit: "number (optional, default: 10)",
    },
    response: "AlertsResponse",
  },

  // 3. GET ATHLETES LIST
  GET_ATHLETES: {
    url: "/v2/api/kanglink/custom/admin/athletes",
    method: "GET",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    queryParams: {
      page: "number (optional, default: 1)",
      limit: "number (optional, default: 20)",
      search: "string (optional)",
      status: "string (optional)",
      joinedAfter: "string (optional)",
      sortBy: "string (optional, default: 'createdAt')",
      sortOrder: "string (optional, default: 'desc')",
    },
    response: "UsersListResponse",
  },

  // 4. GET TRAINERS LIST
  GET_TRAINERS: {
    url: "/v2/api/kanglink/custom/admin/trainers",
    method: "GET",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    queryParams: {
      page: "number (optional, default: 1)",
      limit: "number (optional, default: 20)",
      search: "string (optional)",
      status: "string (optional)",
      rating: "number (optional)",
      sortBy: "string (optional, default: 'createdAt')",
      sortOrder: "string (optional, default: 'desc')",
    },
    response: "UsersListResponse",
  },

  // 5. GET PROGRAMS LIST (CONTENT MODERATION)
  GET_PROGRAMS: {
    url: "/v2/api/kanglink/custom/admin/programs",
    method: "GET",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    queryParams: {
      page: "number (optional, default: 1)",
      limit: "number (optional, default: 20)",
      status: "string (optional)",
      trainerId: "string (optional)",
      search: "string (optional)",
      submittedAfter: "string (optional)",
      sortBy: "string (optional, default: 'submittedAt')",
      sortOrder: "string (optional, default: 'desc')",
    },
    response: "ProgramsListResponse",
  },

  // 6. GET REFUND REQUESTS
  GET_REFUNDS: {
    url: "/v2/api/kanglink/custom/admin/refunds",
    method: "GET",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    queryParams: {
      page: "number (optional, default: 1)",
      limit: "number (optional, default: 20)",
      status: "string (optional)",
      requestedAfter: "string (optional)",
      amountMin: "number (optional)",
      amountMax: "number (optional)",
      sortBy: "string (optional, default: 'requestedAt')",
      sortOrder: "string (optional, default: 'desc')",
    },
    response: "RefundsListResponse",
  },

  // 7. APPROVE/REJECT PROGRAM
  REVIEW_PROGRAM: {
    url: "/v2/api/kanglink/custom/admin/programs/{programId}/review",
    method: "POST",
    params: {
      programId: "string",
    },
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    body: "ProgramReviewRequest",
    response: {
      success: "boolean",
      data: {
        programId: "string",
        status: "string",
        reviewedAt: "string",
        reviewedBy: "string",
        notes: "string",
        notificationSent: "boolean",
      },
    },
  },

  // 8. PROCESS REFUND REQUEST
  PROCESS_REFUND: {
    url: "/v2/api/kanglink/custom/admin/refunds/{refundId}/process",
    method: "POST",
    params: {
      refundId: "string",
    },
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    body: "RefundProcessRequest",
    response: {
      success: "boolean",
      data: {
        refundId: "string",
        status: "string",
        refundAmount: "number",
        processingFee: "number",
        netRefund: "number",
        processedAt: "string",
        processedBy: "string",
        estimatedRefundDate: "string",
        transactionId: "string",
      },
    },
  },

  // 9. UPDATE USER STATUS
  UPDATE_USER_STATUS: {
    url: "/v2/api/kanglink/custom/admin/users/{userId}/status",
    method: "POST",
    params: {
      userId: "string",
    },
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    body: "UserStatusUpdateRequest",
    response: {
      success: "boolean",
      data: {
        userId: "string",
        previousStatus: "string",
        newStatus: "string",
        updatedAt: "string",
        updatedBy: "string",
        suspensionEndsAt: "string (optional)",
        notificationSent: "boolean",
      },
    },
  },
};
