import React, { useState } from "react";
import { StarIcon } from "@heroicons/react/24/solid";

interface StarRatingProps {
  rating: number;
  maxRating?: number;
  size?: "sm" | "md" | "lg";
  showRating?: boolean;
  className?: string;
  interactive?: boolean;
  onRatingChange?: (rating: number) => void;
}

const StarRating: React.FC<StarRatingProps> = ({
  rating,
  maxRating = 5,
  size = "md",
  showRating = false,
  className = "",
  interactive = false,
  onRatingChange,
}) => {
  const [hoverRating, setHoverRating] = useState<number>(0);
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6",
  };

  const handleStarClick = (starIndex: number) => {
    if (interactive && onRatingChange) {
      onRatingChange(starIndex + 1);
    }
  };

  const handleStarHover = (starIndex: number) => {
    if (interactive) {
      setHoverRating(starIndex + 1);
    }
  };

  const handleMouseLeave = () => {
    if (interactive) {
      setHoverRating(0);
    }
  };

  const displayRating = interactive && hoverRating > 0 ? hoverRating : rating;

  const renderStars = () => {
    return Array.from({ length: maxRating }, (_, index) => (
      <StarIcon
        key={index}
        className={`${sizeClasses[size]} ${
          index < Math.floor(displayRating)
            ? "text-primary"
            : "text-text-disabled"
        } ${
          interactive
            ? "cursor-pointer hover:text-primary transition-colors duration-150"
            : ""
        }`}
        onClick={() => handleStarClick(index)}
        onMouseEnter={() => handleStarHover(index)}
      />
    ));
  };

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <div
        className="flex items-center gap-0.5"
        onMouseLeave={handleMouseLeave}
      >
        {renderStars()}
      </div>
      {showRating && (
        <span className="text-sm text-text-secondary ml-1">
          ({rating.toFixed(1)})
        </span>
      )}
    </div>
  );
};

export default StarRating;
