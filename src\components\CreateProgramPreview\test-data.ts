// Test data with snake_case naming convention for CreateProgramPreview component
export const testStepOneData = {
  program_name: "Advanced Strength Training Program",
  type_of_program: "Strength Training",
  program_description: "A comprehensive 12-week strength training program designed for intermediate to advanced athletes. This program focuses on compound movements and progressive overload to maximize strength gains.",
  payment_plan: ["monthly"],
  track_progress: true,
  allow_comments: true,
  allow_private_messages: false,
  target_levels: ["intermediate", "expert"],
  split_program: 2,
  currency: "USD",
  days_for_preview: 5,
  splits: [
    {
      title: "Upper/Lower Split",
      full_price: 150,
      subscription: 25,
      split_id: "split-1",
    },
    {
      title: "Push/Pull/Legs Split",
      full_price: 200,
      subscription: 30,
      split_id: "split-2",
    },
  ],
};

export const testStepTwoData = {
  program_split: "split-1",
  description: "Detailed program structure with progressive overload principles",
  equipment_required: "Barbell, Dumbbells, Pull-up bar, Bench, Squat rack",
  splitConfigurations: {
    "split-1": [
      {
        id: "week-1",
        name: "Foundation Week",
        days: [
          {
            id: "day-1",
            name: "Upper Body",
            is_rest_day: false,
            sessions: [
              {
                id: "session-1",
                name: "Compound Upper",
                exercises: [
                  {
                    id: "exercise-1",
                    name: "Bench Press",
                    sets: "4",
                    reps_or_time: "8-10",
                    reps_time_type: "reps" as const,
                    video_url: "https://example.com/bench-press",
                    exercise_details: "Focus on controlled movement and full range of motion",
                    rest_duration_minutes: 2,
                    rest_duration_seconds: 30,
                    label: "A",
                    label_number: "1",
                    is_linked: false,
                    exercise_order: 1,
                  },
                  {
                    id: "exercise-2",
                    name: "Incline Dumbbell Press",
                    sets: "3",
                    reps_or_time: "10-12",
                    reps_time_type: "reps" as const,
                    video_url: "https://example.com/incline-press",
                    exercise_details: "45-degree incline, control the negative",
                    rest_duration_minutes: 2,
                    rest_duration_seconds: 0,
                    label: "A",
                    label_number: "2",
                    is_linked: true,
                    exercise_order: 2,
                  },
                  {
                    id: "exercise-3",
                    name: "Pull-ups",
                    sets: "3",
                    reps_or_time: "6-8",
                    reps_time_type: "reps" as const,
                    video_url: "https://example.com/pullups",
                    exercise_details: "Full dead hang to chin over bar",
                    rest_duration_minutes: 3,
                    rest_duration_seconds: 0,
                    label: "B",
                    label_number: "1",
                    is_linked: false,
                    exercise_order: 3,
                  },
                ],
              },
            ],
          },
          {
            id: "day-2",
            name: "Lower Body",
            is_rest_day: false,
            sessions: [
              {
                id: "session-2",
                name: "Compound Lower",
                exercises: [
                  {
                    id: "exercise-4",
                    name: "Back Squat",
                    sets: "4",
                    reps_or_time: "6-8",
                    reps_time_type: "reps" as const,
                    video_url: "https://example.com/squat",
                    exercise_details: "Full depth squat, maintain neutral spine",
                    rest_duration_minutes: 3,
                    rest_duration_seconds: 0,
                    label: "A",
                    label_number: "1",
                    is_linked: false,
                    exercise_order: 1,
                  },
                  {
                    id: "exercise-5",
                    name: "Romanian Deadlift",
                    sets: "3",
                    reps_or_time: "8-10",
                    reps_time_type: "reps" as const,
                    video_url: "https://example.com/rdl",
                    exercise_details: "Hip hinge movement, feel stretch in hamstrings",
                    rest_duration_minutes: 2,
                    rest_duration_seconds: 30,
                    label: "B",
                    label_number: "1",
                    is_linked: false,
                    exercise_order: 2,
                  },
                ],
              },
            ],
          },
          {
            id: "day-3",
            name: "Rest Day",
            is_rest_day: true,
            sessions: [],
          },
        ],
      },
    ],
    "split-2": [
      {
        id: "week-2",
        name: "Intensity Week",
        days: [
          {
            id: "day-4",
            name: "Push Day",
            is_rest_day: false,
            sessions: [
              {
                id: "session-3",
                name: "Push Movements",
                exercises: [
                  {
                    id: "exercise-6",
                    name: "Overhead Press",
                    sets: "4",
                    reps_or_time: "6-8",
                    reps_time_type: "reps" as const,
                    video_url: "https://example.com/ohp",
                    exercise_details: "Strict press, no leg drive",
                    rest_duration_minutes: 3,
                    rest_duration_seconds: 0,
                    label: "A",
                    label_number: "1",
                    is_linked: false,
                    exercise_order: 1,
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};
