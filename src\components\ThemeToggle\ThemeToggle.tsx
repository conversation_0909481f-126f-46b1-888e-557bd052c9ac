import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { SunIcon, MoonIcon } from "@heroicons/react/24/outline";
import { Theme } from "@/utils/Enums";

interface ThemeToggleProps {
  className?: string;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ className = "" }) => {
  const { state, dispatch, TOGGLE_THEME } = useTheme();
  const isDark = state.theme === Theme.DARK;
  const mode = state?.theme;

  const toggleTheme = () => {
    dispatch({ type: TOGGLE_THEME });
  };

  const buttonStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
    borderColor: THEME_COLORS[mode].BORDER,
    color: THEME_COLORS[mode].TEXT_SECONDARY,
  };

  const iconStyles = {
    color: isDark
      ? THEME_COLORS[mode].PRIMARY
      : THEME_COLORS[mode].TEXT_SECONDARY,
  };

  return (
    <button
      onClick={toggleTheme}
      className={`flex items-center justify-center rounded-lg p-2 border transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 ${className}`}
      style={buttonStyles}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor =
          THEME_COLORS[mode].BACKGROUND_HOVER;
        e.currentTarget.style.borderColor = THEME_COLORS[mode].PRIMARY;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor =
          THEME_COLORS[mode].BACKGROUND_SECONDARY;
        e.currentTarget.style.borderColor = THEME_COLORS[mode].BORDER;
      }}
      onFocus={(e) => {
        e.currentTarget.style.boxShadow = `0 0 0 2px ${THEME_COLORS[mode].PRIMARY}40`;
      }}
      onBlur={(e) => {
        e.currentTarget.style.boxShadow = "none";
      }}
      aria-label={`Switch to ${isDark ? "light" : "dark"} mode`}
      title={`Switch to ${isDark ? "light" : "dark"} mode`}
    >
      {isDark ? (
        <SunIcon
          className="h-5 w-5 transition-colors duration-200"
          style={iconStyles}
        />
      ) : (
        <MoonIcon
          className="h-5 w-5 transition-colors duration-200"
          style={iconStyles}
        />
      )}
    </button>
  );
};

export default ThemeToggle;
