export interface TrainerAthleteFilters {
  athlete_name: string;
  payment_type: string;
  program_name: string;
  progress: string;
}

export interface TrainerAthleteSorting {
  sort_by: string;
  sort_order: string;
}

export interface TrainerAthleteData {
  enrollment_id: number;
  athlete_id: number;
  athlete_name: string;
  athlete_email: string;
  athlete_photo: string;
  program_id: number;
  program_name: string;
  split_id: number;
  split_title: string;
  payment_type: string;
  payment_type_display: string;
  amount: number;
  currency: string;
  enrollment_date: string;
  enrollment_status: string;
  payment_status: string;
  progress_percentage: number;
  progress_status: string;
  total_days_completed: number;
  total_exercises_completed: number;
  last_activity_date: string;
}

export interface TrainerAthletePagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}
