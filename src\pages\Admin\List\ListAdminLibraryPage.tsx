import React, { useState, useEffect, useMemo } from "react";
import { useContexts } from "@/hooks/useContexts";
import {
  LibraryFilters,
  ExerciseTable,
  VideoPlayerModal,
  ExerciseModal,
  DeleteConfirmationModal,
} from "@/components/Library";
import { useLibrary } from "@/hooks/useLibrary/useLibrary";
import { Exercise } from "@/interfaces";

const ListAdminLibraryPage: React.FC = () => {
  const { globalDispatch } = useContexts();

  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [dateFilter, setDateFilter] = useState("Select date");
  const [typeFilter, setTypeFilter] = useState("Search video");
  const [currentExercisePage, setCurrentExercisePage] = useState(1);
  const [pageSize] = useState(4); // Match the design showing 4 items per table

  // Modal state
  const [isVideoPlayerModalOpen, setIsVideoPlayerModalOpen] = useState(false);
  const [isExerciseModalOpen, setIsExerciseModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedExercise, setSelectedExercise] = useState<Exercise | null>(
    null
  );
  const [exerciseToDelete, setExerciseToDelete] = useState<Exercise | null>(
    null
  );
  const [isDeletingExercise, setIsDeletingExercise] = useState(false);

  // Exercise library hook
  const {
    adminLibraryData: exerciseData,
    isLoading: isLoadingExercises,
    error: exerciseError,
    adminPagination: exercisePagination,
    updatePagination: updateExercisePagination,
    searchLibraryItems: searchExercises,
    deleteLibraryItem: deleteExercise,
  } = useLibrary({
    libraryType: "exercise",
    initialPagination: {
      page: currentExercisePage,
      limit: pageSize,
    },
    disable: {
      type_two: true,
      type_one: false,
    },
  });

  // Set path in global state for navigation highlighting
  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "library",
      },
    });
  }, [globalDispatch]);

  // Transform Exercise data to LibraryExercise format
  const exercises: Exercise[] = useMemo(() => {
    if (!exerciseData) return [];

    return exerciseData.map((exercise: Exercise) => ({
      id: exercise.id as number,
      name: exercise.name || "Unknown Exercise",
      exercise_type: exercise.exercise_type,
      video_url: exercise.video_url,
      created_at: exercise.created_at
        ? new Date(exercise.created_at).toISOString().split("T")[0]
        : "Unknown",
    }));
  }, [exerciseData]);

  // Transform Video data to Video format
  // const videos: Video[] = useMemo(() => {
  //   if (!videoData) return [];

  //   return videoData.map((video: Video) => ({
  //     id: video.id as number,
  //     name: video.name || "Unknown Video",
  //     video_type: video.video_type,
  //     created_at: video.created_at
  //       ? new Date(video.created_at).toISOString().split("T")[0]
  //       : "Unknown",
  //     status: video.url ? ("Open" as const) : ("Add Video" as const),
  //     url: video.url,
  //   }));
  // }, [videoData]);

  // Pagination info from API response
  const totalExercisePages = exercisePagination.num_pages;
  // const totalVideoPages = videoPagination.num_pages;

  // Update pagination when page changes
  useEffect(() => {
    updateExercisePagination({ page: currentExercisePage });
  }, [currentExercisePage, updateExercisePagination]);

  // useEffect(() => {
  //   updateVideoPagination({ page: currentVideoPage });
  // }, [currentVideoPage, updateVideoPagination]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentExercisePage(1);
  }, [searchTerm, typeFilter, dateFilter]);

  const handleApplyFilter = () => {
    // Reset to first page when applying filters
    setCurrentExercisePage(1);
    // setCurrentVideoPage(1);

    // Apply search filter if search term exists
    if (searchTerm.trim()) {
      searchExercises(searchTerm.trim());
      // searchVideos(searchTerm.trim());
    } else {
      searchExercises("");
    }
  };

  const handleDeleteExercise = (exerciseId: number | string) => {
    const exercise = exerciseData?.find((e: Exercise) => e.id === exerciseId);
    if (exercise) {
      setExerciseToDelete(exercise);
      setIsDeleteModalOpen(true);
    }
  };

  const handleEditExercise = (exerciseId: number | string) => {
    const exercise = exerciseData?.find((e: Exercise) => e.id === exerciseId);
    if (exercise) {
      setSelectedExercise(exercise);
      setIsExerciseModalOpen(true);
    }
  };

  const handleExerciseVideoAction = (exerciseId: number | string) => {
    const exercise = exercises.find((e) => e.id === exerciseId);
    const fullExercise = exerciseData?.find(
      (e: Exercise) => e.id === exerciseId
    );
    if (exercise && fullExercise) {
      if (exercise.video_url) {
        // Open video player modal with the video URL
        setIsVideoPlayerModalOpen(true);
      } else {
        // Open exercise modal to add a video URL
        setSelectedExercise(fullExercise);
        setIsExerciseModalOpen(true);
      }
    }
  };

  const handleAddExercise = () => {
    setSelectedExercise(null);
    setIsExerciseModalOpen(true);
  };

  // Modal handlers

  const handleCloseVideoPlayerModal = () => {
    setIsVideoPlayerModalOpen(false);
  };

  // Exercise modal handlers
  const handleCloseExerciseModal = () => {
    setIsExerciseModalOpen(false);
    setSelectedExercise(null);
  };

  const handleExerciseSuccess = () => {
    // Refresh exercise data after successful create/update
    // Note: refreshExercises is not available, data will refresh automatically via React Query
  };

  // Delete confirmation handlers
  const handleCloseDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setExerciseToDelete(null);
  };

  const handleConfirmDelete = async () => {
    // Delete exercise
    if (exerciseToDelete && exerciseToDelete.id) {
      setIsDeletingExercise(true);
      try {
        await deleteExercise(exerciseToDelete.id);
        // Data will be refreshed automatically by React Query
        handleCloseDeleteModal();
      } catch (error) {
        console.error("Failed to delete exercise:", error);
      } finally {
        setIsDeletingExercise(false);
      }
    }
  };

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "content libraries",
      },
    });
  }, [globalDispatch]);

  return (
    <div className="w-full bg-background p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h1 className="text-2xl font-bold text-text">Library Management</h1>
        </div>

        {/* Filters Section */}
        <LibraryFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          dateFilter={dateFilter}
          setDateFilter={setDateFilter}
          typeFilter={typeFilter}
          setTypeFilter={setTypeFilter}
          onApplyFilter={handleApplyFilter}
        />

        {/* Exercises Table Section */}
        {isLoadingExercises ? (
          <div className="bg-background border border-border rounded-lg shadow-sm p-8 text-center">
            <p className="text-text">Loading exercises...</p>
          </div>
        ) : exerciseError ? (
          <div className="bg-background border border-border rounded-lg shadow-sm p-8 text-center">
            <p className="text-red-600">
              Error loading exercises. Please try again.
            </p>
          </div>
        ) : (
          <ExerciseTable
            exercises={exercises}
            currentPage={currentExercisePage}
            totalPages={totalExercisePages}
            pageSize={pageSize}
            onPageChange={setCurrentExercisePage}
            onDeleteExercise={handleDeleteExercise}
            onEditExercise={handleEditExercise}
            onVideoAction={handleExerciseVideoAction}
            onAddExercise={handleAddExercise}
          />
        )}

        {/* Videos Table Section */}
        {/* {isLoadingVideos ? (
          <div className="bg-background border border-border rounded-lg shadow-sm p-8 text-center">
            <p className="text-text">Loading videos...</p>
          </div>
        ) : videoError ? (
          <div className="bg-background border border-border rounded-lg shadow-sm p-8 text-center">
            <p className="text-red-600">
              Error loading videos. Please try again.
            </p>
          </div>
        ) : (
          <VideoTable
            videos={videos}
            currentPage={currentVideoPage}
            totalPages={totalVideoPages}
            pageSize={pageSize}
            onPageChange={setCurrentVideoPage}
            onDeleteVideo={handleDeleteVideo}
            onEditVideo={handleEditVideo}
            onVideoAction={handleVideoAction}
            onAddVideo={handleAddVideo}
          />
        )} */}

        {/* Video Modal */}
        {/* <VideoModal
          isOpen={isVideoModalOpen}
          onClose={handleCloseVideoModal}
          video={selectedVideo}
          onSuccess={handleVideoSuccess}
        /> */}

        {/* Video Player Modal */}
        <VideoPlayerModal
          isOpen={isVideoPlayerModalOpen}
          onClose={handleCloseVideoPlayerModal}
          data={{
            video_url: selectedExercise?.video_url || "",
            name: selectedExercise?.name || "",
          }}
        />

        {/* Exercise Modal */}
        <ExerciseModal
          isOpen={isExerciseModalOpen}
          onClose={handleCloseExerciseModal}
          exercise={selectedExercise}
          onSuccess={handleExerciseSuccess}
        />

        {/* Delete Confirmation Modal */}
        <DeleteConfirmationModal
          isOpen={isDeleteModalOpen}
          onClose={handleCloseDeleteModal}
          onConfirm={handleConfirmDelete}
          title={"Delete Exercise"}
          message={"Are you sure you want to delete this exercise?"}
          itemName={exerciseToDelete?.name}
          isLoading={isDeletingExercise}
        />
      </div>
    </div>
  );
};

export default ListAdminLibraryPage;
