import { useState } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { ChevronDownIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { WorkoutWeek } from "@/assets/data/program_data";
import { DayCard } from "@/components/DayCard";

interface WeekCardProps {
  week: WorkoutWeek;
  onExerciseComplete: (exerciseId: string) => void;
}

const WeekCard = ({ week, onExerciseComplete }: WeekCardProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [isCollapsed, setIsCollapsed] = useState(week.isCollapsed || false);

  const cardStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  const headerStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div
      className="bg-background border border-border rounded-lg shadow-sm mb-4 transition-colors duration-200"
      style={cardStyles}
    >
      {/* Week Header */}
      <button
        onClick={toggleCollapse}
        className="w-full p-4 sm:p-6 text-left transition-colors duration-200 hover:bg-background-secondary rounded-t-lg"
        style={headerStyles}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              {isCollapsed ? (
                <ChevronRightIcon className="w-5 h-5 text-text-secondary" />
              ) : (
                <ChevronDownIcon className="w-5 h-5 text-text-secondary" />
              )}
            </div>
            <div>
              <h2 className="text-xl font-bold text-text">
                Week {week.weekNumber}
              </h2>
              <p className="text-base text-text-secondary mt-1">{week.name}</p>
            </div>
          </div>

          {/* Week Stats */}
          <div className="flex items-center gap-4 text-sm text-text-secondary">
            <span>{week.days.length} Days</span>
            <span>
              {week.days.reduce((total, day) => total + day.sessions.length, 0)}{" "}
              Sessions
            </span>
          </div>
        </div>
      </button>

      {/* Week Content */}
      {!isCollapsed && (
        <div className="p-4 sm:p-6 pt-0 space-y-4">
          {week.days.map((day) => (
            <DayCard
              key={day.id}
              day={day}
              weekNumber={week.weekNumber}
              onExerciseComplete={onExerciseComplete}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default WeekCard;
