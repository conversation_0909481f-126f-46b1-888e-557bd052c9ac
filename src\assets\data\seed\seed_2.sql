
CREATE TABLE kanglink_qualification (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type TINYINT NOT NULL CHECK (type IN (1, 2)), -- 1 or 2 as specified
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE kanglink_specialization (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type TINYINT NOT NULL CHECK (type IN (1, 2)), -- 1 or 2 as specified
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Seed data for qualifications
INSERT INTO kanglink_qualification (name, type, user_id) VALUES
('Certified Personal Trainer', 1, 1),
('Group Fitness Instructor Certification', 1, 1),
('Nutrition and Dietetics Certification', 1, 1),
('Sports Medicine', 1, 1),
('Corrective Exercise Specialist', 1, 1),
('Youth Fitness', 1, 1),
('Senior Fitness', 1, 1),
('Functional Movement', 1, 1);

-- Seed data for specializations
INSERT INTO kanglink_specialization (name, type, user_id) VALUES
('Body Building', 1, 1),
('Endurance Training', 1, 1),
('Strength Training', 1, 1),
('Functional Fitness', 1, 1),
('Cross Fit', 1, 1),
('Yoga', 1, 1),
('Calisthenics', 1, 1),
('Pilates', 1, 1);
