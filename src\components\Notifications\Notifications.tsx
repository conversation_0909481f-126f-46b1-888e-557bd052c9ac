import { useState, useEffect } from "react";
import NotificationItem from "./NotificationItem";
import NotificationService, { Notification, NotificationResponse } from "../../utils/NotificationService";
import MkdSDK from "../../utils/MkdSDK";

interface NotificationsProps {
  setSidebar: (sidebar: boolean) => void;
}

const Notifications = ({ setSidebar }: NotificationsProps) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [unreadCount, setUnreadCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasNext, setHasNext] = useState(false);
  const [hasPrev, setHasPrev] = useState(false);
  const [totalPages, setTotalPages] = useState(1);

  const sdk = new MkdSDK();
  const notificationService = new NotificationService(sdk);

  useEffect(() => {
    fetchNotifications();
    fetchUnreadCount();
  }, [currentPage]);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response: NotificationResponse = await notificationService.getNotifications({
        page: currentPage,
        limit: 10,
        unread_only: false,
      });

      setNotifications(response.notifications);
      setHasNext(response.pagination.has_next);
      setHasPrev(response.pagination.has_prev);
      setTotalPages(response.pagination.total_pages);
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  const fetchUnreadCount = async () => {
    try {
      const count = await notificationService.getUnreadCount();
      setUnreadCount(count);
    } catch (err) {
      console.error('Error fetching unread count:', err);
    }
  };

  const handleMarkAsRead = async (notificationId: number) => {
    try {
      await notificationService.markAsRead(notificationId);
      
      // Update the notification in the list
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, is_read: true }
            : notification
        )
      );
      
      // Update unread count
      fetchUnreadCount();
    } catch (err) {
      console.error('Error marking notification as read:', err);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await notificationService.markAllAsRead();
      
      // Update all notifications to read
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, is_read: true }))
      );
      
      // Update unread count
      setUnreadCount(0);
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
    }
  };

  const handleNextPage = () => {
    if (hasNext) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (hasPrev) {
      setCurrentPage(prev => prev - 1);
    }
  };

  if (loading && notifications.length === 0) {
    return (
      <div>
        <div className="flex items-center p-3 gap-4 border-b border-b-[#E0E0E0] justify-between">
          <div className="flex items-center gap-3">
            <svg
              onClick={() => setSidebar(false)}
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218"
                stroke="#A8A8A8"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <span className="text-lg font-semibold">
              Notifications <sup className="text-gray-400">({unreadCount})</sup>
            </span>
          </div>
        </div>
        <div className="p-4">
          <div className="animate-pulse space-y-3">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="flex items-start py-5 px-2 gap-3">
                <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center p-3 gap-4 border-b border-b-[#E0E0E0] justify-between">
        <div className="flex items-center gap-3">
          <svg
            onClick={() => setSidebar(false)}
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218"
              stroke="#A8A8A8"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="text-lg font-semibold">
            Notifications <sup className="text-gray-400">({unreadCount})</sup>
          </span>
        </div>
        {unreadCount > 0 && (
          <button
            onClick={handleMarkAllAsRead}
            className="text-sm text-blue-600 hover:text-blue-800 underline"
          >
            Mark all as read
          </button>
        )}
      </div>
      
      {error && (
        <div className="p-4">
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-red-800 text-sm">{error}</p>
            <button
              onClick={fetchNotifications}
              className="text-red-600 hover:text-red-800 underline text-sm mt-2"
            >
              Try again
            </button>
          </div>
        </div>
      )}

      <div className="p-4 space-y-3">
        {notifications.length === 0 && !loading ? (
          <div className="text-center py-8">
            <div className="text-gray-400 text-6xl mb-4">📢</div>
            <p className="text-gray-500">No notifications yet</p>
            <p className="text-gray-400 text-sm">You'll see notifications here when you have updates</p>
          </div>
        ) : (
          notifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onMarkAsRead={handleMarkAsRead}
            />
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between p-4 border-t border-gray-200">
          <button
            onClick={handlePrevPage}
            disabled={!hasPrev}
            className={`px-3 py-1 rounded ${
              hasPrev
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
          >
            Previous
          </button>
          <span className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </span>
          <button
            onClick={handleNextPage}
            disabled={!hasNext}
            className={`px-3 py-1 rounded ${
              hasNext
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

export default Notifications;
