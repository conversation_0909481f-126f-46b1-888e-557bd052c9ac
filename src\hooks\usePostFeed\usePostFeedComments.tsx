import { usePostFeedActions } from "./usePostFeedActions";

interface UsePostFeedCommentsProps {
  postId: string;
  onCommentAdded?: (comment: any) => void;
  onCommentDeleted?: (commentId: string) => void;
}

export const usePostFeedComments = ({
  postId,
  onCommentAdded,
  onCommentDeleted,
}: UsePostFeedCommentsProps) => {
  const {
    addComment,
    deleteComment,
    fetchComments,
    comments,
    loading,
    setComments,
  } = usePostFeedActions({
    postId,
    onCommentAdded,
    onCommentDeleted,
  });

  return {
    // Actions
    addComment,
    deleteComment,
    fetchComments,

    // State
    comments,
    setComments,

    // Loading states
    isAddingComment: loading.isAddingComment,
    isDeletingComment: loading.isDeletingComment,
    isFetchingComments: loading.isFetchingComments,
  };
}; 