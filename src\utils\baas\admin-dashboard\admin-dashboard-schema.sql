-- ============================================================================
-- ADMIN DASHBOARD DATABASE SCHEMA
-- ============================================================================

-- Users table (extends existing user table for admin functionality)
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(255) PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('athlete', 'trainer', 'admin', 'super_admin') NOT NULL DEFAULT 'athlete',
    status ENUM('active', 'inactive', 'suspended', 'banned', 'pending') NOT NULL DEFAULT 'active',
    profile_image VARCHAR(500),
    verification_status ENUM('verified', 'pending', 'rejected') NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_active_at TIMESTAMP,
    suspended_until TIMESTAMP NULL,
    suspension_reason TEXT,
    admin_notes TEXT,
    
    -- Athlete specific fields
    programs_enrolled INT DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0.00,
    
    -- Trainer specific fields
    programs_created INT DEFAULT 0,
    total_earnings DECIMAL(10,2) DEFAULT 0.00,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INT DEFAULT 0,
    specializations JSON,
    
    -- Common fields
    flagged_reports INT DEFAULT 0,
    
    INDEX idx_users_role (role),
    INDEX idx_users_status (status),
    INDEX idx_users_created_at (created_at),
    INDEX idx_users_last_active (last_active_at),
    INDEX idx_users_verification (verification_status)
);

-- Programs table (for content moderation)
CREATE TABLE IF NOT EXISTS programs (
    id VARCHAR(255) PRIMARY KEY,
    trainer_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    difficulty ENUM('beginner', 'intermediate', 'advanced') NOT NULL,
    duration VARCHAR(50),
    status ENUM('draft', 'pending', 'approved', 'rejected', 'published') NOT NULL DEFAULT 'draft',
    submitted_at TIMESTAMP NULL,
    reviewed_at TIMESTAMP NULL,
    reviewed_by VARCHAR(255) NULL,
    moderation_notes TEXT,
    flagged_content JSON,
    requires_changes BOOLEAN DEFAULT FALSE,
    publication_date TIMESTAMP NULL,
    price_one_time DECIMAL(10,2),
    price_monthly DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (trainer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_programs_trainer (trainer_id),
    INDEX idx_programs_status (status),
    INDEX idx_programs_submitted (submitted_at),
    INDEX idx_programs_category (category)
);

-- Transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    program_id VARCHAR(255) NOT NULL,
    trainer_id VARCHAR(255) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    payment_method VARCHAR(50) NOT NULL,
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') NOT NULL DEFAULT 'pending',
    transaction_type ENUM('one_time', 'subscription') NOT NULL,
    stripe_payment_intent_id VARCHAR(255),
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE CASCADE,
    FOREIGN KEY (trainer_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_transactions_user (user_id),
    INDEX idx_transactions_program (program_id),
    INDEX idx_transactions_trainer (trainer_id),
    INDEX idx_transactions_status (payment_status),
    INDEX idx_transactions_processed (processed_at)
);

-- Refund requests table
CREATE TABLE IF NOT EXISTS refund_requests (
    id VARCHAR(255) PRIMARY KEY,
    transaction_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    program_id VARCHAR(255) NOT NULL,
    trainer_id VARCHAR(255) NOT NULL,
    refund_amount DECIMAL(10,2) NOT NULL,
    reason TEXT NOT NULL,
    status ENUM('pending', 'approved', 'rejected', 'processed') NOT NULL DEFAULT 'pending',
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP NULL,
    reviewed_by VARCHAR(255) NULL,
    processed_at TIMESTAMP NULL,
    admin_notes TEXT,
    refund_method ENUM('original_payment', 'store_credit') DEFAULT 'original_payment',
    processing_fee DECIMAL(10,2) DEFAULT 0.00,
    net_refund DECIMAL(10,2),
    refund_transaction_id VARCHAR(255),
    estimated_refund_date TIMESTAMP NULL,
    
    -- Eligibility check fields
    days_from_purchase INT,
    refund_policy VARCHAR(100),
    program_progress VARCHAR(20),
    is_eligible BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE CASCADE,
    FOREIGN KEY (trainer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_refunds_transaction (transaction_id),
    INDEX idx_refunds_user (user_id),
    INDEX idx_refunds_status (status),
    INDEX idx_refunds_requested (requested_at)
);

-- Admin alerts table
CREATE TABLE IF NOT EXISTS admin_alerts (
    id VARCHAR(255) PRIMARY KEY,
    type ENUM('user_flagged', 'program_approval', 'refund_request', 'system') NOT NULL,
    priority ENUM('high', 'medium', 'low') NOT NULL DEFAULT 'medium',
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    count INT DEFAULT 1,
    action_url VARCHAR(500),
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    
    INDEX idx_alerts_type (type),
    INDEX idx_alerts_priority (priority),
    INDEX idx_alerts_read (is_read),
    INDEX idx_alerts_created (created_at)
);

-- User reports table (for flagged users)
CREATE TABLE IF NOT EXISTS user_reports (
    id VARCHAR(255) PRIMARY KEY,
    reported_user_id VARCHAR(255) NOT NULL,
    reporter_user_id VARCHAR(255) NOT NULL,
    reason ENUM('inappropriate_content', 'harassment', 'spam', 'fake_profile', 'other') NOT NULL,
    description TEXT,
    status ENUM('pending', 'reviewed', 'resolved', 'dismissed') NOT NULL DEFAULT 'pending',
    reviewed_by VARCHAR(255) NULL,
    reviewed_at TIMESTAMP NULL,
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (reported_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reporter_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_reports_reported_user (reported_user_id),
    INDEX idx_reports_reporter (reporter_user_id),
    INDEX idx_reports_status (status),
    INDEX idx_reports_created (created_at)
);

-- Dashboard statistics cache table (for performance)
CREATE TABLE IF NOT EXISTS dashboard_stats_cache (
    id VARCHAR(255) PRIMARY KEY,
    period VARCHAR(10) NOT NULL, -- '7d', '30d', '90d', '1y'
    stats_data JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    
    INDEX idx_stats_period (period),
    INDEX idx_stats_expires (expires_at)
);

-- Admin activity log table
CREATE TABLE IF NOT EXISTS admin_activity_log (
    id VARCHAR(255) PRIMARY KEY,
    admin_id VARCHAR(255) NOT NULL,
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(50), -- 'user', 'program', 'refund', etc.
    target_id VARCHAR(255),
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_activity_admin (admin_id),
    INDEX idx_activity_action (action),
    INDEX idx_activity_target (target_type, target_id),
    INDEX idx_activity_created (created_at)
);

-- ============================================================================
-- VIEWS FOR DASHBOARD QUERIES
-- ============================================================================

-- View for dashboard statistics
CREATE OR REPLACE VIEW dashboard_stats_view AS
SELECT 
    -- Athletes stats
    (SELECT COUNT(*) FROM users WHERE role = 'athlete') as total_athletes,
    (SELECT COUNT(*) FROM users WHERE role = 'athlete' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) as new_athletes_this_month,
    (SELECT COUNT(*) FROM users WHERE role = 'athlete' AND last_active_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) as active_athletes_this_month,
    
    -- Trainers stats
    (SELECT COUNT(*) FROM users WHERE role = 'trainer') as total_trainers,
    (SELECT COUNT(*) FROM users WHERE role = 'trainer' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) as new_trainers_this_month,
    (SELECT COUNT(*) FROM users WHERE role = 'trainer' AND last_active_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) as active_trainers_this_month,
    (SELECT COUNT(*) FROM users WHERE role = 'trainer' AND status = 'pending') as pending_trainer_approval,
    
    -- Programs stats
    (SELECT COUNT(*) FROM programs) as total_programs,
    (SELECT COUNT(*) FROM programs WHERE status = 'pending') as pending_program_approval,
    (SELECT COUNT(*) FROM programs WHERE status = 'published' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) as published_programs_this_month,
    (SELECT COUNT(*) FROM programs WHERE status = 'rejected' AND reviewed_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) as rejected_programs_this_month,
    
    -- Refunds stats
    (SELECT COUNT(*) FROM refund_requests WHERE status = 'pending') as pending_refund_requests,
    (SELECT COUNT(*) FROM refund_requests WHERE status IN ('approved', 'processed') AND reviewed_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) as processed_refunds_this_month,
    (SELECT COALESCE(SUM(refund_amount), 0) FROM refund_requests WHERE status = 'pending') as total_pending_refund_amount,
    
    -- Revenue stats
    (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE payment_status = 'completed' AND processed_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) as revenue_this_month,
    (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE payment_status = 'completed' AND processed_at >= DATE_SUB(NOW(), INTERVAL 2 MONTH) AND processed_at < DATE_SUB(NOW(), INTERVAL 1 MONTH)) as revenue_last_month;

-- ============================================================================
-- STORED PROCEDURES
-- ============================================================================

-- Procedure to update dashboard statistics cache
DELIMITER //
CREATE OR REPLACE PROCEDURE UpdateDashboardStatsCache(IN period_param VARCHAR(10))
BEGIN
    DECLARE stats_json JSON;
    DECLARE cache_id VARCHAR(255);
    DECLARE expires_time TIMESTAMP;
    
    SET cache_id = CONCAT('dashboard_stats_', period_param);
    SET expires_time = DATE_ADD(NOW(), INTERVAL 1 HOUR);
    
    -- Build stats JSON based on period
    SELECT JSON_OBJECT(
        'athletes', JSON_OBJECT(
            'total', total_athletes,
            'newThisMonth', new_athletes_this_month,
            'activeThisMonth', active_athletes_this_month
        ),
        'trainers', JSON_OBJECT(
            'total', total_trainers,
            'newThisMonth', new_trainers_this_month,
            'activeThisMonth', active_trainers_this_month,
            'pendingApproval', pending_trainer_approval
        ),
        'programs', JSON_OBJECT(
            'total', total_programs,
            'pendingApproval', pending_program_approval,
            'publishedThisMonth', published_programs_this_month,
            'rejectedThisMonth', rejected_programs_this_month
        ),
        'refunds', JSON_OBJECT(
            'pendingRequests', pending_refund_requests,
            'processedThisMonth', processed_refunds_this_month,
            'totalAmountPending', total_pending_refund_amount
        ),
        'revenue', JSON_OBJECT(
            'totalThisMonth', revenue_this_month,
            'totalLastMonth', revenue_last_month
        ),
        'lastUpdated', NOW()
    ) INTO stats_json
    FROM dashboard_stats_view;
    
    -- Insert or update cache
    INSERT INTO dashboard_stats_cache (id, period, stats_data, expires_at)
    VALUES (cache_id, period_param, stats_json, expires_time)
    ON DUPLICATE KEY UPDATE 
        stats_data = stats_json,
        created_at = NOW(),
        expires_at = expires_time;
END //
DELIMITER ;
