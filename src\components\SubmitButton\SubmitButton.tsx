import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { InteractiveButton } from "@/components/InteractiveButton";

interface SubmitButtonProps {
  label?: string;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
}

const SubmitButton = ({ 
  label = "Submit", 
  onClick, 
  disabled = false,
  loading = false,
  className = ""
}: SubmitButtonProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const buttonStyles = {
    backgroundColor: THEME_COLORS[mode].PRIMARY,
    borderColor: THEME_COLORS[mode].PRIMARY,
    color: THEME_COLORS[mode].TEXT_ON_PRIMARY,
  };

  return (
    <div className="flex justify-center py-6">
      <InteractiveButton
        onClick={onClick}
        disabled={disabled}
        loading={loading}
        className={`px-8 py-3 bg-primary hover:bg-primary-hover text-white font-semibold min-w-[200px] ${className}`}
        style={buttonStyles}
      >
        {label}
      </InteractiveButton>
    </div>
  );
};

export default SubmitButton;
