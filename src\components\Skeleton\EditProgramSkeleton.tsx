import React from "react";
import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

interface EditProgramSkeletonProps {
  /** Custom className for the container */
  className?: string;
  /** Current step being shown (1, 2, or 3) */
  currentStep?: 1 | 2 | 3;
}

const EditProgramSkeleton: React.FC<EditProgramSkeletonProps> = ({
  className = "",
  currentStep = 1,
}) => {
  const { state } = useTheme();
  const mode = state?.theme || "light";

  const renderStepOneSkeleton = () => (
    <div className="space-y-6 h-full flex flex-col">
      {/* Page Header */}
      <div className="space-y-2">
        <Skeleton height={32} width="40%" />
        <Skeleton height={16} width="60%" />
      </div>

      {/* Form Sections */}
      <div className="grow justify-center items-center grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-6">
          {Array.from({ length: 2 }).map((_, sectionIndex) => (
            <div key={sectionIndex} className="space-y-4">
              <Skeleton height={20} width="30%" />
              {Array.from({ length: 2 }).map((_, fieldIndex) => (
                <div key={fieldIndex} className="space-y-2">
                  <Skeleton height={16} width="25%" />
                  <Skeleton height={40} width="100%" />
                </div>
              ))}
            </div>
          ))}
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {Array.from({ length: 2 }).map((_, sectionIndex) => (
            <div key={sectionIndex} className="space-y-4">
              <Skeleton height={20} width="35%" />
              {Array.from({ length: 2 }).map((_, fieldIndex) => (
                <div key={fieldIndex} className="space-y-2">
                  <Skeleton height={16} width="30%" />
                  <Skeleton height={40} width="100%" />
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3">
        <Skeleton height={40} width={100} />
        <Skeleton height={40} width={100} />
      </div>
    </div>
  );

  const renderStepTwoSkeleton = () => (
    <div className="space-y-6 flex h-full flex-col">
      {/* Page Header */}
      <div className="space-y-2">
        <Skeleton height={32} width="45%" />
        <Skeleton height={16} width="55%" />
      </div>

      {/* Split Selection */}
      <div className="flex items-center gap-4">
        <Skeleton height={20} width="20%" />
        <Skeleton height={40} width="200px" />
      </div>

      {/* Program Structure */}
      <div className="space-y-4 grow">
        {Array.from({ length: 2 }).map((_, weekIndex) => (
          <div key={weekIndex} className="border border-border rounded-lg">
            {/* Week Header */}
            <div className="p-4 bg-background-secondary border-b border-border">
              <div className="flex items-center justify-between">
                <Skeleton height={20} width="25%" />
                <div className="flex gap-2">
                  <Skeleton height={32} width={80} />
                  <Skeleton height={32} width={80} />
                </div>
              </div>
            </div>

            {/* Days */}
            <div className="p-4 space-y-3">
              {Array.from({ length: 1 }).map((_, dayIndex) => (
                <div key={dayIndex} className="border border-border rounded-md">
                  <div className="p-3 bg-input border-b border-border">
                    <div className="flex items-center justify-between">
                      <Skeleton height={18} width="20%" />
                      <Skeleton height={28} width={60} />
                    </div>
                  </div>

                  {/* Sessions */}
                  <div className="p-3 space-y-2">
                    <div className="border border-border rounded">
                      <div className="p-2 bg-background-hover">
                        <div className="flex items-center justify-between">
                          <Skeleton height={16} width="18%" />
                          <Skeleton height={24} width={50} />
                        </div>
                      </div>

                      {/* Exercises */}
                      <div className="p-2 space-y-2">
                        {Array.from({ length: 1 }).map((_, exerciseIndex) => (
                          <div
                            key={exerciseIndex}
                            className="flex items-center gap-3 p-2 bg-background rounded"
                          >
                            <Skeleton height={32} width={32} />
                            <div className="flex-1 space-y-1">
                              <Skeleton height={14} width="70%" />
                              <Skeleton height={12} width="50%" />
                            </div>
                            <Skeleton height={24} width={60} />
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Skeleton height={40} width={100} />
        <div className="flex gap-3">
          <Skeleton height={40} width={100} />
          <Skeleton height={40} width={100} />
        </div>
      </div>
    </div>
  );

  const renderStepThreeSkeleton = () => (
    <div className="space-y-6 h-full flex flex-col">
      {/* Header */}
      <div className="space-y-2">
        <Skeleton height={32} width="50%" />
      </div>

      {/* Main Content Card */}
      <div className="grow bg-background rounded-lg border border-border shadow-sm">
        <div className="p-4 sm:p-6 space-y-6">
          {/* Course Details */}
          <div className="space-y-4">
            <Skeleton height={24} width="30%" />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-3">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div
                    key={index}
                    className="flex justify-between items-center"
                  >
                    <Skeleton height={16} width="40%" />
                    <Skeleton height={16} width="50%" />
                  </div>
                ))}
              </div>
              <div className="space-y-3">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div
                    key={index}
                    className="flex justify-between items-center"
                  >
                    <Skeleton height={16} width="35%" />
                    <Skeleton height={16} width="45%" />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Program Structure Preview */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Skeleton height={24} width="25%" />
              <Skeleton height={32} width="150px" />
            </div>

            {/* Condensed Structure */}
            <div className="space-y-3">
              <div className="border border-border rounded-lg">
                <div className="p-3 bg-background-secondary border-b border-border">
                  <div className="flex items-center justify-between">
                    <Skeleton height={16} width="30%" />
                    <Skeleton height={12} width={12} circle />
                  </div>
                </div>
                <div className="p-3">
                  <div className="border border-border rounded-md">
                    <div className="p-2 bg-input border-b border-border">
                      <Skeleton height={14} width="25%" />
                    </div>
                    <div className="p-2 space-y-1">
                      {Array.from({ length: 1 }).map((_, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 p-1 bg-background rounded"
                        >
                          <Skeleton height={20} width={20} />
                          <Skeleton height={10} width="60%" />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3">
        <Skeleton height={40} width={100} />
        <Skeleton height={40} width={100} />
      </div>
    </div>
  );

  return (
    <div
      className={`max-h-full h-full min-h-full py-5 overflow-hidden flex flex-col ${className}`}
    >
      <SkeletonTheme
        baseColor={THEME_COLORS[mode].TEXT_DISABLED}
        highlightColor={THEME_COLORS[mode].BACKGROUND_HOVER}
      >
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex-1 overflow-auto">
          {currentStep === 1 && renderStepOneSkeleton()}
          {currentStep === 2 && renderStepTwoSkeleton()}
          {currentStep === 3 && renderStepThreeSkeleton()}
        </div>
      </SkeletonTheme>
    </div>
  );
};

export default EditProgramSkeleton;
