# Kanglink Refund System API Documentation

## Overview

The Kanglink refund system allows athletes to request refunds for one-time program purchases within a configurable time window. The system provides a complete workflow from request submission to processing, with notifications for all stakeholders.

## Key Features

- **Time-based Eligibility**: Refunds must be requested within the payout time window (default: 24 hours)
- **One-time Purchases Only**: Only one-time payments are eligible for refunds (not subscriptions)
- **Admin Approval Workflow**: All refunds require admin approval before processing
- **Stripe Integration**: Actual refund processing through Stripe API
- **Comprehensive Notifications**: Real-time updates for athletes, trainers, and admins
- **Audit Trail**: Complete tracking of refund lifecycle

## Authentication

All endpoints require authentication via Bearer token in the Authorization header:

```
Authorization: Bearer <token>
```

## Base URL

```
https://your-domain.com/v2/api/kanglink/custom
```

---

## Athlete Endpoints

### 1. Submit Refund Request

**Endpoint:** `POST /athlete/refund/request`

**Description:** Submit a refund request for an enrollment.

**Authentication:** Required (member role)

**Request Body:**

```json
{
  "enrollment_id": 123,
  "reason": "Detailed reason for refund request (minimum 10 characters)"
}
```

**Response (Success - 201):**

```json
{
  "error": false,
  "message": "Refund request submitted successfully",
  "data": {
    "refund_request_id": 456,
    "status": "pending",
    "amount": 99.99,
    "currency": "USD",
    "requested_at": "2025-01-09T10:30:00Z"
  }
}
```

**Eligibility Requirements:**

- Enrollment must be a one-time purchase (not subscription)
- Enrollment must have "paid" payment status
- Request must be within payout time window (default: 24 hours)
- No existing pending/approved refund request for the enrollment
- Enrollment must not already be marked as refunded

### 2. Check Refund Status

**Endpoint:** `GET /athlete/refund/status/:enrollment_id`

**Description:** Check refund eligibility and current status for an enrollment.

**Authentication:** Required (member role)

**Response (Success - 200):**

```json
{
  "error": false,
  "data": {
    "enrollment": {
      "id": 123,
      "program_name": "Advanced Strength Training",
      "split_name": "Upper Body Focus",
      "amount": 99.99,
      "currency": "USD",
      "payment_type": "one_time",
      "payment_status": "paid",
      "status": "active",
      "enrollment_date": "2025-01-09T08:00:00Z"
    },
    "refund_eligibility": {
      "is_eligible": true,
      "reasons": [],
      "time_remaining_hours": 18.5,
      "payout_time_limit_hours": 24
    },
    "refund_request": null
  }
}
```

### 3. Get Refund Request History

**Endpoint:** `GET /athlete/refund/requests`

**Description:** Get athlete's refund request history with pagination.

**Authentication:** Required (member role)

**Query Parameters:**

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `status` (optional): Filter by status (pending, approved, rejected, processed)

**Response (Success - 200):**

```json
{
  "error": false,
  "data": {
    "refund_requests": [
      {
        "id": 456,
        "enrollment_id": 123,
        "amount": 99.99,
        "currency": "USD",
        "reason": "Program didn't meet expectations",
        "status": "processed",
        "requested_at": "2025-01-09T10:30:00Z",
        "processed_at": "2025-01-09T14:15:00Z",
        "admin_notes": "Approved due to technical issues",
        "refund_amount": 99.99,
        "program": {
          "name": "Advanced Strength Training",
          "split_name": "Upper Body Focus"
        },
        "enrollment": {
          "enrollment_date": "2025-01-09T08:00:00Z"
        }
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

---

## Admin Endpoints

### 1. Get All Refund Requests

**Endpoint:** `GET /admin/refund/requests`

**Description:** Get all refund requests with advanced filtering and pagination.

**Authentication:** Required (super_admin role)

**Query Parameters:**

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `status` (optional): Filter by status (pending, approved, rejected, processed)
- `trainer_name` (optional): Filter by trainer name
- `date_from` (optional): Filter by date range start (YYYY-MM-DD)
- `date_to` (optional): Filter by date range end (YYYY-MM-DD)

**Response (Success - 200):**

```json
{
  "error": false,
  "data": {
    "refund_requests": [
      {
        "id": 456,
        "enrollment_id": 123,
        "amount": 99.99,
        "currency": "USD",
        "reason": "Program didn't meet expectations",
        "status": "pending",
        "requested_at": "2025-01-09T10:30:00Z",
        "processed_at": null,
        "admin_notes": null,
        "refund_amount": null,
        "program": {
          "name": "Advanced Strength Training",
          "split_name": "Upper Body Focus"
        },
        "athlete": {
          "email": "<EMAIL>",
          "full_name": "John Doe"
        },
        "trainer": {
          "full_name": "Jane Smith"
        },
        "enrollment": {
          "enrollment_date": "2025-01-09T08:00:00Z",
          "payment_status": "paid"
        }
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

### 2. Get Refund Request Details

**Endpoint:** `GET /admin/refund/request/:id`

**Description:** Get detailed information about a specific refund request.

**Authentication:** Required (super_admin role)

**Response (Success - 200):**

```json
{
  "error": false,
  "data": {
    "id": 456,
    "enrollment_id": 123,
    "amount": 99.99,
    "currency": "USD",
    "reason": "Program didn't meet expectations",
    "status": "pending",
    "requested_at": "2025-01-09T10:30:00Z",
    "processed_at": null,
    "admin_notes": null,
    "stripe_refund_id": null,
    "refund_amount": null,
    "program": {
      "name": "Advanced Strength Training",
      "split_name": "Upper Body Focus"
    },
    "athlete": {
      "email": "<EMAIL>",
      "full_name": "John Doe"
    },
    "trainer": {
      "full_name": "Jane Smith"
    },
    "enrollment": {
      "enrollment_date": "2025-01-09T08:00:00Z",
      "payment_status": "paid",
      "stripe_payment_intent_id": "pi_1234567890"
    },
    "processed_by": null
  }
}
```

### 3. Approve or Reject Refund Request

**Endpoint:** `PUT /admin/refund/request/:id/decision`

**Description:** Approve or reject a pending refund request.

**Authentication:** Required (super_admin role)

**Request Body:**

```json
{
  "decision": "approve", // "approve" or "reject"
  "admin_notes": "Optional admin notes about the decision"
}
```

**Response (Success - 200):**

```json
{
  "error": false,
  "message": "Refund request approved successfully",
  "data": {
    "id": 456,
    "status": "approved",
    "processed_at": "2025-01-09T14:15:00Z",
    "admin_notes": "Approved due to technical issues"
  }
}
```

### 4. Process Approved Refund

**Endpoint:** `POST /admin/refund/request/:id/process`

**Description:** Execute the actual refund through Stripe for an approved request.

**Authentication:** Required (super_admin role)

**Request Body:**

```json
{
  "refund_amount": 99.99 // Optional: for partial refunds, defaults to full amount
}
```

**Response (Success - 200):**

```json
{
  "error": false,
  "message": "Refund processed successfully",
  "data": {
    "refund_request_id": 456,
    "stripe_refund_id": "re_1234567890",
    "refund_amount": 99.99,
    "currency": "USD",
    "status": "processed",
    "processed_at": "2025-01-09T14:30:00Z"
  }
}
```

---

## Error Responses

### Common Error Codes

**400 Bad Request:**

```json
{
  "error": true,
  "message": "enrollment_id and reason are required"
}
```

**401 Unauthorized:**

```json
{
  "error": true,
  "message": "Authentication required"
}
```

**403 Forbidden:**

```json
{
  "error": true,
  "message": "Access denied"
}
```

**404 Not Found:**

```json
{
  "error": true,
  "message": "Enrollment not found or you don't have access to it"
}
```

**500 Internal Server Error:**

```json
{
  "error": true,
  "message": "Failed to create refund request"
}
```

### Business Logic Errors

**Ineligible for Refund:**

```json
{
  "error": true,
  "message": "Only one-time purchases are eligible for refunds"
}
```

**Time Window Expired:**

```json
{
  "error": true,
  "message": "Refund requests must be made within 24 hours of enrollment. This enrollment is 48 hours old."
}
```

**Duplicate Request:**

```json
{
  "error": true,
  "message": "A refund request for this enrollment is already pending or approved"
}
```

---

## Workflow States

### Refund Request Status Flow

1. **pending** - Initial state when athlete submits request
2. **approved** - Admin has approved the request, ready for processing
3. **rejected** - Admin has rejected the request
4. **processed** - Refund has been executed through Stripe

### Business Rules

1. **Eligibility Requirements:**

   - Must be one-time purchase (not subscription)
   - Must have "paid" payment status
   - Must be within payout time window
   - No existing pending/approved requests
   - Enrollment not already refunded

2. **Time Window:**

   - Configurable via payout_settings table
   - Default: 24 hours from enrollment date
   - Calculated in real-time for each request

3. **Approval Process:**

   - Only super_admin can approve/reject requests
   - Admin notes are optional but recommended
   - Notifications sent to athlete and trainer

4. **Processing:**
   - Only approved requests can be processed
   - Supports partial refunds
   - Updates enrollment status to "refund"
   - Updates commission records
   - Uses database transactions for consistency

---

## Notifications

The system automatically sends notifications for:

- **Refund Requested**: Trainer receives notification when athlete requests refund
- **Refund Approved**: Athlete and trainer receive notification when admin approves
- **Refund Rejected**: Athlete receives notification when admin rejects
- **Refund Processed**: Athlete and trainer receive notification when refund is completed

---

## Database Schema

### refund_request Table

```sql
CREATE TABLE kanglink_refund_request (
  id INT AUTO_INCREMENT PRIMARY KEY,
  enrollment_id INT NOT NULL,
  athlete_id INT NOT NULL,
  trainer_id INT NOT NULL,
  program_id INT NOT NULL,
  split_id INT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) NOT NULL DEFAULT 'USD',
  reason TEXT NOT NULL,
  status ENUM('pending', 'approved', 'rejected', 'processed') NOT NULL DEFAULT 'pending',
  requested_at DATETIME NULL,
  processed_at DATETIME NULL,
  processed_by INT NULL,
  admin_notes TEXT NULL,
  stripe_refund_id VARCHAR(255) NULL,
  refund_amount DECIMAL(10,2) NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

---

## Integration Notes

### Frontend Integration

1. **Check Eligibility**: Always check refund eligibility before showing refund option
2. **Real-time Updates**: Poll refund status or implement WebSocket for real-time updates
3. **Error Handling**: Implement proper error handling for all business logic errors
4. **Time Display**: Show remaining time for refund eligibility

### Stripe Integration

- Refunds are processed through Stripe API
- Original payment method is credited
- Refund typically takes 5-10 business days to appear
- Partial refunds are supported

### Commission Impact

- Commission records are updated when refunds are processed
- Trainer earnings may be adjusted based on refund amount
- Payout calculations should account for refunded amounts

---

## Testing

### Test Scenarios

1. **Happy Path**: Submit request → Admin approves → Process refund
2. **Rejection Path**: Submit request → Admin rejects with notes
3. **Eligibility Validation**: Test all eligibility requirements
4. **Time Window**: Test requests within and outside time window
5. **Duplicate Prevention**: Test multiple requests for same enrollment
6. **Partial Refunds**: Test partial refund processing
7. **Error Handling**: Test all error scenarios

### Test Data Requirements

- Active enrollments with one-time payments
- Enrollments outside refund window
- Subscription enrollments (should be ineligible)
- Admin user accounts
- Valid Stripe payment intents

---

## Security Considerations

1. **Role-based Access**: Strict role enforcement for all endpoints
2. **Data Validation**: Comprehensive input validation
3. **Business Logic**: Robust eligibility checking
4. **Audit Trail**: Complete tracking of all actions
5. **Transaction Safety**: Database transactions for consistency
6. **Stripe Security**: Secure handling of Stripe API calls
