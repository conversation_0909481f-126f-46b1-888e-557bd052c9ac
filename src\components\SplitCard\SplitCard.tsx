import React, { useState, useEffect, useMemo, useRef } from "react";
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";
import { MkdButton } from "@/components/MkdButton";
import {
  useEnrollment,
  SplitPricing,
  DiscountPreviewResponse,
} from "@/hooks/useEnrollment";
import { useCustomerCards, StripeCard } from "@/hooks/useCustomerCards";
import { useToast } from "@/hooks/useToast";
import { StripePaymentModal, CardConfirmationModal } from "./index";
import LoginConfirmationModal from "./LoginConfirmationModal";
import { TransformedProgramData } from "@/interfaces";

interface SplitCardProps {
  splitId: number | string;
  programId: number | string;
  splitName: string;
  description: string;
  subscriptionPrice: number;
  buyPrice: number;
  paymentPlan?: string[];
  programData?: TransformedProgramData | null;
  onSubscribe?: () => void;
  onBuy?: () => void;
}

const SplitCard: React.FC<SplitCardProps> = ({
  splitId,
  programId,
  splitName,
  description,
  subscriptionPrice,
  buyPrice,
  paymentPlan,
  programData,
  onSubscribe,
  onBuy,
}) => {
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [isCardConfirmationModalOpen, setIsCardConfirmationModalOpen] =
    useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [selectedPaymentType, setSelectedPaymentType] = useState<
    "subscription" | "one_time"
  >("subscription");
  const [selectedCard, setSelectedCard] = useState<StripeCard | null>(null);
  const [forceNewCard, setForceNewCard] = useState(false);
  const [affiliateCode, setAffiliateCode] = useState<string | null>(null);
  const [couponCode, setCouponCode] = useState<string>("");
  const [discountPreview, setDiscountPreview] = useState<
    DiscountPreviewResponse["data"] | null
  >(null);
  const [isLoadingDiscount, setIsLoadingDiscount] = useState(false);
  const [couponError, setCouponError] = useState<string | null>(null);
  const [debounceTimeout, setDebounceTimeout] = useState<number | null>(null);
  const [loadingTimeout, setLoadingTimeout] = useState<number | null>(null);
  const currentRequestRef = useRef<string | null>(null);
  const { showToast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const {
    isLoggedIn,
    useCheckEligibility,
    useGetPricing,
    useGetUserEnrollments,
    useGetDiscountPreview,
  } = useEnrollment();
  const { useGetCustomerCards } = useCustomerCards();
  const getDiscountPreview = useGetDiscountPreview();

  // Check eligibility and pricing (only when logged in for eligibility, always for pricing)
  const { data: eligibility, isLoading: eligibilityLoading } =
    useCheckEligibility(splitId);
  const {
    data: pricing,
    isLoading: pricingLoading,
    error: pricingError,
  } = useGetPricing(splitId);
  const { data: userEnrollments } = useGetUserEnrollments();
  const { data: customerCards } = useGetCustomerCards(isLoggedIn);

  const canPreview = true

  // const canPreview = useMemo(() => {
  //   if (programData?.days_for_preview && programData.days_for_preview > 0) {
  //     // days_for_preview is a number, for example 30
  //     // date of program approval is a string, for example "2025-01-01"
  //     // if the date of program approval is less than the current date minus the days_for_preview, then show the payment modal
  //     const programApprovalDate = new Date(programData?.approval_date || "");
  //     const currentDate = new Date();
  //     const daysDiff = Math.ceil(
  //       (currentDate.getTime() - programApprovalDate.getTime()) /
  //         (1000 * 60 * 60 * 24)
  //     );
  //     if (daysDiff <= programData.days_for_preview) {
  //       return true;
  //     } else {
  //       return false;
  //       // showToast(
  //       //   `You can only preview this program for ${programData.days_for_preview} days after it is approved`,
  //       //   5000
  //       // );
  //     }
  //   }
  //   return false;
  // }, [programData, affiliateCode, programId, navigate]);

  // Function to fetch discount preview (centralized in SplitCard)
  const fetchDiscountPreview = async (
    code: string,
    paymentType: "subscription" | "one_time"
  ) => {
    if (!code.trim()) {
      setDiscountPreview(null);
      setCouponError(null);
      setIsLoadingDiscount(false);
      currentRequestRef.current = null;
      return;
    }

    // Prevent duplicate requests for the same code and payment type
    const requestKey = `${code.trim()}-${paymentType}`;
    if (currentRequestRef.current === requestKey) {
      return;
    }

    currentRequestRef.current = requestKey;
    setIsLoadingDiscount(true);

    // Set a timeout to reset loading state if request takes too long (10 seconds)
    const timeoutId = window.setTimeout(() => {
      if (currentRequestRef.current === requestKey) {
        setIsLoadingDiscount(false);
        setCouponError("Request timed out. Please try again.");
        currentRequestRef.current = null;
      }
    }, 10000);
    setLoadingTimeout(timeoutId);

    try {
      const result = await getDiscountPreview.mutateAsync({
        split_id: splitId,
        payment_type: paymentType,
        coupon_code: code.trim(),
      });

      // Only update state if this is still the current request
      // if (currentRequestRef.current === requestKey) {
      if (result.error) {
        setDiscountPreview(null);
        setCouponError("Invalid or expired coupon code");
      } else {
        // Check if coupon validation exists and is invalid
        if (
          result.data.coupon_validation &&
          !result.data.coupon_validation.valid
        ) {
          setDiscountPreview(result.data); // Still set the data to show pricing info
          setCouponError(
            result.data.coupon_validation.message || "Invalid coupon code"
          );
        } else {
          setDiscountPreview(result.data);
          setCouponError(null);
        }
      }
      // Always reset loading state for the current request
      setIsLoadingDiscount(false);
      // Clear the timeout since request completed
      if (loadingTimeout) {
        window.clearTimeout(loadingTimeout);
        setLoadingTimeout(null);
      }
      // }
    } catch (error: any) {
      // Only update state if this is still the current request
      // if (currentRequestRef.current === requestKey) {
      setDiscountPreview(null);
      setCouponError(error.message || "Invalid coupon code");
      // Always reset loading state for the current request
      setIsLoadingDiscount(false);
      // Clear the timeout since request completed
      if (loadingTimeout) {
        window.clearTimeout(loadingTimeout);
        setLoadingTimeout(null);
      }
      // }
    }
  };

  // Handle coupon code input changes with debouncing
  const handleCouponCodeChange = (code: string) => {
    setCouponCode(code);
    setCouponError(null);

    // Clear any existing timeouts
    if (debounceTimeout) {
      window.clearTimeout(debounceTimeout);
      setDebounceTimeout(null);
    }
    if (loadingTimeout) {
      window.clearTimeout(loadingTimeout);
      setLoadingTimeout(null);
    }

    // Cancel any pending request
    currentRequestRef.current = null;

    // Clear discount preview immediately if code is empty
    if (!code.trim()) {
      setDiscountPreview(null);
      setIsLoadingDiscount(false);
      return;
    }

    // Set new timeout for debounced API call
    const timeoutId = window.setTimeout(() => {
      fetchDiscountPreview(code, selectedPaymentType);
      setDebounceTimeout(null);
    }, 500);

    setDebounceTimeout(timeoutId);
  };

  // Extract affiliate code and coupon code from URL parameters
  useEffect(() => {
    const refCode = searchParams.get("ref");
    if (refCode) {
      setAffiliateCode(refCode);
    }

    const coupon = searchParams.get("coupon");
    if (coupon) {
      setCouponCode(coupon);
      // Trigger discount preview for initial coupon
      handleCouponCodeChange(coupon);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]); // handleCouponCodeChange excluded to prevent infinite loops

  // Cleanup function to cancel pending requests
  useEffect(() => {
    return () => {
      currentRequestRef.current = null;
      if (debounceTimeout) {
        window.clearTimeout(debounceTimeout);
      }
      if (loadingTimeout) {
        window.clearTimeout(loadingTimeout);
      }
    };
  }, [debounceTimeout, loadingTimeout]);

  // Re-fetch discount preview when payment type changes
  useEffect(() => {
    // Cancel any pending request and reset loading state
    currentRequestRef.current = null;
    setIsLoadingDiscount(false);

    // Clear any existing timeouts
    if (loadingTimeout) {
      window.clearTimeout(loadingTimeout);
      setLoadingTimeout(null);
    }

    if (couponCode.trim()) {
      fetchDiscountPreview(couponCode, selectedPaymentType);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedPaymentType]); // couponCode and fetchDiscountPreview excluded to prevent infinite loops

  // Create fallback pricing object from props if API fails (memoized to prevent re-renders)
  const fallbackPricing: SplitPricing = useMemo(
    () => ({
      split_id: typeof splitId === "string" ? parseInt(splitId) : splitId,
      program_name: splitName,
      split_title: splitName,
      currency: "USD",
      pricing: {
        one_time: {
          amount: buyPrice,
          available: paymentPlan?.includes("one_time") || false,
          stripe_configured: true,
          description:
            "Lifetime access, no updates when trainer modifies split",
        },
        subscription: {
          amount: subscriptionPrice,
          available: paymentPlan?.includes("monthly") || false,
          stripe_configured: true,
          description: "Monthly billing, access with automatic updates",
        },
        minimum: Math.min(subscriptionPrice, buyPrice),
      },
      recommendations: {
        best_value:
          buyPrice < subscriptionPrice * 4
            ? ("one_time" as const)
            : ("subscription" as const),
        savings_months: Math.ceil(buyPrice / subscriptionPrice),
      },
    }),
    [splitId, splitName, buyPrice, subscriptionPrice, paymentPlan]
  );

  // Use API pricing if available, otherwise use fallback
  const effectivePricing = pricing || (pricingError ? fallbackPricing : null);

  // Check if user is already enrolled (memoized to prevent re-renders)
  const isAlreadyEnrolled = useMemo(() => {
    return userEnrollments?.some(
      (enrollment: any) =>
        enrollment.split_id === splitId && enrollment.status === "active"
    );
  }, [userEnrollments, splitId]);

  const handleSubscribe = () => {
    if (!isLoggedIn) {
      setSelectedPaymentType("subscription");
      setIsLoginModalOpen(true);
      return;
    }

    if (isAlreadyEnrolled) {
      showToast("You are already enrolled in this program", 5000);
      return;
    }

    if (eligibility && !eligibility.eligibility.can_enroll) {
      showToast(
        `Cannot enroll: ${eligibility.eligibility.reasons.join(", ")}`,
        5000
      );
      return;
    }

    if (onSubscribe) {
      onSubscribe();
    } else {
      setSelectedPaymentType("subscription");
      // Check if user has existing cards
      if (
        customerCards?.payment_methods &&
        customerCards.payment_methods.length > 0
      ) {
        setIsCardConfirmationModalOpen(true);
      } else {
        // No cards, go directly to payment modal for new card
        setSelectedCard(null);
        setForceNewCard(true);
        setIsPaymentModalOpen(true);
      }
    }
  };

  const handleBuy = () => {
    if (!isLoggedIn) {
      setSelectedPaymentType("one_time");
      setIsLoginModalOpen(true);
      return;
    }

    if (isAlreadyEnrolled) {
      showToast("You are already enrolled in this program", 5000);
      return;
    }

    if (eligibility && !eligibility.eligibility.can_enroll) {
      showToast(
        `Cannot enroll: ${eligibility.eligibility.reasons.join(", ")}`,
        5000
      );
      return;
    }

    if (onBuy) {
      onBuy();
    } else {
      setSelectedPaymentType("one_time");
      // For one-time purchases, go directly to enrollment (no card saving needed)
      setSelectedCard(null);
      setForceNewCard(true);
      setIsPaymentModalOpen(true);
    }
  };

  const handleLoginConfirm = () => {
    setIsLoginModalOpen(false);

    // Get current page URL for redirect after login
    const currentPath = location.pathname + location.search;
    const redirectUri = encodeURIComponent(currentPath);

    // Navigate to login page with redirect_uri parameter
    navigate(`/login?redirect_uri=${redirectUri}`);
  };

  const handleUseExistingCard = (card: StripeCard, coupon?: string) => {
    setSelectedCard(card);
    setForceNewCard(false);
    if (coupon) {
      setCouponCode(coupon);
    }
    setIsCardConfirmationModalOpen(false);
    setIsPaymentModalOpen(true);
  };

  const handleAddNewCard = (coupon?: string) => {
    setSelectedCard(null);
    setForceNewCard(true);
    if (coupon) {
      setCouponCode(coupon);
    }
    setIsCardConfirmationModalOpen(false);
    setIsPaymentModalOpen(true);
  };

  const handleBackToCardSelection = () => {
    setIsPaymentModalOpen(false);
    setIsCardConfirmationModalOpen(true);
    setForceNewCard(false);
  };

  const getCurrentPrice = (type: "subscription" | "one_time") => {
    if (effectivePricing?.pricing) {
      return effectivePricing.pricing[type].amount;
    }
    return type === "subscription" ? subscriptionPrice : buyPrice;
  };

  const getButtonText = (type: "subscription" | "one_time") => {
    if (pricingLoading) {
      return "Loading...";
    }

    // Show pricing from API if available, otherwise fall back to props
    let price = type === "subscription" ? subscriptionPrice : buyPrice;
    if (effectivePricing?.pricing) {
      price = effectivePricing.pricing[type].amount;
    }

    if (!isLoggedIn) {
      return type === "subscription"
        ? `Subscribe for $${price}`
        : `Buy for $${price}`;
    }

    if (isAlreadyEnrolled) {
      return "Already Enrolled";
    }

    if (eligibilityLoading) {
      return "Loading...";
    }

    if (eligibility && !eligibility.eligibility.can_enroll) {
      return "Not Available";
    }

    return type === "subscription"
      ? `Subscribe for $${price}`
      : `Buy for $${price}`;
  };

  const isButtonDisabled = (type: "subscription" | "one_time") => {
    // Don't disable for non-logged-in users (they can click to get login prompt)
    if (!isLoggedIn) return false;

    // For logged-in users, check various conditions
    // Only disable if we're still loading critical data
    if (eligibilityLoading) return true;

    // Don't disable due to pricing loading - we have fallback pricing
    // if (pricingLoading) return true;

    if (isAlreadyEnrolled) return true;

    // Only disable if we have eligibility data AND it explicitly says can't enroll
    if (
      eligibility &&
      eligibility.eligibility &&
      !eligibility.eligibility.can_enroll
    ) {
      return true;
    }

    // Only disable if we have eligibility data AND payment option is explicitly unavailable
    if (
      eligibility &&
      eligibility.eligibility &&
      eligibility.eligibility.payment_options &&
      !eligibility.eligibility.payment_options[type]?.available
    ) {
      return true;
    }

    return false;
  };

  return (
    <>
      <div className="w-full max-w-sm mx-auto lg:max-w-none bg-input rounded-lg shadow-md border border-border">
        <div
          onClick={() => {
            if (canPreview) {
              let url = `/athlete/program-preview/${splitId}`;
              if (affiliateCode) {
                url += `?ref=${affiliateCode}`;
              }
              navigate(url);
            }
          }}
          className={`p-4 lg:p-6 ${canPreview ? "cursor-pointer" : ""}`}
        >
          {/* Split Name */}
          <h3 className="text-base lg:text-lg font-medium text-text mb-4">
            {splitName}
          </h3>

          {/* Description */}
          <div className="mb-6 h-32 lg:h-40 overflow-hidden">
            <p className="text-sm text-text-secondary leading-relaxed">
              {description}
            </p>
          </div>

          {/* Enrollment Status */}
          {isAlreadyEnrolled && (
            <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
              <p className="text-sm text-green-800 dark:text-green-200 font-medium">
                ✓ You are enrolled in this program
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            {/* Subscribe Button */}
            {paymentPlan?.includes("monthly") && (
              <button
                onClick={handleSubscribe}
                disabled={isButtonDisabled("subscription")}
                className={`w-full h-11 rounded-md border transition-colors duration-200 ${
                  isButtonDisabled("subscription")
                    ? "border-border bg-input text-text-secondary cursor-not-allowed"
                    : "border-primary bg-transparent hover:bg-primary/5 text-primary"
                }`}
              >
                <span className="text-base font-semibold">
                  {getButtonText("subscription")}
                </span>
              </button>
            )}

            {/* Buy Button */}
            {paymentPlan?.includes("one_time") && (
              <MkdButton
                onClick={handleBuy}
                disabled={isButtonDisabled("one_time")}
                className={`w-full h-11 text-base font-semibold ${
                  isButtonDisabled("one_time")
                    ? "bg-gray-400 hover:bg-gray-400 cursor-not-allowed"
                    : "bg-primary hover:bg-primary-hover text-white"
                }`}
              >
                {getButtonText("one_time")}
              </MkdButton>
            )}
          </div>
        </div>
      </div>

      {/* Stripe Payment Modal */}
      {effectivePricing && isPaymentModalOpen && (
        <StripePaymentModal
          isOpen={isPaymentModalOpen}
          onClose={() => setIsPaymentModalOpen(false)}
          splitId={splitId}
          programId={programId}
          pricing={effectivePricing}
          paymentType={selectedPaymentType}
          existingCard={selectedCard}
          forceNewCard={forceNewCard}
          affiliateCode={affiliateCode}
          onBackToCardSelection={
            customerCards?.payment_methods &&
            customerCards.payment_methods.length > 0
              ? handleBackToCardSelection
              : undefined
          }
          programData={programData}
          initialCouponCode={couponCode}
          // Centralized discount preview props
          discountPreview={discountPreview}
          isLoadingDiscount={isLoadingDiscount}
          couponError={couponError}
          onCouponCodeChange={handleCouponCodeChange}
        />
      )}

      {/* Card Confirmation Modal */}
      {effectivePricing && isCardConfirmationModalOpen && (
        <CardConfirmationModal
          isOpen={isCardConfirmationModalOpen}
          onClose={() => setIsCardConfirmationModalOpen(false)}
          onUseExistingCard={handleUseExistingCard}
          onAddNewCard={handleAddNewCard}
          splitName={splitName}
          paymentType={selectedPaymentType}
          amount={getCurrentPrice(selectedPaymentType)}
          currency={effectivePricing.currency}
          splitId={splitId}
          programData={programData}
          // Centralized discount preview props
          discountPreview={discountPreview}
          isLoadingDiscount={isLoadingDiscount}
          couponError={couponError}
          onCouponCodeChange={handleCouponCodeChange}
          initialCouponCode={couponCode}
        />
      )}

      {/* Debug: Show modal state when pricing is missing */}
      {isPaymentModalOpen && !effectivePricing && pricingLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-background rounded-lg p-6 w-full max-w-md mx-4 border border-border">
            <div className="text-center">
              <h3 className="text-lg font-medium text-text mb-4">
                Loading Payment Options...
              </h3>
              <p className="text-text-secondary mb-4">
                Please wait while we fetch the pricing information.
              </p>
              <button
                onClick={() => setIsPaymentModalOpen(false)}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Login Confirmation Modal */}
      <LoginConfirmationModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
        onConfirm={handleLoginConfirm}
        actionType={selectedPaymentType}
        splitName={splitName}
        price={getCurrentPrice(selectedPaymentType)}
      />
    </>
  );
};

export default SplitCard;
