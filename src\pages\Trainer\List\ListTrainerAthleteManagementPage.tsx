import { useEffect } from "react";
import { Container } from "@/components/Container";
import { useContexts } from "@/hooks/useContexts";
import { useTrainerAthletes } from "@/hooks/useTrainerAthletes";
import {
  TrainerAthleteFilters,
  TrainerAthleteTable,
  TrainerAthletePagination,
} from "@/components/TrainerAthletes";

const TrainerAthleteManagementPage = () => {
  const { globalDispatch } = useContexts();

  // Use the custom hook for trainer athletes
  const {
    athletes,
    filters,
    isLoading,
    currentPage,
    totalPages,
    updateFilter,
    applyFilters,
    changePage,
  } = useTrainerAthletes();

  // Set the path in global state for navigation highlighting
  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "athlete",
      },
    });
  }, [globalDispatch]);

  return (
    <>
      <Container>
        <div className="flex flex-col gap-6 py-6 px-2 sm:px-4 md:px-8 w-full max-w-7xl mx-auto">
          {/* Header */}
          <header>
            <h1 className="text-2xl font-bold text-text mb-2">
              Athlete Management
            </h1>
          </header>

          {/* Filter Section */}
          <TrainerAthleteFilters
            filters={filters}
            onFilterChange={updateFilter}
            onApplyFilter={applyFilters}
          />
          {/* Athletes Table Section */}
          <section className="flex flex-col gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
              <h2 className="text-2xl font-bold text-text">Athletes</h2>
            </div>

            {/* Athletes Table */}
            <TrainerAthleteTable athletes={athletes} isLoading={isLoading} />

            {/* Pagination */}
            <TrainerAthletePagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={changePage}
            />
          </section>
        </div>
      </Container>
    </>
  );
};

export default TrainerAthleteManagementPage;
