# Trainer Transactions Test Data

This directory contains test data for the trainer transactions components to facilitate development and testing without requiring real API endpoints.

## How to Enable Test Data

There are two ways to enable test data:

### Method 1: URL Parameter

Add `?test=true` to any URL to enable test data for that session:

```
http://localhost:3000/trainer/transactions?test=true
```

### Method 2: Environment Variable

Set the environment variable in your `.env.local` file:

```
REACT_APP_USE_TEST_DATA=true
```

## Test Data Components

### Transaction Stats (`mockTransactionStats`)

- **Total Earnings**: $2,847.50
- **Pending Payouts**: $425.75
- **Available to Withdraw**: $1,250.25
- **Withdrawn**: $1,171.50
- **Currency**: USD
- **Payout Time**: 24 hours

### Earnings Graph (`mockEarningsGraphData`)

6 months of earnings data from January to June 2024:

- Jan: $180.50
- Feb: $245.75
- Mar: $320.25
- Apr: $410.80
- May: $385.90
- Jun: $465.30

### Stripe Connect Status

The test data cycles through three different account states:

1. **Not Set Up** (`mockStripeConnectStatusNotSetup`)
   - No Stripe Connect account
   - Shows setup button

2. **Incomplete Setup** (`mockStripeConnectStatusIncomplete`)
   - Account created but onboarding not completed
   - Shows completion button

3. **Complete Setup** (`mockStripeConnectStatusComplete`)
   - Fully verified and ready for payments
   - Shows account details with edit and delete buttons

### Stripe Connect Account Management

- **Setup**: Creates new Stripe Connect account with onboarding flow
  - **Configurable Country**: Choose from 23+ supported countries
  - **Business Type**: Individual or Company
  - **Persistent Settings**: Country and business type saved to localStorage
- **Edit**: Allows re-entering onboarding for incomplete accounts
  - Can update country and business type before re-entering onboarding
- **Delete**: Removes Stripe Connect account with confirmation modal
  - Shows warning about permanent deletion
  - Requires confirmation before proceeding
  - Simulates 1-second processing delay in test mode

### Supported Countries

The component supports Stripe Connect for the following countries:

- **North America**: Canada, United States
- **Europe**: United Kingdom, Germany, France, Italy, Spain, Netherlands, Sweden, Norway, Denmark, Finland, Switzerland, Austria, Belgium, Ireland, Portugal, Luxembourg
- **Asia-Pacific**: Singapore, Hong Kong, Japan, Australia, New Zealand

### Configuration Persistence

- **Country selection** is saved to `localStorage` as `stripe_connect_country`
- **Business type** is saved to `localStorage` as `stripe_connect_business_type`
- Settings persist across browser sessions and page reloads
- Default values: Country = "CA" (Canada), Business Type = "individual"

### Transaction History (`mockTransactionHistory`)

3 sample transactions:

1. **Advanced Strength Training** - $69.99 commission (Processed)
2. **Beginner Fitness Journey** - $13.99 commission (Pending)
3. **Elite Performance Training** - $119.99 commission (Processed)

### Withdrawal Simulation (`mockWithdrawalResponse`)

- Simulates 2-second processing delay
- Returns mock transfer details
- Shows success toast with "(Test Mode)" indicator

## Test Page

A dedicated test page is available at:

```
src/pages/Trainer/View/TestTrainerTransactionsPage.tsx
```

This page includes:

- All transaction components with test data
- Test mode indicators
- Control buttons for testing
- Information about active test data

## API Simulation

When test data is enabled:

- All API calls are intercepted and return mock data
- Realistic delays are simulated (600ms - 2000ms)
- Loading states work as expected
- Error handling can be tested by modifying the test data

## Customizing Test Data

To modify test data:

1. Edit `trainer_transactions_test_data.ts`
2. Update the relevant mock objects
3. Restart the development server

## Stripe Connect Testing

The Stripe Connect status automatically cycles through different states based on the current time. This allows you to see all possible account states without manual intervention.

## Development Tips

1. **Use URL parameter** for quick testing of specific scenarios
2. **Use environment variable** for extended development sessions
3. **Check browser console** for test mode indicators
4. **Refresh components** using the test controls to see state changes
5. **Test error states** by temporarily modifying mock data to return errors

## Production Safety

Test data is automatically disabled in production builds. The `USE_TEST_DATA` flag checks for:

- `NODE_ENV === "development"`
- `REACT_APP_USE_TEST_DATA === "true"` OR `?test=true` in URL

This ensures test data never appears in production environments.
