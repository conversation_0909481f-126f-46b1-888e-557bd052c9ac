import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { HeartIcon } from "@heroicons/react/24/solid";
import { HeartIcon as HeartOutlineIcon } from "@heroicons/react/24/outline";

interface TrainingPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  level: string;
  image: string;
  isFavorite?: boolean;
}

interface TrainingPlanCardProps {
  plan: TrainingPlan;
  onFavoriteToggle?: (planId: string, isFavorite: boolean) => void;
}

const TrainingPlanCard = ({
  plan,
  onFavoriteToggle,
}: TrainingPlanCardProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const navigate = useNavigate();
  const [isFavorite, setIsFavorite] = useState(plan.isFavorite || false);

  const handleCardClick = () => {
    navigate(`/athlete/program/${plan.id}`);
  };

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    const newFavoriteState = !isFavorite;
    setIsFavorite(newFavoriteState);
    onFavoriteToggle?.(plan.id, newFavoriteState);
  };

  return (
    <div
      className="w-full max-w-sm md:max-w-none lg:w-72 xl:w-80 2xl:w-96 rounded-lg shadow-lg border transition-all duration-200 hover:shadow-xl hover:scale-105 cursor-pointer flex-shrink-0 mx-auto lg:mx-0"
      style={{
        backgroundColor: THEME_COLORS[mode].CARD_BG,
        borderColor: THEME_COLORS[mode].BORDER,
      }}
      onClick={handleCardClick}
    >
      {/* Image Container */}
      <div className="relative">
        <div className="w-full h-40 sm:h-48 md:h-44 lg:h-48 xl:h-52 2xl:h-56 rounded-t-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
          <img
            src={plan.image}
            alt={plan.name}
            className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
          />
        </div>

        {/* Favorite Button */}
        <button
          onClick={handleFavoriteClick}
          className="absolute top-2 right-2 sm:top-3 sm:right-3 w-8 h-8 sm:w-9 sm:h-9 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center hover:bg-black/40 transition-colors duration-200"
        >
          {isFavorite ? (
            <HeartIcon className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
          ) : (
            <HeartOutlineIcon className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
          )}
        </button>
      </div>

      {/* Content */}
      <div className="p-3 sm:p-4 lg:p-4 xl:p-5">
        {/* Plan Name */}
        <h3
          className="text-sm sm:text-base lg:text-base xl:text-lg font-semibold mb-2 sm:mb-3 transition-colors duration-200"
          style={{ color: THEME_COLORS[mode].TEXT }}
        >
          {plan.name}
        </h3>

        {/* Description */}
        <p
          className="text-xs sm:text-sm lg:text-sm xl:text-base mb-3 sm:mb-4 line-clamp-3 transition-colors duration-200"
          style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
        >
          {plan.description}
        </p>

        {/* Price and Level */}
        <div
          className="pt-2 sm:pt-3 border-t transition-colors duration-200"
          style={{ borderColor: THEME_COLORS[mode].BORDER }}
        >
          <div className="text-center">
            <span
              className="text-sm sm:text-base lg:text-base xl:text-lg font-medium transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              {plan.level} Plan ${plan.price}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrainingPlanCard;
