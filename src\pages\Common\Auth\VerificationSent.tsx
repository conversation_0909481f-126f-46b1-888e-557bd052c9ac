import { Container } from "@/components/Container";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEnvelope, faCheckCircle } from "@fortawesome/free-solid-svg-icons";

const VerificationSent = () => {
  const navigate = useNavigate();

  return (
    <Container className="bg-background min-h-screen">
      <main className="flex-1 flex items-center justify-center px-4 py-8">
        <div className="w-full max-w-md">
          {/* Card */}
          <div className="w-full bg-secondary border border-border shadow-lg rounded-lg px-8 py-10">
            <div className="text-center">
              <FontAwesomeIcon 
                icon={faEnvelope} 
                className="text-4xl text-primary mb-4" 
              />
              <h2 className="text-2xl font-bold text-text mb-2">
                Check Your Email
              </h2>
              <p className="text-text-secondary mb-6">
                We've sent a verification link to your email address. Please check your inbox and click the link to verify your account.
              </p>
              
              <div className="bg-primary/10 border border-primary/20 rounded-lg p-4 mb-6">
                <div className="flex items-start space-x-3">
                  <FontAwesomeIcon 
                    icon={faCheckCircle} 
                    className="text-green-500 mt-1" 
                  />
                  <div className="text-left">
                    <h3 className="font-semibold text-text mb-1">
                      What's Next?
                    </h3>
                    <ul className="text-sm text-text-secondary space-y-1">
                      <li>• Check your email inbox</li>
                      <li>• Click the verification link</li>
                      <li>• Complete your profile setup</li>
                      <li>• Start using your account</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <InteractiveButton
                  onClick={() => navigate('/login')}
                  className="w-full bg-primary text-white px-6 py-2 rounded-md hover:bg-primary-hover"
                >
                  Go to Login
                </InteractiveButton>
                
                <InteractiveButton
                  onClick={() => navigate('/')}
                  className="w-full bg-secondary text-text px-6 py-2 rounded-md hover:bg-secondary-hover border border-border"
                >
                  Back to Home
                </InteractiveButton>
              </div>

              <div className="mt-6 text-sm text-text-secondary">
                <p>Didn't receive the email?</p>
                <p className="mt-1">
                  Check your spam folder or contact support if you need help.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </Container>
  );
};

export default VerificationSent; 