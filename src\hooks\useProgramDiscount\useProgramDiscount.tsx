import { useState, useEffect } from "react";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { useViewModelQuery } from "@/query/shared/viewModel";
import { Models } from "@/utils/baas/models";
import {
  Program,
  ProgramDiscount,
  Discount,
  Coupon,
} from "@/interfaces/model.interface";
import { useContexts } from "../useContexts";
import { ToastStatusEnum } from "@/utils/Enums";

interface UseProgramDiscountProps {
  programId: number | string;
}

interface ProgramDiscountData {
  program: Program | null;
  programDiscount: ProgramDiscount | null;
  discounts: Discount[];
  coupons: Coupon[];
}

export const useProgramDiscount = ({ programId }: UseProgramDiscountProps) => {
  const { tokenExpireError, showToast } = useContexts();
  const [programDiscountData, setProgramDiscountData] =
    useState<ProgramDiscountData>({
      program: null,
      programDiscount: null,
      discounts: [],
      coupons: [],
    });
  const [isLoading, setIsLoading] = useState(false);
  const [loading, setLoading] = useState({
    creating_discount: false,
    updating_discount: false,
  });
  const [error, setError] = useState<string | null>(null);

  // Hook for making custom API calls
  const { mutateAsync: fetchCustomData } = useCustomModelQuery();

  // Hook for fetching program details
  const {
    data: programData,
    isLoading: isProgramLoading,
    error: programError,
  } = useViewModelQuery(Models.PROGRAM, programId, {
    join: ["split"],
  });

  // Function to fetch program discount details
  const fetchProgramDiscountDetails = async () => {
    if (!programId) return;

    setIsLoading(true);
    setError(null);

    try {
      // Fetch program discount settings
      const discountResponse = await fetchCustomData({
        endpoint: `/v2/api/kanglink/custom/trainer/programs/${programId}/discounts`,
        method: "GET",
      });

      if (!discountResponse?.error && discountResponse?.data) {
        const discountData = discountResponse.data;

        setProgramDiscountData((prev) => ({
          ...prev,
          program: {
            ...programData,
            split: undefined,
            splits: programData?.split,
          },
          programDiscount: {
            id: discountData.id,
            program_id: programId,
            affiliate_link: discountData.affiliate_link,
            sale_discount_type: discountData.sale_discount?.type || null,
            sale_discount_value: discountData.sale_discount?.value || null,
            sale_apply_to_all:
              discountData.sale_discount?.apply_to_all || false,
            created_at: discountData.last_updated,
            updated_at: discountData.last_updated,
          },
          discounts:
            discountData.subscription_discounts
              ?.map((item: any) => ({
                id: item?.id,
                program_id: programId,
                split_id: item?.tier_id,
                discount_type: item?.discount_type,
                discount_value: item?.discount_value,
                applies_to: "subscription",
                is_active: true,
              }))
              .concat(
                discountData.full_price_discounts?.map((item: any) => ({
                  id: item?.id,
                  program_id: programId,
                  split_id: item?.tier_id,
                  discount_type: item?.discount_type,
                  discount_value: item?.discount_value,
                  applies_to: "full_payment",
                  is_active: true,
                }))
              ) || [],
          coupons: discountData.promo_code
            ? [
                {
                  id: discountData.promo_code?.id,
                  program_id: programId,
                  code: discountData.promo_code?.code,
                  discount_type: discountData.promo_code?.discount_type,
                  discount_value: discountData.promo_code?.discount_value,
                  applies_to: (() => {
                    const appliesTo = discountData.promo_code?.applies_to;
                    if (appliesTo?.subscription && appliesTo?.full_payment)
                      return "both";
                    if (appliesTo?.subscription) return "subscription";
                    if (appliesTo?.full_payment) return "full_payment";
                    return "subscription";
                  })(),
                  is_active: discountData.promo_code?.is_active,
                  expiry_date: discountData.promo_code?.expiry_date,
                  usage_limit: discountData.promo_code?.usage_limit,
                  used_count: discountData.promo_code?.used_count || 0,
                },
              ]
            : [],
        }));
      }
    } catch (err: any) {
      console.error("Error fetching program discount details:", err);
      setError(err.message || "Failed to fetch discount details");
    } finally {
      setIsLoading(false);
    }
  };

  // Function to save/update program discount settings
  const saveProgramDiscountSettings = async (discountSettings: any) => {
    setLoading((prev) => ({
      ...prev,
      updating_discount: true,
    }));
    setError(null);

    try {
      const response = await fetchCustomData({
        endpoint: `/v2/api/kanglink/custom/trainer/programs/${programId}/discounts`,
        method: "PUT",
        body: discountSettings,
      });

      if (!response?.error) {
        // Refresh the discount data after successful save
        await fetchProgramDiscountDetails();
        return response;
      } else {
        throw new Error(
          response?.message || "Failed to save discount settings"
        );
      }
    } catch (err: any) {
      console.error("Error saving program discount settings:", err);
      setError(err.message || "Failed to save discount settings");
      const message = err?.response?.data?.message || err?.message;
      showToast(message, 4000, ToastStatusEnum.ERROR);
      tokenExpireError(message);
      throw err;
    } finally {
      setLoading((prev) => ({
        ...prev,
        updating_discount: false,
      }));
    }
  };

  const handleCreateDiscount = async (discountSettings: any) => {
    setLoading((prev) => ({
      ...prev,
      is_creating_discount: true,
    }));
    setError(null);

    try {
      const response = await fetchCustomData({
        endpoint: `/v2/api/kanglink/custom/trainer/programs/${programId}/discounts`,
        method: "POST",
        body: discountSettings,
      });

      if (!response?.error) {
        // Refresh the discount data after successful save
        await fetchProgramDiscountDetails();
        return response;
      } else {
        throw new Error(
          response?.message || "Failed to create discount settings"
        );
      }
    } catch (err: any) {
      console.error("Error creating program discount settings:", err);
      setError(err.message || "Failed to create discount settings");
      const message = err?.response?.data?.message || err?.message;
      showToast(message, 4000, ToastStatusEnum.ERROR);
      tokenExpireError(message);
      throw err;
    } finally {
      setLoading((prev) => ({
        ...prev,
        is_creating_discount: false,
      }));
    }
  };

  // Update program data when it's loaded
  // useEffect(() => {
  //   if (programData && !isProgramLoading) {
  //     setProgramDiscountData((prev) => ({
  //       ...prev,
  //       program: programData,
  //     }));
  //   }
  // }, [programData, isProgramLoading]);

  // Fetch discount details when programId changes
  useEffect(() => {
    if (programId && programData?.id) {
      fetchProgramDiscountDetails();
    }
  }, [programId, programData?.id]);

  // Handle program loading error
  useEffect(() => {
    if (programError) {
      setError("Failed to fetch program details");
    }
  }, [programError]);

  return {
    ...programDiscountData,
    isLoading: isLoading || isProgramLoading,
    error,
    refetch: fetchProgramDiscountDetails,
    saveProgramDiscountSettings,
    handleCreateDiscount,
    loading,
  };
};
