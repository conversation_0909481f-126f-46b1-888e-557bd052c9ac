export const jenkinsFrontendConfig = (project: string) => {
  return `
  cd ${project}_frontend/;
echo "pull";
echo -e "ln8kmyhfezQ4kjyngk2cgA\n" | sudo -S git switch master
echo -e "ln8kmyhfezQ4kjyngk2cgA\n" | sudo -S git pull origin master
echo -e "ln8kmyhfezQ4kjyngk2cgA\n" | sudo -S chmod +x redeploy.sh
echo -e "ln8kmyhfezQ4kjyngk2cgA\n" | sudo -S ./redeploy.sh
cd ../;
echo "done";
`;
};

// #!/bin/bash

// # Exit on any error
// set -e

// echo "🚀 Starting deployment process..."

// # Configuration - these values are pre-computed
// DEPLOY_PATH="/var/www/kanglink.manaknightdigital.com"
// NGINX_CONFIG="/etc/nginx/sites-available/kanglink.manaknightdigital.com"
// DOMAIN="kanglink.manaknightdigital.com"

// # Build the application
// echo "📦 Building application..."
// echo -e "ln8kmyhfezQ4kjyngk2cgA
// " | sudo npm i --force

// echo "Building project..."
// echo -e "ln8kmyhfezQ4kjyngk2cgA
// " | sudo npm run build

// # Ensure deploy directory exists and set permissions
// echo "📁 Setting up deployment directory..."
// echo -e "ln8kmyhfezQ4kjyngk2cgA
// " | sudo -S mkdir -p /var/www/kanglink.manaknightdigital.com/html
// echo -e "ln8kmyhfezQ4kjyngk2cgA
// " | sudo -S chown -R deploy:deploy /var/www/kanglink.manaknightdigital.com/html
// echo -e "ln8kmyhfezQ4kjyngk2cgA
// " | sudo -S chmod -R 755 /var/www/kanglink.manaknightdigital.com

// # Copy build files to deployment directory
// echo "📋 Copying files to deployment directory..."
// echo -e "ln8kmyhfezQ4kjyngk2cgA
// " | sudo -S cp -r dist/* /var/www/kanglink.manaknightdigital.com
// echo -e "ln8kmyhfezQ4kjyngk2cgA
// " | sudo -S mv /var/www/kanglink.manaknightdigital.com/index.html /var/www/kanglink.manaknightdigital.com/index.php
// echo -e "ln8kmyhfezQ4kjyngk2cgA
// " | sudo -S chown -R www-data:www-data /var/www/kanglink.manaknightdigital.com/index.php
// echo -e "ln8kmyhfezQ4kjyngk2cgA
// " | sudo -S chmod -R 775 /var/www/kanglink.manaknightdigital.com/*

// echo "✅ Deployment completed successfully!"
