import { useQuery } from "@tanstack/react-query";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { AthleteEnrollmentsResponse } from "@/interfaces/model.interface";
import { AthleteEndpoints } from "@/utils/baas/athlete";

// Custom hook for fetching athlete enrollments
export const useAthleteEnrollments = (options?: {
  enabled?: boolean;
  refetchInterval?: number;
}) => {
  const customQuery = useCustomModelQuery();

  return useQuery({
    queryKey: ["athlete-library"],
    queryFn: async (): Promise<AthleteEnrollmentsResponse> => {
      const response = await customQuery.mutateAsync({
        endpoint: AthleteEndpoints.ATHLETE_LIBRARY.GET_ALL.url,
        method: AthleteEndpoints.ATHLETE_LIBRARY.GET_ALL.method,
        requiresAuth: true,
      });

      // Handle API error responses
      if (response.error || !response.data) {
        throw new Error(
          response.message || "Failed to fetch athlete enrollments"
        );
      }

      return response as AthleteEnrollmentsResponse;
    },
    enabled: options?.enabled ?? true,
    refetchInterval: options?.refetchInterval ?? 30000, // Refetch every 30 seconds
    staleTime: 10000, // Consider data stale after 10 seconds
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Hook for refetching enrollments manually
export const useRefreshAthleteEnrollments = () => {
  const customQuery = useCustomModelQuery();

  const refreshEnrollments = async (): Promise<AthleteEnrollmentsResponse> => {
    const response = await customQuery.mutateAsync({
      endpoint: AthleteEndpoints.ATHLETE_ENROLLMENTS.GET_ALL.url,
      method: AthleteEndpoints.ATHLETE_ENROLLMENTS.GET_ALL.method,
      requiresAuth: true,
    });

    // Handle API error responses
    if (response.error || !response.data) {
      throw new Error(
        response.message || "Failed to refresh athlete enrollments"
      );
    }

    return response.data;
  };

  return { refreshEnrollments };
};
