import React, { Suspense, memo } from "react";
import { AthleteHeader } from "@/components/AthleteHeader";
import { Spinner } from "@/assets/svgs";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { LazyLoad } from "@/components/LazyLoad";
import { ThemeToggle } from "@/components/ThemeToggle";

interface AthleteWrapperProps {
  children: React.ReactNode;
}
const AthleteWrapper = ({ children }: AthleteWrapperProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <>
      <AthleteHeader />

      <div className={`min-h-full w-full h-full max-h-full`}>
        <Suspense
          fallback={
            <div
              className={`flex min-h-full w-full h-full max-h-full  items-center justify-center`}
            >
              <Spinner size={40} color={THEME_COLORS[mode].PRIMARY} />
            </div>
          }
        >
          <div
            className={`min-h-full w-full h-full max-h-full overflow-y-auto`}
          >
            {children}
          </div>
          {/* Floating Theme Toggle */}
          <div className="fixed z-50 bottom-6 right-6 md:bottom-8 md:right-8">
            <LazyLoad>
              <ThemeToggle className="shadow-lg border bg-background-secondary hover:scale-110 transition-all" />
            </LazyLoad>
          </div>
        </Suspense>
      </div>
    </>
  );
};

export default memo(AthleteWrapper);
