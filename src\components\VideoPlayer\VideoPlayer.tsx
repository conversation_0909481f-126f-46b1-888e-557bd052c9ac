import { useState, useRef, useEffect } from "react";
import { PlayIcon, PauseIcon } from "@heroicons/react/24/solid";

interface VideoPlayerProps {
  thumbnailUrl?: string;
  videoUrl?: string;
  duration?: string;
  progress?: number; // 0-100
}

const VideoPlayer = ({
  thumbnailUrl = "https://placehold.co/1134x250",
  videoUrl,
  duration: _duration,
  progress: _progress,
}: VideoPlayerProps) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [progress, setProgress] = useState(0);
  const [showControls, setShowControls] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);

  // Check if video is from external platforms
  const isYouTube =
    videoUrl?.includes("youtube.com") || videoUrl?.includes("youtu.be");
  const isVimeo = videoUrl?.includes("vimeo.com");
  const isExternalVideo = isYouTube || isVimeo;

  // Convert YouTube URL to embed format
  const getYouTubeEmbedUrl = (url: string) => {
    const videoId = url.match(
      /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/
    )?.[1];
    return videoId ? `https://www.youtube.com/embed/${videoId}` : url;
  };

  // Convert Vimeo URL to embed format
  const getVimeoEmbedUrl = (url: string) => {
    const videoId = url.match(/vimeo\.com\/(\d+)/)?.[1];
    return videoId ? `https://player.vimeo.com/video/${videoId}` : url;
  };

  const getEmbedUrl = () => {
    if (!videoUrl) return "";

    if (isYouTube) {
      return getYouTubeEmbedUrl(videoUrl);
    } else if (isVimeo) {
      return getVimeoEmbedUrl(videoUrl);
    }

    return videoUrl;
  };

  // Update progress when currentTime changes
  useEffect(() => {
    if (duration > 0) {
      setProgress((currentTime / duration) * 100);
    }
  }, [currentTime, duration]);

  // Auto-hide controls after 3 seconds of no interaction
  useEffect(() => {
    if (isPlaying && showControls) {
      const timer = setTimeout(() => {
        setShowControls(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isPlaying, showControls]);

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const handlePlay = async () => {
    if (!videoRef.current || !videoUrl) return;

    try {
      setIsLoading(true);
      if (isPlaying) {
        videoRef.current.pause();
        setIsPlaying(false);
      } else {
        await videoRef.current.play();
        setIsPlaying(true);
      }
    } catch {
      // Handle video playback errors silently
      setIsPlaying(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const handleError = () => {
    setHasError(true);
    setIsPlaying(false);
    setIsLoading(false);
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!videoRef.current || !duration) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newTime = (clickX / rect.width) * duration;

    videoRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleMouseMove = () => {
    setShowControls(true);
  };

  const overlayStyles = {
    backgroundColor: showControls ? "rgba(0, 0, 0, 0.3)" : "transparent",
  };

  const playButtonStyles = {
    backgroundColor: "rgba(255, 255, 255, 0.9)",
  };

  const progressBarStyles = {
    backgroundColor: "rgba(255, 255, 255, 0.3)",
  };

  const progressFillStyles = {
    width: `${progress}%`,
    backgroundColor: "white",
  };

  return (
    <div
      className="relative w-full rounded-md overflow-hidden bg-black mb-4"
      style={{ aspectRatio: "16/6.75" }} // Reduced height by 25% (9 * 0.75 = 6.75)
      onMouseMove={handleMouseMove}
    >
      {/* Video Element */}
      {videoUrl ? (
        isExternalVideo ? (
          /* External video (YouTube/Vimeo) embed */
          <div
            className="relative w-full h-full"
            style={{ paddingBottom: "56.25%" }}
          >
            <iframe
              src={getEmbedUrl()}
              title="Exercise demonstration video"
              className="absolute top-0 left-0 w-full h-full rounded-lg border-0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          </div>
        ) : (
          /* Direct video file */
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            poster={thumbnailUrl}
            onTimeUpdate={handleTimeUpdate}
            onLoadedMetadata={handleLoadedMetadata}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            onError={handleError}
            preload="metadata"
            style={{ maxHeight: "60vh" }}
          >
            <source src={videoUrl} type="video/mp4" />
            <source src={videoUrl} type="video/webm" />
            <source src={videoUrl} type="video/ogg" />
            Your browser does not support the video tag.
          </video>
        )
      ) : (
        /* Fallback to thumbnail if no video URL */
        <img
          src={thumbnailUrl}
          alt="Exercise demonstration"
          className="w-full h-full object-cover"
        />
      )}

      {/* Controls Overlay - Only show for direct video files, not external embeds */}
      {!isExternalVideo && (
        <div
          className={`absolute inset-0 flex items-center justify-center cursor-pointer transition-opacity duration-300 ${
            showControls ? "opacity-100" : "opacity-0"
          }`}
          style={overlayStyles}
          onClick={handlePlay}
        >
          {/* Play/Pause Button */}
          {(!isPlaying || showControls) && (
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center shadow-lg hover:scale-105 transition-transform duration-200"
              style={playButtonStyles}
            >
              {isLoading ? (
                <div className="w-8 h-8 border-2 border-gray-800 border-t-transparent rounded-full animate-spin" />
              ) : isPlaying ? (
                <PauseIcon className="w-8 h-8 text-gray-800" />
              ) : (
                <PlayIcon className="w-8 h-8 text-gray-800 ml-1" />
              )}
            </div>
          )}
        </div>
      )}

      {/* Progress Bar - Only show for direct video files */}
      {!isExternalVideo && (
        <div
          className={`absolute bottom-0 left-0 right-0 h-1 cursor-pointer transition-opacity duration-300 ${
            showControls ? "opacity-100" : "opacity-0"
          }`}
          style={progressBarStyles}
          onClick={handleProgressClick}
        >
          <div
            className="h-full transition-all duration-300"
            style={progressFillStyles}
          />
        </div>
      )}

      {/* Time Display - Only show for direct video files */}
      {!isExternalVideo && (
        <div
          className={`absolute bottom-2 right-2 text-white text-sm font-medium bg-black bg-opacity-50 px-2 py-1 rounded transition-opacity duration-300 ${
            showControls ? "opacity-100" : "opacity-0"
          }`}
        >
          {duration > 0
            ? `${formatTime(currentTime)} / ${formatTime(duration)}`
            : "0:00"}
        </div>
      )}

      {/* Error Message */}
      {hasError && !isExternalVideo && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75">
          <div className="text-white text-center p-4">
            <PlayIcon className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p className="text-sm opacity-75 mb-2">Unable to load video</p>
            {videoUrl && (
              <a
                href={videoUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-400 hover:text-blue-300 underline text-sm"
              >
                Open video in new tab
              </a>
            )}
          </div>
        </div>
      )}

      {/* No Video URL Message */}
      {!videoUrl && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-white text-center">
            <PlayIcon className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p className="text-sm opacity-75">No video available</p>
          </div>
        </div>
      )}

      {/* External Video Fallback Link */}
      {isExternalVideo && (
        <div className="absolute bottom-2 left-2">
          <a
            href={videoUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="text-white text-xs bg-black bg-opacity-50 px-2 py-1 rounded hover:bg-opacity-75 transition-colors"
          >
            Open in new tab
          </a>
        </div>
      )}
    </div>
  );
};

export default VideoPlayer;
