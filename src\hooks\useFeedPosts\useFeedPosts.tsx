import { useState, useEffect, useCallback } from "react";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { PostFeed } from "@/interfaces/model.interface";
import { useContexts } from "@/hooks/useContexts";

export interface UseFeedPostsProps {
  program_id?: string | number | null;
  enabled?: boolean;
}

export interface UseFeedPostsReturn {
  posts: PostFeed[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  addPost: (post: PostFeed) => void;
}

export const useFeedPosts = ({
  program_id,
  enabled = true,
}: UseFeedPostsProps): UseFeedPostsReturn => {
  const [posts, setPosts] = useState<PostFeed[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { mutateAsync: fetchPosts } = useCustomModelQuery();
  const { showToast } = useContexts();

  const fetchFeedPosts = useCallback(async () => {
    if (!enabled || !program_id) {
      setPosts([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetchPosts({
        endpoint: `/v2/api/kanglink/custom/trainer/feed?program_id=${program_id}&page=1&limit=50`,
        method: "GET",
      });

      if (response?.error) {
        throw new Error(response.message || "Failed to fetch posts");
      }

      const feed_posts =
        response?.data?.list || response?.data || response?.list || [];
      setPosts(feed_posts);
    } catch (error: any) {
      console.error("Error fetching feed posts:", error);
      setError(error.message || "Failed to fetch posts");
      showToast(error.message || "Failed to fetch posts", 4000, "error" as any);
    } finally {
      setIsLoading(false);
    }
  }, [program_id, enabled, fetchPosts, showToast]);

  const addPost = useCallback((new_post: PostFeed) => {
    setPosts((prev_posts) => [new_post, ...prev_posts]);
  }, []);

  // useEffect(() => {
  //   fetchFeedPosts();
  // }, [fetchFeedPosts]);

  return {
    posts,
    isLoading,
    error,
    refetch: fetchFeedPosts,
    addPost,
  };
};

export default useFeedPosts;
