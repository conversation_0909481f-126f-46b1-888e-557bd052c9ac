// User interface (matching backend specification and existing patterns)

// Athlete-specific data interface
export interface AthleteData {
  full_name?: string;
  date_of_birth?: string;
  level?: "beginner" | "intermediate" | "expert";
  fitness_goals?: string[];
  terms?: boolean;
}

// Trainer-specific data interface
export interface TrainerData {
  full_name?: string;
  gender?: "Man" | "Woman" | "Other" | "Prefer not to say";
  bio?: string;
  years_of_experience?: string;
  qualifications?: string[];
  specializations?: string[];
  email_notifications?: boolean;
  in_app_notifications?: boolean;
}
export interface User {
  id?: number | string;
  email?: string;
  first_name?: string;
  last_name?: string;
  middle_name?: string; // Added for name parsing
  full_name?: string; // Alternative field name from backend spec
  photo?: string | null;
  role?: "trainer" | "member" | "super_admin";
  role_id?: "trainer" | "member" | "super_admin"; // Alternative field name
  // Status values: 0 = Inactive, 1 = Active, 2 = Suspended
  status?: 0 | 1 | 2;
  two_factor_enabled?: boolean; // Added for 2FA status
  data?: any | AthleteData | TrainerData | Record<string, any>; // Typed user data
  created_at?: string;
  updated_at?: string;
  program?: Array<Program>;
  enrollment?: Enrollment[];
}

// Trainer interface
export interface Trainer extends User {
  role?: "trainer";
  role_id?: "trainer";
  rating?: number;
  data?: TrainerData;
  enrollments?: number;
  programs?: number;
  dateAdded?: string;
  name?: string;
}

// Athlete interface (alias for Member)
export interface Athlete extends User {
  role?: "member";
  role_id?: "member";
  data?: AthleteData;
}

// Constants for type safety
export const USER_LEVELS = ["beginner", "intermediate", "expert"] as const;
export type UserLevel = (typeof USER_LEVELS)[number];

export const GENDER_OPTIONS = [
  "Man",
  "Woman",
  "Other",
  "Prefer not to say",
] as const;
export type Gender = (typeof GENDER_OPTIONS)[number];

export const EXPERIENCE_LEVELS = [
  "0-1 years",
  "1-3 years",
  "3-5 years",
  "5-10 years",
  "10+ years",
] as const;
export type ExperienceLevel = (typeof EXPERIENCE_LEVELS)[number];

export const FITNESS_GOALS = [
  "Sports Performance",
  "Weight Loss",
  "Muscle Gain",
  "Endurance",
  "Flexibility",
  "General Fitness",
] as const;
export type FitnessGoal = (typeof FITNESS_GOALS)[number];

// Example data objects (properly formatted)
export const EXAMPLE_ATHLETE_DATA: AthleteData = {
  full_name: "Ben Athlete",
  date_of_birth: "2025-06-26",
  level: "beginner",
  fitness_goals: ["Sports Performance", "Weight Loss", "Muscle Gain"],
  terms: true,
};

export const EXAMPLE_TRAINER_DATA: TrainerData = {
  full_name: "Benjamin Possible",
  gender: "Man",
  bio: "Experienced fitness trainer focused on strength and conditioning",
  years_of_experience: "3-5 years",
  qualifications: ["NASM-CPT", "CSCS"],
  specializations: ["Strength Training", "Weight Loss", "Sports Performance"],
  email_notifications: true,
  in_app_notifications: true,
};

// Helper types for forms and API requests
export type AthleteSignupForm = AthleteData & {
  email: string;
  password: string;
  confirm_password: string;
};

export type TrainerSignupForm = TrainerData & {
  email: string;
  password: string;
  confirm_password: string;
};

export type UpdateAthleteProfile = Partial<AthleteData>;
export type UpdateTrainerProfile = Partial<TrainerData>;

// API request/response types
export type CreateUserRequest = {
  email: string;
  password: string;
  role: "trainer" | "member";
  data: AthleteData | TrainerData;
};

export type UserResponse = User & {
  token?: string;
  refresh_token?: string;
};

export interface Program {
  id?: number | string;
  user_id?: number | string;
  image?: string;
  program_name?: string;
  type_of_program?: string;
  program_description?: string;
  payment_plan?: string[];
  track_progress?: boolean;
  allow_comments?: boolean;
  allow_private_messages?: boolean;
  target_levels?: string[];
  split_program?: number;
  currency?: string;
  days_for_preview?: number;
  created_at?: string;
  updated_at?: string;
  approval_date?: string;
  status?: "draft" | "pending_approval" | "published" | "rejected" | "archived";
  splits?: Split[];
  rating?: number;
  user?: User | Trainer | Athlete;
  enrollment?: Enrollment[];
  program_discount?: ProgramDiscount | ProgramDiscount[];
  program?: Program;
}

export interface Split {
  id?: string | number;
  program_id?: string | number;
  equipment_required?: string;
  title?: string;
  full_price?: number;
  subscription?: number;
  created_at?: string;
  updated_at?: string;
  weeks?: Week[];
}

export interface Week {
  id?: string | number;
  split_id?: string | number;
  title?: string;
  week_order?: number;
  equipment_required?: string;
  created_at?: string;
  updated_at?: string;
  days?: Day[];
}

export interface Day {
  id?: string | number;
  week_id?: string | number;
  title?: string;
  day_order?: number;
  is_rest_day?: boolean;
  created_at?: string;
  updated_at?: string;
  sessions?: Session[];
}

export interface Session {
  id?: string | number;
  day_id?: string | number;
  title?: string;
  session_order?: number;
  created_at?: string;
  updated_at?: string;
  exercises?: ExerciseInstance[];
}

export interface Exercise {
  id?: number | string;
  name?: string;
  type?: number;
  exercise_type?: string;
  user_id?: number | null;
  video_url?: string | null;
  created_at?: string;
  updated_at?: string;
  temp?: boolean;
}

export interface ExerciseInstance {
  id?: number | string;
  user_id?: number;
  session_id?: number | string;
  exercise_id?: number | string;
  exercise_name?: string | null;
  video_url?: string | null;
  exercise?: Exercise;
  sets?: string | null;
  reps_or_time?: string | null;
  reps_time_type?: "reps" | "time";
  exercise_details?: string | null;
  rest_duration_minutes?: number;
  rest_duration_seconds?: number;
  label?: string | null;
  label_number?: string;
  is_linked?: boolean;
  exercise_order?: number;
  time_minutes?: number;
  time_seconds?: number;
  created_at?: string;
  updated_at?: string;
}

// Discount-related interfaces
export interface Discount {
  id?: number | string;
  program_id?: number | string;
  split_id?: number | string;
  discount_type?: "fixed" | "percentage";
  discount_value?: number;
  applies_to?: "subscription" | "full_payment" | "both";
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Coupon {
  id?: number | string;
  program_id?: number | string;
  code?: string;
  discount_type?: "fixed" | "percentage";
  discount_value?: number;
  applies_to?: "subscription" | "full_payment" | "both";
  is_active?: boolean;
  expiry_date?: string | null;
  usage_limit?: number | null;
  used_count?: number;
  created_at?: string;
  updated_at?: string;
}

export interface CouponUsage {
  id?: number | string;
  coupon_id?: number | string;
  user_id?: number | string;
  program_id?: number | string;
  split_id?: number | string | null;
  discount_amount?: number;
  used_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface ProgramDiscount {
  id?: number | string;
  program_id?: number | string;
  affiliate_link?: string;
  sale_discount_type?: "fixed" | "percentage" | null;
  sale_discount_value?: number | null;
  sale_apply_to_all?: boolean;
  program?: Program;
  discounts?: Discount[];
  coupons?: Coupon[];
  created_at?: string;
  updated_at?: string;
}

// Post Feed System
export interface PostFeed {
  id?: number | string;
  user_id?: number | string;
  program_id?: number | string;
  split_id?: number | string | null;

  post_type?: "review" | "announcement" | "question" | "update";
  content?: string;
  rating?: number; // Only for reviews
  attachments?: string[] | null;

  is_private?: boolean;
  visibility_scope?: "public" | "program_members" | "private"; // New
  is_pinned?: boolean; // New
  pin_expiration?: string | null; // New
  is_edited?: boolean; // New
  is_flagged?: boolean; // New
  flag_reason?: string | null; // New

  user?: User;
  program?: Program;
  split?: Split;

  comments?: Comment[];
  reactions?: Reaction[];

  reaction_count?: number;
  comment_count?: number;

  created_at?: string;
  updated_at?: string;
}

export interface Comment {
  id?: number | string;
  user_id?: number | string;
  post_id?: number | string;

  content?: string;
  parent_comment_id?: number | string | null; // For nested replies
  is_private?: boolean;
  mentioned_users?: (number | string)[] | null;

  attachments?: string[] | null; // New
  is_edited?: boolean; // New
  is_flagged?: boolean; // New
  flag_reason?: string | null; // New

  user?: User;
  post?: PostFeed;

  reactions?: Reaction[];
  reaction_count?: number;

  replies?: Comment[]; // Nested replies

  created_at?: string;
  updated_at?: string;
}

export interface Reaction {
  id?: number | string;
  user_id?: number | string;

  target_type?: "post" | "comment"; // What is being reacted to
  target_id?: number | string; // Unified target reference

  reaction_type?: "like" | "love" | "fire" | "strong";

  user?: User;
  created_at?: string;
  updated_at?: string;
}

// Specialized types extending the PostFeed system for better type safety

// Program Review - extends PostFeed with review-specific constraints
export interface ProgramReview extends PostFeed {
  post_type: "review"; // Required to be review
  rating: number; // Required for reviews (1-5 stars)
  content: string; // Required review text
  program_id: number | string; // Required program reference
}

// Program Announcement - extends PostFeed for trainer announcements
export interface ProgramAnnouncement extends PostFeed {
  post_type: "announcement";
  content: string; // Required announcement text
  program_id: number | string; // Required program reference
  is_pinned?: boolean; // Announcements can be pinned
  pin_expiration?: string | null;
}

// Program Question - extends PostFeed for user questions
export interface ProgramQuestion extends PostFeed {
  post_type: "question";
  content: string; // Required question text
  program_id: number | string; // Required program reference
  is_answered?: boolean; // Track if question has been answered
  best_answer_comment_id?: number | string | null; // Reference to best answer
}

// Program Update - extends PostFeed for program updates
export interface ProgramUpdate extends PostFeed {
  post_type: "update";
  content: string; // Required update text
  program_id: number | string; // Required program reference
  update_type?: "content" | "schedule" | "pricing" | "general"; // Type of update
}

// Review-specific comment (extends Comment with review context)
export interface ReviewComment extends Comment {
  post?: ProgramReview; // Specifically references a review post
}

// Helper type for creating new posts
export type CreatePostFeed = Omit<
  PostFeed,
  | "id"
  | "created_at"
  | "updated_at"
  | "user"
  | "program"
  | "split"
  | "comments"
  | "reactions"
  | "reaction_count"
  | "comment_count"
>;

// Helper type for creating new reviews
export type CreateProgramReview = Omit<
  ProgramReview,
  | "id"
  | "created_at"
  | "updated_at"
  | "user"
  | "program"
  | "split"
  | "comments"
  | "reactions"
  | "reaction_count"
  | "comment_count"
>;

// Helper type for creating new comments
export type CreateComment = Omit<
  Comment,
  | "id"
  | "created_at"
  | "updated_at"
  | "user"
  | "post"
  | "reactions"
  | "reaction_count"
  | "replies"
>;

// Helper type for creating new reactions
export type CreateReaction = Omit<
  Reaction,
  "id" | "created_at" | "updated_at" | "user"
>;

// Trainer Dashboard Models - Updated to match API response
export interface Notification {
  id: number;
  notification_type: string;
  category:
    | "progress"
    | "enrollment"
    | "payment"
    | "communication"
    | "system"
    | "general";
  title: string;
  message: string;
  data?: {
    program_id?: number;
    enrollment_id?: number;
    [key: string]: any;
  };
  is_read: boolean;
  read_at?: string | null;
  created_at: string;
  sender_id?: number;
  sender_name?: string;
  related_id?: number;
  related_type?: "enrollment" | "program" | "payment" | "review";
}

export interface Activity {
  id: number;
  user_id: number;
  user_name: string;
  user_email?: string;
  actor_id: number;
  actor_name: string;
  activity_type: string;
  title: string;
  description: string;
  metadata?: {
    exercise_instance_id?: number;
    session_id?: number;
    day_id?: number;
    program_name?: string;
    split_title?: string;
    sets_completed?: number;
    reps_completed?: string;
    weight_used?: string;
    time_taken_seconds?: number;
    difficulty_rating?: number;
    enrollment_id?: number;
    total_exercises?: number;
    completed_exercises?: number;
    progress_percentage?: number;
    [key: string]: any;
  };
  visibility: "all" | "public" | "private" | "trainer_only";
  related_id?: number;
  related_type?: "day" | "week" | "program" | "enrollment" | "exercise";
  related_name?: string | null;
  program_name?: string;
  split_title?: string;
  enrollment_id?: number;
  created_at: string;
  updated_at?: string;
}

export interface DashboardStats {
  active_programs_count: number;
  active_athletes_count: number;
  monthly_revenue: number;
  total_programs_count: number;
  total_enrollments_count: number;
  currency: string;
  period: {
    month: number;
    year: number;
  };
}

// Pagination interface for API responses
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// API Response interfaces
export interface NotificationsResponse {
  error: boolean;
  data: {
    notifications: Notification[];
    pagination: PaginationInfo;
    unreadCount: number;
  };
}

export interface ActivitiesResponse {
  error: boolean;
  message?: string;
  data: {
    activities: Activity[];
    pagination: PaginationInfo;
  };
}

export interface StatsResponse {
  error: boolean;
  data: DashboardStats;
}

// Enrollment System
export interface Enrollment {
  id?: number | string;
  trainer_id?: number | string;
  athlete_id?: number | string;
  program_id?: number | string;
  split_id?: number | string;
  payment_type?: "subscription" | "one_time";
  amount?: number;
  currency?: string;
  enrollment_date?: string;
  expiry_date?: string | null;
  status?: "active" | "expired" | "cancelled" | "pending";
  payment_status?: "paid" | "pending" | "failed" | "refunded";
  created_at?: string;
  updated_at?: string;

  // Affiliate and commission fields
  affiliate_code?: string;
  affiliate_user_id?: number | string;
  commission_calculated?: boolean;
  original_amount?: number;
  discount_amount?: number;
  discount_details?: string;

  // Related entities
  trainer?: User;
  athlete?: User;
  user?: User; // Added for joining user data
  program?: Program;
  split?: Split;
}

// Transaction System - extends Enrollment for admin transaction management
export interface Transaction extends Enrollment {
  // Additional transaction-specific fields
  transaction_type?: "purchase" | "subscription" | "refund" | "payout";
  payment_method?: string;
  payment_gateway?: string;
  gateway_transaction_id?: string;
  description?: string;
  metadata?: Record<string, any>;
  processed_at?: string;

  // Computed fields for admin display
  trainer_name?: string;
  athlete_name?: string;
  program_name?: string;
  split_title?: string;
  total_amount?: number;
  coach_revenue?: number;
  platform_earning?: number;
}

// Helper type for creating new enrollments
export type CreateEnrollment = Omit<
  Enrollment,
  | "id"
  | "created_at"
  | "updated_at"
  | "trainer"
  | "athlete"
  | "program"
  | "split"
>;

// Qualification and Specialization interfaces
export interface Qualification {
  id?: number;
  name?: string;
  type?: 1 | 2;
  user?: User;
  user_id?: number;
  created_at?: string;
  updated_at?: string;
}

export interface Specialization {
  id?: number;
  name?: string;
  type?: 1 | 2;
  user?: User;
  user_id?: number;
  created_at?: string;
  updated_at?: string;
}

// Athlete Enrollment Interfaces
export interface EnrollmentRefundInfo {
  can_request: boolean;
  hours_remaining: number;
  time_limit_hours: number;
  status?: "pending" | "approved" | "rejected" | "processed";
  request_id?: number;
  requested_at?: string;
  processed_at?: string;
  admin_notes?: string;
  refund_amount?: number;
}

export interface EnrollmentSubscriptionInfo {
  billing_failed: boolean;
  stripe_subscription_id: string | null;
}

export interface EnrollmentPricing {
  full_price: number;
  subscription_price: number;
  price: number;
  currency: string;
}

export interface EnrollmentTrainer {
  id: number;
  email: string;
  full_name: string;
  first_name: string;
  last_name: string;
  photo: string | null;
}

export interface EnrollmentProgram {
  id: number;
  name: string;
  type: string;
  description: string;
  image_url: string | null;
}

export interface EnrollmentSplit {
  id: number;
  title: string;
  description: string;
  duration_weeks: number;
}

export interface EnrollmentItem {
  id: number;
  trainer_id: number;
  athlete_id: number;
  program_id: number;
  split_id: number;
  payment_type: "one_time" | "subscription";
  amount: number;
  currency: string;
  enrollment_date: string;
  status: "active" | "expired" | "cancelled" | "pending" | "refund";
  payment_status: "paid" | "failed" | "refunded" | "pending";
  category: "owned" | "subscribed" | "pending_refund" | "refunded";
  pricing: EnrollmentPricing;
  trainer: EnrollmentTrainer;
  program: EnrollmentProgram;
  split: EnrollmentSplit;
  refund_info: EnrollmentRefundInfo;
  subscription_info: EnrollmentSubscriptionInfo;
}

export interface AthleteEnrollmentsResponse {
  error: boolean;
  data: {
    owned: EnrollmentItem[];
    subscribed: EnrollmentItem[];
    pending_refund: EnrollmentItem[];
    refunded: EnrollmentItem[];
  };
  meta: {
    total_enrollments: number;
    refund_time_limit_hours: number;
    categories: {
      owned: number;
      subscribed: number;
      pending_refund: number;
      refunded: number;
    };
  };
}

// Athlete Favorites Interfaces
export interface FavoriteProgramTrainer {
  id: number;
  full_name: string;
  first_name: string;
  last_name: string;
  photo: string | null;
}

export interface FavoriteProgramItem {
  favorite_id: number;
  favorited_at: string;
  id: number;
  name: string;
  description: string;
  type: string;
  image_url: string | null;
  status: string;
  created_at: string;
  updated_at: string;
  price: number;
  average_rating: number;
  review_count: number;
  trainer: FavoriteProgramTrainer;
}

export interface FavoriteTrainerItem {
  favorite_id: number;
  favorited_at: string;
  id: number;
  email: string;
  full_name: string;
  first_name: string;
  last_name: string;
  photo: string | null;
  bio: string;
  location: string;
  average_rating: number;
  review_count: number;
  program_count: number;
}

export interface AthleteFavoriteProgramsResponse {
  error: boolean;
  data: FavoriteProgramItem[];
}

export interface AthleteFavoriteTrainersResponse {
  error: boolean;
  data: FavoriteTrainerItem[];
}

export interface FavoriteActionResponse {
  error: boolean;
  data: {
    id?: number;
    message: string;
  };
}

// Refund System Interfaces
export interface RefundEligibility {
  is_eligible: boolean;
  reasons: string[];
  time_remaining_hours: number;
  payout_time_limit_hours: number;
}

export interface RefundRequest {
  id: number;
  enrollment_id: number;
  amount: number;
  currency: string;
  reason: string;
  status: "pending" | "approved" | "rejected" | "processed";
  requested_at: string;
  processed_at?: string;
  admin_notes?: string;
  refund_amount?: number;
  stripe_refund_id?: string;
  program: {
    name: string;
    split_name?: string;
  };
  athlete?: {
    email: string;
    full_name: string;
  };
  trainer?: {
    full_name: string;
  };
  enrollment: {
    enrollment_date: string;
    payment_status?: string;
    stripe_payment_intent_id?: string;
  };
  processed_by?: number;
}

export interface RefundStatusResponse {
  enrollment: {
    id: number;
    program_name: string;
    split_name?: string;
    amount: number;
    currency: string;
    payment_type: string;
    payment_status: string;
    status: string;
    enrollment_date: string;
  };
  refund_eligibility: RefundEligibility;
  refund_request?: RefundRequest;
}

export interface RefundHistoryResponse {
  refund_requests: RefundRequest[];
  pagination: {
    current_page: number;
    per_page: number;
    total: number;
    total_pages: number;
  };
}

export interface SubmitRefundRequestPayload {
  enrollment_id: number;
  reason: string;
}

export interface SubmitRefundRequestResponse {
  refund_request_id: number;
  status: string;
  amount: number;
  currency: string;
  requested_at: string;
}

// Admin-specific refund interfaces
export interface AdminRefundListResponse {
  error: boolean;
  data: {
    refund_requests: RefundRequest[];
    pagination: {
      current_page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
}

export interface AdminRefundDetailResponse {
  error: boolean;
  data: RefundRequest;
}

export interface RefundDecisionPayload {
  decision: "approve" | "reject";
  admin_notes?: string;
}

export interface RefundDecisionResponse {
  error: boolean;
  message: string;
  data: {
    id: number;
    status: string;
    processed_at: string;
    admin_notes?: string;
  };
}

export interface ProcessRefundPayload {
  refund_amount?: number;
}

export interface ProcessRefundResponse {
  error: boolean;
  message: string;
  data: {
    refund_request_id: number;
    stripe_refund_id: string;
    refund_amount: number;
    currency: string;
    status: string;
    processed_at: string;
  };
}

export interface AdminRefundQueryParams {
  page?: number;
  limit?: number;
  status?: "pending" | "approved" | "rejected" | "processed";
  trainer_name?: string;
  date_from?: string;
  date_to?: string;
}
