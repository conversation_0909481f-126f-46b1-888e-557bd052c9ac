import React from "react";
import { MkdInput } from "@/components/MkdInput";
import { InteractiveButton } from "@/components/InteractiveButton";
import { ChevronDown } from "lucide-react";
import {
  trainerDateFilterOptions,
  trainerStatusFilterOptions,
} from "@/assets/data";

interface TrainerFiltersProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  dateFilter: string;
  setDateFilter: (value: string) => void;
  statusFilter: string;
  setStatusFilter: (value: string) => void;
  onApplyFilter: () => void;
}

const TrainerFilters: React.FC<TrainerFiltersProps> = ({
  searchTerm,
  setSearchTerm,
  dateFilter,
  setDateFilter,
  statusFilter,
  setStatusFilter,
  onApplyFilter,
}) => {
  return (
    <div className="bg-background border border-border rounded-lg p-4 sm:p-6 shadow-sm">
      <h2 className="text-xl font-semibold text-text mb-4">Filter by</h2>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
        {/* Name Search */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text">Name</label>
          <MkdInput
            type="text"
            name="trainerName"
            placeholder="Search"
            value={searchTerm}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSearchTerm(e.target.value)
            }
            className="w-full"
          />
        </div>

        {/* Date Added Filter */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text">
            Date Added
          </label>
          <div className="relative">
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="w-full h-12 px-3 pr-10 bg-input border border-border rounded-md text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent appearance-none"
            >
              {trainerDateFilterOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary pointer-events-none" />
          </div>
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text">Status</label>
          <div className="relative">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full h-12 px-3 pr-10 bg-input border border-border rounded-md text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent appearance-none"
            >
              {trainerStatusFilterOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary pointer-events-none" />
          </div>
        </div>

        {/* Apply Filter Button */}
        <div>
          <InteractiveButton
            onClick={onApplyFilter}
            type={"button"}
            className="w-full !h-12 !text-white px-3 bg-primary sm:w-auto"
          >
            Apply Filter
          </InteractiveButton>
        </div>
      </div>
    </div>
  );
};

export default TrainerFilters;
