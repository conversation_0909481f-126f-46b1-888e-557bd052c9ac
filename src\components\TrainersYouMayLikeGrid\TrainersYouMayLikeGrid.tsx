import { useRef } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import TrainerCard from "@/components/TrainerCard/TrainerCard";

interface Trainer {
  id: string;
  name: string;
  description: string;
  image: string;
  rating: number;
  startingPrice: number;
  isFavorite?: boolean;
}

interface TrainersYouMayLikeGridProps {
  trainers: Trainer[];
  onFavoriteToggle?: (trainerId: string, isFavorite: boolean) => void;
}

const TrainersYouMayLikeGrid = ({
  trainers,
  onFavoriteToggle,
}: TrainersYouMayLikeGridProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -320, // Card width + gap
        behavior: "smooth",
      });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 320, // Card width + gap
        behavior: "smooth",
      });
    }
  };

  return (
    <div className="w-full py-6 lg:py-8 xl:py-12">
      {/* Section Title */}
      <h2
        className="text-lg sm:text-xl lg:text-2xl font-bold mb-6 lg:mb-8 transition-colors duration-200"
        style={{ color: THEME_COLORS[mode].TEXT }}
      >
        Trainers You May Like
      </h2>

      {/* Trainers Container */}
      <div className="relative">
        {/* Mobile/Tablet Grid View */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:hidden gap-4 sm:gap-6 md:gap-4">
          {trainers.map((trainer) => (
            <TrainerCard
              key={trainer.id}
              trainer={trainer}
              onFavoriteToggle={onFavoriteToggle}
            />
          ))}
        </div>

        {/* Desktop Scroll Container */}
        <div className="hidden lg:block">
          <div
            ref={scrollContainerRef}
            className="flex gap-4 xl:gap-6 2xl:gap-8 overflow-x-auto scrollbar-hide pb-4"
            style={{
              scrollbarWidth: "none",
              msOverflowStyle: "none",
              minWidth: "100%",
            }}
          >
            {trainers.map((trainer) => (
              <TrainerCard
                key={trainer.id}
                trainer={trainer}
                onFavoriteToggle={onFavoriteToggle}
              />
            ))}
          </div>

          {/* Navigation Arrows - Desktop Only */}
          {trainers.length > 3 && (
            <>
              {/* Left Arrow */}
              <button
                onClick={scrollLeft}
                className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 xl:-translate-x-6 w-10 h-10 xl:w-12 xl:h-12 rounded-full shadow-lg border transition-all duration-200 hover:shadow-xl z-10"
                style={{
                  backgroundColor: THEME_COLORS[mode].CARD_BG,
                  borderColor: THEME_COLORS[mode].BORDER,
                }}
              >
                <ChevronLeftIcon
                  className="w-5 h-5 xl:w-6 xl:h-6 mx-auto transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                />
              </button>

              {/* Right Arrow */}
              <button
                onClick={scrollRight}
                className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 xl:translate-x-6 w-10 h-10 xl:w-12 xl:h-12 rounded-full shadow-lg border transition-all duration-200 hover:shadow-xl z-10"
                style={{
                  backgroundColor: THEME_COLORS[mode].CARD_BG,
                  borderColor: THEME_COLORS[mode].BORDER,
                }}
              >
                <ChevronRightIcon
                  className="w-5 h-5 xl:w-6 xl:h-6 mx-auto transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                />
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default TrainersYouMayLikeGrid;
