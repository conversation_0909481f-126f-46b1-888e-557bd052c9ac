import React, { useState } from "react";
import { useAdmin<PERSON>ler<PERSON>, Admin<PERSON>lert, PendingProgram, RefundRequest } from "@/hooks/useAdminAlerts";
import { useCreateSystemAlert } from "@/hooks/useAdminAlerts";
import { useAdminDashboard } from "@/hooks/useAdminDashboard";
import { MkdLoader } from "@/components/MkdLoader";
import { Modal } from "@/components/Modal";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { InteractiveButton } from "@/components/InteractiveButton";

interface AdminDashboardAlertsProps {
  className?: string;
  showCreateAlert?: boolean;
}

const AdminDashboardAlerts: React.FC<AdminDashboardAlertsProps> = ({
  className = "",
  showCreateAlert = false,
}) => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [createFormData, setCreateFormData] = useState({
    title: "",
    description: "",
  });

  // Get data from dashboard hook
  const { detailedData, isLoading: dashboardLoading } = useAdminDashboard();

  // Fetch recent alerts (last 5) - still need this for the alerts list
  const { data: alertsData, isLoading: alertsLoading } = useAdminAlerts({
    limit: 5,
    page: 1,
  });

  // Create system alert mutation
  const { createSystemAlert } = useCreateSystemAlert();

  const handleCreateAlert = async () => {
    if (!createFormData.title || !createFormData.description) {
      return;
    }

    try {
      await createSystemAlert({
        title: createFormData.title,
        description: createFormData.description,
      });
      
      setCreateFormData({ title: "", description: "" });
      setIsCreateModalOpen(false);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const getAlertIcon = (activityType: string) => {
    switch (activityType) {
      case "program_approval_pending":
        return (
          <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24">
            <path
              d="M12 2L2 7L12 12L22 7L12 2Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-yellow-500"
            />
            <path
              d="M2 17L12 22L22 17"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-yellow-500"
            />
            <path
              d="M2 12L12 17L22 12"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-yellow-500"
            />
          </svg>
        );
      case "new_athlete_signup":
        return (
          <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24">
            <path
              d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H6C4.93913 15 3.92172 15.4214 3.17157 16.1716C2.42143 16.9217 2 17.9391 2 19V21"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500"
            />
            <circle
              cx="9"
              cy="7"
              r="4"
              stroke="currentColor"
              strokeWidth="2"
              className="text-green-500"
            />
            <path
              d="M22 21V19C22 18.1137 21.6485 17.2528 21.0112 16.6118C20.3739 15.9709 19.5104 15.5 18.5 15H13.5"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500"
            />
            <path
              d="M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11883 19.0078 7.005C19.0078 7.89117 18.7122 8.75608 18.1676 9.45768C17.623 10.1593 16.8604 10.6597 16 10.88"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500"
            />
          </svg>
        );
      case "new_trainer_signup":
        return (
          <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24">
            <path
              d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H10C8.93913 15 7.92172 15.4214 7.17157 16.1716C6.42143 16.9217 6 17.9391 6 19V21"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-blue-500"
            />
            <circle
              cx="13"
              cy="7"
              r="4"
              stroke="currentColor"
              strokeWidth="2"
              className="text-blue-500"
            />
          </svg>
        );
      case "new_transaction":
        return (
          <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24">
            <path
              d="M12 2V6"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500"
            />
            <path
              d="M12 18V22"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500"
            />
            <path
              d="M4.93 4.93L7.76 7.76"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500"
            />
            <path
              d="M16.24 16.24L19.07 19.07"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500"
            />
            <path
              d="M2 12H6"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500"
            />
            <path
              d="M18 12H22"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500"
            />
            <path
              d="M4.93 19.07L7.76 16.24"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500"
            />
            <path
              d="M16.24 7.76L19.07 4.93"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500"
            />
          </svg>
        );
      case "refund_requested":
      case "refund_approved":
      case "refund_rejected":
        return (
          <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24">
            <path
              d="M12 2L2 7L12 12L22 7L12 2Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={activityType === "refund_approved" ? "text-green-500" : activityType === "refund_rejected" ? "text-red-500" : "text-orange-500"}
            />
            <path
              d="M2 17L12 22L22 17"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={activityType === "refund_approved" ? "text-green-500" : activityType === "refund_rejected" ? "text-red-500" : "text-orange-500"}
            />
            <path
              d="M2 12L12 17L22 12"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={activityType === "refund_approved" ? "text-green-500" : activityType === "refund_rejected" ? "text-red-500" : "text-orange-500"}
            />
          </svg>
        );
      case "low_rated_trainer":
        return (
          <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24">
            <path
              d="M12 2L15.09 8.26L22 9L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9L8.91 8.26L12 2Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-red-500"
            />
          </svg>
        );
      case "system_alert":
        return (
          <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24">
            <path
              d="M10.29 3.86L1.82 18A2 2 0 0 0 3.5 21H20.5A2 2 0 0 0 22.18 18L13.71 3.86A2 2 0 0 0 10.29 3.86Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-blue-500"
            />
            <line
              x1="12"
              y1="9"
              x2="12"
              y2="13"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-blue-500"
            />
            <line
              x1="12"
              y1="17"
              x2="12.01"
              y2="17"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-blue-500"
            />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24">
            <path
              d="M10.29 3.86L1.82 18A2 2 0 0 0 3.5 21H20.5A2 2 0 0 0 22.18 18L13.71 3.86A2 2 0 0 0 10.29 3.86Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-gray-500"
            />
            <line
              x1="12"
              y1="9"
              x2="12"
              y2="13"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-gray-500"
            />
            <line
              x1="12"
              y1="17"
              x2="12.01"
              y2="17"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-gray-500"
            />
          </svg>
        );
    }
  };

  const getAlertColor = (activityType: string) => {
    switch (activityType) {
      case "program_approval_pending":
        return "bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800";
      case "new_athlete_signup":
        return "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800";
      case "new_trainer_signup":
        return "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800";
      case "new_transaction":
        return "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800";
      case "refund_requested":
        return "bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800";
      case "refund_approved":
        return "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800";
      case "refund_rejected":
        return "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800";
      case "low_rated_trainer":
        return "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800";
      case "system_alert":
        return "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800";
      default:
        return "bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800";
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const isLoading = alertsLoading || dashboardLoading;
  // Convert pending programs to AdminAlert structure
  const pendingProgramAlerts: AdminAlert[] = (detailedData?.pendingPrograms || []).map((program: PendingProgram) => ({
    id: program.id,
    activity_type: "program_approval_pending",
    title: "Program Approval Pending",
    description: `Program "${program.program_name}" by ${program.trainer_name} is pending approval`,
    metadata: {
      program_id: program.id,
      program_name: program.program_name,
      trainer_id: program.trainer_id,
      trainer_name: program.trainer_name,
      trainer_email: program.trainer_email,
      type_of_program: program.type_of_program,
    },
    actor_name: program.trainer_name,
    actor_email: program.trainer_email,
    created_at: program.created_at,
    updated_at: program.updated_at,
  }));

  // Convert pending refunds to AdminAlert structure
  const pendingRefundAlerts: AdminAlert[] = (detailedData?.pendingRefunds || []).map((refund: RefundRequest) => ({
    id: refund.id,
    activity_type: "refund_requested",
    title: "Refund Request Pending",
    description: `Refund request for ${refund.currency} ${refund.amount} from ${refund.athlete_name}`,
    metadata: {
      refund_id: refund.id,
      amount: refund.amount,
      currency: refund.currency,
      reason: refund.reason,
      athlete_name: refund.athlete_name,
      trainer_name: refund.trainer_name,
      program_name: refund.program_name,
    },
    actor_name: refund.athlete_name,
    actor_email: refund.athlete_email,
    created_at: refund.created_at,
    updated_at: refund.updated_at,
  }));

  // Combine all alerts
  const alerts = [
    ...(alertsData?.alerts || []),
    ...pendingProgramAlerts,
    ...pendingRefundAlerts,
  ];
  const stats = detailedData?.alertStats;

  return (
    <div className={`w-full lg:w-[370px] ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold text-text">Alerts</h2>
        {showCreateAlert && (
          <InteractiveButton
            onClick={() => setIsCreateModalOpen(true)}
            className="text-sm px-3 py-1 bg-primary text-white"
          >
            Create Alert
          </InteractiveButton>
        )}
      </div>

      <div className="flex flex-col gap-4 sm:gap-6">
        {/* Statistics Cards */}
        {/* {stats && (
          <div className="grid grid-cols-2 gap-3 mb-4">
            <div className="bg-background-secondary rounded-lg p-3 border border-border">
              <div className="text-2xl font-bold text-text">{stats.recent_alerts_count}</div>
              <div className="text-xs text-text-secondary">Recent Alerts</div>
            </div>
            <div className="bg-background-secondary rounded-lg p-3 border border-border">
              <div className="text-2xl font-bold text-text">{stats.unread_alerts_count}</div>
              <div className="text-xs text-text-secondary">Unread</div>
            </div>
            <div className="bg-background-secondary rounded-lg p-3 border border-border">
              <div className="text-2xl font-bold text-text">{stats.pending_approval_programs_count || pendingPrograms.length}</div>
              <div className="text-xs text-text-secondary">Pending Programs</div>
            </div>
            <div className="bg-background-secondary rounded-lg p-3 border border-border">
              <div className="text-2xl font-bold text-text">{stats.pending_refund_requests_count || pendingRefunds.length}</div>
              <div className="text-xs text-text-secondary">Pending Refunds</div>
            </div>
          </div>
        )} */}

        {/* Alerts List */}
        <div className="bg-background-secondary rounded-md shadow-md p-4 border border-border">
          <h3 className="text-base font-medium text-text mb-3">Recent Alerts</h3>
          
          {isLoading ? (
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 mb-2"></div>
                  <div className="bg-gray-200 dark:bg-gray-700 rounded h-3"></div>
                </div>
              ))}
            </div>
          ) : alerts.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-text-secondary text-sm">No alerts found</div>
            </div>
          ) : (
            <div className="space-y-3">
              {alerts.map((alert) => (
                <div
                  key={alert.id}
                  className={`rounded-md p-3 border ${getAlertColor(alert.activity_type)}`}
                >
                  <div className="flex items-start">
                    {getAlertIcon(alert.activity_type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-text truncate">
                          {alert.title}
                        </h4>
                        <span className="text-xs text-text-secondary ml-2 flex-shrink-0">
                          {formatTimeAgo(alert.created_at)}
                        </span>
                      </div>
                      <p className="text-xs text-text-secondary mt-1 line-clamp-2">
                        {alert.description}
                      </p>
                      {alert.actor_name && alert.actor_name !== "Unknown User" && (
                        <div className="text-xs text-text-secondary mt-1">
                          by {alert.actor_name}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>


      </div>

      {/* Create System Alert Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        modalCloseClick={() => setIsCreateModalOpen(false)}
        title="Create System Alert"
        modalHeader={true}
        classes={{ modal: "h-full", modalDialog: "max-w-md", modalContent: "" }}
      >
        <div className="space-y-4 p-5">
          <div>
            <div className="mb-2">
              <label className="text-sm font-medium text-text">Alert Title</label>
            </div>
            <MkdInputV2
              value={createFormData.title}
              onChange={(value) => setCreateFormData(prev => ({ ...prev, title: value }))}
              placeholder="Enter alert title"
              required
            >
              <input
                type="text"
                className="w-full px-3 py-2 border border-border rounded-md bg-background text-text"
                placeholder="Enter alert title"
                value={createFormData.title}
                onChange={(e) => setCreateFormData(prev => ({ ...prev, title: e.target.value }))}
              />
            </MkdInputV2>
          </div>
          <div>
            <div className="mb-2">
              <label className="text-sm font-medium text-text">Alert Description</label>
            </div>
            <MkdInputV2
              value={createFormData.description}
              onChange={(value) => setCreateFormData(prev => ({ ...prev, description: value }))}
              placeholder="Enter alert description"
              type="textarea"
              required
            >
              <textarea
                className="w-full px-3 py-2 border border-border rounded-md bg-background text-text"
                placeholder="Enter alert description"
                rows={4}
                value={createFormData.description}
                onChange={(e) => setCreateFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </MkdInputV2>
          </div>
          <div className="flex justify-end gap-3 pt-4">
            <InteractiveButton
              onClick={() => setIsCreateModalOpen(false)}
              className="bg-gray-500 text-white"
            >
              Cancel
            </InteractiveButton>
            <InteractiveButton
              onClick={handleCreateAlert}
              className="bg-primary text-white"
              disabled={!createFormData.title || !createFormData.description}
            >
              Create Alert
            </InteractiveButton>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default AdminDashboardAlerts;
