import * as yup from "yup";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { InteractiveButton } from "@/components/InteractiveButton";
// import { LoginBgNew } from "@/assets/images";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum, RoleEnum } from "@/utils/Enums";
import { LazyLoad } from "@/components/LazyLoad";
import { useSDK } from "@/hooks/useSDK";
import { MkdPasswordInput } from "@/components/MkdPasswordInput";
import MkdInputV2 from "@/components/MkdInputV2";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import TwoFactorLoginModal from "@/components/Profile/TwoFactorLoginModal";

interface AdminLoginPageProps {
  role?: string;
}

const AdminLoginPage = ({
  role = RoleEnum.SUPER_ADMIN,
}: AdminLoginPageProps) => {
  const { sdk } = useSDK();
  const { state } = useTheme();
  const mode = state?.theme;

  const { authDispatch: dispatch, showToast } = useContexts();

  const [submitLoading, setSubmitLoading] = useState(false);
  
  // 2FA state
  const [show2FAModal, setShow2FAModal] = useState(false);
  const [twoFAData, setTwoFAData] = useState({
    qrCodeUrl: "",
    oneTimeToken: "",
    role: "",
  });
  
  const location = useLocation();

  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get("redirect_uri");

  const navigate = useNavigate();

  const schema = yup
    .object({
      email: yup.string().email().required(),
      password: yup.string().required(),
      remember_me: yup.boolean().optional(),
    })
    .required();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      remember_me: false,
    },
  });

  const onSubmit = async (data: yup.InferType<typeof schema>) => {
    try {
      setSubmitLoading(true);
      const result = await sdk.login(
        data.email,
        data.password,
        role,
        data.remember_me ?? false
      );

      if (!result.error) {
        localStorage.setItem("role", result.role);
        // Check if 2FA is enabled
        if (result.two_factor_authentication) {
          // Get 2FA setup data
          const twoFAResult = await sdk.get2FALoginSetup(
            data.email,
            data.password,
            role
          );

          if (!twoFAResult.error) {
            setTwoFAData({
              qrCodeUrl: twoFAResult.qr_code,
              oneTimeToken: twoFAResult.one_time_token,
              role: role,
            });
            setShow2FAModal(true);
            setSubmitLoading(false);
            return;
          } else {
            showToast(
              twoFAResult.message || "Failed to setup 2FA",
              4000,
              ToastStatusEnum.ERROR
            );
            setSubmitLoading(false);
            return;
          }
        }

        // No 2FA required, proceed with normal login
        dispatch({
          type: "LOGIN",
          payload: { ...result, remember_me: data.remember_me } as any,
        });
        showToast("Succesfully Logged In", 4000, ToastStatusEnum.SUCCESS);
        navigate(redirect_uri ?? `/admin/dashboard`);
      } else {
        setSubmitLoading(false);
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field as "email" | "password", {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error: any) {
      setSubmitLoading(false);
      showToast(
        error?.response?.data?.message
          ? error?.response?.data?.message
          : error?.message,
        4000,
        ToastStatusEnum.ERROR
      );
      setError("email", {
        type: "manual",
        message: error?.response?.data?.message
          ? error?.response?.data?.message
          : error?.message,
      });
    }
  };

  const cardStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderColor: THEME_COLORS[mode].BORDER,
    boxShadow: `0 10px 15px -3px ${THEME_COLORS[mode].SHADOW}20, 0 4px 6px -2px ${THEME_COLORS[mode].SHADOW}10`,
  };

  const welcomeTextStyles = {
    color: THEME_COLORS[mode].TEXT,
  };

  const linkStyles = {
    color: THEME_COLORS[mode].PRIMARY,
  };

  const secondaryTextStyles = {
    color: THEME_COLORS[mode].TEXT_SECONDARY,
  };

  // Handle 2FA success
  const handle2FASuccess = (data: any) => {
    dispatch({
      type: "LOGIN",
      payload: { ...data, remember_me: false } as any,
    });
    showToast("Succesfully Logged In", 4000, ToastStatusEnum.SUCCESS);
    navigate(redirect_uri ?? `/admin/dashboard`);
  };

  // Handle 2FA modal close
  const handle2FAModalClose = () => {
    setShow2FAModal(false);
    setTwoFAData({
      qrCodeUrl: "",
      oneTimeToken: "",
      role: "",
    });
  };

  return (
    <main
      className="flex justify-center min-h-svh max-h-svh h-svh flex-col bg-cover bg-no-repeat items-center transition-colors duration-200"
      style={{
        backgroundColor: THEME_COLORS[mode].BACKGROUND,
      }}
    >
      <div
        className="flex justify-center w-[90%] max-w-md flex-col items-center rounded-lg border p-6 transition-all duration-200 sm:w-[400px] md:w-[420px]"
        style={cardStyles}
      >
        {/* <svg
          xmlns="http://www.w3.org/2000/svg"
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
        >
          <path
            d="M12.5 2C10.0147 2 8 4.01472 8 6.5C8 8.98528 10.0147 11 12.5 11C14.9853 11 17 8.98528 17 6.5C17 4.01472 14.9853 2 12.5 2Z"
            fill={THEME_COLORS[mode].PRIMARY}
          />
          <path
            d="M12.5004 12.5C8.3271 12.5 5.27345 15.2936 4.4402 19.0013C4.19057 20.112 5.10014 21 6.09882 21H18.902C19.9007 21 20.8102 20.112 20.5606 19.0013C19.7274 15.2936 16.6737 12.5 12.5004 12.5Z"
            fill={THEME_COLORS[mode].PRIMARY}
          />
        </svg> */}
        <div
          className="text-xl font-semibold mb-6 transition-colors duration-200"
          style={welcomeTextStyles}
        >
          Login as Admin
        </div>

        {/* <div className="flex mb-5 items-center text-sm">
          <span className="mr-1 text-[#525252]">Don’t have account? </span>{" "}
          <Link to={`/admin/sign-up`} className="text-[#4F46E5]">
            Sign up here
          </Link>
        </div> */}

        <form
          className="w-full space-y-5 max-w-full"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="flex flex-col text-sm">
            <LazyLoad>
              <MkdInputV2
                name="email"
                type="email"
                register={register}
                errors={errors}
                required
                placeholder="Enter Email Address"
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Label className="">Email:</MkdInputV2.Label>
                  <MkdInputV2.Field
                    // placeholder="Enter your email"
                    className="border-blue-200 focus:border-blue-500"
                  />
                  <MkdInputV2.Error />
                </MkdInputV2.Container>
              </MkdInputV2>
            </LazyLoad>
          </div>
          <div className="flex flex-col text-sm">
            <LazyLoad>
              <MkdPasswordInput
                required
                name="password"
                label="Password:"
                errors={errors}
                register={register}
              />
            </LazyLoad>
          </div>
          <div className="my-2 flex justify-between text-sm">
            <div
              className="flex items-center transition-colors duration-200"
              style={secondaryTextStyles}
            >
              {/* <label
                className="flex h-[1.5rem] items-center justify-center gap-3 py-1 transition-colors duration-200"
                style={{ color: THEME_COLORS[mode].TEXT }}
              >
                <input
                  type="checkbox"

                  className="h-[1.5rem] w-[1.5rem] cursor-pointer rounded-[0.5rem] outline-0 focus:outline-none focus:ring-0 transition-colors duration-200"
                  style={checkboxStyles}
                />
                <div className="h-full cursor-pointer">Remember me</div>
              </label> */}

              <LazyLoad>
                <MkdInputV2
                  name="remember_me"
                  type="checkbox"
                  register={register}
                  errors={errors}
                  required
                >
                  <MkdInputV2.Container className="flex h-[1.5rem] items-center justify-center gap-3 py-1 transition-colors duration-200">
                    <MkdInputV2.Field
                      // placeholder="Enter your email"
                      className="h-[1.5rem] w-[1.5rem] cursor-pointer  outline-0 focus:outline-none focus:ring-0 transition-colors duration-200"
                    />
                    <MkdInputV2.Label className="!font-normal">
                      Remember me
                    </MkdInputV2.Label>
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </LazyLoad>
            </div>
            {/* <Link
              to={`/admin/forgot`}
              className="transition-colors duration-200 hover:opacity-80"
              style={linkStyles}
            >
              Forgot Password?
            </Link> */}
          </div>
          <InteractiveButton
            type="submit"
            className={`flex w-full items-center justify-center rounded-md py-2 tracking-wide text-white outline-none focus:outline-none`}
            style={{
              backgroundColor: THEME_COLORS[mode].PRIMARY,
            }}
            loading={submitLoading}
            disabled={submitLoading}
          >
            {!submitLoading ? <>Login as Administrator</> : null}
          </InteractiveButton>
        </form>
      </div>

      {/* 2FA Login Modal */}
      <TwoFactorLoginModal
        isOpen={show2FAModal}
        onClose={handle2FAModalClose}
        onSuccess={handle2FASuccess}
        qrCodeUrl={twoFAData.qrCodeUrl}
        oneTimeToken={twoFAData.oneTimeToken}
        role={twoFAData.role}
      />
    </main>
  );
};

export default AdminLoginPage;
