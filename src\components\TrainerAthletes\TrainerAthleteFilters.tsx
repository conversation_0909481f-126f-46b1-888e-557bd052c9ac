import React from "react";
import { TrainerAthleteFilters } from "./types";

interface TrainerAthleteFiltersProps {
  filters: TrainerAthleteFilters;
  onFilterChange: (key: keyof TrainerAthleteFilters, value: string) => void;
  onApplyFilter: () => void;
}

// Filter options based on API documentation
const paymentTypeOptions = [
  { value: "", label: "All" },
  { value: "subscription", label: "Subscription" },
  { value: "one_time", label: "One Time" },
];

const progressOptions = [
  { value: "", label: "All" },
  { value: "below_10", label: "Below 10%" },
  { value: "below_50", label: "Below 50%" },
  { value: "above_50", label: "Above 50%" },
  { value: "above_90", label: "Above 90%" },
  { value: "completed", label: "Completed" },
];

const TrainerAthleteFiltersComponent: React.FC<TrainerAthleteFiltersProps> = ({
  filters,
  onFilterChange,
  onApplyFilter,
}) => {
  return (
    <section className="bg-background rounded-lg shadow-sm border border-border p-6">
      <h2 className="text-xl font-semibold text-text mb-6">Filter by</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4 items-end">
        {/* Athlete Name */}
        <div className="flex-1">
          <label
            className="block text-sm font-semibold text-text mb-2"
            htmlFor="athleteName"
          >
            Athlete Name
          </label>
          <input
            type="text"
            id="athleteName"
            value={filters.athlete_name}
            onChange={(e) => onFilterChange("athlete_name", e.target.value)}
            placeholder="Search by athlete name"
            className="w-full px-3 py-2.5 border border-border rounded-md bg-background text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 hover:border-primary/50"
          />
        </div>

        {/* Type of Purchase */}
        <div className="flex-1">
          <label
            className="block text-sm font-semibold text-text mb-2"
            htmlFor="paymentType"
          >
            Type of Purchase
          </label>
          <select
            id="paymentType"
            value={filters.payment_type}
            onChange={(e) => onFilterChange("payment_type", e.target.value)}
            className="w-full px-3 py-2.5 border border-border rounded-md bg-background text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 hover:border-primary/50 cursor-pointer"
          >
            {paymentTypeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Program Name */}
        <div className="flex-1">
          <label
            className="block text-sm font-semibold text-text mb-2"
            htmlFor="programName"
          >
            Program Name
          </label>
          <input
            type="text"
            id="programName"
            value={filters.program_name}
            onChange={(e) => onFilterChange("program_name", e.target.value)}
            placeholder="Search by program name"
            className="w-full px-3 py-2.5 border border-border rounded-md bg-background text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 hover:border-primary/50"
          />
        </div>

        {/* Progress */}
        <div className="flex-1">
          <label
            className="block text-sm font-semibold text-text mb-2"
            htmlFor="progress"
          >
            Progress
          </label>
          <select
            id="progress"
            value={filters.progress}
            onChange={(e) => onFilterChange("progress", e.target.value)}
            className="w-full px-3 py-2.5 border border-border rounded-md bg-background text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 hover:border-primary/50 cursor-pointer"
          >
            {progressOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Apply Filter Button */}
        <div className="flex-none">
          <button
            type="button"
            onClick={onApplyFilter}
            className="flex w-[7.49613rem] h-[2.625rem] justify-center items-center flex-shrink-0 rounded border bg-primary text-white hover:bg-primary/90 transition-colors duration-200 font-semibold text-sm"
            style={{
              padding: "0.5975rem 1.05863rem 0.5275rem 1.0625rem",
            }}
          >
            Apply Filter
          </button>
        </div>
      </div>
    </section>
  );
};

export default TrainerAthleteFiltersComponent;
