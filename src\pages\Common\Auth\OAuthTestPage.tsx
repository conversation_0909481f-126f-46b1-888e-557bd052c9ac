import React from "react";
import { useNavigate } from "react-router-dom";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

const OAuthTestPage: React.FC = () => {
  const navigate = useNavigate();
  const { state } = useTheme();
  const mode = state?.theme;

  const testSuccessfulOAuth = () => {
    const successData = {
      error: false,
      role: "member",
      token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo2OSwicm9sZSI6Im1lbWJlciIsImlhdCI6MTc1MzM2OTgxNiwiZXhwIjoxNzUzMzczNDE2fQ.shqHoRFWvgqAA-ZsbZW_A542NJCT9x2Gun-ruCBe_WM",
      expire_at: 3600,
      user_id: 69
    };
    
    const encodedData = encodeURIComponent(JSON.stringify(successData));
    navigate(`/login/oauth?data=${encodedData}`);
  };

  const testFailedOAuth = () => {
    const errorData = {
      error: true,
      message: "User already registered without google login!"
    };
    
    const encodedData = encodeURIComponent(JSON.stringify(errorData));
    navigate(`/login/oauth?data=${encodedData}`);
  };

  const testTrainerOAuth = () => {
    const trainerData = {
      error: false,
      role: "trainer",
      token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo2OSwicm9sZSI6InRyYWluZXIiLCJpYXQiOjE3NTMzNjk4MTYsImV4cCI6MTc1MzM3MzQxNn0.shqHoRFWvgqAA-ZsbZW_A542NJCT9x2Gun-ruCBe_WM",
      expire_at: 3600,
      user_id: 69
    };
    
    const encodedData = encodeURIComponent(JSON.stringify(trainerData));
    navigate(`/login/oauth?data=${encodedData}`);
  };

  return (
    <div
      className="min-h-full grid grid-cols-1 grid-rows-1 h-full max-h-full transition-colors duration-200"
      style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND }}
    >
      <main className="flex h-full items-center justify-center px-4 py-8">
        <div className="w-full max-w-md">
          <div
            className="relative w-full shadow-lg rounded-lg px-8 py-10 transition-colors duration-200"
            style={{
              backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
              borderColor: THEME_COLORS[mode].BORDER,
              color: THEME_COLORS[mode].TEXT,
            }}
          >
            <h1
              className="text-2xl font-bold mb-6 text-center transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              OAuth Callback Test
            </h1>
            
            <div className="space-y-4">
              <button
                onClick={testSuccessfulOAuth}
                className="w-full h-12 rounded font-semibold text-base transition-colors duration-200"
                style={{
                  backgroundColor: "#10B981",
                  color: "white",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "#059669";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "#10B981";
                }}
              >
                Test Successful Member OAuth
              </button>

              <button
                onClick={testTrainerOAuth}
                className="w-full h-12 rounded font-semibold text-base transition-colors duration-200"
                style={{
                  backgroundColor: "#3B82F6",
                  color: "white",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "#2563EB";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "#3B82F6";
                }}
              >
                Test Successful Trainer OAuth
              </button>

              <button
                onClick={testFailedOAuth}
                className="w-full h-12 rounded font-semibold text-base transition-colors duration-200"
                style={{
                  backgroundColor: "#EF4444",
                  color: "white",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "#DC2626";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "#EF4444";
                }}
              >
                Test Failed OAuth
              </button>

              <button
                onClick={() => navigate("/login")}
                className="w-full h-12 rounded font-semibold text-base transition-colors duration-200"
                style={{
                  backgroundColor: THEME_COLORS[mode].PRIMARY,
                  color: THEME_COLORS[mode].TEXT_ON_PRIMARY || THEME_COLORS[mode].BACKGROUND,
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor =
                    THEME_COLORS[mode].PRIMARY_HOVER;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor =
                    THEME_COLORS[mode].PRIMARY;
                }}
              >
                Back to Login
              </button>
            </div>

            <div className="mt-6 text-sm text-center">
              <p
                className="transition-colors duration-200"
                style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
              >
                This page simulates OAuth callback responses for testing purposes.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default OAuthTestPage;
