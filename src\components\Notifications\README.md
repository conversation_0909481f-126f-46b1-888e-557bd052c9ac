# Notification System

A comprehensive notification system for the KSL platform that supports real-time notifications for members, trainers, and super admins.

## Features

- **Real-time notifications** with automatic polling
- **Role-based notifications** (member, trainer, super_admin)
- **Multiple notification types** (enrollment, progress, payment, system alerts, etc.)
- **Unread count badges** with automatic updates
- **Mark as read** functionality (individual and bulk)
- **System alerts** for super admins
- **Responsive design** with mobile support
- **TypeScript support** with full type safety

## Components

### Core Components

- `NotificationBell` - Bell icon with unread count badge
- `NotificationPanel` - Sidebar/modal for displaying notifications
- `NotificationItem` - Individual notification display
- `SystemAlertForm` - Form for super admins to create system alerts

### Context & Hooks

- `NotificationProvider` - Context provider for notification state
- `useNotifications` - Hook for accessing notification context
- `useNotificationPanel` - Hook for managing panel state

## Quick Start

### 1. Wrap your app with NotificationProvider

```tsx
import { NotificationProvider } from './context/NotificationContext';

function App() {
  return (
    <NotificationProvider>
      {/* Your app components */}
    </NotificationProvider>
  );
}
```

### 2. Add notification bell to your header

```tsx
import { NotificationBell, NotificationPanel } from './components/Notifications';
import useNotificationPanel from './hooks/useNotificationPanel';

function Header() {
  const { isOpen, openPanel, closePanel } = useNotificationPanel();

  return (
    <header>
      {/* Your header content */}
      <NotificationBell onOpenNotifications={openPanel} />
      
      <NotificationPanel
        isOpen={isOpen}
        onClose={closePanel}
        position="right"
      />
    </header>
  );
}
```

### 3. Use notification context in components

```tsx
import { useNotifications } from './context/NotificationContext';

function SomeComponent() {
  const { unreadCount, notifications, markAsRead } = useNotifications();

  return (
    <div>
      <p>Unread notifications: {unreadCount}</p>
      {notifications.map(notification => (
        <div key={notification.id}>
          {notification.title}
          <button onClick={() => markAsRead(notification.id)}>
            Mark as read
          </button>
        </div>
      ))}
    </div>
  );
}
```

## API Integration

The notification system automatically integrates with the backend API endpoints:

- **GET** `/v2/api/kanglink/custom/athlete/notifications` - Get athlete notifications
- **GET** `/v2/api/kanglink/custom/trainer/dashboard/notifications` - Get trainer notifications  
- **GET** `/v2/api/kanglink/custom/super_admin/notifications` - Get super admin notifications
- **PUT** `/{endpoint}/{id}/read` - Mark notification as read
- **PUT** `/{endpoint}/read-all` - Mark all notifications as read
- **GET** `/{endpoint}/unread-count` - Get unread count
- **POST** `/v2/api/kanglink/custom/super_admin/notifications/system-alert` - Create system alert

## Notification Types

The system supports various notification types:

- `exercise_completed` - When an athlete completes an exercise
- `day_completed` - When an athlete completes a day
- `week_completed` - When an athlete completes a week
- `program_completed` - When an athlete completes a program
- `milestone_reached` - When an athlete reaches a milestone
- `new_enrollment` - When someone enrolls in a program
- `payment_received` - When a payment is received
- `program_updated` - When a program is updated
- `athlete_message` - When an athlete sends a message
- `system_alert` - System-wide alerts (super admin only)
- `refund_requested` - When a refund is requested
- `refund_approved` - When a refund is approved
- `refund_rejected` - When a refund is rejected
- `refund_processed` - When a refund is processed

## Styling

The components use Tailwind CSS classes and are fully responsive. You can customize the appearance by:

1. **Modifying Tailwind classes** in the component files
2. **Using CSS modules** or styled-components
3. **Overriding with custom CSS**

## Advanced Usage

### Custom Notification Service

```tsx
import NotificationService from './utils/NotificationService';
import MkdSDK from './utils/MkdSDK';

const sdk = new MkdSDK();
const notificationService = new NotificationService(sdk);

// Get notifications with filters
const notifications = await notificationService.getNotifications({
  page: 1,
  limit: 20,
  unread_only: false,
  category: 'payment'
});

// Mark as read
await notificationService.markAsRead(notificationId);

// Get unread count
const count = await notificationService.getUnreadCount();
```

### System Alerts (Super Admin Only)

```tsx
import { SystemAlertForm } from './components/Notifications';

function SuperAdminDashboard() {
  const [showAlertForm, setShowAlertForm] = useState(false);

  return (
    <div>
      <button onClick={() => setShowAlertForm(true)}>
        Create System Alert
      </button>
      
      {showAlertForm && (
        <SystemAlertForm
          onClose={() => setShowAlertForm(false)}
          onSuccess={() => {
            console.log('Alert created successfully');
          }}
        />
      )}
    </div>
  );
}
```

### Custom Notification Panel

```tsx
import { NotificationPanel } from './components/Notifications';

function CustomLayout() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="flex">
      {/* Left sidebar */}
      <div className="w-64 bg-gray-100">
        {/* Your sidebar content */}
      </div>
      
      {/* Main content */}
      <div className="flex-1">
        {/* Your main content */}
      </div>
      
      {/* Notification panel on the left */}
      <NotificationPanel
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        position="left"
        className="z-50"
      />
    </div>
  );
}
```

## Configuration

### Polling Interval

The notification system polls for updates every 30 seconds by default. You can modify this in the `NotificationProvider`:

```tsx
// In NotificationProvider.tsx
useEffect(() => {
  const interval = setInterval(fetchUnreadCount, 30000); // 30 seconds
  return () => clearInterval(interval);
}, []);
```

### API Endpoints

The system automatically detects the user role and uses the appropriate endpoint. You can customize this in `NotificationService.ts`:

```tsx
private getNotificationEndpoint(): string {
  const role = this.sdk.getRole();
  
  switch (role) {
    case 'super_admin':
      return '/v2/api/kanglink/custom/super_admin/notifications';
    case 'trainer':
      return '/v2/api/kanglink/custom/trainer/dashboard/notifications';
    case 'member':
    default:
      return '/v2/api/kanglink/custom/athlete/notifications';
  }
}
```

## Error Handling

The notification system includes comprehensive error handling:

- **Network errors** are caught and displayed to users
- **API errors** are logged and handled gracefully
- **Loading states** are shown during API calls
- **Retry functionality** is available for failed requests

## Performance

- **Automatic polling** with configurable intervals
- **Optimistic updates** for better UX
- **Debounced API calls** to prevent spam
- **Efficient re-renders** with React.memo where appropriate

## Browser Support

- **Modern browsers** (Chrome, Firefox, Safari, Edge)
- **Mobile responsive** design
- **Progressive enhancement** for older browsers

## Contributing

When adding new notification types:

1. **Update the backend** notification service
2. **Add the type** to the frontend NotificationService
3. **Add icons and colors** for the new type
4. **Update documentation** and examples

## Troubleshooting

### Common Issues

1. **Notifications not loading**: Check API endpoints and authentication
2. **Unread count not updating**: Verify polling is working
3. **Role-based issues**: Ensure user role is correctly set
4. **Styling conflicts**: Check for CSS conflicts with existing styles

### Debug Mode

Enable debug logging by setting:

```tsx
// In NotificationService.ts
console.log('Notification API call:', endpoint, params);
```

## License

This notification system is part of the KSL platform and follows the same licensing terms. 