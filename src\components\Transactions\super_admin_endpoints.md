# Super Admin API Endpoints Documentation

This document describes all available API endpoints for super admin functionality in the KangLink platform.

## Authentication

All super admin endpoints require authentication with a valid token and `super_admin` role.

**Headers Required:**
```
Authorization: Bearer <token>
```

## Base URL

All endpoints are prefixed with: `/v2/api/kanglink/custom/super_admin`

---

## Program Management Endpoints

### 1. Approve Program

**Endpoint:** `PUT /programs/:program_id/approve`

**Description:** Approves a program that is pending approval and creates an affiliate link for it.

**Parameters:**
- `program_id` (path parameter): The ID of the program to approve

**Prerequisites:**
- Program must exist and have status `pending_approval`
- Payout settings must be configured in the system

**Response:**
```json
{
  "error": false,
  "message": "Program approved successfully and affiliate link created"
}
```

**Error Responses:**
- `400`: Invalid program ID, program not in pending_approval status, or payout settings not configured
- `404`: Program not found
- `500`: Server error

---

### 2. Update Program Status

**Endpoint:** `PUT /programs/:program_id/status`

**Description:** Updates the status of a program to any valid status.

**Parameters:**
- `program_id` (path parameter): The ID of the program to update

**Request Body:**
```json
{
  "status": "published",
  "rejection_reason": "Optional reason if status is 'rejected'"
}
```

**Valid Status Values:**
- `draft`
- `pending_approval`
- `live`
- `published`
- `rejected`
- `archived`

**Response:**
```json
{
  "error": false,
  "message": "Program status updated to published successfully",
  "data": {
    "program_id": 123,
    "status": "published",
    "trainer": {
      "id": 456,
      "email": "<EMAIL>",
      "name": "John Doe"
    },
    "updated_at": "2024-01-01 12:00:00",
    "rejection_reason": "Only included if status is 'rejected'"
  }
}
```

**Error Responses:**
- `400`: Invalid program ID, invalid status, or missing rejection reason for rejected status
- `404`: Program not found
- `500`: Server error

---

### 3. Get Pending Programs

**Endpoint:** `GET /programs/pending`

**Description:** Retrieves all programs that are pending approval with pagination.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of items per page (default: 10)

**Response:**
```json
{
  "error": false,
  "message": "Pending programs retrieved successfully",
  "data": {
    "programs": [
      {
        "id": 123,
        "program_name": "Advanced Strength Training",
        "type_of_program": "strength",
        "program_description": "A comprehensive strength training program",
        "status": "pending_approval",
        "split_count": 3,
        "payment_plan": {},
        "image": "program_image.jpg",
        "trainer": {
          "id": 456,
          "email": "<EMAIL>",
          "name": "John Doe",
          "photo": "trainer_photo.jpg"
        },
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 11:00:00"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "total_pages": 3
    }
  }
}
```

---

### 4. Get All Programs with Filtering

**Endpoint:** `GET /programs`

**Description:** Retrieves all programs with advanced filtering, searching, and pagination options.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of items per page (default: 10)
- `status` (optional): Filter by program status
- `trainer_id` (optional): Filter by trainer ID
- `search` (optional): Search in program name, description, or trainer email
- `sort_by` (optional): Sort field - `created_at`, `updated_at`, `program_name`, `status` (default: `created_at`)
- `sort_order` (optional): Sort direction - `ASC` or `DESC` (default: `DESC`)

**Response:**
```json
{
  "error": false,
  "message": "Programs retrieved successfully",
  "data": {
    "programs": [
      {
        "id": 123,
        "program_name": "Advanced Strength Training",
        "type_of_program": "strength",
        "program_description": "A comprehensive strength training program",
        "status": "published",
        "split_count": 3,
        "payment_plan": {},
        "image": "program_image.jpg",
        "rating": 4.5,
        "trainer": {
          "id": 456,
          "email": "<EMAIL>",
          "name": "John Doe",
          "photo": "trainer_photo.jpg"
        },
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 11:00:00"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "total_pages": 10
    },
    "filters": {
      "status": "published",
      "trainer_id": null,
      "search": null,
      "sort_by": "created_at",
      "sort_order": "DESC"
    }
  }
}
```

---

### 5. Get Program Details

**Endpoint:** `GET /programs/:program_id`

**Description:** Retrieves detailed information about a specific program including splits, structure summary, and trainer details.

**Parameters:**
- `program_id` (path parameter): The ID of the program to retrieve

**Response:**
```json
{
  "error": false,
  "message": "Program details retrieved successfully",
  "data": {
    "id": 123,
    "program_name": "Advanced Strength Training",
    "type_of_program": "strength",
    "program_description": "A comprehensive strength training program",
    "status": "published",
    "rating": 4.5,
    "track_progress": true,
    "allow_comments": true,
    "allow_private_messages": true,
    "target_levels": ["beginner", "intermediate"],
    "split_program": true,
    "currency": "USD",
    "days_for_preview": 3,
    "image": "program_image.jpg",
    "payment_plan": {},
    "pricing": {
      "min_price": 29.99,
      "max_price": 99.99,
      "currency": "USD"
    },
    "structure": {
      "split_count": 3,
      "week_count": 12,
      "day_count": 84,
      "session_count": 168,
      "exercise_count": 500
    },
    "splits": [
      {
        "id": 1,
        "title": "Upper Body",
        "full_price": 49.99,
        "subscription": 19.99,
        "equipment_required": "dumbbells, bench",
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 11:00:00"
      }
    ],
    "trainer": {
      "id": 456,
      "email": "<EMAIL>",
      "name": "John Doe",
      "photo": "trainer_photo.jpg",
      "bio": "Certified personal trainer with 10 years experience",
      "specializations": ["strength training", "bodybuilding"],
      "qualifications": ["NASM-CPT", "CSCS"],
      "years_of_experience": 10,
      "joined_at": "2023-01-01 00:00:00"
    },
    "created_at": "2024-01-01 10:00:00",
    "updated_at": "2024-01-01 11:00:00"
  }
}
```

**Error Responses:**
- `400`: Invalid program ID
- `404`: Program not found
- `500`: Server error

---

## Payout Settings Management

### 6. Get Payout Settings

**Endpoint:** `GET /payout-settings`

**Description:** Retrieves the current active payout settings.

**Response:**
```json
{
  "error": false,
  "data": {
    "id": 1,
    "trainer_payout_time_hours": 72,
    "split_company_percentage": 30.0,
    "split_trainer_percentage": 70.0,
    "affiliate_company_percentage": 20.0,
    "affiliate_trainer_percentage": 80.0,
    "is_active": true,
    "created_at": "2024-01-01 10:00:00",
    "updated_at": "2024-01-01 10:00:00"
  }
}
```

**Error Responses:**
- `404`: No active payout settings found
- `500`: Server error

---

### 7. Create/Update Payout Settings

**Endpoint:** `POST /payout-settings`

**Description:** Creates new payout settings and deactivates existing ones.

**Request Body:**
```json
{
  "trainer_payout_time_hours": 72,
  "split_company_percentage": 30.0,
  "split_trainer_percentage": 70.0,
  "affiliate_company_percentage": 20.0,
  "affiliate_trainer_percentage": 80.0
}
```

**Validation Rules:**
- All fields are required
- `split_company_percentage` + `split_trainer_percentage` must equal 100
- `affiliate_company_percentage` + `affiliate_trainer_percentage` must equal 100

**Response:**
```json
{
  "error": false,
  "message": "Payout settings created successfully",
  "data": {
    "id": 2,
    "trainer_payout_time_hours": 72,
    "split_company_percentage": 30.0,
    "split_trainer_percentage": 70.0,
    "affiliate_company_percentage": 20.0,
    "affiliate_trainer_percentage": 80.0,
    "is_active": true,
    "created_at": "2024-01-01 12:00:00",
    "updated_at": "2024-01-01 12:00:00"
  }
}
```

**Error Responses:**
- `400`: Missing required fields or invalid percentage totals
- `500`: Server error

---

## Commission and Payout Management

### 8. Get Pending Payouts

**Endpoint:** `GET /payouts/pending`

**Description:** Retrieves all commissions that are ready for payout processing.

**Response:**
```json
{
  "error": false,
  "data": [
    {
      "commission_id": 123,
      "trainer_id": 456,
      "trainer_name": "John Doe",
      "trainer_email": "<EMAIL>",
      "amount": 35.00,
      "commission_type": "split",
      "enrollment_id": 789,
      "program_name": "Advanced Strength Training",
      "split_title": "Upper Body",
      "payout_status": "pending",
      "created_at": "2024-01-01 10:00:00"
    }
  ],
  "count": 1
}
```

**Error Responses:**
- `500`: Server error

---

### 9. Process Commission Payout

**Endpoint:** `PUT /payouts/:commission_id/process`

**Description:** Marks a specific commission as processed.

**Parameters:**
- `commission_id` (path parameter): The ID of the commission to process

**Response:**
```json
{
  "error": false,
  "message": "Commission payout processed successfully",
  "data": {
    "id": 123,
    "trainer_id": 456,
    "amount": 35.00,
    "payout_status": "processed",
    "processed_at": "2024-01-01 15:00:00"
  }
}
```

**Error Responses:**
- `400`: Invalid commission ID or commission not in pending status
- `404`: Commission not found
- `500`: Server error

---

### 10. Get Trainer Commission Summary

**Endpoint:** `GET /commissions/trainer/:trainer_id`

**Description:** Retrieves commission summary for a specific trainer.

**Parameters:**
- `trainer_id` (path parameter): The ID of the trainer

**Query Parameters:**
- `status` (optional): Filter by payout status (`pending`, `processed`, `failed`)

**Response:**
```json
{
  "error": false,
  "data": {
    "trainer_id": 456,
    "trainer_name": "John Doe",
    "total_commissions": 500.00,
    "pending_amount": 150.00,
    "processed_amount": 350.00,
    "commission_count": 25,
    "commissions": [
      {
        "id": 123,
        "amount": 35.00,
        "commission_type": "split",
        "payout_status": "pending",
        "enrollment_id": 789,
        "program_name": "Advanced Strength Training",
        "created_at": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

**Error Responses:**
- `400`: Invalid trainer ID
- `500`: Server error

---

### 11. Get Affiliate Commission Summary

**Endpoint:** `GET /commissions/affiliate/:trainer_id`

**Description:** Retrieves affiliate commission summary for a specific trainer.

**Parameters:**
- `trainer_id` (path parameter): The ID of the trainer

**Query Parameters:**
- `status` (optional): Filter by payout status (`pending`, `processed`, `failed`)

**Response:**
```json
{
  "error": false,
  "data": {
    "trainer_id": 456,
    "trainer_name": "John Doe",
    "total_affiliate_commissions": 200.00,
    "pending_amount": 75.00,
    "processed_amount": 125.00,
    "commission_count": 10,
    "commissions": [
      {
        "id": 124,
        "amount": 25.00,
        "commission_type": "affiliate",
        "payout_status": "pending",
        "enrollment_id": 790,
        "program_name": "Cardio Blast",
        "referred_trainer_name": "Jane Smith",
        "created_at": "2024-01-01 11:00:00"
      }
    ]
  }
}
```

**Error Responses:**
- `400`: Invalid trainer ID
- `500`: Server error

---

## Error Handling

All endpoints follow a consistent error response format:

```json
{
  "error": true,
  "message": "Error description"
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created successfully
- `400`: Bad request (validation errors)
- `401`: Unauthorized (invalid or missing token)
- `403`: Forbidden (insufficient permissions)
- `404`: Resource not found
- `500`: Internal server error

---

## Notes

1. **Affiliate Link Generation**: When a program is approved, an affiliate link is automatically generated using the format: `{origin}/athlete/program/{program_id}?ref={affiliate_code}`

2. **Payout Settings**: Must be configured before any programs can be approved. The system validates that percentage splits add up to 100%.

3. **Commission Processing**: Commissions become eligible for payout based on the `trainer_payout_time_hours` setting in payout settings.

4. **Program Status Flow**: Typical flow is `draft` → `pending_approval` → `published` (via approval) or `rejected`

5. **Pagination**: Most list endpoints support pagination with `page` and `limit` parameters.

6. **Search and Filtering**: The programs endpoint supports advanced filtering and searching capabilities.
