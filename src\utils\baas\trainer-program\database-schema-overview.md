# Trainer Program Database Schema Overview

## Schema Structure

The database schema is designed to support the complete trainer program creation and management functionality, including:

- Multi-step program creation (draft/publish workflow)
- Hierarchical program structure (splits → weeks → days → sessions → exercises)
- Exercise and video library management
- Audit logging and access tracking
- Configuration management

---

## Core Tables

### 1. Program Management Tables

#### `trainer_programs`
- **Purpose**: Main programs table storing basic program information
- **Key Fields**: 
  - `id`, `trainer_id`, `program_name`, `type_of_program`
  - `status` (draft/published/archived)
  - `track_progress`, `allow_comments`, `allow_private_messages`
  - `currency`, `days_for_preview`

#### `program_payment_plans`
- **Purpose**: Stores selected payment options (oneTime, monthly)
- **Relationship**: Many-to-many with programs

#### `program_target_levels`
- **Purpose**: Stores target audience levels (beginner, intermediate, expert)
- **Relationship**: Many-to-many with programs

#### `program_splits`
- **Purpose**: Pricing tiers/splits for programs
- **Key Fields**: `split_id` (UUID), `title`, `full_price`, `subscription_price`

#### `program_metadata`
- **Purpose**: Additional program details from step two
- **Key Fields**: `equipment_required`, `program_description`

---

### 2. Program Structure Tables (Hierarchical)

#### `program_weeks`
- **Purpose**: Week-level organization within splits
- **Key Fields**: `week_id` (UUID), `name`, `split_id`, `sort_order`

#### `program_days`
- **Purpose**: Day-level organization within weeks
- **Key Fields**: `day_id` (UUID), `name`, `is_rest_day`, `sort_order`

#### `program_sessions`
- **Purpose**: Session-level organization within days
- **Key Fields**: `session_id` (UUID), `name`, `session_letter`, `session_number`

#### `program_exercises`
- **Purpose**: Individual exercises within sessions
- **Key Fields**: 
  - `exercise_id` (UUID), `name`, `sets`, `reps_or_time`
  - `video_url`, `exercise_details`
  - `rest_duration_minutes`, `rest_duration_seconds`
  - `linked_exercise_id`, `is_linked`

---

### 3. Library Tables

#### `exercise_library`
- **Purpose**: Centralized exercise database (admin + trainer exercises)
- **Key Fields**: 
  - `name`, `created_by` (admin/trainer), `trainer_id`
  - `category`, `muscle_groups` (JSON), `equipment` (JSON)

#### `video_library`
- **Purpose**: Centralized video database (admin + trainer videos)
- **Key Fields**: 
  - `name`, `url`, `created_by` (admin/trainer), `trainer_id`
  - `duration`, `thumbnail_url`, `file_size`

---

### 4. Configuration Tables

#### `program_types`
- **Purpose**: Available program categories
- **Sample Data**: Body building, High Jump, CrossFit, Cardio, Flexibility

#### `supported_currencies`
- **Purpose**: Available currencies for pricing
- **Sample Data**: USD, EUR, GBP, CAD, AUD

---

### 5. Audit and Tracking Tables

#### `program_audit_log`
- **Purpose**: Complete audit trail of all program changes
- **Key Fields**: 
  - `action_type` (CREATE, UPDATE, DELETE, PUBLISH, ARCHIVE, DUPLICATE)
  - `entity_type` (PROGRAM, SPLIT, WEEK, DAY, SESSION, EXERCISE)
  - `old_values`, `new_values` (JSON)

#### `program_access_log`
- **Purpose**: Track program views, downloads, purchases
- **Key Fields**: `access_type` (VIEW, DOWNLOAD, PREVIEW, PURCHASE)

---

## Key Relationships

### Hierarchical Structure
```
trainer_programs (1)
├── program_splits (n)
│   └── program_weeks (n)
│       └── program_days (n)
│           └── program_sessions (n)
│               └── program_exercises (n)
```

### Supporting Relationships
```
trainer_programs (1)
├── program_payment_plans (n)
├── program_target_levels (n)
├── program_metadata (1)
├── program_audit_log (n)
└── program_access_log (n)
```

---

## Views for Easy Data Retrieval

### `program_overview`
- Complete program information with aggregated payment plans and target levels
- Includes metadata and split count

### `program_structure_summary`
- Statistical summary of program structure per split
- Counts of weeks, days, sessions, exercises, rest days

### `exercise_library_view`
- Exercise library with usage statistics
- Filters by trainer access permissions

### `video_library_view`
- Video library with usage statistics
- Filters by trainer access permissions

---

## Stored Procedures

### `DuplicateProgram`
- **Purpose**: Create a copy of an existing program
- **Parameters**: source_program_id, new_program_name, target_trainer_id, copy_pricing, copy_structure
- **Features**: Handles copying of all related data with audit logging

### `GetProgramStatistics`
- **Purpose**: Get comprehensive statistics for a program
- **Returns**: Counts of all structural elements, exercises with videos, average rest duration

### `ValidateProgramStructure`
- **Purpose**: Identify structural issues in a program
- **Returns**: List of validation issues (splits without weeks, days without sessions, etc.)

---

## Data Flow

### Program Creation Flow
1. **Step One**: Insert into `trainer_programs`, `program_payment_plans`, `program_target_levels`, `program_splits`
2. **Step Two**: Insert into `program_metadata`, then hierarchical structure tables
3. **Save/Publish**: Update `status` field, insert audit log entry

### Exercise/Video Management
1. **Library Access**: Query from `exercise_library_view` or `video_library_view`
2. **Custom Addition**: Insert into `exercise_library` or `video_library` with trainer ownership
3. **Usage Tracking**: Reference in `program_exercises` table

### Audit Trail
- All changes logged in `program_audit_log` with before/after values
- Access patterns tracked in `program_access_log`
- Stored procedures automatically create audit entries

---

## Indexing Strategy

### Performance Indexes
- `trainer_id` on all trainer-owned tables
- `program_id` on all program-related tables
- `status` for filtering published/draft programs
- `created_at` for chronological queries

### Unique Constraints
- `(trainer_id, program_name)` - Prevent duplicate program names per trainer
- `(program_id, split_id)` - Prevent duplicate splits per program
- UUID fields with program context for frontend consistency

---

## Sample Data Included

### Configuration Data
- 5 program types (Body building, High Jump, CrossFit, Cardio, Flexibility)
- 5 currencies (USD, EUR, GBP, CAD, AUD)

### Library Data
- 5 admin exercises (Push-ups, Squats, Plank, Deadlift, Bench Press)
- 5 admin videos with sample URLs and metadata

---

## Security Considerations

### Data Isolation
- Trainer-specific data filtered by `trainer_id`
- Admin content accessible to all trainers
- Audit logs track all access and modifications

### Data Integrity
- Foreign key constraints ensure referential integrity
- Cascading deletes prevent orphaned records
- JSON validation for structured fields

### Performance
- Proper indexing for common query patterns
- Views optimize complex joins
- Stored procedures reduce round trips for complex operations
