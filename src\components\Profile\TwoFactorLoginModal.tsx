import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Modal } from "@/components/Modal";
import { InteractiveButton } from "@/components/InteractiveButton";
import MkdInputV2 from "@/components/MkdInputV2";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

interface TwoFactorLoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (data: any) => void;
  qrCodeUrl?: string;
  oneTimeToken: string;
  role: string;
}

// Validation schema for 2FA verification
const verificationSchema = yup.object().shape({
  verification_code: yup
    .string()
    .required("Verification code is required")
    .length(6, "Verification code must be 6 digits")
    .matches(/^\d{6}$/, "Verification code must contain only numbers"),
});

type VerificationFormData = yup.InferType<typeof verificationSchema>;

const TwoFactorLoginModal: React.FC<TwoFactorLoginModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  qrCodeUrl: _qrCodeUrl,
  oneTimeToken,
  role: _role,
}) => {
  const { sdk } = useSDK();
  const { showToast } = useContexts();
  const { state } = useTheme();
  const mode = state?.theme;

  // State management
  const [loading, setLoading] = useState(false);

  // React Hook Form for verification
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<VerificationFormData>({
    resolver: yupResolver(verificationSchema),
  });

  // Verify 2FA code during login
  const onVerifySubmit = async (data: VerificationFormData) => {
    try {
      setLoading(true);
      const result = await sdk.authenticate2FA(
        data.verification_code,
        oneTimeToken
      );

      if (!result.error) {
        showToast("Login successful!", 3000, ToastStatusEnum.SUCCESS);
        onSuccess(result);
        onClose();
      } else {
        showToast(
          result.message || "Invalid verification code",
          3000,
          ToastStatusEnum.ERROR
        );
      }
    } catch (error: any) {
      const message =
        error?.response?.data?.message || error?.message || "An error occurred";
      showToast(message, 5000, ToastStatusEnum.ERROR);
    } finally {
      setLoading(false);
    }
  };

  // Reset form when modal closes
  React.useEffect(() => {
    if (!isOpen) {
      reset();
    }
  }, [isOpen, reset]);

  const containerStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
    borderColor: THEME_COLORS[mode].BORDER,
    color: THEME_COLORS[mode].TEXT,
  };

  const buttonStyles = {
    backgroundColor: THEME_COLORS[mode].PRIMARY,
    color: THEME_COLORS[mode].TEXT_ON_PRIMARY || THEME_COLORS[mode].BACKGROUND,
  };

  const buttonHoverStyles = {
    backgroundColor: THEME_COLORS[mode].PRIMARY_HOVER,
  };

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title="Two-Factor Authentication Required"
      modalHeader
      classes={{
        modalDialog: "!w-full !px-0 md:!w-[32rem] !h-fit",
        modalContent: "!z-10 !px-0 overflow-hidden !pt-0 !mt-0",
        modal: "h-full",
      }}
    >
      <div className="p-6" style={containerStyles}>
        <div className="space-y-6">
          <div className="text-center">
            <h3
              className="text-lg font-semibold mb-2 transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              Enter Your 2FA Code
            </h3>
            <p
              className="text-sm transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
            >
              Please enter the 6-digit code from your authenticator app to
              complete login.
            </p>
          </div>

          {/* QR Code */}
          {/* {qrCodeUrl && (
            <div className="flex justify-center">
              <div className="bg-white p-4 rounded-lg border">
                <img src={qrCodeUrl} alt="2FA QR Code" className="w-48 h-48" />
              </div>
            </div>
          )} */}

          {/* Verification Code Input */}
          <form onSubmit={handleSubmit(onVerifySubmit)} className="space-y-6">
            <div className="space-y-2">
              <MkdInputV2
                name="verification_code"
                type="text"
                register={register}
                errors={errors}
                required
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Label
                    className="text-sm font-medium transition-colors duration-200"
                    style={{ color: THEME_COLORS[mode].TEXT }}
                  >
                    Verification Code
                  </MkdInputV2.Label>
                  <MkdInputV2.Field
                    placeholder="000000"
                    className="w-full h-11 px-3 py-2 rounded-md text-center text-lg font-mono tracking-widest placeholder-text-secondary focus:ring-0 transition-colors duration-200"
                    style={{
                      backgroundColor: THEME_COLORS[mode].INPUT,
                      borderColor: THEME_COLORS[mode].BORDER,
                      color: THEME_COLORS[mode].TEXT,
                    }}
                  />
                  <MkdInputV2.Error />
                </MkdInputV2.Container>
              </MkdInputV2>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between">
              <InteractiveButton
                type="button"
                onClick={onClose}
                disabled={loading}
                className="px-4 py-2 rounded border transition-colors duration-200"
                style={{
                  backgroundColor: THEME_COLORS[mode].BACKGROUND,
                  borderColor: THEME_COLORS[mode].BORDER,
                  color: THEME_COLORS[mode].TEXT,
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor =
                    THEME_COLORS[mode].BACKGROUND_HOVER;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor =
                    THEME_COLORS[mode].BACKGROUND;
                }}
              >
                Cancel
              </InteractiveButton>
              <InteractiveButton
                type="submit"
                loading={loading}
                disabled={loading}
                className="px-6 py-2 rounded font-semibold transition-colors duration-200"
                style={buttonStyles}
                onMouseEnter={(e) => {
                  if (!loading) {
                    e.currentTarget.style.backgroundColor =
                      buttonHoverStyles.backgroundColor;
                  }
                }}
                onMouseLeave={(e) => {
                  if (!loading) {
                    e.currentTarget.style.backgroundColor =
                      buttonStyles.backgroundColor;
                  }
                }}
              >
                {!loading ? "Verify & Login" : null}
              </InteractiveButton>
            </div>
          </form>
        </div>
      </div>
    </Modal>
  );
};

export default TwoFactorLoginModal;
