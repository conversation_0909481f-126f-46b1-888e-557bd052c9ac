import React from "react";
import CreateProgramPreview from "./CreateProgramPreview";
import { testStepOneData, testStepTwoData } from "./test-data";

/**
 * Test component to verify CreateProgramPreview works with snake_case data
 * This component demonstrates the correct usage of the CreateProgramPreview
 * component with properly formatted snake_case data structures.
 */
const CreateProgramPreviewTest: React.FC = () => {
  const handleSubmit = () => {
    console.log("Program preview submitted");
    console.log("Step One Data:", testStepOneData);
    console.log("Step Two Data:", testStepTwoData);
  };

  const handleCancel = () => {
    console.log("Program preview cancelled");
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-text mb-4">
              CreateProgramPreview Test
            </h1>
            <p className="text-text-secondary">
              This test component demonstrates the CreateProgramPreview component
              working with snake_case data conventions. All field names follow
              the snake_case pattern as specified in the database schema.
            </p>
          </div>

          <CreateProgramPreview
            stepOneData={testStepOneData}
            stepTwoData={testStepTwoData}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />

          <div className="mt-8 p-6 bg-background-secondary rounded-lg border border-border">
            <h2 className="text-lg font-semibold text-text mb-4">
              Data Structure Verification
            </h2>
            <div className="space-y-4 text-sm">
              <div>
                <h3 className="font-medium text-text">Step One Data (snake_case fields):</h3>
                <ul className="list-disc list-inside text-text-secondary mt-2 space-y-1">
                  <li>program_name: "{testStepOneData.program_name}"</li>
                  <li>type_of_program: "{testStepOneData.type_of_program}"</li>
                  <li>program_description: "{testStepOneData.program_description.substring(0, 50)}..."</li>
                  <li>target_levels: [{testStepOneData.target_levels.join(", ")}]</li>
                  <li>track_progress: {testStepOneData.track_progress.toString()}</li>
                  <li>allow_comments: {testStepOneData.allow_comments.toString()}</li>
                  <li>allow_private_messages: {testStepOneData.allow_private_messages.toString()}</li>
                  <li>days_for_preview: {testStepOneData.days_for_preview}</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium text-text">Exercise Data (snake_case fields):</h3>
                <ul className="list-disc list-inside text-text-secondary mt-2 space-y-1">
                  <li>reps_or_time: "8-10"</li>
                  <li>reps_time_type: "reps"</li>
                  <li>video_url: "https://example.com/..."</li>
                  <li>rest_duration_minutes: 2</li>
                  <li>rest_duration_seconds: 30</li>
                  <li>is_linked: true/false</li>
                  <li>exercise_order: 1, 2, 3...</li>
                  <li>label: "A", "B", "C"</li>
                  <li>label_number: "1", "2", "3"</li>
                </ul>
              </div>

              <div>
                <h3 className="font-medium text-text">Day Data (snake_case fields):</h3>
                <ul className="list-disc list-inside text-text-secondary mt-2 space-y-1">
                  <li>is_rest_day: true/false</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateProgramPreviewTest;
