import React from "react";

const ViewAdminDashboardPage: React.FC = () => {
  return (
    <div className="relative w-full min-h-screen bg-transparent p-6">
      {/* Page Title */}
      <h1 className="text-2xl font-bold text-[#1e1e1e] mb-8">Dashboard</h1>

      {/* Stats Cards */}
      <div className="flex gap-6 mb-12">
        <div className="bg-white rounded-md shadow p-6 w-64 flex flex-col items-center border border-[#d9dce0]">
          <span className="text-3xl font-bold text-[#1e1e1e]">120</span>
          <span className="text-sm text-[#757b8a] mt-2">No. of Athlete</span>
        </div>
        <div className="bg-white rounded-md shadow p-6 w-64 flex flex-col items-center border border-[#d9dce0]">
          <span className="text-3xl font-bold text-[#1e1e1e]">60</span>
          <span className="text-sm text-[#757b8a] mt-2">No. of Trainer</span>
        </div>
        <div className="bg-white rounded-md shadow p-6 w-64 flex flex-col items-center border border-[#d9dce0]">
          <span className="text-3xl font-bold text-[#1e1e1e]">12</span>
          <span className="text-sm text-[#757b8a] mt-2 text-center">
            Program Pending Approval
          </span>
        </div>
        <div className="bg-white rounded-md shadow p-6 w-64 flex flex-col items-center border border-[#d9dce0]">
          <span className="text-3xl font-bold text-[#1e1e1e]">5</span>
          <span className="text-sm text-[#757b8a] mt-2">Refund Requests</span>
        </div>
      </div>

      <div className="flex gap-6">
        {/* Quick Links */}
        <div className="w-[740px]">
          <h2 className="text-xl font-bold text-[#1e1e1e] mb-4">Quick Links</h2>
          <div className="flex flex-col gap-6">
            <div className="bg-white rounded-md shadow p-4 border border-[#d9dce0]">
              <h3 className="text-base font-medium text-[#1e1e1e]">
                Manage Users
              </h3>
              <p className="text-sm text-[#757b8a]">
                Links to user to manage users quickly
              </p>
            </div>
            <div className="bg-white rounded-md shadow p-4 border border-[#d9dce0]">
              <h3 className="text-base font-medium text-[#1e1e1e]">
                Content Moderation
              </h3>
              <p className="text-sm text-[#757b8a]">
                Check and approve pending program
              </p>
            </div>
            <div className="bg-white rounded-md shadow p-4 border border-[#d9dce0]">
              <h3 className="text-base font-medium text-[#1e1e1e]">
                Refund Request
              </h3>
              <p className="text-sm text-[#757b8a]">
                Quickly remove all pending refund requests
              </p>
            </div>
          </div>
        </div>

        {/* Alerts */}
        <div className="w-[370px]">
          <h2 className="text-xl font-bold text-[#1e1e1e] mb-4">Alert</h2>
          <div className="flex flex-col gap-6">
            <div className="bg-white rounded-md shadow p-4 border border-[#d9dce0]">
              <h3 className="text-base font-medium text-[#1e1e1e]">
                User was Flagged
              </h3>
              <div className="bg-[#e6f4ec] rounded-md p-2 flex items-center mt-2">
                {/* Alert Icon */}
                <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 14 16">
                  <g clipPath="url(#clip0_291_69)">
                    <path
                      d="M7 0C6.44687 0 6 0.446875 6 1V1.6C3.71875 2.0625 2 4.08125 2 6.5V7.0875C2 8.55625 1.45938 9.975 0.484375 11.075L0.253125 11.3344C-0.00937497 11.6281 -0.071875 12.05 0.0875 12.4094C0.246875 12.7688 0.60625 13 1 13H13C13.3938 13 13.75 12.7688 13.9125 12.4094C14.075 12.05 14.0094 11.6281 13.7469 11.3344L13.5156 11.075C12.5406 9.975 12 8.55938 12 7.0875V6.5C12 4.08125 10.2812 2.0625 8 1.6V1C8 0.446875 7.55312 0 7 0ZM8.41562 15.4156C8.79062 15.0406 9 14.5312 9 14H7H5C5 14.5312 5.20937 15.0406 5.58437 15.4156C5.95937 15.7906 6.46875 16 7 16C7.53125 16 8.04062 15.7906 8.41562 15.4156Z"
                      fill="#4CBF6D"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_291_69">
                      <path d="M0 0H14V16H0V0Z" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                <span className="text-sm text-[#1e1e1e]">
                  5 new program approval pending
                </span>
              </div>
              <div className="bg-[#e6f4ec] rounded-md p-2 flex items-center mt-2">
                {/* Alert Icon */}
                <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 14 16">
                  <g clipPath="url(#clip0_291_69)">
                    <path
                      d="M7 0C6.44687 0 6 0.446875 6 1V1.6C3.71875 2.0625 2 4.08125 2 6.5V7.0875C2 8.55625 1.45938 9.975 0.484375 11.075L0.253125 11.3344C-0.00937497 11.6281 -0.071875 12.05 0.0875 12.4094C0.246875 12.7688 0.60625 13 1 13H13C13.3938 13 13.75 12.7688 13.9125 12.4094C14.075 12.05 14.0094 11.6281 13.7469 11.3344L13.5156 11.075C12.5406 9.975 12 8.55938 12 7.0875V6.5C12 4.08125 10.2812 2.0625 8 1.6V1C8 0.446875 7.55312 0 7 0ZM8.41562 15.4156C8.79062 15.0406 9 14.5312 9 14H7H5C5 14.5312 5.20937 15.0406 5.58437 15.4156C5.95937 15.7906 6.46875 16 7 16C7.53125 16 8.04062 15.7906 8.41562 15.4156Z"
                      fill="#4CBF6D"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_291_69">
                      <path d="M0 0H14V16H0V0Z" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                <span className="text-sm text-[#1e1e1e]">
                  2 new Trainer with 1 Star rating
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewAdminDashboardPage;
