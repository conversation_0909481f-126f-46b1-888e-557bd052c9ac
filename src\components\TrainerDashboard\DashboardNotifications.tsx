import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { IconDefinition } from "@fortawesome/free-solid-svg-icons";
import {
  faUserPlus,
  faEdit,
  faCreditCard,
  faStar,
  faBell,
  faCheck,
  faCheckDouble,
} from "@fortawesome/free-solid-svg-icons";
import { Notification } from "@/interfaces/model.interface";
import { useDate } from "@/hooks/useDate";
import { useTrainerDashboard } from "@/hooks/useTrainerDashboard";
import { useState } from "react";

interface DashboardNotificationsProps {
  notifications?: Notification[];
  isLoading?: boolean;
}

const DashboardNotifications = ({
  notifications,
  isLoading = false,
}: DashboardNotificationsProps) => {
  const { convertDate } = useDate();
  const { markNotificationAsRead, markAllNotificationsAsRead } = useTrainerDashboard();
  const [isMarkingAsRead, setIsMarkingAsRead] = useState<number | null>(null);
  const [isMarkingAllAsRead, setIsMarkingAllAsRead] = useState(false);

  // Icon mapping function based on notification type and category
  const getNotificationIcon = (
    notificationType?: string,
    category?: string
  ): IconDefinition => {
    // Map based on category first, then notification type
    switch (category) {
      case "enrollment":
        return faUserPlus;
      case "payment":
        return faCreditCard;
      case "progress":
        return faStar;
      case "communication":
        return faBell;
      case "system":
        return faEdit;
      default:
        // Fallback to notification type mapping
        switch (notificationType) {
          case "new_enrollment":
            return faUserPlus;
          case "payment_received":
            return faCreditCard;
          case "program_updated":
            return faEdit;
          case "milestone_reached":
            return faStar;
          default:
            return faBell;
        }
    }
  };

  // Handle notification click
  const handleNotificationClick = async (notification: Notification) => {
    if (!notification.is_read && !isMarkingAsRead) {
      setIsMarkingAsRead(notification.id);
      try {
        await markNotificationAsRead(notification.id);
      } catch (error) {
        console.error("Error marking notification as read:", error);
      } finally {
        setIsMarkingAsRead(null);
      }
    }
    
    // Handle navigation based on notification type
    if (notification.related_type === "enrollment" && notification.related_id) {
      // Navigate to enrollment details
      // You can implement navigation logic here
      console.log("Navigate to enrollment:", notification.related_id);
    } else if (notification.related_type === "program" && notification.related_id) {
      // Navigate to program details
      console.log("Navigate to program:", notification.related_id);
    }
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    if (isMarkingAllAsRead) return;
    
    setIsMarkingAllAsRead(true);
    try {
      await markAllNotificationsAsRead();
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    } finally {
      setIsMarkingAllAsRead(false);
    }
  };

  const notificationList = notifications?.length ? notifications : [];
  const unreadCount = notificationList.filter(n => !n.is_read).length;

  if (isLoading) {
    return (
      <div className="divide-y divide-border">
        {[1, 2, 3].map((index) => (
          <div key={index} className="flex items-center gap-4 px-6 py-4">
            <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
            <div className="flex flex-col gap-2 flex-1">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!notificationList.length) {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-6">
        <div className="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-4">
          <FontAwesomeIcon
            icon={faBell}
            className="w-6 h-6 text-gray-400 dark:text-gray-600"
          />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          No Notifications
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 text-center max-w-sm">
          You're all caught up! New notifications about enrollments, payments,
          and athlete progress will appear here.
        </p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-border">
      {/* Header with mark all as read button */}
      {unreadCount > 0 && (
        <div className="flex items-center justify-between px-6 py-3 bg-gray-50 dark:bg-gray-800">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
          </span>
          <button
            onClick={handleMarkAllAsRead}
            disabled={isMarkingAllAsRead}
            className={`text-sm font-medium transition-colors ${
              isMarkingAllAsRead 
                ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                : 'text-primary hover:text-primary-hover'
            }`}
          >
            {isMarkingAllAsRead ? 'Marking...' : 'Mark all as read'}
          </button>
        </div>
      )}

      {/* Notifications list */}
      {notificationList.map((notification) => (
        <div
          key={notification.id}
          onClick={() => handleNotificationClick(notification)}
          className={`flex items-center gap-4 px-6 py-4 cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-800 ${
            !notification.is_read ? "bg-blue-50 dark:bg-blue-900/20" : ""
          }`}
        >
          <span
            className={`inline-flex items-center justify-center w-8 h-8 rounded-full ${
              !notification.is_read ? "bg-primary" : "bg-gray-400 dark:bg-gray-600"
            }`}
          >
            <FontAwesomeIcon
              icon={getNotificationIcon(
                notification.notification_type,
                notification.category
              )}
              className="w-4 h-4 text-white"
            />
          </span>
          <div className="flex flex-col flex-1">
            <div className="flex items-center justify-between">
              <span
                className={`text-sm ${
                  !notification.is_read
                    ? "font-semibold text-text dark:text-gray-100"
                    : "text-text dark:text-gray-100"
                }`}
              >
                {notification.title}
              </span>
              <div className="flex items-center gap-2">
                {!notification.is_read && (
                  <span className="w-2 h-2 rounded-full bg-primary"></span>
                )}
                {notification.is_read && (
                  <FontAwesomeIcon
                    icon={faCheck}
                    className="w-3 h-3 text-gray-400 dark:text-gray-600"
                  />
                )}
                {isMarkingAsRead === notification.id && (
                  <div className="w-3 h-3 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                )}
              </div>
            </div>
            <span className="text-sm text-gray-600 dark:text-gray-300 mt-1">
              {notification.message}
            </span>
            <div className="flex items-center justify-between mt-2">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {notification.created_at
                  ? convertDate(notification.created_at, {
                      year: undefined,
                      month: "short",
                      day: "numeric",
                      hour: "2-digit",
                      minute: "2-digit",
                    })
                  : "Unknown time"}
              </span>
              {notification.sender_name && (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  from {notification.sender_name}
                </span>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default DashboardNotifications;
