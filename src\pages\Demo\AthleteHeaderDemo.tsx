import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { ThemeProvider } from '@/context/Theme';
import { useTheme } from '@/hooks/useTheme';
import { THEME_COLORS } from '@/context/Theme';
import AthleteHeader from '@/components/AthleteHeader/AthleteHeader';
import { ThemeToggle } from '@/components/ThemeToggle';

const DemoContent = () => {
  const { state } = useTheme();
  const mode = state?.theme;

  const contentStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    color: THEME_COLORS[mode].TEXT,
    minHeight: '100vh',
  };

  return (
    <div style={contentStyles} className="transition-colors duration-200">
      <AthleteHeader />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center space-y-8">
          <div>
            <h1 className="text-4xl font-bold mb-4" style={{ color: THEME_COLORS[mode].TEXT }}>
              AthleteHeader Demo
            </h1>
            <p className="text-lg" style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}>
              This demo showcases the responsive AthleteHeader component with theme support.
            </p>
          </div>

          <div className="flex justify-center">
            <ThemeToggle className="mb-8" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div 
              className="p-6 rounded-lg border transition-colors duration-200"
              style={{ 
                backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
                borderColor: THEME_COLORS[mode].BORDER 
              }}
            >
              <h3 className="text-xl font-semibold mb-3" style={{ color: THEME_COLORS[mode].TEXT }}>
                Responsive Design
              </h3>
              <p style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}>
                The header adapts to different screen sizes, hiding the search bar on mobile and showing a search icon instead.
              </p>
            </div>

            <div 
              className="p-6 rounded-lg border transition-colors duration-200"
              style={{ 
                backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
                borderColor: THEME_COLORS[mode].BORDER 
              }}
            >
              <h3 className="text-xl font-semibold mb-3" style={{ color: THEME_COLORS[mode].TEXT }}>
                Theme Support
              </h3>
              <p style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}>
                Full dark and light theme support with smooth transitions and proper contrast ratios.
              </p>
            </div>

            <div 
              className="p-6 rounded-lg border transition-colors duration-200"
              style={{ 
                backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
                borderColor: THEME_COLORS[mode].BORDER 
              }}
            >
              <h3 className="text-xl font-semibold mb-3" style={{ color: THEME_COLORS[mode].TEXT }}>
                Interactive Elements
              </h3>
              <p style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}>
                All buttons and icons have hover states and are fully interactive with proper accessibility.
              </p>
            </div>
          </div>

          <div className="mt-12">
            <h2 className="text-2xl font-bold mb-4" style={{ color: THEME_COLORS[mode].TEXT }}>
              Features
            </h2>
            <div className="text-left max-w-2xl mx-auto">
              <ul className="space-y-2" style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}>
                <li>• Responsive search bar (hidden on mobile, visible on tablet+)</li>
                <li>• Theme-aware colors and hover states</li>
                <li>• Sticky header with proper z-index</li>
                <li>• Notification badge indicator</li>
                <li>• Mobile-first design approach</li>
                <li>• Smooth transitions and animations</li>
                <li>• Proper semantic HTML structure</li>
                <li>• Accessibility considerations</li>
              </ul>
            </div>
          </div>

          <div className="mt-12 p-6 rounded-lg border" 
               style={{ 
                 backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
                 borderColor: THEME_COLORS[mode].BORDER 
               }}>
            <h3 className="text-xl font-semibold mb-3" style={{ color: THEME_COLORS[mode].TEXT }}>
              Try It Out
            </h3>
            <p style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}>
              Resize your browser window to see the responsive behavior, toggle between light and dark themes, 
              and interact with the search bar and navigation icons.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

const AthleteHeaderDemo = () => {
  return (
    <BrowserRouter>
      <ThemeProvider>
        <DemoContent />
      </ThemeProvider>
    </BrowserRouter>
  );
};

export default AthleteHeaderDemo;
