import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { WorkoutProgramHeader } from "@/components/WorkoutProgramHeader";
import { SubmitButton } from "@/components/SubmitButton";
import { SessionCard } from "@/components/SessionCard";
import { useSearchParams } from "react-router-dom";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { RestAPIMethodEnum } from "@/utils/Enums";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import {
  Enrollment,
  Program,
  Split,
  WorkoutDay,
  WorkoutWeek,
} from "@/interfaces";

interface OverallProgress {
  current_week_id: number | null;
  current_day_id: number | null;
  total_days_completed: number;
  total_exercises_completed: number;
  progress_percentage: number;
  last_activity_date: string | null;
}

interface EnrollmentProgramResponse {
  error: boolean;
  data: {
    enrollment: Enrollment;
    program: Program;
    split: Split;
    trainer: {
      id: number;
      email: string;
      full_name: string;
      first_name: string;
      last_name: string;
      photo: string;
    };
    weeks: WorkoutWeek[];
    overall_progress: OverallProgress;
  };
}

const ViewAthleteEmrollmentProgressPage = () => {
  const [searchParams] = useSearchParams();
  const enrollmentId = searchParams.get("enrollment");

  const [currentWeek, setCurrentWeek] = useState<WorkoutWeek | null>(null);
  const [currentDay, setCurrentDay] = useState<WorkoutDay | null>(null);
  const [currentWeekId, setCurrentWeekId] = useState<string | number | null>(
    null
  );
  const [isSubmittingDay, setIsSubmittingDay] = useState(false);
  const [completingExercises, setCompletingExercises] = useState<Set<string>>(
    new Set()
  );

  // Custom query for fetching enrollment program data
  const customQuery = useCustomModelQuery();
  const { showToast } = useContexts();

  const {
    data: enrollmentData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["enrollment-program", enrollmentId],
    queryFn: async (): Promise<EnrollmentProgramResponse> => {
      if (!enrollmentId) {
        throw new Error("Enrollment ID is required");
      }

      const response = await customQuery.mutateAsync({
        endpoint: `/v2/api/kanglink/custom/athlete/enrollment/${enrollmentId}/program`,
        method: RestAPIMethodEnum.GET,
        requiresAuth: true,
      });

      if (response.error) {
        throw new Error(
          response.message || "Failed to fetch enrollment program data"
        );
      }

      return response as EnrollmentProgramResponse;
    },
    enabled: !!enrollmentId,
    retry: 3,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Set current week and day based on progress when data loads
  useEffect(() => {
    if (enrollmentData?.data) {
      const { weeks, program } = enrollmentData.data;

      // Update page title
      document.title = `${program.program_name} - Workout Progress | KangLink`;

      // Auto-select the first incomplete week to guide user progress
      // This helps users continue from where they left off
      const firstIncompleteWeek = weeks.find(
        (week) => !week.progress?.is_completed
      );

      // Use first incomplete week or fallback to first week if all are completed
      const weekToUse = firstIncompleteWeek || weeks[0];

      if (weekToUse) {
        setCurrentWeek(weekToUse);
        setCurrentWeekId(weekToUse.id || null);

        // Auto-select the first incomplete day within the selected week
        // This ensures users see the next workout they need to complete
        const firstIncompleteDay = weekToUse.days?.find(
          (day) => !day.progress?.is_completed
        );

        // Use first incomplete day or fallback to first day if all are completed
        const dayToUse = firstIncompleteDay || weekToUse.days?.[0];
        setCurrentDay(dayToUse || null);
      }
    }
  }, [enrollmentData]);

  // Cleanup page title on unmount
  useEffect(() => {
    return () => {
      document.title = "KangLink";
    };
  }, []);

  const handleExerciseComplete = async (exerciseId: string) => {
    if (!enrollmentId || !currentDay) return;

    // Add exercise to loading set
    setCompletingExercises((prev) => new Set(prev).add(exerciseId));

    try {
      const response = await customQuery.mutateAsync({
        endpoint: `/v2/api/kanglink/custom/athlete/exercise/complete`,
        method: RestAPIMethodEnum.POST,
        requiresAuth: true,
        body: {
          enrollment_id: parseInt(enrollmentId),
          exercise_instance_id: parseInt(exerciseId),
          sets_completed: 1, // Default values - could be enhanced with user input
          reps_completed: "completed",
          weight_used: "bodyweight",
          time_taken_seconds: 0,
          difficulty_rating: 3,
          notes: "",
        },
      });

      if (!response.error) {
        // Refetch data to update UI with new completion status
        refetch();
        showToast(
          "Exercise marked as complete!",
          3000,
          ToastStatusEnum.SUCCESS
        );
      }
    } catch (_error) {
      showToast(
        "Failed to mark exercise as complete",
        5000,
        ToastStatusEnum.ERROR
      );
    } finally {
      // Remove exercise from loading set
      setCompletingExercises((prev) => {
        const newSet = new Set(prev);
        newSet.delete(exerciseId);
        return newSet;
      });
    }
  };

  const handleSubmit = async () => {
    if (!enrollmentId || !currentDay) return;

    setIsSubmittingDay(true);
    try {
      const response = await customQuery.mutateAsync({
        endpoint: `/v2/api/kanglink/custom/athlete/day/complete`,
        method: RestAPIMethodEnum.POST,
        requiresAuth: true,
        body: {
          enrollment_id: parseInt(enrollmentId),
          day_id: currentDay.id,
          notes: "Workout completed successfully!",
        },
      });

      if (!response.error) {
        // Refetch data to update UI and move to next day
        refetch();
        showToast("Day completed successfully!", 3000, ToastStatusEnum.SUCCESS);
      }
    } catch (_error) {
      showToast("Failed to mark day as complete", 5000, ToastStatusEnum.ERROR);
    } finally {
      setIsSubmittingDay(false);
    }
  };

  const handleWeekChange = (week: WorkoutWeek | null) => {
    if (week && enrollmentData?.data) {
      // Find the week in the program data
      const foundWeek = enrollmentData.data.weeks.find((w) => w.id === week.id);
      if (foundWeek) {
        setCurrentWeek(foundWeek);
        setCurrentWeekId(week.id || null);
        // Set to first day of the new week
        setCurrentDay(foundWeek.days?.[0] || null);
      }
    } else if (enrollmentData?.data) {
      // Reset to first week if cleared
      const firstWeek = enrollmentData.data.weeks[0];
      if (firstWeek) {
        setCurrentWeek(firstWeek);
        setCurrentWeekId(firstWeek.id || null);
        setCurrentDay(firstWeek.days?.[0] || null);
      }
    }
  };

  const handleDayChange = (day: WorkoutDay | null) => {
    if (day && currentWeek) {
      // Find the day in the current week data
      const foundDay = currentWeek.days?.find((d) => d.id === day.id);
      if (foundDay) {
        setCurrentDay(foundDay);
      }
    } else if (currentWeek?.days) {
      // Reset to first day if cleared
      setCurrentDay(currentWeek.days[0] || null);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background transition-colors duration-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="animate-pulse space-y-6">
            <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            <div className="space-y-4">
              <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-background transition-colors duration-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center py-12">
            <div className="text-red-600 dark:text-red-400 mb-4">
              <h2 className="text-xl font-semibold">Error Loading Program</h2>
              <p className="text-sm mt-2">
                {error instanceof Error
                  ? error.message
                  : "Failed to load enrollment program data"}
              </p>
            </div>
            <button
              onClick={() => refetch()}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  // No enrollment ID
  if (!enrollmentId) {
    return (
      <div className="min-h-screen bg-background transition-colors duration-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              No Enrollment Selected
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Please select an enrollment to view the program.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // No data or current day
  if (!enrollmentData?.data || !currentDay || !currentWeek) {
    return (
      <div className="min-h-screen bg-background transition-colors duration-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              No Program Data Available
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              This enrollment does not have any program data available.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const { data } = enrollmentData;

  return (
    <div className="min-h-screen bg-background transition-colors duration-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <WorkoutProgramHeader
          dayNumber={currentDay.day_order!}
          splitTitle={data.split.title || "Split title"}
          programWeek={currentWeek.title || "Week title"}
          equipmentRequired={
            data.split.equipment_required ||
            data.split.equipment_required ||
            "No equipment specified"
          }
          weeks={data.weeks}
          currentWeekId={currentWeekId?.toString() || null}
          onWeekChange={(week) => {
            if (week) {
              const foundWeek = data.weeks.find(
                (w) => w.id!.toString() === week.id
              );
              if (foundWeek) {
                handleWeekChange(foundWeek);
              }
            } else {
              handleWeekChange(null);
            }
          }}
          currentWeek={currentWeek}
          currentDayId={currentDay?.id?.toString() || null}
          onDayChange={handleDayChange}
        />

        {/* Program Content */}
        <div className="space-y-6">
          {currentDay.sessions.map((session) => (
            <SessionCard
              key={session.id}
              session={{
                ...session,
                // duration: "45 min", // Default duration
                exercises: session.exercise_instances.map((exercise) => ({
                  ...exercise,
                  name: exercise?.exercise?.name,
                  sets: exercise.sets || "1",
                  reps: exercise.reps_or_time || "As needed",
                  rest:
                    exercise?.rest_duration_seconds &&
                    exercise?.rest_duration_seconds > 0
                      ? `${exercise.rest_duration_seconds}s`
                      : "No rest",
                })),
              }}
              weekNumber={currentWeek.week_order || 0}
              dayNumber={currentDay.day_order || 0}
              onExerciseComplete={handleExerciseComplete}
              completingExercises={completingExercises}
            />
          ))}
        </div>

        {/* Submit Button */}
        <SubmitButton
          label="Submit Workout"
          onClick={handleSubmit}
          loading={isSubmittingDay}
          disabled={isSubmittingDay || currentDay?.progress?.is_completed}
        />
      </div>
    </div>
  );
};

export default ViewAthleteEmrollmentProgressPage;
