import { useState } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import {
  ChevronDownIcon,
  ChevronRightIcon,
  CheckIcon,
} from "@heroicons/react/24/outline";
import { ExerciseStatsGrid } from "@/components/ExerciseStatsGrid";
import { VideoPlayer } from "@/components/VideoPlayer";
import { ExerciseDetails } from "@/components/ExerciseDetails";
import { InteractiveButton } from "@/components/InteractiveButton";
import { WorkoutExercise } from "@/interfaces";

interface ExerciseCardProps {
  exercise: WorkoutExercise;
  onMarkComplete?: () => void;
  isCompleting?: boolean;
}

const ExerciseCard = ({
  exercise,
  onMarkComplete,
  isCompleting = false,
}: ExerciseCardProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [isCollapsed, setIsCollapsed] = useState(true); // Default to collapsed

  const cardStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  const exerciseIdStyles = {
    backgroundColor: THEME_COLORS[mode].PRIMARY,
    color: THEME_COLORS[mode].TEXT_ON_PRIMARY,
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const handleVideoClick = () => {
    setIsCollapsed(false); // Expand the card when video is clicked
  };

  return (
    <div
      className="bg-background border border-border rounded-lg p-4 sm:p-6 mb-6 transition-colors duration-200"
      style={cardStyles}
    >
      {/* Exercise Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          {/* Exercise ID Badge */}
          <div
            className="w-8 h-8 rounded-full flex items-center justify-center text-base font-bold"
            style={exerciseIdStyles}
          >
            {exercise?.label}
          </div>

          {/* Exercise Name */}
          <h3 className="text-xl font-semibold text-text">
            {exercise?.exercise?.name}
          </h3>
        </div>

        {/* Dropdown Icon */}
        <button
          onClick={toggleCollapse}
          className="p-1 hover:bg-background-secondary rounded transition-colors duration-200"
        >
          {isCollapsed ? (
            <ChevronRightIcon className="w-4 h-4 text-text-secondary" />
          ) : (
            <ChevronDownIcon className="w-4 h-4 text-text-secondary" />
          )}
        </button>
      </div>

      {/* Exercise Stats Grid */}
      <ExerciseStatsGrid
        sets={exercise?.sets!.toString()}
        reps={exercise?.reps_or_time!}
        rest={exercise?.rest_duration_seconds!.toString()}
        weight={"bodyweight"}
        showVideoPreview={isCollapsed}
        videoUrl={exercise?.video_url as string}
        thumbnailUrl={exercise?.video_url as string}
        onVideoClick={handleVideoClick}
      />

      {/* Collapsible Content */}
      {!isCollapsed && (
        <>
          {/* Video Player */}
          <VideoPlayer
            thumbnailUrl={""}
            videoUrl={exercise?.video_url as string}
            duration={""}
            progress={0}
          />

          {/* Exercise Details */}
          <ExerciseDetails
            description={exercise?.exercise_details!}
            muscleGroups={[]}
          />
        </>
      )}

      {/* Mark Complete Button */}
      <div className="flex justify-end">
        <InteractiveButton
          onClick={onMarkComplete}
          className={`px-6 py-3 ${
            exercise?.progress?.is_completed
              ? "bg-green-600 hover:bg-green-700"
              : "bg-primary hover:bg-primary-hover"
          } text-white`}
          disabled={exercise?.progress?.is_completed || isCompleting}
          loading={isCompleting}
        >
          <CheckIcon className="w-4 h-4 mr-2" />
          {isCompleting
            ? "Marking Complete..."
            : exercise?.progress?.is_completed
              ? "Completed"
              : "Mark Complete"}
        </InteractiveButton>
      </div>
    </div>
  );
};

export default ExerciseCard;
