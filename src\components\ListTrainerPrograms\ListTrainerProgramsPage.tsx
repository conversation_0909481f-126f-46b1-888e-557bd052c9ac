import React, { useEffect, useState } from "react";
// import { Container } from "@/components/Container";
import { useTheme } from "@/hooks/useTheme";
// import { useContexts } from "@/hooks/useContexts";
import { LazyLoad } from "@/components/LazyLoad";
import { useProfile } from "@/hooks/useProfile";

import { useGetPaginateQuery } from "@/query/shared/listModel";

import { TrainerProgram } from "./types";
import { Program } from "@/interfaces";
import { ProgramSearchSection, ProgramsTable, CustomPagination } from "./index";
import { Models } from "@/utils/baas";
import { Container } from "@/components/Container";
import { useContexts } from "@/hooks/useContexts";

const ListTrainerProgramsPage = () => {
  const { state: _state } = useTheme();
  const { profile } = useProfile();
  const { globalDispatch } = useContexts();

  // Filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<
    Program["status"] | "" | "All"
  >("");

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(5); // Show 5 items per page

  // Build filter array for search and status
  const buildFilters = (profile: Record<string, any> | null) => {
    const filters: string[] = [];
    if (profile?.id) {
      filters.push(`user_id,eq,${profile?.id}`);
    }

    if (debouncedSearchTerm) {
      filters.push(`program_name,cs,${debouncedSearchTerm}`);
    }

    if (statusFilter && statusFilter !== "All") {
      filters.push(`status,eq,${statusFilter}`);
    }

    return filters.length > 0 ? filters : undefined;
  };

  // Fetch paginated program data
  const {
    data: programsResponse,
    isLoading,
    isError,
    error,
  } = useGetPaginateQuery(
    Models.PROGRAM,
    {
      page: currentPage,
      size: pageSize,
      filter: buildFilters(profile),
      order: "id",
      direction: "desc",
      join: ["program_discount"],
    },
    {
      enabled: !!profile?.id,
      keepPreviousData: true,
    }
  );

  const programs: TrainerProgram[] = programsResponse?.data || [];
  const totalPages = programsResponse?.num_pages || 1;

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value as Program["status"] | "" | "All");
  };

  const handleApplyFilter = () => {
    // Filter logic is already applied in real-time
    // This function can be used for additional filter actions if needed
  };

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Reset to first page when search or filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm, statusFilter]);

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "program",
      },
    });
  }, [globalDispatch]);

  return (
    <LazyLoad>
      <Container className="space-y-6 p-5">
        <LazyLoad>
          <ProgramSearchSection
            searchTerm={searchTerm}
            statusFilter={statusFilter}
            onSearchChange={handleSearchChange}
            onStatusChange={handleStatusChange}
            onApplyFilter={handleApplyFilter}
          />
        </LazyLoad>

        <LazyLoad>
          <ProgramsTable
            programs={programs}
            isLoading={isLoading}
            isError={isError}
            error={error}
          />
        </LazyLoad>
        {/* Pagination */}
        {totalPages > 1 && (
          <LazyLoad>
            <CustomPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          </LazyLoad>
        )}
      </Container>
    </LazyLoad>
  );
};

export default ListTrainerProgramsPage;
