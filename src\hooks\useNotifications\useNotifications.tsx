import { useState, useEffect } from "react";
import { useContexts } from "@/hooks/useContexts";
import { useCustomModelQuery } from "@/query/shared";
import { ToastStatusEnum } from "@/utils/Enums";

interface Notification {
  id: number;
  notification_type: string;
  category: string;
  title: string;
  message: string;
  data: any;
  is_read: boolean;
  read_at: string | null;
  created_at: string;
  sender_id: number | null;
  sender_name: string | null;
  sender_email: string | null;
  related_id: number | null;
  related_type: string | null;
}

interface UseNotificationsReturn {
  unreadCount: number;
  notifications: Notification[];
  loading: boolean;
  error: string | null;
  fetchNotifications: () => Promise<void>;
  markAsRead: (notificationId: number) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  refetchUnreadCount: () => Promise<void>;
}

export const useNotifications = (): UseNotificationsReturn => {
  const { mutateAsync: customModelQuery } = useCustomModelQuery()
  const { showToast, tokenExpireError } = useContexts();

  const [unreadCount, setUnreadCount] = useState(0);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch unread notification count
  const fetchUnreadCount = async () => {
    try {
      const response = await customModelQuery({
        endpoint: "/v2/api/kanglink/custom/athlete/notifications/unread-count",
        method: "GET",
      });

      if (!response.error && response.data) {
        setUnreadCount(response.data.count || 0);
      } else {
        console.error("Failed to fetch unread count:", response.message);
      }
    } catch (error: any) {
      console.error("Error fetching unread count:", error);
      const message = error?.response?.data?.message || error?.message || "Failed to fetch unread count";
      setError(message);
    }
  };

  // Fetch notifications
  const fetchNotifications = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await customModelQuery({
        endpoint: "/v2/api/kanglink/custom/athlete/notifications",
        method: "GET",
        params: {
          page: 1,
          limit: 20,
        },
      });

      if (!response.error && response.data) {
        setNotifications(response.data.notifications || []);
      } else {
        console.error("Failed to fetch notifications:", response.message);
        setError(response.message || "Failed to fetch notifications");
      }
    } catch (error: any) {
      console.error("Error fetching notifications:", error);
      const message = error?.response?.data?.message || error?.message || "Failed to fetch notifications";
      setError(message);
      tokenExpireError(message);
    } finally {
      setLoading(false);
    }
  };

  // Mark notification as read
  const markAsRead = async (notificationId: number) => {
    try {
      const response = await customModelQuery({
        endpoint: `/v2/api/kanglink/custom/athlete/notifications/${notificationId}/read`,
        method: "PUT",
      });

      if (!response.error) {
        setNotifications(prev =>
          prev.map(notification =>
            notification.id === notificationId
              ? { ...notification, is_read: true, read_at: new Date().toISOString() }
              : notification
          )
        );
        
        // Update unread count
        setUnreadCount(prev => Math.max(0, prev - 1));
      } else {
        console.error("Failed to mark notification as read:", response.message);
        setError(response.message || "Failed to mark notification as read");
      }
    } catch (error: any) {
      console.error("Error marking notification as read:", error);
      const message = error?.response?.data?.message || error?.message || "Failed to mark notification as read";
      setError(message);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      const response = await customModelQuery({
        endpoint: "/v2/api/kanglink/custom/athlete/notifications/read-all",
        method: "PUT",
      });

      if (!response.error) {
        setNotifications(prev =>
          prev.map(notification => ({
            ...notification,
            is_read: true,
            read_at: new Date().toISOString(),
          }))
        );
        
        // Reset unread count
        setUnreadCount(0);
        showToast("All notifications marked as read", 3000, ToastStatusEnum.SUCCESS);
      } else {
        console.error("Failed to mark all notifications as read:", response.message);
        setError(response.message || "Failed to mark all notifications as read");
      }
    } catch (error: any) {
      console.error("Error marking all notifications as read:", error);
      const message = error?.response?.data?.message || error?.message || "Failed to mark all notifications as read";
      setError(message);
    }
  };

  // Refetch unread count
  const refetchUnreadCount = async () => {
    await fetchUnreadCount();
};

// Initial fetch of unread count and notifications
  useEffect(() => {
    fetchUnreadCount();
    fetchNotifications();
}, []);

// Set up polling for unread count (every 30 seconds)
useEffect(() => {
    const interval = setInterval(() => {
        fetchUnreadCount();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  return {
    unreadCount,
    notifications,
    loading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    refetchUnreadCount,
  };
}; 