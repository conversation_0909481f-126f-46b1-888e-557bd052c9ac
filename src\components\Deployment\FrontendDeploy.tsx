import { useContexts } from "@/hooks/useContexts";
import { ViewMapType } from "@/components/ViewWrapper";

import { InteractiveButton } from "@/components/InteractiveButton";
import { DisplayDomainUrl } from "@/components/DisplayDomainUrl";
// import { ModalPrompt } from "@/components/Modal";

import { FaCloudUploadAlt, FaGlobe, FaNodeJs, FaReact } from "react-icons/fa";
import { SiSemanticuireact } from "react-icons/si";
import { useState } from "react";
import { useFrontendDeploymentHook } from "@/hooks/useFrontendDeploymentHook";

interface FrontendDeployProps {
  view: ViewMapType["value"];
}

const FrontendDeploy = ({}: FrontendDeployProps) => {
  const {
    globalState: { project },
  } = useContexts();

  useFrontendDeploymentHook({});

  const [_deployment, _setDeployment] = useState({}) as any;
  const [isLoading, _setIsLoading] = useState({ state: false, target: "page" });
  const [frontendBranches, _setFrontendBranches] = useState([]) as any;
  const [_feBranch, setFeBranch] = useState<string>("");
  const [branchesLoading, setBranchesLoading] = useState<boolean>(false);

  const [platformBranchLoading, _setPlatformBranchLoading] = useState({
    state: false,
    platform: "",
  });

  const handleInitializeProjectDeployment = async () => {};

  const handleCreateRepo = async (_platform: string) => {};

  const handleCreateDomain = async () => {};

  const handleDeleteDomain = async () => {};

  const handleCreateJenkinsJob = async (_platform: string) => {};

  const branchTimeToDate = (branch = "") => {
    const branchNameUnits = branch.split("_");
    if (branchNameUnits.length <= 1) return branch;

    const date = new Date(Number(branchNameUnits[1]));
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const seconds = date.getSeconds();

    const formattedDateTime = `${branchNameUnits[0]} - Commit ${year}-${month
      .toString()
      .padStart(2, "0")}-${day.toString().padStart(2, "0")} ${hours
      .toString()
      .padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;

    return formattedDateTime;
  };

  const DEPLOY_ITEMS = [
    {
      id: 1,
      name: "Deployment",
      actionBtn: () => (
        <InteractiveButton
          className={`flex items-center rounded-md px-3 py-2 shadow-sm !cursor-default border border-[#C6C6C6] !bg-green-500`}
          loading={
            isLoading.state == true && isLoading.target == "initializing"
          }
          disabled={true}
          // onClick={handleInitializeProjectDeployment}
        >
          Initialized
        </InteractiveButton>
      ),
      icon: () => <FaCloudUploadAlt className="h-6 w-6 text-green-500" />,
    },
    // {
    //   id: 11,
    //   name: "Clear Development Images",
    //   actionBtn: () => (
    //     <InteractiveButton
    //       disabled={
    //         isLoading.state == true && isLoading.target == "clear_s3_images"
    //       }
    //       onClick={clearS3Images}
    //     >
    //       {isLoading.state == true && isLoading.target == "clear_s3_images"
    //         ? "Clearing..."
    //         : "Clear"}
    //     </InteractiveButton>
    //   ),
    //   icon: () => <FaCameraRetro className="h-6 w-6 text-red-500" />
    // },
    {
      id: 6,
      name: "Domain",
      actionBtn: () => {
        return !_deployment.has_domain ? (
          <InteractiveButton
            className={`flex cursor-pointer items-center rounded-md bg-primaryBlue px-3 py-2 text-white shadow-sm ${
              isLoading.state == true && isLoading.target == "create_domain"
                ? "!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700"
                : ""
            } `}
            loading={
              isLoading.state == true && isLoading.target == "create_domain"
            }
            disabled={
              isLoading.state == true && isLoading.target == "create_domain"
            }
            onClick={(_e: any) => handleCreateDomain()}
          >
            Create
          </InteractiveButton>
        ) : _deployment.has_domain ? (
          <InteractiveButton
            className={`flex cursor-pointer items-center rounded-md border border-[#C6C6C6] !bg-[#DC2626]  px-3 py-2 shadow-sm ${
              isLoading.state == true && isLoading.target == "delete_domain"
                ? "!cursor-not-allowed !bg-yellow-700"
                : ""
            } `}
            loading={
              isLoading.state == true && isLoading.target == "delete_domain"
            }
            disabled={
              isLoading.state == true && isLoading.target == "delete_domain"
            }
            onClick={(_e: any) => handleDeleteDomain()}
          >
            Delete
          </InteractiveButton>
        ) : null;
      },
      icon: () => <FaGlobe className="h-6 w-6 text-yellow-700" />,
    },
    {
      id: 2,
      name: "React repository",
      actionBtn: () => (
        <div className="flex items-center gap-4">
          {_deployment.has_fe_repo ? (
            <DisplayDomainUrl
              text={`http://23.29.118.76:3000/mkdlabs/${project.slug}_frontend.git`}
            />
          ) : (
            ""
          )}
          <InteractiveButton
            className={`flex cursor-pointer items-center rounded-md px-3 py-2 shadow-sm ${
              _deployment.has_fe_repo
                ? "!cursor-not-allowed border border-[#C6C6C6] !bg-green-500"
                : isLoading.state === true &&
                    isLoading.target === "create_frontend_repo"
                  ? "!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700"
                  : "bg-primaryBlue text-white"
            } `}
            loading={
              isLoading.state == true &&
              isLoading.target == "create_frontend_repo"
            }
            disabled={
              _deployment.has_fe_repo ||
              (isLoading.state === true &&
                isLoading.target === "create_frontend_repo")
            }
            onClick={() => handleCreateRepo("frontend")}
          >
            {_deployment.has_fe_repo ? "Created" : "Create"}
          </InteractiveButton>
        </div>
      ),
      icon: () => <FaReact className="h-6 w-6 text-blue-500" />,
    },
    {
      id: 9,
      name: "React Deploy",
      actionBtn: () => (
        <div className="flex items-center gap-4">
          {frontendBranches.length > 0 && (
            <select
              name=""
              id=""
              className="rounded-md border border-[#C6C6C6] px-3 py-1.5 shadow-sm"
              onChange={(e) => setFeBranch(e.target.value)}
            >
              <option disabled>Choose branch to deploy</option>
              {frontendBranches.map((branch: any) => (
                <option key={branch.name} value={branch.name}>
                  {branchTimeToDate(branch.name)}
                </option>
              ))}
            </select>
          )}
          <InteractiveButton
            className={`flex cursor-pointer items-center rounded-md px-3 py-2 shadow-sm ${
              frontendBranches.length == 0
                ? "!cursor-not-allowed border border-[#C6C6C6] !bg-black"
                : _deployment.fe_deployed
                  ? "!cursor-not-allowed border border-[#C6C6C6] !bg-green-500"
                  : isLoading.state == true &&
                      isLoading.target == "deploy_frontend"
                    ? "!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700"
                    : "bg-primaryBlue text-white"
            } `}
            loading={
              isLoading.state == true && isLoading.target == "deploy_frontend"
            }
            disabled={
              frontendBranches.length == 0 ||
              !_deployment.has_domain ||
              _deployment.fe_deployed ||
              (isLoading.state == true &&
                isLoading.target == "deploy_frontend") ||
              (platformBranchLoading.state == true &&
                platformBranchLoading.platform == "frontend")
            }
            onClick={(_e: any) => {}}
          >
            {_deployment.fe_deployed
              ? "Deployed"
              : platformBranchLoading.state == true &&
                  platformBranchLoading.platform == "frontend"
                ? "Fetching branches..."
                : frontendBranches.length == 0
                  ? "No branch to deploy"
                  : "Deploy"}
          </InteractiveButton>
        </div>
      ),
      icon: () => <SiSemanticuireact className="h-6 w-6 text-purple-900" />,
    },
    {
      id: 7,
      name: "React Jenkins job",
      actionBtn: () => (
        <div className="flex items-center gap-4">
          {_deployment.has_fe_job ? (
            <DisplayDomainUrl
              text={`http://23.29.118.76:8080/job/${project.slug}_frontend/`}
            />
          ) : (
            ""
          )}
          <InteractiveButton
            className={`flex cursor-pointer items-center rounded-md px-3 py-2 shadow-sm ${
              _deployment.has_fe_job
                ? "!cursor-not-allowed border border-[#C6C6C6] !bg-green-500"
                : isLoading.state == true &&
                    isLoading.target == "create_frontend_job"
                  ? "!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700"
                  : "bg-primaryBlue text-white"
            } `}
            loading={
              isLoading.state == true &&
              isLoading.target == "create_frontend_job"
            }
            disabled={
              _deployment.has_fe_job ||
              (isLoading.state == true &&
                isLoading.target == "create_frontend_job")
            }
            onClick={(_e: any) => handleCreateJenkinsJob("frontend")}
          >
            {_deployment.has_fe_job ? "Created" : "Create"}
          </InteractiveButton>
        </div>
      ),
      icon: () => <SiSemanticuireact className="h-6 w-6 text-blue-900" />,
    },
  ];

  const BRANCHES: any[] = [];
  return (
    <>
      <div className="relative px-10 pb-6 pt-3 flex w-full rounded-b-md border-t-0  bg-white py-2 text-sm text-gray-700 ">
        <ol className="list-decimal">
          <li>Create Domain</li>
          <li>Create React repository</li>
          <li>Click to Deploy</li>
          <li>Create React Jenkins job</li>
          {/* <li>Select branch from React Deploy</li> */}
        </ol>
      </div>

      <div className="flex justify-between border-t border-[#E0E0E0] bg-[#f9f9f9] px-8">
        <div className="mb-4 mt-6 w-full rounded-md border border-[#E0E0E0] bg-white px-5 py-3">
          <div className="divide-y">
            {isLoading.state == true && isLoading.target == "page" ? (
              <>
                {["", "", "", "", "", "", "", "", ""].map((_item, index) => (
                  <div
                    key={index}
                    className=" rounded-md border-b-0 border-l-0 border-[#C6C6C6] px-3 py-1.5 shadow-sm "
                  >
                    <div className="my-2 flex animate-pulse items-center justify-center rounded-md bg-gray-300 py-6"></div>
                  </div>
                ))}
              </>
            ) : (
              DEPLOY_ITEMS.map((item) => (
                <div
                  key={item.id}
                  className="flex items-center justify-between py-6 text-sm text-[#393939]"
                >
                  <div className="flex items-center gap-4">
                    <span>{item.icon()}</span>
                    <span>{item.name}</span>
                  </div>
                  <div>{item.actionBtn()}</div>
                </div>
              ))
            )}

            {branchesLoading ? (
              <>
                <div className=" rounded-md border-b-0 border-r-0 border-[#C6C6C6] px-3 py-1.5 shadow-sm ">
                  <div className="my-2 flex animate-pulse items-center justify-center rounded-md bg-gray-300 py-6"></div>
                </div>
                <div className=" rounded-md border-b-0 border-l-0 border-[#C6C6C6] px-3 py-1.5 shadow-sm ">
                  <div className="my-2 flex animate-pulse items-center justify-center rounded-md bg-gray-300 py-6"></div>
                </div>
              </>
            ) : (
              BRANCHES.map((branch) => (
                <div
                  key={branch.id}
                  className="flex items-center justify-between py-6 text-sm text-[#393939]"
                >
                  <div className="flex items-center gap-4">
                    <span>{branch.icon()}</span>
                    <span>{branch.name}</span>
                  </div>
                  <div>{branch.actionBtn()}</div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default FrontendDeploy;
