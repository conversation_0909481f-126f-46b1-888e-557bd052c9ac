import { StringCaser } from "@/utils";

// Helper function for modal titles
export function determineModalTitle(modal: string) {
  const stringCaser = new StringCaser();
  const modalSplit = modal.split("_");
  const includesDate = modalSplit.includes("date");
  const includesTime = modalSplit.includes("time");

  if (includesDate && includesTime) {
    return "Pick date & time";
  }

  if (includesDate) {
    return "Pick date";
  }

  if (includesTime) {
    return "Select Time";
  }

  const title = stringCaser.Capitalize(modal, { separator: "space" });
  return title;
}

export const displayOptionLabel = (
  option: any,
  display: string | number | any[] | { and: string[]; or: string[] },
  displaySeparator: string
) => {
  if (typeof display === "string") {
    return String(option[display]);
  }
  if (typeof display === "object") {
    if (Array.isArray(display)) {
      // handle array case
      const invalid = display.some((item) => typeof item !== "string");
      if (invalid) {
        return String(option[Object.keys(option)[0]]);
      }
      const result = display.map((key) => option[key]);

      return String(
        result.length ? result.join(` ${displaySeparator} `) : result.join(" ")
      );
    } else {
      // handle object case

      const isNotOneOrTwoFields = ![1, 2].includes(Object.keys(display).length);
      if (isNotOneOrTwoFields) {
        return String(option[Object.keys(option)[0]]);
      }

      const isNotOnlyAndOr = Object.keys(display).some(
        (key) => !["and", "or"].includes(key)
      );
      if (isNotOnlyAndOr) {
        return String(option[Object.keys(option)[0]]);
      }

      const andField = display["and"];
      const orField = display["or"];

      if (andField && orField) {
        if (typeof andField === "string" && typeof orField === "string") {
          const andValue = option[andField];
          const orValue = option[orField];

          return String(andValue || orValue || option[Object.keys(option)[0]]);
        }
        if (Array.isArray(andField) && Array.isArray(orField)) {
          const someAndValuesNotExist = andField.some((key) => !option[key]);
          if (someAndValuesNotExist) {
            const orValue = orField
              .map((key) => {
                if (option[key]) {
                  return option[key];
                }
              })
              .filter(Boolean);

            return String(
              orValue.length
                ? orValue.length
                  ? orValue.join(` ${displaySeparator} `)
                  : orValue.join(" ")
                : option[Object.keys(option)[0]]
            );
          }

          const andValue = andField
            .map((key) => {
              if (option[key]) {
                return option[key];
              }
            })
            .filter(Boolean);

          return String(
            andValue.length
              ? andValue.join(` ${displaySeparator} `)
              : andValue.join(" ")
          );
        }

        if (Array.isArray(andField) && typeof orField === "string") {
          const someAndValuesNotExist = andField.some((key) => !option[key]);
          if (someAndValuesNotExist) {
            const orValue = option[orField];

            return String(orValue || option[Object.keys(option)[0]]);
          }
          const andValue = andField
            .map((key) => {
              if (option[key]) {
                return option[key];
              }
            })
            .filter(Boolean);

          return String(
            andValue.length
              ? andValue.join(` ${displaySeparator} `)
              : andValue.join(" ")
          );
        }

        if (Array.isArray(orField) && typeof andField === "string") {
          const andValue = option[andField];
          if (andValue) {
            return String(andValue);
          }

          const orValue = orField
            .map((key) => {
              if (option[key]) {
                return option[key];
              }
            })
            .filter(Boolean);

          return String(
            orValue.length
              ? orValue.length
                ? orValue.join(` ${displaySeparator} `)
                : orValue.join(" ")
              : option[Object.keys(option)[0]]
          );
        }
      } else if (andField && !orField) {
        if (typeof andField === "string") {
          const andValue = option[andField];

          return String(andValue || option[Object.keys(option)[0]]);
        }
        if (Array.isArray(andField)) {
          const andValue = andField
            .map((key) => {
              if (option[key]) {
                return option[key];
              }
            })
            .filter(Boolean);

          return String(
            andValue.length
              ? andValue.length
                ? andValue.join(` ${displaySeparator} `)
                : andValue.join(" ")
              : option[Object.keys(option)[0]]
          );
        }
      } else if (!andField && orField) {
        if (typeof orField === "string") {
          const orValue = option[orField];

          return String(orValue || option[Object.keys(option)[0]]);
        }

        if (Array.isArray(orField)) {
          const orValue = orField
            .map((key) => {
              if (option[key]) {
                return option[key];
              }
            })
            .filter(Boolean);

          return String(
            orValue.length
              ? orValue.length
                ? orValue.join(` ${displaySeparator} `)
                : orValue.join(" ")
              : option[Object.keys(option)[0]]
          );
        }
      }
    }
  }
};
