import React from "react";
import { CommentInputProps } from "./types";

const CommentInput: React.FC<CommentInputProps> = ({
  user_avatar = "https://placehold.co/40x40",
  value,
  on_change,
  on_submit,
  placeholder = "Write a comment...",
  is_loading = false,
}) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      on_submit();
    }
  };

  return (
    <div className="flex space-x-3 mt-4">
      <img
        src={user_avatar}
        alt="Your avatar"
        className="w-10 h-10 rounded-full object-cover flex-shrink-0 ring-2 ring-border"
      />
      <div className="flex-1 flex space-x-3">
        <input
          type="text"
          value={value}
          onChange={(e) => on_change(e.target.value)}
          placeholder={placeholder}
          className="flex-1 px-4 py-3 border border-border rounded-xl bg-background text-text placeholder-text-disabled focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 text-sm"
          onKeyDown={handleKeyDown}
        />
        <button
          onClick={on_submit}
          disabled={!value.trim() || is_loading}
          className="px-6 py-3 bg-primary text-white rounded-xl hover:bg-primary-hover disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-semibold text-sm shadow-sm hover:shadow-md"
        >
          {is_loading ? "Posting..." : "Post"}
        </button>
      </div>
    </div>
  );
};

export default CommentInput;
