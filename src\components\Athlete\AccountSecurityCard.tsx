interface AccountSecurityCardProps {
  onChangePassword?: () => void;
  onTwoFactorAuth?: () => void;
  authType?: string;
}

export const AccountSecurityCard = ({ 
  onChangePassword, 
  onTwoFactorAuth,
  authType
}: AccountSecurityCardProps) => {
  const handleChangePassword = () => {
    if (onChangePassword) {
      onChangePassword();
    } else {
      // Handle password change logic
      // console.log("Change password clicked");
    }
  };

  const handleTwoFactorAuth = () => {
    if (onTwoFactorAuth) {
      onTwoFactorAuth();
    } else {
      // Handle two-factor authentication setup
      // console.log("Two-factor authentication clicked");
    }
  };

  return (
    <div className="bg-card-bg rounded-lg shadow-sm border border-border p-6">
      {/* Card Header */}
      <div className="mb-6">
        <h2 className="text-lg font-medium text-text">Account Security</h2>
      </div>

      {/* Security Actions */}
      <div className="flex flex-col gap-4">
        {/* Change Password Button */}
        {authType !== "oauth" && (
        <button
          type="button"
          onClick={handleChangePassword}
          className="md:w-[15.8125rem] w-auto px-6 py-2 bg-transparent border border-primary text-primary text-sm font-semibold rounded hover:bg-primary hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
        >
          Change Password
        </button>
        )}
        {/* Two-Factor Authentication Button */}
        <button
          type="button"
          onClick={handleTwoFactorAuth}
          className="md:w-[15.8125rem] w-auto px-6 py-2 bg-transparent border border-primary text-primary text-sm font-semibold rounded hover:bg-primary hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
        >
          Two-Factor Authentication
        </button>
      </div>
    </div>
  );
};
