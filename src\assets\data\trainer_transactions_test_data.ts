// Test data for trainer transactions components
import {
  TransactionStats,
  EarningsGraphData,
  StripeConnectStatus,
  TransactionHistoryResponse,
  WithdrawalResponse,
} from "@/hooks/useTrainerTransactions";

// Mock transaction stats data
export const mockTransactionStats: TransactionStats = {
  total_earnings: 2847.5,
  pending_payouts: 425.75,
  available_to_withdraw: 1250.25,
  withdrawn: 1171.5,
  currency: "USD",
  payout_time_hours: 24,
};

// Mock earnings graph data
export const mockEarningsGraphData: EarningsGraphData = {
  earnings_by_month: [
    {
      month: "2024-01",
      month_name: "Jan",
      year: 2024,
      month_number: 1,
      earnings: 180.5,
    },
    {
      month: "2024-02",
      month_name: "Feb",
      year: 2024,
      month_number: 2,
      earnings: 245.75,
    },
    {
      month: "2024-03",
      month_name: "Mar",
      year: 2024,
      month_number: 3,
      earnings: 320.25,
    },
    {
      month: "2024-04",
      month_name: "Apr",
      year: 2024,
      month_number: 4,
      earnings: 410.8,
    },
    {
      month: "2024-05",
      month_name: "May",
      year: 2024,
      month_number: 5,
      earnings: 385.9,
    },
    {
      month: "2024-06",
      month_name: "Jun",
      year: 2024,
      month_number: 6,
      earnings: 465.3,
    },
  ],
  total_months: 6,
  currency: "USD",
};

// Mock Stripe Connect status - Account not set up
export const mockStripeConnectStatusNotSetup: StripeConnectStatus = {
  has_stripe_connect: false,
  onboarded: false,
  details_submitted: false,
  charges_enabled: false,
  payouts_enabled: false,
  message: "Stripe Connect account not set up",
};

// Mock Stripe Connect status - Account setup but incomplete
export const mockStripeConnectStatusIncomplete: StripeConnectStatus = {
  has_stripe_connect: true,
  stripe_connect_account_id: "acct_1234567890abcdef",
  onboarded: false,
  details_submitted: false,
  charges_enabled: false,
  payouts_enabled: false,
  requirements: {
    currently_due: ["individual.first_name", "individual.last_name"],
    eventually_due: ["individual.ssn_last_4"],
    past_due: [],
    pending_verification: [],
  },
  message: "Please complete your Stripe Connect onboarding",
};

// Mock Stripe Connect status - Account fully set up
export const mockStripeConnectStatusComplete: StripeConnectStatus = {
  has_stripe_connect: true,
  stripe_connect_account_id: "acct_1234567890abcdef",
  onboarded: true,
  details_submitted: true,
  charges_enabled: true,
  payouts_enabled: true,
  message: "Account ready for payments",
};

// Mock transaction history data
export const mockTransactionHistory: TransactionHistoryResponse = {
  transactions: [
    {
      commission_id: 1,
      enrollment_id: 101,
      type: "earnings",
      commission_type: "split_purchase",
      amount: {
        total: 99.99,
        original: 99.99,
        discount: 0,
        trainer_amount: 69.99,
        company_amount: 30.0,
        currency: "USD",
      },
      status: "processed",
      dates: {
        created: "2024-06-15T10:30:00Z",
        scheduled_payout: "2024-06-16T10:30:00Z",
        processed_payout: "2024-06-16T11:45:00Z",
        enrollment_date: "2024-06-15T10:30:00Z",
      },
      program: {
        id: 1,
        title: "Advanced Strength Training",
        description: "12-week comprehensive strength program",
      },
      split: {
        id: 1,
        title: "Upper/Lower Split",
        full_price: 99.99,
        subscription_price: 19.99,
      },
      athlete: {
        email: "<EMAIL>",
        name: "John Doe",
      },
      enrollment: {
        payment_type: "one_time",
        status: "active",
        payment_status: "paid",
        stripe_payment_intent_id: "pi_1234567890",
      },
    },
    {
      commission_id: 2,
      enrollment_id: 102,
      type: "earnings",
      commission_type: "subscription",
      amount: {
        total: 19.99,
        original: 19.99,
        discount: 0,
        trainer_amount: 13.99,
        company_amount: 6.0,
        currency: "USD",
      },
      status: "pending",
      dates: {
        created: "2024-06-14T15:20:00Z",
        scheduled_payout: "2024-06-15T15:20:00Z",
        enrollment_date: "2024-06-14T15:20:00Z",
      },
      program: {
        id: 2,
        title: "Beginner Fitness Journey",
        description: "8-week beginner-friendly program",
      },
      split: {
        id: 2,
        title: "Full Body Workout",
        full_price: 79.99,
        subscription_price: 19.99,
      },
      athlete: {
        email: "<EMAIL>",
        name: "Jane Smith",
      },
      enrollment: {
        payment_type: "subscription",
        status: "active",
        payment_status: "paid",
        stripe_subscription_id: "sub_1234567890",
      },
    },
    {
      commission_id: 3,
      enrollment_id: 103,
      type: "earnings",
      commission_type: "affiliate",
      amount: {
        total: 149.99,
        original: 149.99,
        discount: 0,
        trainer_amount: 119.99,
        company_amount: 30.0,
        currency: "USD",
      },
      status: "processed",
      dates: {
        created: "2024-06-13T09:15:00Z",
        scheduled_payout: "2024-06-14T09:15:00Z",
        processed_payout: "2024-06-14T10:30:00Z",
        enrollment_date: "2024-06-13T09:15:00Z",
      },
      program: {
        id: 3,
        title: "Elite Performance Training",
        description: "Advanced athletic performance program",
      },
      split: {
        id: 3,
        title: "Athletic Performance Split",
        full_price: 149.99,
        subscription_price: 29.99,
      },
      athlete: {
        email: "<EMAIL>",
        name: "Mike Johnson",
      },
      enrollment: {
        payment_type: "one_time",
        status: "active",
        payment_status: "paid",
        stripe_payment_intent_id: "pi_0987654321",
      },
    },
  ],
  pagination: {
    current_page: 1,
    per_page: 20,
    total_records: 3,
    total_pages: 1,
    has_next: false,
    has_prev: false,
  },
  filters: {
    type: "all",
  },
};

// Mock withdrawal response
export const mockWithdrawalResponse: WithdrawalResponse = {
  withdrawal_id: "wd_1234567890",
  amount: 500.0,
  currency: "USD",
  status: "processing",
  transfer_details: {
    stripe_transfer_id: "tr_1234567890",
    destination_account: "acct_1234567890abcdef",
    created: **********,
  },
  message: "Withdrawal request processed successfully",
};

// Test Stripe Connect setup response
export const mockStripeConnectSetupResponse = {
  stripe_connect_account_id: "acct_1234567890abcdef",
  onboarding_url: "https://connect.stripe.com/setup/s/acct_1234567890abcdef",
  account_status: {
    onboarded: false,
    details_submitted: false,
    charges_enabled: false,
    payouts_enabled: false,
    requirements: {
      currently_due: ["individual.first_name", "individual.last_name"],
      eventually_due: ["individual.ssn_last_4"],
      past_due: [],
      pending_verification: [],
    },
  },
  expires_at: **********,
  message: "Please complete the Stripe Connect onboarding process",
  country: "CA",
  business_type: "individual",
};

// Test Stripe Connect delete response
export const mockStripeConnectDeleteResponse = {
  success: true,
  message: "Stripe Connect account deleted successfully",
  deleted_account_id: "acct_1234567890abcdef",
};

// Environment flag to enable test data
const isDevelopment =
  typeof window !== "undefined" &&
  (window.location.hostname === "localhost" ||
    window.location.hostname.includes("dev"));

const hasTestFlag =
  typeof window !== "undefined" && window.location.search.includes("test=true");

export const USE_TEST_DATA = isDevelopment && hasTestFlag;

// Helper function to simulate API delay
export const simulateApiDelay = (ms: number = 1000): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};
