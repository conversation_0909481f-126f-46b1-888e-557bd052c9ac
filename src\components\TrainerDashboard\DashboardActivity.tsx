import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { IconDefinition } from "@fortawesome/free-solid-svg-icons";
import {
  faPlay,
  faCheckCircle,
  faCalendarAlt,
  faShoppingCart,
  faStar,
  faD<PERSON><PERSON>ll,
  faTrophy,
  faUser<PERSON><PERSON>ck,
  faClock,
  faFilter,
} from "@fortawesome/free-solid-svg-icons";
import { Activity } from "@/interfaces/model.interface";
import { useDate } from "@/hooks/useDate";
import { useState } from "react";
import { useTrainerActivities } from "@/hooks/useTrainerActivities";

interface DashboardActivityProps {
  activities?: Activity[];
  isLoading?: boolean;
  showFilters?: boolean;
}

const DashboardActivity = ({
  activities: propActivities,
  isLoading: propIsLoading = false,
  showFilters = false,
}: DashboardActivityProps) => {
  const { convertDate } = useDate();
  const [activityTypeFilter, setActivityTypeFilter] = useState<string>("");

  // Use the hook if no activities are provided
  const {
    activities: hookActivities,
    isLoading: hookIsLoading,
    refetch,
  } = useTrainerActivities({
    enabled: !propActivities,
    limit: 10,
    activity_type: activityTypeFilter || undefined,
  });

  const activities = propActivities || hookActivities;
  const isLoading = propIsLoading || hookIsLoading;

  // Icon mapping function based on activity type
  const getActivityIcon = (activityType?: string): IconDefinition => {
    switch (activityType) {
      case "workout_started":
        return faPlay;
      case "workout_completed":
        return faDumbbell;
      case "day_completed":
        return faCheckCircle;
      case "week_completed":
        return faCalendarAlt;
      case "program_completed":
        return faTrophy;
      case "session_scheduled":
        return faClock;
      case "new_enrollment":
        return faUserCheck;
      case "payment_made":
        return faShoppingCart;
      case "milestone_reached":
        return faStar;
      default:
        return faPlay;
    }
  };

  const activityList = activities?.length ? activities : [];

  if (isLoading) {
    return (
      <div className="divide-y divide-border">
        {[1, 2, 3].map((index) => (
          <div key={index} className="flex items-center gap-4 px-6 py-4">
            <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
            <div className="flex flex-col gap-2 flex-1">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!activityList.length) {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-6">
        <div className="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-4">
          <FontAwesomeIcon
            icon={faPlay}
            className="w-6 h-6 text-gray-400 dark:text-gray-600"
          />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          No Activity Yet
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 text-center max-w-sm">
          When your athletes start working out and engaging with your programs,
          their activity will appear here.
        </p>
      </div>
    );
  }

  return (
    <div>
      {/* Filter Section */}
      {showFilters && (
        <div className="px-6 py-4 border-b border-border">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <FontAwesomeIcon
                icon={faFilter}
                className="w-4 h-4 text-gray-500 dark:text-gray-400"
              />
              <span className="text-sm font-medium text-text dark:text-gray-100">
                Filter:
              </span>
            </div>
            <select
              value={activityTypeFilter}
              onChange={(e) => setActivityTypeFilter(e.target.value)}
              className="px-3 py-1 text-sm border border-border rounded-md bg-background text-text dark:bg-neutral-800 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="">All Activities</option>
              <option value="workout_completed">Workouts Completed</option>
              <option value="day_completed">Days Completed</option>
              <option value="week_completed">Weeks Completed</option>
              <option value="program_completed">Programs Completed</option>
              <option value="milestone_reached">Milestones Reached</option>
            </select>
            <button
              onClick={() => {
                setActivityTypeFilter("");
                refetch();
              }}
              className="px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-text dark:hover:text-gray-100"
            >
              Clear
            </button>
          </div>
        </div>
      )}

      {/* Activities List */}
      <div className="divide-y divide-border">
        {activityList.map((activity) => (
        <div key={activity.id} className="flex items-center gap-4 px-6 py-4">
          <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-primary">
            <FontAwesomeIcon
              icon={getActivityIcon(activity.activity_type)}
              className="w-4 h-4 text-white"
            />
          </span>
          <div className="flex flex-col flex-1">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-text dark:text-gray-100">
                {activity.title}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {activity.user_name}
              </span>
            </div>
            {activity.program_name && (
              <span className="text-xs text-primary dark:text-primary-light mt-1">
                {activity.program_name}
              </span>
            )}
            <span className="text-sm text-gray-600 dark:text-gray-300 mt-1">
              {activity.description}
            </span>
            {activity.metadata && (
              <div className="flex gap-4 mt-1 flex-wrap">
                {activity.metadata.sets_completed && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Sets: {activity.metadata.sets_completed}
                  </span>
                )}
                {activity.metadata.reps_completed && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Reps: {activity.metadata.reps_completed}
                  </span>
                )}
                {activity.metadata.weight_used && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Weight: {activity.metadata.weight_used}
                  </span>
                )}
                {activity.metadata.time_taken_seconds && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Time: {Math.round(activity.metadata.time_taken_seconds / 60)}min
                  </span>
                )}
                {activity.metadata.difficulty_rating && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Difficulty: {activity.metadata.difficulty_rating}/5
                  </span>
                )}
                {activity.metadata.total_exercises && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Exercises: {activity.metadata.completed_exercises}/{activity.metadata.total_exercises}
                  </span>
                )}
                {activity.metadata.progress_percentage && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Progress: {activity.metadata.progress_percentage}%
                  </span>
                )}
              </div>
            )}
            <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {activity.created_at
                ? convertDate(activity.created_at, {
                    year: undefined,
                    month: "short",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                  })
                : "Unknown time"}
            </span>
          </div>
        </div>
      ))}
      </div>
    </div>
  );
};

export default DashboardActivity;
