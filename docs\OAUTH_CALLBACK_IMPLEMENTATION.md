# OAuth Callback Implementation

## Overview

This implementation adds a public route `/login/oauth` to handle OAuth authentication callbacks from social login providers (Google, Facebook, etc.). The route processes the authentication response and automatically logs in users or displays error messages.

## Features

- **Error Handling**: Shows error messages when OAuth login fails
- **Success Handling**: Automatically logs in users and redirects based on their role
- **Role-based Redirects**:
  - `member` (athlete) → Home page (`/`)
  - `trainer` → Trainer dashboard (`/trainer/dashboard`)
  - `admin`/`super_admin` → Admin dashboard (`/admin/dashboard`)
- **Loading States**: Shows processing indicator while handling the callback
- **Theme Support**: Fully integrated with the application's theme system

## URL Format

The OAuth callback expects data in the following URL format:

```
/login/oauth?data=<encoded_json_data>
```

### Success Response Format

```json
{
  "error": false,
  "role": "member",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expire_at": 3600,
  "user_id": 69,
  "refresh_token": "optional_refresh_token"
}
```

### Error Response Format

```json
{
  "error": true,
  "message": "User already registered without google login!"
}
```

## Implementation Details

### Files Added/Modified

1. **`/src/pages/Common/Auth/OAuthCallbackPage.tsx`** - Main OAuth callback component
2. **`/src/routes/LazyLoad.ts`** - Added lazy loading for OAuth components
3. **`/src/routes/Routes.tsx`** - Added OAuth callback route
4. **`/src/pages/Common/Auth/OAuthTestPage.tsx`** - Test page for OAuth functionality

### Route Configuration

```tsx
<Route
  path="/login/oauth"
  element={
    <PublicRoute
      path={"/login/oauth"}
      element={
        <PublicWrapper>
          <OAuthCallbackPage />
        </PublicWrapper>
      }
    />
  }
/>
```

## Backend Integration

The backend OAuth handlers (Google, Facebook, etc.) should redirect to this route with the appropriate data:

```javascript
// Example backend redirect
const resData = JSON.stringify({
  error: false,
  role: role,
  token: JwtService.createAccessToken(...),
  expire_at: config.access_jwt_expire,
  user_id: id,
  refresh_token: refreshToken,
});

const encodedURI = encodeURI(resData);
res.redirect(`https://${hostname}/login/oauth?data=${encodedURI}`);
```

## Testing

A test page is available at `/oauth-test` that allows you to simulate different OAuth callback scenarios:

- Successful member login
- Successful trainer login
- Failed OAuth login

### Test URLs

You can also test directly by navigating to URLs like:

```
/login/oauth?data=%7B"error":false,"role":"member","token":"test_token","expire_at":3600,"user_id":69%7D

/login/oauth?data=%7B"error":true,"message":"User%20already%20registered%20without%20google%20login!"%7D
```

## Error Handling

The component handles various error scenarios:

1. **Missing data parameter**: Shows "No OAuth data received"
2. **Invalid JSON**: Shows "Failed to process OAuth callback"
3. **OAuth error response**: Shows the error message from the backend
4. **Missing required fields**: Shows "Invalid OAuth response data"

All errors automatically redirect to the login page after 3 seconds.

## Authentication Flow

1. User clicks social login button on login page
2. User is redirected to OAuth provider (Google, Facebook, etc.)
3. OAuth provider redirects back to `/login/oauth?data=<response>`
4. `OAuthCallbackPage` processes the response:
   - If error: Show error message and redirect to login
   - If success: Log in user and redirect based on role
5. User is now authenticated and redirected to appropriate dashboard

## Security Considerations

- The OAuth callback route is public (no authentication required)
- JWT tokens are validated by the authentication context
- All user data is stored in localStorage and authentication context
- Refresh tokens are handled automatically if provided

## Social Login Providers Supported

### 1. Google Login

- **Backend Route**: `/v2/api/lambda/google/code`
- **Frontend Route**: `/v2/api/lambda/member/google/login`
- **Login Type**: `1`
- **Status**: ✅ Working with OAuth callback

### 2. Facebook Login

- **Backend Route**: `/v2/api/lambda/facebook/code`
- **Frontend Route**: `/v2/api/lambda/member/facebook/login`
- **Login Type**: `5`
- **Status**: ✅ Updated to work with OAuth callback

### 3. LinkedIn Login

- **Backend Route**: `/v2/api/lambda/linkedin/code`
- **Frontend Route**: `/v2/api/lambda/member/linkedin/login`
- **Login Type**: `7`
- **Status**: ✅ Updated to work with OAuth callback

### 4. Instagram Login

- **Backend Route**: `/v2/api/lambda/instagram/code`
- **Frontend Route**: `/v2/api/lambda/member/instagram/login`
- **Login Type**: `6`
- **Status**: ✅ Newly implemented with OAuth callback

## Backend Configuration Required

Each social login provider requires configuration in your backend config:

```javascript
{
  google: {
    client_id: "your_google_client_id",
    client_secret: "your_google_client_secret",
    redirect_url: "https://your-backend.com/v2/api/lambda/google/code"
  },
  facebook: {
    client_id: "your_facebook_client_id",
    client_secret: "your_facebook_client_secret",
    redirect_url: "https://your-backend.com/v2/api/lambda/facebook/code"
  },
  linkedin: {
    client_id: "your_linkedin_client_id",
    client_secret: "your_linkedin_client_secret",
    redirect_url: "https://your-backend.com/v2/api/lambda/linkedin/code"
  },
  instagram: {
    client_id: "your_instagram_client_id",
    client_secret: "your_instagram_client_secret",
    redirect_url: "https://your-backend.com/v2/api/lambda/instagram/code"
  }
}
```

## Database Schema

All social logins create users with the following structure:

```sql
-- user table
login_type: INTEGER (1=Google, 2=Microsoft, 3=Apple, 4=Twitter, 5=Facebook, 6=Instagram, 7=LinkedIn)
email: VARCHAR
role_id: VARCHAR (member, trainer, admin, etc.)
data: JSON (contains first_name, last_name, etc.)

-- preference table
user_id: INTEGER
first_name: VARCHAR
last_name: VARCHAR
photo: VARCHAR (optional)

-- tokens table (for OAuth tokens)
user_id: INTEGER
token: TEXT (access_token)
type: INTEGER (matches login_type)
data: JSON (full OAuth response)
```

## Future Enhancements

- Add support for additional OAuth providers (Twitter, Microsoft, Apple)
- Implement OAuth state parameter validation for enhanced security
- Add analytics tracking for OAuth login events
- Support for custom redirect URLs after OAuth login
- Add refresh token handling for long-lived sessions
