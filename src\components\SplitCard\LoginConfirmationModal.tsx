import React from "react";
import { MkdButton } from "@/components/MkdButton";

interface LoginConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  actionType: "subscription" | "one_time";
  splitName: string;
  price: number;
}

const LoginConfirmationModal: React.FC<LoginConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  actionType,
  splitName,
  price,
}) => {
  if (!isOpen) return null;

  const actionText =
    actionType === "subscription" ? "subscribe to" : "purchase";
  const priceText =
    actionType === "subscription" ? `$${price}/month` : `$${price}`;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-background rounded-lg p-6 w-full max-w-md mx-4 border border-border">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-text mb-2">
            Login Required
          </h2>
          <p className="text-text-secondary">
            You need to log in to {actionText} "{splitName}" for {priceText}.
          </p>
          <p className="text-text-secondary mt-2">
            Would you like to continue to the login page? You'll be redirected
            back here after logging in.
          </p>
        </div>

        <div className="flex gap-3 justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 text-text-secondary hover:text-text transition-colors"
          >
            Cancel
          </button>
          <MkdButton
            onClick={onConfirm}
            className="px-6 py-2 bg-primary hover:bg-primary-hover text-white"
          >
            Go to Login
          </MkdButton>
        </div>
      </div>
    </div>
  );
};

export default LoginConfirmationModal;
