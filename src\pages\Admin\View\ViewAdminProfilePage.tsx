import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useContexts } from "@/hooks/useContexts";
import useProfile from "@/hooks/useProfile/useProfile";
import { useSDK } from "@/hooks/useSDK";
import {
  ProfileHeader,
  AccountSecurity,
  TwoFactorAuthModal,
} from "@/components/Profile";
import { InteractiveButton } from "@/components/InteractiveButton";
import MkdInputV2 from "@/components/MkdInputV2";
import UpdatePasswordModal from "@/components/UserProfile/UpdatePasswordModal";
import { ToastStatusEnum } from "@/utils/Enums";

// Form validation schema
const schema = yup.object().shape({
  full_name: yup.string().required("Full name is required"),
  email: yup
    .string()
    .email("Invalid email format")
    .required("Email is required"),
});

type FormData = yup.InferType<typeof schema>;

const ViewAdminProfilePage: React.FC = () => {
  const { globalDispatch, showToast, tokenExpireError } = useContexts();
  const { profile, getProfile } = useProfile();
  const { sdk } = useSDK();

  // State management
  const [loading, setLoading] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [show2FAModal, setShow2FAModal] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);

  // React Hook Form setup
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isDirty },
    reset,
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      full_name: "",
      email: "",
    },
  });

  // Handle form submission
  const onSubmit = async (data: FormData) => {
    setLoading(true);
    try {
      // Update profile with full_name and email
      const updatePayload: any = {
        payload: {
          full_name: data.full_name,
        },
      };

      const result = await sdk.updateProfile(updatePayload);

      if (!result.error) {
        showToast(
          "Profile updated successfully",
          5000,
          ToastStatusEnum.SUCCESS
        );
        // Refresh profile data
        getProfile();
        // Reset form dirty state
        reset(data);
      } else {
        showToast(
          result.message || "Failed to update profile",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    } catch (error: any) {
      const message =
        error?.response?.data?.message || error?.message || "An error occurred";
      showToast(message, 5000, ToastStatusEnum.ERROR);
      tokenExpireError(message);
    } finally {
      setLoading(false);
    }
  };

  // Handle password change
  const handleChangePassword = () => {
    setShowPasswordModal(true);
  };

  // Handle 2FA toggle
  const handleTwoFactorAuth = () => {
    setShow2FAModal(true);
  };

  // Handle 2FA modal success
  const handle2FASuccess = (enabled: boolean) => {
    setTwoFactorEnabled(enabled);
    // Refresh profile data to get updated 2FA status
    getProfile();
  };

  // Check 2FA status when component loads
  useEffect(() => {
    const check2FAStatus = async () => {
      try {
        const result = await sdk.get2FAStatus();
        if (!result.error) {
          setTwoFactorEnabled(result.enabled || false);
        }
      } catch (error) {
        console.error("Error checking 2FA status:", error);
      }
    };

    if (profile) {
      check2FAStatus();
    }
  }, [profile, sdk]);
  
  // Load profile data when component mounts or profile changes
  useEffect(() => {
    if (profile) {
      const {
        data: { full_name },
        email,
      } = profile;

      setValue("full_name", full_name || "");
      setValue("email", email || "");

      // Reset form dirty state after loading data
      setTimeout(() => reset(watch()), 100);
    }
  }, [profile, setValue, reset, watch]);

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "settings",
      },
    });
  }, [globalDispatch]);

  return (
    <div className="w-full bg-background min-h-full p-4 sm:p-6 lg:p-8 transition-colors duration-200">
      <div className="max-w-7xl mx-auto ">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <ProfileHeader title="Profile" className="mb-0" />
        </div>

        <form
          onSubmit={handleSubmit(onSubmit)}
          className="space-y-6 sm:space-y-8"
        >
          {/* Profile Management Section */}
          <div className="bg-background-secondary border border-border rounded-lg p-4 sm:p-6 shadow-sm">
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-text">
                Profile Management
              </h2>

              <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                {/* Full Name Field */}
                <div className="space-y-2">
                  <MkdInputV2
                    name="full_name"
                    type="text"
                    register={register}
                    errors={errors}
                    required
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label className="text-sm font-medium text-text">
                        Full Name
                      </MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="Full Name"
                        className="w-full h-11 px-3 py-2 bg-background border border-border rounded-md text-text placeholder-text-secondary focus:border-primary focus:ring-0 transition-colors duration-200 hover:border-border-hover disabled:bg-background-disabled disabled:cursor-not-allowed"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>

                {/* Email Field */}
                <div className="space-y-2">
                  <MkdInputV2
                    name="email"
                    type="email"
                    register={register}
                    errors={errors}
                    disabled
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label className="text-sm font-medium text-text">
                        Email Address
                      </MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="Email Address"
                        className="w-full h-11 px-3 py-2 bg-background border border-border rounded-md text-text placeholder-text-secondary focus:border-primary focus:ring-0 transition-colors duration-200 hover:border-border-hover disabled:bg-background-disabled disabled:cursor-not-allowed"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>
              </div>
            </div>
          </div>

          {/* Account Security Section */}
          <AccountSecurity
            onChangePassword={handleChangePassword}
            onTwoFactorAuth={handleTwoFactorAuth}
            className="w-full"
          />

          <div className="w-full flex justify-end">
            <InteractiveButton
              type="submit"
              loading={loading}
              disabled={loading || !isDirty}
              className="!h-11 w-fit px-6 bg-transparent border border-primary text-primary font-semibold hover:bg-primary hover:text-white active:bg-primary-active transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:border-primary-disabled disabled:text-primary-disabled"
            >
              Save Changes
            </InteractiveButton>
          </div>
        </form>

        {/* Password Change Modal */}
        <UpdatePasswordModal
          isOpen={showPasswordModal}
          onClose={() => setShowPasswordModal(false)}
        />

        {/* Two-Factor Authentication Modal */}
        <TwoFactorAuthModal
          isOpen={show2FAModal}
          onClose={() => setShow2FAModal(false)}
          onSuccess={handle2FASuccess}
          currentlyEnabled={twoFactorEnabled}
        />
      </div>
    </div>
  );
};

export default ViewAdminProfilePage;
