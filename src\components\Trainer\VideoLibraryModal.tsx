import React, { useState } from "react";
import { Modal } from "@/components/Modal";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useLibrary } from "@/hooks/useLibrary/useLibrary";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import { useProfile } from "@/hooks/useProfile";
import DeleteConfirmationModal from "@/components/Library/DeleteConfirmationModal";
import AddVideoModal from "@/components/Trainer/AddVideoModal";
import EditVideoModal from "@/components/Trainer/EditVideoModal";

interface VideoLibraryModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const VideoLibraryModal: React.FC<VideoLibraryModalProps> = ({
  isOpen,
  onClose,
}) => {
  const { showToast } = useContexts();
  const [searchTerm, setSearchTerm] = useState("");
  const [hoveredVideo, setHoveredVideo] = useState<number | null>(null);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [videoToDelete, setVideoToDelete] = useState<any | null>(null);
  const [showAddVideoModal, setShowAddVideoModal] = useState(false);
  const [showEditVideoModal, setShowEditVideoModal] = useState(false);
  const [videoToEdit, setVideoToEdit] = useState<any | null>(null);
  const { profile } = useProfile();
  const userId = profile?.id;
  // Video library hook for user's videos (type = 2)
  const {
    libraryData: videoData,
    isLoading,
    error,
    searchLibraryItems: searchVideos,
    deleteLibraryItem: deleteVideo,
    isCreating,
    isUpdating,
  } = useLibrary({
    libraryType: "video",
    initialPagination: {
      page: 1,
      limit: 50,
      filters: { type: 2, ...(userId && { user_id: Number(userId) }) }, // User's videos
    },
    disable: {
      type_two: false,
      type_one: true,
    },
  });

  // Filter videos based on search term
  const filteredVideos =
    videoData?.filter((video: any) =>
      video.name?.toLowerCase().includes(searchTerm.toLowerCase())
    ) || [];

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    if (value.trim()) {
      searchVideos(value.trim());
    } else {
      searchVideos(value);
    }
  };

  // Handle delete video confirmation
  const handleDeleteVideoClick = (video: any) => {
    setVideoToDelete(video);
    setShowDeleteConfirmation(true);
  };

  // Handle delete video
  const handleDeleteVideo = async () => {
    if (!videoToDelete) return;

    try {
      await deleteVideo(videoToDelete.id as number);
      showToast("Video deleted successfully", 3000, ToastStatusEnum.SUCCESS);
    } catch (error: any) {
      const message = error?.message || "Failed to delete video";
      showToast(message, 3000, ToastStatusEnum.ERROR);
    }
  };

  // Handle edit video
  const handleEditVideo = (video: any) => {
    setVideoToEdit(video);
    setShowEditVideoModal(true);
  };

  // // Handle update video
  // const handleUpdateVideo = async (
  //   videoId: number,
  //   videoName: string,
  //   videoUrl: string
  // ) => {
  //   try {
  //     // await updateVideo(videoId, {
  //     //   name: videoName,
  //     //   url: videoUrl,
  //     //   type: 2,
  //     //   user_id: userId,
  //     // });
  //   } catch (error) {
  //     // Error is already handled by the useLibrary hook
  //     console.error("Error updating video:", error);
  //   }
  // };

  // // Handle add new video
  // const handleAddVideo = () => {
  //   setShowAddVideoModal(true);
  // };

  // // Handle create video
  // const handleCreateVideo = async (videoName: string, videoUrl: string) => {
  //   try {
  //     // await createVideo({
  //     //   name: videoName,
  //     //   url: videoUrl,
  //     //   type: 2, // Trainer created
  //     //   user_id: userId,
  //     // });
  //   } catch (error) {
  //     // Error is already handled by the useLibrary hook
  //     console.error("Error creating video:", error);
  //   }
  // };

  return (
    <>
      <Modal
        isOpen={isOpen}
        modalCloseClick={onClose}
        title="My Video Library"
        modalHeader
        classes={{
          modalDialog: "!w-full !px-0 md:!w-[32rem] !h-fit",
          modalContent: "!z-10 !px-0 overflow-hidden !pt-0 !mt-0",
          modal: "h-full",
        }}
      >
        <div className="p-6">
          {/* Header with Add Button */}
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-text"></h3>
            <InteractiveButton
              type="button"
              onClick={() => {}}
              className="w-8 h-8 p-2 rounded-full bg-green-500 text-white flex items-center justify-center hover:bg-green-600 transition-colors"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M8 1V15M1 8H15"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </InteractiveButton>
          </div>

          {/* Search Bar */}
          <div className="relative mb-4">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg
                className="h-5 w-5 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search"
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-border rounded-md bg-background text-text placeholder-text-secondary focus:border-primary focus:ring-0 transition-colors"
            />
          </div>

          {/* Video List */}
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                <p className="text-text-secondary">Loading videos...</p>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-500">Error loading videos</p>
              </div>
            ) : filteredVideos.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-text-secondary">
                  {searchTerm ? "No videos found" : "No videos yet"}
                </p>
              </div>
            ) : (
              filteredVideos.map((video: any) => (
                <div
                  key={video.id}
                  className="relative p-3 border-b border-border last:border-b-0 hover:bg-background-secondary transition-colors"
                  onMouseEnter={() => setHoveredVideo(video.id as number)}
                  onMouseLeave={() => setHoveredVideo(null)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {/* Video Icon */}
                      <div className="w-8 h-8 bg-gray-200 rounded-md flex items-center justify-center">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M6.5 4.5L10.5 8L6.5 11.5V4.5Z"
                            fill="currentColor"
                          />
                        </svg>
                      </div>
                      <span className="text-text font-medium">
                        {video.name || "Unnamed Video"}
                      </span>
                    </div>

                    {/* Edit and Delete buttons - visible on hover */}
                    {hoveredVideo === video.id && (
                      <div className="flex items-center space-x-2">
                        <InteractiveButton
                          type="button"
                          onClick={() => handleEditVideo(video)}
                          className="p-1 border-none text-blue-500 hover:text-blue-600 hover:bg-transparent transition-colors"
                          title="Edit"
                        >
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M11.25 1.5L14.5 4.75L5.75 13.5H2.5V10.25L11.25 1.5Z"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </InteractiveButton>

                        <InteractiveButton
                          type="button"
                          onClick={() => handleDeleteVideoClick(video)}
                          className="p-1 border-none text-red-500 hover:text-red-600 hover:bg-transparent transition-colors"
                          title="Delete"
                        >
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M2 4H3.33333H14"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M12.3333 4V13.3333C12.3333 13.687 12.1929 14.0261 11.9428 14.2761C11.6928 14.5262 11.3537 14.6667 11 14.6667H5C4.64638 14.6667 4.30724 14.5262 4.05719 14.2761C3.80714 14.0261 3.66667 13.687 3.66667 13.3333V4M5.33333 4V2.66667C5.33333 2.31305 5.47381 1.97391 5.72386 1.72386C5.97391 1.47381 6.31305 1.33333 6.66667 1.33333H9.33333C9.68695 1.33333 10.0261 1.47381 10.2761 1.72386C10.5262 1.97391 10.6667 2.31305 10.6667 2.66667V4"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </InteractiveButton>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-end mt-6 pt-4 border-t border-border">
            <InteractiveButton
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-background border border-border text-text rounded-md hover:bg-background-secondary transition-colors"
            >
              Back
            </InteractiveButton>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={showDeleteConfirmation}
        onClose={() => {
          setShowDeleteConfirmation(false);
          setVideoToDelete(null);
        }}
        onConfirm={handleDeleteVideo}
        title="Delete Video"
        message="Are you sure you want to delete this video?"
        itemName={videoToDelete?.name}
      />

      {/* Add Video Modal */}
      <AddVideoModal
        isOpen={showAddVideoModal}
        onClose={() => setShowAddVideoModal(false)}
        onAddVideo={() => {}}
        isLoading={isCreating}
      />

      {/* Edit Video Modal */}
      <EditVideoModal
        isOpen={showEditVideoModal}
        onClose={() => {
          setShowEditVideoModal(false);
          setVideoToEdit(null);
        }}
        video={videoToEdit}
        onUpdateVideo={() => {}}
        isLoading={isUpdating}
      />
    </>
  );
};

export default VideoLibraryModal;
