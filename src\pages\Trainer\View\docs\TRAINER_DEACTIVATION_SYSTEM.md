# Trainer Deactivation System

## Overview

The Trainer Deactivation System is a client-side implementation that prevents deactivated trainers from performing actions while still allowing them to navigate and view content. A trainer is considered deactivated when their `status` is `0` (or `false`) but they are `verified` (`verify = 1`).

## Status Logic

### Trainer Status Types

1. **Active Trainer**: `status = 1` and `verify = 1`
   - Can perform all actions
   - Full access to all features

2. **Deactivated Trainer**: `status = 0` and `verify = 1`
   - Can navigate and view content
   - Cannot perform any actions
   - Shows warning banner

3. **Unverified Trainer**: `status = 0` and `verify = 0`
   - Limited access
   - Cannot perform actions
   - Shows verification pending message

## Implementation

### Core Hook: `useTrainerStatus`

Located at: `src/hooks/useTrainerStatus.ts`

This hook centralizes the logic for determining a trainer's status and provides utility functions.

#### Interface

```typescript
export interface TrainerStatus {
  isDeactivated: boolean;
  isVerified: boolean;
  isActive: boolean;
  canPerformActions: boolean;
  statusMessage: string;
}
```

#### Usage

```typescript
import { useTrainerStatus } from '@/hooks/useTrainerStatus';

const { isDeactivated, canPerformActions, statusMessage } = useTrainerStatus();
```

#### Utility Functions

- `showDeactivationWarning(showToast)`: Shows a warning toast for deactivated trainers
- `disableActionForDeactivatedTrainer(action, showToast, trainerStatus)`: Wraps actions to prevent execution for deactivated trainers

### Warning Component: `DeactivationWarning`

Located at: `src/components/DeactivationWarning.tsx`

A persistent warning banner displayed at the bottom of the screen for deactivated trainers.

#### Usage

```typescript
import { DeactivationWarning } from '@/components/DeactivationWarning';

{isDeactivated && (
  <DeactivationWarning message={statusMessage} />
)}
```

## Affected Pages

### 1. ViewTrainerProfilePage
- **File**: `src/pages/Trainer/View/ViewTrainerProfilePage.tsx`
- **Changes**:
  - Added `useTrainerStatus` hook
  - Disabled form fields when deactivated
  - Added early return in `onSubmit`
  - Wrapped action handlers with `disableActionForDeactivatedTrainer`
  - Added `DeactivationWarning` component

### 2. AddTrainerProgramPage
- **File**: `src/pages/Trainer/Add/AddTrainerProgramPage.tsx`
- **Changes**:
  - Added `useTrainerStatus` hook
  - Added early returns in submit handlers
  - Added `DeactivationWarning` component

### 3. ViewTrainerFeedPage
- **File**: `src/pages/Trainer/View/ViewTrainerFeedPage.tsx`
- **Changes**:
  - Added `useTrainerStatus` hook
  - Added early return in `handleCreatePost`
  - Added `DeactivationWarning` component

### 4. ViewTrainerDashboardPage
- **File**: `src/pages/Trainer/View/ViewTrainerDashboardPage.tsx`
- **Changes**:
  - Added `useTrainerStatus` hook
  - Added `DeactivationWarning` component
  - Conditionally hide "Create New Program" link

### 5. ViewTrainerDiscountPage
- **File**: `src/pages/Trainer/View/ViewTrainerDiscountPage.tsx`
- **Changes**:
  - Added `useTrainerStatus` hook
  - Added early return in `handleSaveDiscountSettings`
  - Added `DeactivationWarning` component

## User Experience

### For Deactivated Trainers

1. **Visual Indicators**:
   - Persistent warning banner at bottom of screen
   - Disabled form fields and buttons
   - Toast notifications when attempting actions

2. **Behavior**:
   - Can navigate all pages
   - Can view all content
   - Cannot submit forms or perform actions
   - Clear feedback when attempting restricted actions

3. **Warning Messages**:
   - Banner: "Your account has been deactivated by admin. You can view content but cannot perform actions."
   - Toast: Same message with 5-second duration

### For Active Trainers

- No changes to existing functionality
- No visual indicators
- Full access to all features

## Testing

### Test File: `useTrainerStatus.test.ts`

Located at: `src/hooks/useTrainerStatus.test.ts`

Tests cover:
- Active trainer status
- Deactivated trainer status
- Unverified trainer status
- Undefined profile handling
- Utility functions behavior

## Future Enhancements

1. **Additional Restrictions**: Add more action restrictions as needed
2. **Admin Interface**: Allow admins to deactivate/reactivate trainers
3. **Audit Logging**: Track deactivation events
4. **Email Notifications**: Notify trainers of status changes
5. **Graceful Degradation**: Handle network errors during status checks

## Technical Notes

- All status checks are client-side only
- No server-side validation implemented
- Uses existing `useProfile` hook for data
- Integrates with existing toast notification system
- Follows existing component patterns and styling

## Dependencies

- `@/hooks/useProfile` - For trainer profile data
- `@/hooks/useToast` - For toast notifications
- `@/context/Theme` - For styling consistency
- `@fortawesome/react-fontawesome` - For warning icon 