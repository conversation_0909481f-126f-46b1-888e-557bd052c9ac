import React from "react";
import { useTheme } from "@/hooks/useTheme";

interface DiscountActionsProps {
  onSave?: () => void;
  onRefresh?: () => void;
  isLoading?: boolean;
}

const DiscountActions = ({
  onSave,
  onRefresh,
  isLoading = false,
}: DiscountActionsProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const handleCancel = () => {
    onRefresh?.();
  };

  const handleApply = () => {
    onSave?.();
  };

  return (
    <div className="flex gap-4">
      <button
        onClick={handleCancel}
        disabled={isLoading}
        className="px-6 py-2 border border-border rounded-md bg-background text-text hover:bg-background-hover transition-colors duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Refresh
      </button>
      <button
        onClick={handleApply}
        disabled={isLoading}
        className="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary-hover transition-colors duration-200 font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? "Saving..." : "Save Changes"}
      </button>
    </div>
  );
};

export default DiscountActions;
