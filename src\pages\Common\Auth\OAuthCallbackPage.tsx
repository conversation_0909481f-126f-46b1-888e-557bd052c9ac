import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { useQueryClient } from "@tanstack/react-query";

interface OAuthData {
  error: boolean;
  message?: string;
  role?: string;
  token?: string;
  expire_at?: number;
  user_id?: number;
  refresh_token?: string;
  profile_update?: boolean;
  [key: string]: any;
}

const OAuthCallbackPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { authDispatch, showToast } = useContexts();
  const { state } = useTheme();
  const mode = state?.theme;
  const queryClient = useQueryClient();


  const [isProcessing, setIsProcessing] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const updateProfile = async (data: any) => {
    authDispatch({
      type: "LOGIN",
      payload: {
        user_id: data.user_id.toString(),
        token: data.token,
        role: data.role,
        refresh_token: data.refresh_token,
        remember_me: true, // OAuth logins are typically remembered
      },
    });
    // wait for 1 second
    return await new Promise(resolve => setTimeout(resolve, 1000));
  }

  const processOAuthCallback = async () => {
    try {
      // Parse the URL parameters
      const searchParams = new URLSearchParams(location.search);
      const dataParam = searchParams.get("data");

      if (!dataParam) {
        setErrorMessage("No OAuth data received");
        setIsProcessing(false);
        return;
      }

      // Decode the data
      const decodedData = decodeURIComponent(dataParam);
      const oauthData: OAuthData = JSON.parse(decodedData);
      console.log("oauthData", oauthData);
      if (oauthData.error) {
        // Handle error case
        setErrorMessage(oauthData.message || "OAuth login failed");
        setIsProcessing(false);
        showToast(
          oauthData.message || "OAuth login failed",
          4000,
          ToastStatusEnum.ERROR
        );
        
        // Redirect to login page after showing error
        setTimeout(() => {
          navigate("/login");
        }, 5000);
        return;
      }

      // Handle success case
      if (oauthData.token && oauthData.role && oauthData.user_id) {
        // Dispatch login action
        localStorage.setItem("token", oauthData.token);
        localStorage.setItem("role", oauthData.role);
        localStorage.setItem("user_id", oauthData.user_id.toString());
        localStorage.setItem("refresh_token", oauthData.refresh_token || "");
        
        await updateProfile(oauthData);
        
        showToast("Successfully logged in!", 4000, ToastStatusEnum.SUCCESS);
        
        // Invalidate all queries
        queryClient.invalidateQueries();

        // Check if profile needs to be completed
        try {
          if (oauthData && !oauthData.profile_update) {
            // Profile needs to be completed
            if (oauthData.role === "member") {
              navigate("/athlete/profile-completion");
            } else if (oauthData.role === "trainer") {
              navigate("/trainer/profile-completion");
            } else {
              // For admin users, redirect to dashboard
              navigate("/");
            }
          } else {
            // Profile is complete, redirect based on role
            if (oauthData.role === "member") {
              navigate("/");
            } else if (oauthData.role === "trainer") {
              navigate("/trainer/dashboard");
            } else if (oauthData.role === "admin" || oauthData.role === "super_admin") {
              navigate("/admin/dashboard");
            } else {
              navigate("/");
            }
          }
        } catch (error) {
          console.error("Error checking profile:", error);
          // navigate("/");
          // Fallback to default redirects
          if (oauthData.role === "member") {
            navigate("/");
          } else if (oauthData.role === "trainer") {
            navigate("/trainer/dashboard");
          } else if (oauthData.role === "admin" || oauthData.role === "super_admin") {
            navigate("/admin/dashboard");
          } else {
            navigate("/");
          }
        }
      } else {
        setErrorMessage("Invalid OAuth response data");
        setIsProcessing(false);
      }
    } catch (error) {
      console.error("Error processing OAuth callback:", error);
      setErrorMessage("Failed to process OAuth callback");
      setIsProcessing(false);
      showToast("Failed to process OAuth callback", 4000, ToastStatusEnum.ERROR);
      
      // Redirect to login page after showing error
      setTimeout(() => {
        navigate("/login");
      }, 5000);
    }
  };

  useEffect(() => {
    processOAuthCallback().catch(error => {
      console.error("Error processing OAuth callback:", error);
      setErrorMessage("Failed to process OAuth callback");
      setIsProcessing(false);
    });
  }, []);

  const handleReturnToLogin = () => {
    navigate("/login");
  };

  return (
    <div
      className="min-h-full grid grid-cols-1 grid-rows-1 h-full max-h-full transition-colors duration-200"
      style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND }}
    >
      <main className="flex h-full items-center justify-center px-4 py-8">
        <div className="w-full max-w-md">
          <div
            className="relative w-full shadow-lg rounded-lg px-8 py-10 text-center transition-colors duration-200"
            style={{
              backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
              borderColor: THEME_COLORS[mode].BORDER,
              color: THEME_COLORS[mode].TEXT,
            }}
          >
            {isProcessing ? (
              <>
                {/* Loading State */}
                <div className="mb-6">
                  <div
                    className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto"
                    style={{ borderColor: THEME_COLORS[mode].PRIMARY }}
                  ></div>
                </div>
                <h1
                  className="text-2xl font-bold mb-4 transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  Processing Login...
                </h1>
                <p
                  className="text-sm transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                >
                  Please wait while we complete your login.
                </p>
              </>
            ) : (
              <>
                {/* Error State */}
                <div className="mb-6">
                  <div
                    className="rounded-full h-12 w-12 mx-auto flex items-center justify-center"
                    style={{ backgroundColor: THEME_COLORS[mode].ERROR }}
                  >
                    <svg
                      className="h-6 w-6 text-white"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </div>
                </div>
                <h1
                  className="text-2xl font-bold mb-4 transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  Login Failed
                </h1>
                <p
                  className="text-sm mb-6 transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                >
                  {errorMessage}
                </p>
                <button
                  onClick={handleReturnToLogin}
                  className="w-full h-12 rounded font-semibold text-base transition-colors duration-200"
                  style={{
                    backgroundColor: THEME_COLORS[mode].PRIMARY,
                    color: THEME_COLORS[mode].TEXT_ON_PRIMARY || THEME_COLORS[mode].BACKGROUND,
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor =
                      THEME_COLORS[mode].PRIMARY_HOVER;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor =
                      THEME_COLORS[mode].PRIMARY;
                  }}
                >
                  Return to Login
                </button>
              </>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default OAuthCallbackPage;
