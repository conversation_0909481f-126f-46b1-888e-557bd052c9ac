import React from "react";
import { TrainerFeedHeaderProps } from "./types";
import ProgramSelector from "./ProgramSelector";

const TrainerFeedHeader: React.FC<TrainerFeedHeaderProps> = ({
  title = "Feed",
  subtitle,
  selected_program,
  on_program_select,
}) => {
  // Dynamic subtitle based on selected program
  const display_subtitle = selected_program
    ? `Welcome to "${selected_program.program_name}"`
    : subtitle || "Select a program to view its feed";

  return (
    <header className="flex flex-col sm:flex-row sm:items-center justify-between gap-6">
      <div>
        <h1 className="text-3xl font-bold text-text mb-2">{title}</h1>
        <p className="text-text-disabled">{display_subtitle}</p>
      </div>
      <div className="flex-shrink-0 w-full sm:w-80">
        <ProgramSelector
          selected_program={selected_program}
          on_program_select={on_program_select}
          placeholder="Search and select a program..."
        />
      </div>
    </header>
  );
};

export default TrainerFeedHeader;
