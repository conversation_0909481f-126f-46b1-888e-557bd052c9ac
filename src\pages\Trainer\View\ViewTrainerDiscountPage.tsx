import { useTheme } from "@/hooks/useTheme";
import {
  AffiliateLinkSection,
  PromoCodeDiscountSection,
  SaleDiscountSection,
  DiscountOnSubscriptionSection,
  DiscountOnFullPriceSection,
  DiscountActions,
} from "@/components/TrainerDiscount";
import { DiscountPageSkeleton } from "@/components/Skeleton";
import { useParams } from "react-router";
import { useProgramDiscount } from "@/hooks/useProgramDiscount";
import { useEffect, useState } from "react";

const TrainerDiscountPage = () => {
  const { state: _themeState } = useTheme();
  const params = useParams();
  const programId = Number(params.programId);

  // State for discount settings
  const [discountSettings, setDiscountSettings] = useState({
    affiliate_link: "",
    sale_discount: {
      type: "percentage" as "fixed" | "percentage",
      value: null as number | null,
      apply_to_all: false,
    },
    promo_code: {
      code: "",
      discount_type: "percentage" as "fixed" | "percentage",
      discount_value: 0,
      applies_to: "subscription" as "subscription" | "full_payment" | "both",
      expiry_date: null as string | null,
      usage_limit: null as number | null,
    },
    subscription_discounts: [] as any[],
    full_price_discounts: [] as any[],
  });

  // Fetch program and discount data
  const {
    program,
    programDiscount,
    discounts,
    coupons,
    isLoading,
    error: _error,
    refetch,
    saveProgramDiscountSettings,
    handleCreateDiscount,
    loading,
  } = useProgramDiscount({ programId });

  // Update local state when data is loaded
  useEffect(() => {
    if (programDiscount) {
      setDiscountSettings((prev) => ({
        ...prev,
        affiliate_link: programDiscount.affiliate_link || "",
        sale_discount: {
          type: programDiscount.sale_discount_type || "percentage",
          value: programDiscount.sale_discount_value || 0,
          apply_to_all: programDiscount.sale_apply_to_all || false,
        },
      }));
    }

    if (coupons.length > 0) {
      const coupon = coupons[0];
      setDiscountSettings((prev) => ({
        ...prev,
        promo_code: {
          code: coupon.code || "",
          discount_type: coupon.discount_type || "percentage",
          discount_value: coupon.discount_value || 0,
          applies_to: coupon.applies_to || "subscription",
          expiry_date: coupon.expiry_date || null,
          usage_limit: coupon.usage_limit || null,
        },
      }));
    }

    if (discounts.length > 0) {
      const subscriptionDiscounts = discounts.filter(
        (d) => d.applies_to === "subscription"
      );
      const fullPriceDiscounts = discounts.filter(
        (d) => d.applies_to === "full_payment"
      );

      setDiscountSettings((prev) => ({
        ...prev,
        subscription_discounts: subscriptionDiscounts.map((d) => ({
          tier_id: d.split_id,
          discount_type: d.discount_type,
          discount_value: d.discount_value,
        })),
        full_price_discounts: fullPriceDiscounts.map((d) => ({
          tier_id: d.split_id,
          discount_type: d.discount_type,
          discount_value: d.discount_value,
        })),
      }));
    }
  }, [programDiscount, coupons, discounts]);

  // console.log(discountSettings);

  // Handle save discount settings
  const handleSaveDiscountSettings = async () => {
    try {
      const transformedData = {
        affiliate_link: discountSettings.affiliate_link,
        sale_discount: {
          type: discountSettings.sale_discount.type,
          value: Number(discountSettings.sale_discount.value),
          apply_to_all: discountSettings.sale_discount.apply_to_all,
        },
        promo_code: discountSettings.promo_code,
        subscription_discounts: discountSettings.subscription_discounts,
        full_price_discounts: discountSettings.full_price_discounts,
      };
      await saveProgramDiscountSettings(transformedData);
      // Optionally show success message or redirect
    } catch (error) {
      console.error("Failed to save discount settings:", error);
    }
  };

  if (isLoading) {
    return <DiscountPageSkeleton />;
  }

  return (
    <div className="min-h-screen bg-background-secondary py-6 px-2 sm:px-4 md:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Page Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-text">
            Discount Page - {program?.program_name || "Program"}
          </h1>
          {program && (
            <p className="text-sm text-text-secondary mt-1">
              Program ID: {program.id} | Status: {program.status}
            </p>
          )}
        </div>

        {/* Main Content Container */}
        <div className="bg-background rounded-lg shadow-sm border border-border p-4 md:p-6">
          {/* Two Column Layout */}
          <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
            {/* Left Column */}
            <div className="flex-1 flex flex-col gap-6">
              <AffiliateLinkSection
                value={discountSettings.affiliate_link}
                onChange={(value) =>
                  setDiscountSettings((prev) => ({
                    ...prev,
                    affiliate_link: value,
                  }))
                }
              />
              <PromoCodeDiscountSection
                promoCode={discountSettings.promo_code}
                onChange={(promo_code) =>
                  setDiscountSettings((prev) => ({ ...prev, promo_code }))
                }
                createDiscount={() => {
                  handleCreateDiscount(discountSettings);
                }}
                loading={loading?.creating_discount}
              />
            </div>

            {/* Right Column */}
            <div className="flex-1 flex flex-col gap-6">
              <SaleDiscountSection
                saleDiscount={discountSettings.sale_discount}
                onChange={(sale_discount) =>
                  setDiscountSettings((prev) => ({ ...prev, sale_discount }))
                }
              />
              {program?.payment_plan?.includes("monthly") ? (
                <DiscountOnSubscriptionSection
                  discounts={discountSettings.subscription_discounts}
                  splits={program?.splits || []}
                  onChange={(subscription_discounts) =>
                    setDiscountSettings((prev) => ({
                      ...prev,
                      subscription_discounts,
                    }))
                  }
                />
              ) : null}

              {program?.payment_plan?.includes("one_time") ? (
                <DiscountOnFullPriceSection
                  discounts={discountSettings.full_price_discounts}
                  splits={program?.splits || []}
                  onChange={(full_price_discounts) =>
                    setDiscountSettings((prev) => ({
                      ...prev,
                      full_price_discounts,
                    }))
                  }
                />
              ) : null}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 flex justify-end">
            <DiscountActions
              onSave={handleSaveDiscountSettings}
              onRefresh={refetch}
              isLoading={loading.updating_discount}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrainerDiscountPage;
