import { RestAPIMethodEnum } from "../Enums";

export const AthleteEndpoints = {
  // Enrollment Endpoints
  ATHLETE_ENROLLMENTS: {
    GET_ALL: {
      url: "/v2/api/kanglink/custom/athlete/enrollments",
      method: RestAPIMethodEnum.GET,
      headers: {
        Authorization: "Bearer {token}",
        "Content-Type": "application/json",
      },
      response: {
        error: "boolean",
        data: {
          owned: "EnrollmentItem[]",
          subscribed: "EnrollmentItem[]",
          pending_refund: "EnrollmentItem[]",
          refunded: "EnrollmentItem[]",
        },
        meta: {
          total_enrollments: "number",
          refund_time_limit_hours: "number",
          categories: {
            owned: "number",
            subscribed: "number",
            pending_refund: "number",
            refunded: "number",
          },
        },
      },
    },
  },

  // Enrollment Endpoints
  ATHLETE_LIBRARY: {
    GET_ALL: {
      url: "/v2/api/kanglink/custom/athlete/library",
      method: RestAPIMethodEnum.GET,
      headers: {
        Authorization: "Bearer {token}",
        "Content-Type": "application/json",
      },
      response: {
        error: "boolean",
        data: {
          owned: "EnrollmentItem[]",
          subscribed: "EnrollmentItem[]",
          pending_refund: "EnrollmentItem[]",
          refunded: "EnrollmentItem[]",
        },
        meta: {
          total_enrollments: "number",
          refund_time_limit_hours: "number",
          categories: {
            owned: "number",
            subscribed: "number",
            pending_refund: "number",
            refunded: "number",
          },
        },
      },
    },
  },

  // Favorite Programs Endpoints
  ATHLETE_FAVORITE_PROGRAMS: {
    GET_ALL: {
      url: "/v2/api/kanglink/custom/athlete/favorite/programs",
      method: RestAPIMethodEnum.GET,
      headers: {
        Authorization: "Bearer {token}",
        "Content-Type": "application/json",
      },
      response: {
        error: "boolean",
        data: "FavoriteProgramItem[]",
      },
    },
    ADD: {
      url: "/v2/api/kanglink/custom/athlete/favorite/programs/{programId}",
      method: RestAPIMethodEnum.POST,
      params: {
        programId: "string|number",
      },
      headers: {
        Authorization: "Bearer {token}",
        "Content-Type": "application/json",
      },
      response: {
        error: "boolean",
        data: "number",
        message: "string",
      },
    },
    REMOVE: {
      url: "/v2/api/kanglink/custom/athlete/favorite/programs/{programId}",
      method: RestAPIMethodEnum.PUT,
      params: {
        programId: "string|number",
      },
      headers: {
        Authorization: "Bearer {token}",
        "Content-Type": "application/json",
      },
      response: {
        error: "boolean",
        message: "string",
      },
    },
  },

  // Favorite Trainers Endpoints
  ATHLETE_FAVORITE_TRAINERS: {
    GET_ALL: {
      url: "/v2/api/kanglink/custom/athlete/favorite/trainers",
      method: RestAPIMethodEnum.GET,
      headers: {
        Authorization: "Bearer {token}",
        "Content-Type": "application/json",
      },
      response: {
        error: "boolean",
        data: "FavoriteTrainerItem[]",
      },
    },
    ADD: {
      url: "/v2/api/kanglink/custom/athlete/favorite/trainers/{trainerId}",
      method: RestAPIMethodEnum.POST,
      params: {
        trainerId: "string|number",
      },
      headers: {
        Authorization: "Bearer {token}",
        "Content-Type": "application/json",
      },
      response: {
        error: "boolean",
        data: "number",
        message: "string",
      },
    },
    REMOVE: {
      url: "/v2/api/kanglink/custom/athlete/favorite/trainers/{trainerId}",
      method: RestAPIMethodEnum.PUT,
      params: {
        trainerId: "string|number",
      },
      headers: {
        Authorization: "Bearer {token}",
        "Content-Type": "application/json",
      },
      response: {
        error: "boolean",
        message: "string",
      },
    },
  },
};
