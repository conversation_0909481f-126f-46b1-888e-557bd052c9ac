const AdminLoginPage = () => {
  return (
    <div className="w-[1440px] h-[600px] relative bg-neutral-100 border-gray-200">
      <div className="w-96 h-96 left-[496px] top-[104px] absolute bg-white rounded-md shadow-[0px_4px_6px_0px_rgba(255,255,255,1.00)] border-gray-200">
        <div className="w-96 h-8 left-[32px] top-[24px] absolute bg-black/0 border-gray-200">
          <div className="w-44 h-8 left-[102.25px] top-[-4.76px] absolute text-center justify-start text-stone-900 text-2xl font-bold font-['Inter'] leading-loose">
            Login as Admin
          </div>
        </div>
        <div className="w-96 h-72 left-[32px] top-[88px] absolute bg-black/0 border-gray-200">
          <div className="w-96 h-16 left-0 top-0 absolute bg-black/0 border-gray-200">
            <div className="w-96 h-5 left-0 top-0 absolute bg-black/0 border-gray-200">
              <div className="w-10 h-5 left-0 top-[-0.10px] absolute justify-start text-stone-900 text-sm font-medium font-['Inter']">
                Email:
              </div>
            </div>
            <div className="w-96 h-9 left-0 top-[28px] absolute bg-white rounded outline outline-1 outline-offset-[-1px] outline-gray-300">
              <div className="w-32 h-9 left-[14px] top-0 absolute justify-center text-gray-400 text-sm font-normal font-['Inter'] leading-tight">
                Enter Email Address
              </div>
            </div>
          </div>
          <div className="w-96 h-16 left-0 top-[90px] absolute bg-black/0 border-gray-200">
            <div className="w-96 h-5 left-0 top-0 absolute bg-black/0 border-gray-200">
              <div className="w-16 h-5 left-0 top-[-0.10px] absolute justify-start text-stone-900 text-sm font-medium font-['Inter']">
                Password:
              </div>
            </div>
            <div className="w-96 h-9 left-0 top-[28px] absolute bg-white rounded outline outline-1 outline-offset-[-1px] outline-gray-300">
              <div className="w-24 h-9 left-[14px] top-0 absolute justify-center text-gray-400 text-sm font-normal font-['Inter'] leading-tight">
                Enter Password
              </div>
            </div>
          </div>
          <div className="w-96 h-5 left-0 top-[180px] absolute bg-black/0 border-gray-200">
            <div className="w-28 h-5 left-0 top-0 absolute bg-black/0 border-gray-200">
              <div className="w-3 h-3 left-0 top-[3.50px] absolute bg-white rounded-[1px] border-[0.50px] border-black" />
              <div className="w-24 h-5 left-[21px] top-0 absolute bg-black/0 border-gray-200">
                <div className="w-24 h-5 left-0 top-[-0.10px] absolute justify-start text-gray-500 text-sm font-normal font-['Inter']">
                  Remember me
                </div>
              </div>
            </div>
            <div className="w-32 h-5 left-[264.05px] top-[-1.20px] absolute justify-start text-green-400 text-sm font-medium font-['Inter'] leading-tight">
              Forgot Password?
            </div>
          </div>
          <div className="w-96 h-14 left-0 top-[224px] absolute bg-black/0 border-gray-200">
            <div className="w-96 h-10 left-0 top-[16px] absolute bg-green-400 rounded border-gray-200">
              <div className="w-40 h-5 left-[115.78px] top-[9.80px] absolute text-center justify-start text-white text-sm font-semibold font-['Inter']">
                Login as Administrator
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminLoginPage;
