import { useState } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import {
  ChevronDownIcon,
  ChevronRightIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import { ExerciseCard } from "@/components/ExerciseCard";
import { WorkoutSession } from "@/interfaces";

interface SessionCardProps {
  session: WorkoutSession;
  weekNumber: number;
  dayNumber: number;
  onExerciseComplete: (exerciseId: string) => void;
  completingExercises?: Set<string>;
}

const SessionCard = ({
  session,
  weekNumber: _weekNumber,
  dayNumber: _dayNumber,
  onExerciseComplete,
  completingExercises = new Set(),
}: SessionCardProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [isCollapsed, setIsCollapsed] = useState(false);

  const cardStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  const headerStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div
      className="bg-background border border-border rounded-md shadow-sm transition-colors duration-200"
      style={cardStyles}
    >
      {/* Session Header */}
      <button
        onClick={toggleCollapse}
        className="w-full p-4 text-left transition-colors duration-200 hover:bg-background-secondary rounded-t-md border-b border-border"
        style={headerStyles}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              {isCollapsed ? (
                <ChevronRightIcon className="w-4 h-4 text-text-secondary" />
              ) : (
                <ChevronDownIcon className="w-4 h-4 text-text-secondary" />
              )}
            </div>
            <div className="w-4 h-4 bg-primary rounded-sm flex-shrink-0"></div>
            <div>
              <h4 className="text-lg font-semibold text-text">
                {session.title}
              </h4>
            </div>
          </div>

          {/* Session Stats */}
          <div className="flex items-center gap-3 text-sm text-text-secondary">
            <div className="flex items-center gap-1">
              <ClockIcon className="w-4 h-4" />
              {/* <span>{session.duration}</span> */}
            </div>
            <span>{session.exercise_instances.length} Exercises</span>
          </div>
        </div>
      </button>

      {/* Session Content */}
      {!isCollapsed && (
        <div className="p-4 space-y-4">
          {session.exercise_instances.map((exercise) => {
            // if (exercise.isCompact) {
            //   return (
            //     <CompactExerciseCard
            //       key={exercise.id}
            //       exerciseId={exercise.id}
            //       exerciseName={exercise.name}
            //       sets={exercise.sets}
            //       reps={exercise.reps}
            //       rest={exercise.rest}
            //       thumbnailUrl={exercise.thumbnailUrl}
            //       isCompleted={exercise.isCompleted}
            //       onMarkComplete={() => onExerciseComplete(exercise.id)}
            //     />
            //   );
            // }

            return (
              <ExerciseCard
                key={exercise.id}
                exercise={exercise}
                onMarkComplete={() =>
                  onExerciseComplete(exercise.id!.toString())
                }
                isCompleting={completingExercises.has(exercise.id!.toString())}
              />
            );
          })}
        </div>
      )}
    </div>
  );
};

export default SessionCard;
