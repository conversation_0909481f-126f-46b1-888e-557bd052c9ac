import { useEffect, useState, useRef } from "react";
import {
  CreateProgramStepOne,
  StepOneFormData,
} from "@/components/CreateProgramStepOne";
import { CreateProgramStepTwo } from "@/components/CreateProgramStepTwo";
import { CreateProgramPreview } from "@/components/CreateProgramPreview";
import {
  MkdWizardContainer,
  MkdWizardContainerRef,
} from "@/components/MkdWizardContainer";
import { useContexts } from "@/hooks/useContexts";
import { useParams } from "react-router";
import { useCustomModelQuery } from "@/query/shared";
import { EditProgramSkeleton } from "@/components/Skeleton";

const EditTrainerProgramPage = () => {
  const { globalDispatch } = useContexts();
  const [stepOneData, setStepOneData] = useState<StepOneFormData | null>(null);
  const [stepTwoData, setStepTwoData] = useState<any>(null);
  const wizardRef = useRef<MkdWizardContainerRef>(null);
  const params = useParams();
  const programId = params.programId;

  const { mutateAsync: fetchProgramData, isPending } = useCustomModelQuery();

  const fetchProgram = async () => {
    try {
      const response = await fetchProgramData({
        endpoint: `/v2/api/kanglink/custom/trainer/programs/${programId}`,
        method: "GET",
      });
      const programData = response?.data;

      setStepOneData(programData?.stepOneData);
      setStepTwoData(() => ({ ...programData?.stepTwoData, programId }));
    } catch (error) {
      console.error("Error fetching program data:", error);
    }
  };

  const handleStepOneSubmit = (data: any) => {
    setStepOneData(data);
    // Automatically go to next step after successful submission
    wizardRef.current?.goToNext();
  };

  const handleStepTwoSubmit = (data: any) => {
    setStepTwoData(data);

    // Handle the actual saving/publishing logic here
    const finalData = {
      ...stepOneData,
      ...data,
    };

    if (data.status === "draft") {
      // TODO: Save as draft to API
      console.log("Saving as draft:", finalData);
      // Show success message and go to preview
      // wizardRef.current?.goToNext();
    } else {
      wizardRef.current?.goToNext();
    }
  };

  const handleFinalSubmit = () => {
    // This is now the "Done" action from preview - navigate back to previous page
    window.history.back();
  };

  const handleCancel = () => {
    // TODO: Navigate back or show confirmation
    window.history.back();
  };

  const handleBack = () => {
    wizardRef.current?.goToPrevious();
  };

  useEffect(() => {
    if (programId) {
      fetchProgram();
    }
  }, [fetchProgramData, programId]);

  // Set the path in global state for navigation highlighting
  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "program",
      },
    });
  }, [globalDispatch]);

  // Show skeleton loader while data is loading
  if (isPending || !stepOneData || !stepTwoData) {
    return <EditProgramSkeleton currentStep={1} />;
  }

  return (
    <MkdWizardContainer ref={wizardRef} showButton={false}>
      <CreateProgramStepOne
        componentId={1}
        onSubmit={handleStepOneSubmit}
        onCancel={handleCancel}
        initialData={stepOneData}
      />
      <CreateProgramStepTwo
        componentId={2}
        onSubmit={handleStepTwoSubmit}
        onCancel={handleCancel}
        onBack={handleBack}
        onPreview={handleFinalSubmit}
        stepOneData={stepOneData}
        initialData={stepTwoData}
      />
      <CreateProgramPreview
        componentId={3}
        onSubmit={handleFinalSubmit}
        onCancel={handleBack}
        stepOneData={stepOneData}
        stepTwoData={stepTwoData}
      />
    </MkdWizardContainer>
  );
};

export default EditTrainerProgramPage;
