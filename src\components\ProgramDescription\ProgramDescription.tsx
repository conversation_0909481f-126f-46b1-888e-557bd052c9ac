import React from "react";

interface ProgramDescriptionProps {
  description: string[];
}

const ProgramDescription: React.FC<ProgramDescriptionProps> = ({
  description,
}) => {
  return (
    <div className="w-full bg-background">
      <div className="p-4 lg:p-6">
        <h2 className="text-lg lg:text-xl font-bold text-text mb-4 lg:mb-6">
          Description
        </h2>
        <div className="space-y-4">
          {description.map((paragraph, index) => (
            <p
              key={index}
              className="text-sm lg:text-base text-text-secondary leading-relaxed"
            >
              {paragraph}
            </p>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProgramDescription;
