import MkdSD<PERSON> from "./MkdSDK";
import { RestAPIMethodEnum } from "./Enums";

export interface Notification {
  id: number;
  notification_type: string;
  category: string;
  title: string;
  message: string;
  data: any;
  is_read: boolean;
  read_at: string | null;
  created_at: string;
  sender_id: number | null;
  sender_name: string | null;
  sender_email: string | null;
  related_id: number | null;
  related_type: string | null;
}

export interface NotificationResponse {
  success: boolean;
  notifications: Notification[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  unread_count?: number;
}

export interface UnreadCountResponse {
  unread_count: number;
}

export class NotificationService {
  private sdk: MkdSDK;

  constructor(sdk: MkdSDK) {
    this.sdk = sdk;
  }

  // Get notifications for the current user
  async getNotifications(params: {
    page?: number;
    limit?: number;
    unread_only?: boolean;
    category?: string;
  } = {}): Promise<NotificationResponse> {
    const { page = 1, limit = 20, unread_only = false, category } = params;
    
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());
    queryParams.append('unread_only', unread_only.toString());
    if (category) {
      queryParams.append('category', category);
    }

    const endpoint = this.getNotificationEndpoint();
    const url = `${endpoint}?${queryParams.toString()}`;

    try {
      const response = await this.sdk.request({
        endpoint: url,
        method: RestAPIMethodEnum.GET,
        requiresAuth: true,
      });

      if (response.error) {
        throw new Error(response.message || 'Failed to fetch notifications');
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching notifications:', error);
      throw error;
    }
  }

  // Mark a notification as read
  async markAsRead(notificationId: number): Promise<boolean> {
    const endpoint = this.getNotificationEndpoint();
    
    try {
      const response = await this.sdk.request({
        endpoint: `${endpoint}/${notificationId}/read`,
        method: RestAPIMethodEnum.PUT,
        requiresAuth: true,
      });

      if (response.error) {
        throw new Error(response.message || 'Failed to mark notification as read');
      }

      return true;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  // Mark all notifications as read
  async markAllAsRead(): Promise<boolean> {
    const endpoint = this.getNotificationEndpoint();
    
    try {
      const response = await this.sdk.request({
        endpoint: `${endpoint}/read-all`,
        method: RestAPIMethodEnum.PUT,
        requiresAuth: true,
      });

      if (response.error) {
        throw new Error(response.message || 'Failed to mark all notifications as read');
      }

      return true;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  // Get unread notification count
  async getUnreadCount(): Promise<number> {
    const endpoint = this.getNotificationEndpoint();
    
    try {
      const response = await this.sdk.request({
        endpoint: `${endpoint}/unread-count`,
        method: RestAPIMethodEnum.GET,
        requiresAuth: true,
      });

      if (response.error) {
        throw new Error(response.message || 'Failed to get unread count');
      }

      return response.data.unread_count || 0;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }

  // Create system alert (super admin only)
  async createSystemAlert(alertData: {
    title: string;
    message: string;
    alert_type?: string;
    severity?: string;
    details?: any;
  }): Promise<boolean> {
    const endpoint = this.getNotificationEndpoint();
    
    try {
      const response = await this.sdk.request({
        endpoint: `${endpoint}/system-alert`,
        method: RestAPIMethodEnum.POST,
        requiresAuth: true,
        body: alertData,
      });

      if (response.error) {
        throw new Error(response.message || 'Failed to create system alert');
      }

      return true;
    } catch (error) {
      console.error('Error creating system alert:', error);
      throw error;
    }
  }

  // Get the appropriate notification endpoint based on user role
  private getNotificationEndpoint(): string {
    const role = this.sdk.getRole();
    
    switch (role) {
      case 'super_admin':
        return '/v2/api/kanglink/custom/super_admin/notifications';
      case 'trainer':
        return '/v2/api/kanglink/custom/trainer/dashboard/notifications';
      case 'member':
      default:
        return '/v2/api/kanglink/custom/athlete/notifications';
    }
  }

  // Get notification icon based on type
  static getNotificationIcon(type: string): string {
    const icons: Record<string, string> = {
      exercise_completed: '💪',
      day_completed: '✅',
      week_completed: '🎯',
      program_completed: '🏆',
      milestone_reached: '🎉',
      new_enrollment: '👥',
      payment_received: '💰',
      program_updated: '📝',
      athlete_message: '💬',
      system_alert: '⚠️',
      refund_requested: '💸',
      refund_approved: '✅',
      refund_rejected: '❌',
      refund_processed: '💰',
    };
    
    return icons[type] || '📢';
  }

  // Get notification type color
  static getNotificationTypeColor(type: string): string {
    const colors: Record<string, string> = {
      exercise_completed: 'text-green-600',
      day_completed: 'text-blue-600',
      week_completed: 'text-purple-600',
      program_completed: 'text-yellow-600',
      milestone_reached: 'text-pink-600',
      new_enrollment: 'text-indigo-600',
      payment_received: 'text-green-600',
      program_updated: 'text-orange-600',
      athlete_message: 'text-blue-600',
      system_alert: 'text-red-600',
      refund_requested: 'text-orange-600',
      refund_approved: 'text-green-600',
      refund_rejected: 'text-red-600',
      refund_processed: 'text-green-600',
    };
    
    return colors[type] || 'text-gray-600';
  }

  // Format time ago
  static formatTimeAgo(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;

    return date.toLocaleDateString();
  }

  // Get notification category label
  static getCategoryLabel(category: string): string {
    const labels: Record<string, string> = {
      progress: 'Progress Update',
      enrollment: 'Enrollment',
      payment: 'Payment',
      communication: 'Communication',
      system: 'System',
      general: 'General',
      refund: 'Refund',
    };
    
    return labels[category] || category;
  }
}

export default NotificationService; 