import React, { memo, useEffect } from "react";
import { Navigate } from "react-router";
import metadataJSON from "@/utils/metadata.json";
import { StringCaser } from "@/utils/utils";
import { useContexts } from "@/hooks/useContexts";

interface TrainerRouteProps {
  path: string;
  children: React.ReactNode;
}

const TrainerRoute: React.FC<TrainerRouteProps> = ({ path, children }) => {
  const { authState } = useContexts();
  const stringCaser = new StringCaser();

  const { isAuthenticated } = authState;

  useEffect(() => {
    const metadata = metadataJSON[path ?? "/"];
    if (metadata !== undefined) {
      document.title = metadata?.title
        ? stringCaser.Capitalize(metadata?.title, {
            separator: " "
          })
        : "Kanga SportLink";
    } else {
      document.title = "Kanga SportLink";
    }
  }, [path]);

  return (
    <>{isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />}</>
  );
};

export default memo(TrainerRoute);
