import { useState, useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { Container } from "@/components/Container";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCheckCircle, faExclamationTriangle, faSpinner } from "@fortawesome/free-solid-svg-icons";

const VerifyEmail = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [verificationStatus, setVerificationStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState('');
  const [isResending, setIsResending] = useState(false);

  const { sdk } = useSDK({ role: "member" });
  const { showToast } = useContexts();

  const token = searchParams.get('token');
  const role = searchParams.get('role');
  const verifyEmail = async (verificationToken: string) => {
    try {
      setVerificationStatus('loading');
      let result: any;
      
      try {
        result = await sdk.request({
          endpoint: `/v1/api/kanglink/${role}/lambda/verify`,
          method: 'POST',
          body: { token: verificationToken }
        });
      } catch (memberError) {
        result = await sdk.request({
          endpoint: `/v1/api/kanglink/${role}/lambda/verify`,
          method: 'POST',
          body: { token: verificationToken }
        });
      }

      if (!result.error) {
        setVerificationStatus('success');
        showToast("Email verified successfully!", 4000, ToastStatusEnum.SUCCESS);
        // setTimeout(() => {
        //   navigate('/login');
        // }, 10000);
      } else {
        setVerificationStatus('error');
        setErrorMessage(result.message || 'Verification failed');
      }
    } catch (error: any) {
      setVerificationStatus('error');
      setErrorMessage(error?.response?.data?.message || error?.message || 'Verification failed');
    }
  };

  const resendVerification = async () => {
    try {
      setIsResending(true);
      
      // Get email from URL params or localStorage
      const email = searchParams.get('email') || localStorage.getItem('pending_verification_email');
      const role = searchParams.get('role') || 'member';
      
      if (!email) {
        showToast("Email not found. Please try logging in again.", 4000, ToastStatusEnum.ERROR);
        return;
      }

      const result = await sdk.request({
        endpoint: `/v1/api/kanglink/${role}/lambda/resend_verification`,
        method: 'POST',
        body: { email, role }
      });

      if (!result.error) {
        showToast("Verification email sent successfully!", 4000, ToastStatusEnum.SUCCESS);
      } else {
        showToast(result.message || "Failed to resend verification email", 4000, ToastStatusEnum.ERROR);
      }
    } catch (error: any) {
      showToast(
        error?.response?.data?.message || error?.message || "Failed to resend verification email",
        4000,
        ToastStatusEnum.ERROR
      );
    } finally {
      setIsResending(false);
    }
  };

  useEffect(() => {
    if (token) {
      verifyEmail(token);
    } else {
      setVerificationStatus('error');
      setErrorMessage('No verification token provided');
    }
  }, [token]);

  const renderContent = () => {
    switch (verificationStatus) {
      case 'loading':
        return (
          <div className="text-center">
            <FontAwesomeIcon 
              icon={faSpinner} 
              className="text-4xl text-primary animate-spin mb-4" 
            />
            <h2 className="text-2xl font-bold text-text mb-2">
              Verifying Your Email
            </h2>
            <p className="text-text-secondary">
              Please wait while we verify your email address...
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center">
            <FontAwesomeIcon 
              icon={faCheckCircle} 
              className="text-4xl text-green-500 mb-4" 
            />
            <h2 className="text-2xl font-bold text-text mb-2">
              Email Verified Successfully!
            </h2>
            <p className="text-text-secondary mb-6">
              Your email has been verified and your profile has been set up. You can now log in to your account.
            </p>
            <InteractiveButton
              onClick={() => navigate('/login')}
              className="bg-primary w-full text-white px-6 py-2 rounded-md hover:bg-primary-hover"
            >
              Go to Login
            </InteractiveButton>
          </div>
        );

      case 'error':
        return (
          <div className="text-center">
            <FontAwesomeIcon 
              icon={faExclamationTriangle} 
              className="text-4xl text-red-500 mb-4" 
            />
            <h2 className="text-2xl font-bold text-text mb-2">
              Verification Failed
            </h2>
            <p className="text-text-secondary mb-4">
              {errorMessage}
            </p>
            <div className="space-y-3 " >
              <InteractiveButton
                onClick={() => navigate('/login')}
                className="bg-primary w-full text-white px-6 py-2 rounded-md hover:bg-primary-hover mr-3"
              >
                Go to Login
              </InteractiveButton>
              <InteractiveButton
                onClick={resendVerification}
                loading={isResending}
                disabled={isResending}
                className="bg-secondary hidden w-full text-text px-6 py-2 rounded-md hover:bg-secondary-hover border border-border"
              >
                Resend Verification
              </InteractiveButton>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Container className="bg-background min-h-screen">
      <main className="flex-1 flex items-center justify-center px-4 py-8">
        <div className="w-full max-w-md">
          {/* Card */}
          <div className="w-full bg-secondary border border-border shadow-lg rounded-lg px-8 py-10">
            {renderContent()}
          </div>
        </div>
      </main>
    </Container>
  );
};

export default VerifyEmail; 