import React from "react";
import Chart from "react-apexcharts";
import { useEarningsGraph } from "@/hooks/useTrainerTransactions";
import { useTheme } from "@/hooks/useTheme";

interface EarningsGraphProps {
  onRefresh?: () => void;
}

const EarningsGraph: React.FC<EarningsGraphProps> = ({ onRefresh }) => {
  const { graphData, loading, error, refetch } = useEarningsGraph();
  const { state } = useTheme();
  const mode = state?.theme;

  const handleRefresh = () => {
    refetch();
    onRefresh?.();
  };

  if (loading) {
    return (
      <div className="rounded-lg shadow-sm border border-border bg-secondary p-6 dark:bg-neutral-800 dark:border-[#3a3a3a]">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-32 mb-4"></div>
          <div className="w-full h-72 bg-gray-300 dark:bg-gray-600 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg shadow-sm border border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-red-600 dark:text-red-400">
            Earnings Graph
          </h3>
          <button
            onClick={handleRefresh}
            className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 font-medium"
          >
            Retry
          </button>
        </div>
        <div className="flex items-center">
          <div className="text-red-600 dark:text-red-400">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <span className="ml-2 text-sm text-red-600 dark:text-red-400">
            Failed to load earnings graph data
          </span>
        </div>
      </div>
    );
  }

  if (!graphData || !graphData.earnings_by_month.length) {
    return (
      <div className="rounded-lg shadow-sm border border-border bg-secondary p-6 dark:bg-neutral-800 dark:border-[#3a3a3a]">
        <h3 className="text-lg font-medium text-text dark:text-gray-100 mb-4">
          Earnings Graph
        </h3>
        <div className="flex items-center justify-center h-72 text-text-secondary">
          <div className="text-center">
            <svg
              className="w-12 h-12 mx-auto mb-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              />
            </svg>
            <p className="text-sm">No earnings data available</p>
          </div>
        </div>
      </div>
    );
  }

  // Prepare chart data
  const categories = graphData.earnings_by_month.map((item) => item.month_name);
  const earnings = graphData.earnings_by_month.map((item) => item.earnings);
  const maxEarnings = Math.max(...earnings);

  const chartOptions = {
    chart: {
      id: "earnings-chart",
      toolbar: {
        show: false,
      },
      background: "transparent",
    },
    theme: {
      mode: mode as "light" | "dark",
    },
    colors: ["#10B981"],
    xaxis: {
      categories,
      labels: {
        style: {
          colors: mode === "dark" ? "#9CA3AF" : "#6B7280",
          fontSize: "12px",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          colors: mode === "dark" ? "#9CA3AF" : "#6B7280",
          fontSize: "12px",
        },
        formatter: (value: number) => {
          return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: graphData.currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
          }).format(value);
        },
      },
      min: 0,
      max: maxEarnings > 0 ? Math.ceil(maxEarnings * 1.1) : 100,
    },
    grid: {
      borderColor: mode === "dark" ? "#374151" : "#E5E7EB",
      strokeDashArray: 1,
    },
    legend: {
      show: false,
    },
    plotOptions: {
      bar: {
        columnWidth: "60%",
        borderRadius: 2,
      },
    },
    dataLabels: {
      enabled: false,
    },
    tooltip: {
      theme: mode === "dark" ? "dark" : "light",
      y: {
        formatter: (value: number) => {
          return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: graphData.currency,
          }).format(value);
        },
      },
    },
  };

  const chartSeries = [
    {
      name: "Earnings",
      data: earnings,
    },
  ];

  return (
    <div className="rounded-lg shadow-sm border border-border bg-secondary p-6 dark:bg-neutral-800 dark:border-[#3a3a3a]">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-text dark:text-gray-100">
          Earnings Graph
        </h3>
        <button
          onClick={handleRefresh}
          className="text-sm text-text-secondary hover:text-text font-medium"
        >
          Refresh
        </button>
      </div>
      <div className="w-full h-72">
        <Chart
          options={chartOptions}
          series={chartSeries}
          type="bar"
          height="100%"
          width="100%"
        />
      </div>
    </div>
  );
};

export default EarningsGraph;
