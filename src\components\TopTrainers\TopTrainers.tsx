import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import TrainerCard from "@/components/TrainerCard/TrainerCard";

interface Trainer {
  id: string;
  name: string;
  description: string;
  image: string;
  rating: number;
  startingPrice: number;
  isFavorite?: boolean;
}

const sampleTrainers: Trainer[] = [
  {
    id: "1",
    name: "<PERSON>",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: false,
  },
  {
    id: "2",
    name: "<PERSON>",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: true,
  },
  {
    id: "3",
    name: "Mike Chen",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1567013127542-490d757e51cd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: false,
  },
  {
    id: "4",
    name: "Emma Davis",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1550345332-09e3ac987658?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: false,
  },
];

const TopTrainers = () => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <div className="w-full">
      {/* Section Title */}
      <h2
        className="text-2xl font-bold mb-8 transition-colors duration-200"
        style={{ color: THEME_COLORS[mode].TEXT }}
      >
        Top Trainers
      </h2>

      {/* Trainers Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {sampleTrainers.map((trainer) => (
          <TrainerCard key={trainer.id} trainer={trainer} />
        ))}
      </div>
    </div>
  );
};

export default TopTrainers;
