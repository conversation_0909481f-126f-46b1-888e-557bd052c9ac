import { NavLink, useNavigate } from "react-router-dom";
import { useContexts } from "@/hooks/useContexts";
import { useProfile } from "@/hooks/useProfile";
import { Menu } from "@headlessui/react";
import { LazyLoad } from "@/components/LazyLoad";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/HeaderLogo";
import { BiExpandVertical } from "react-icons/bi";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

interface INavItem {
  to: string;
  text: string;
  icon: any;
  value: string;
}

const NAV_ITEMS: INavItem[] = [
  {
    to: "/admin/dashboard",
    text: "Dashboard",
    icon: ({fill = "#757B8A", stroke = "#E5E7EB"}) => (
      <svg
        width={16}
        height={16}
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M16 16H0V0H16V16Z" stroke={stroke} />
        <path
          d="M2 2C2 1.44687 1.55313 1 1 1C0.446875 1 0 1.44687 0 2V12.5C0 13.8813 1.11875 15 2.5 15H15C15.5531 15 16 14.5531 16 14C16 13.4469 15.5531 13 15 13H2.5C2.225 13 2 12.775 2 12.5V2ZM14.7063 4.70625C15.0969 4.31563 15.0969 3.68125 14.7063 3.29063C14.3156 2.9 13.6812 2.9 13.2906 3.29063L10 6.58437L8.20625 4.79063C7.81563 4.4 7.18125 4.4 6.79063 4.79063L3.29063 8.29062C2.9 8.68125 2.9 9.31563 3.29063 9.70625C3.68125 10.0969 4.31563 10.0969 4.70625 9.70625L7.5 6.91563L9.29375 8.70938C9.68437 9.1 10.3188 9.1 10.7094 8.70938L14.7094 4.70937L14.7063 4.70625Z"
          fill={fill}
        />
      </svg>
    ),
    value: "dashboard",
  },
  {
    to: "/admin/programs",
    text: "Program Management",
    icon: ({fill = "#757B8A", stroke = "#E5E7EB"}) => (
      <svg
        width={14}
        height={16}
        style={{
          alignItems: stroke,
        }}
        viewBox="0 0 14 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_95_4089)">
          <path
            d="M3 0C1.34375 0 0 1.34375 0 3V13C0 14.6562 1.34375 16 3 16H12H13C13.5531 16 14 15.5531 14 15C14 14.4469 13.5531 14 13 14V12C13.5531 12 14 11.5531 14 11V1C14 0.446875 13.5531 0 13 0H12H3ZM3 12H11V14H3C2.44688 14 2 13.5531 2 13C2 12.4469 2.44688 12 3 12ZM4 4.5C4 4.225 4.225 4 4.5 4H10.5C10.775 4 11 4.225 11 4.5C11 4.775 10.775 5 10.5 5H4.5C4.225 5 4 4.775 4 4.5ZM4.5 6H10.5C10.775 6 11 6.225 11 6.5C11 6.775 10.775 7 10.5 7H4.5C4.225 7 4 6.775 4 6.5C4 6.225 4.225 6 4.5 6Z"
            fill={fill}
          />
        </g>
        <defs>
          <clipPath id="clip0_95_4089">
            <path d="M0 0H14V16H0V0Z" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    value: "programs",
  },
  {
    to: "/admin/athletes",
    text: "Athlete Management",
    icon: ({fill = "#757B8A", stroke = "#E5E7EB"}) => (
      <svg
        width={20}
        height={16}
        style={{
          alignItems: stroke,
        }}
        viewBox="0 0 20 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_95_4094)">
          <path
            d="M3 4C3 2.93913 3.42143 1.92172 4.17157 1.17157C4.92172 0.421427 5.93913 0 7 0C8.06087 0 9.07828 0.421427 9.82843 1.17157C10.5786 1.92172 11 2.93913 11 4C11 5.06087 10.5786 6.07828 9.82843 6.82843C9.07828 7.57857 8.06087 8 7 8C5.93913 8 4.92172 7.57857 4.17157 6.82843C3.42143 6.07828 3 5.06087 3 4ZM0 15.0719C0 11.9937 2.49375 9.5 5.57188 9.5H8.42813C11.5063 9.5 14 11.9937 14 15.0719C14 15.5844 13.5844 16 13.0719 16H0.928125C0.415625 16 0 15.5844 0 15.0719ZM19.0406 16H14.7312C14.9 15.7063 15 15.3656 15 15V14.75C15 12.8531 14.1531 11.15 12.8188 10.0063C12.8938 10.0031 12.9656 10 13.0406 10H14.9594C17.7437 10 20 12.2562 20 15.0406C20 15.5719 19.5688 16 19.0406 16ZM13.5 8C12.5312 8 11.6562 7.60625 11.0219 6.97188C11.6375 6.14062 12 5.1125 12 4C12 3.1625 11.7938 2.37188 11.4281 1.67813C12.0094 1.25312 12.725 1 13.5 1C15.4344 1 17 2.56562 17 4.5C17 6.43437 15.4344 8 13.5 8Z"
              fill={fill}
          />
        </g>
        <defs>
          <clipPath id="clip0_95_4094">
            <path d="M0 0H20V16H0V0Z" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    value: "athletes",
  },
  {
    to: "/admin/trainers",
    text: "Trainer Management",
    icon: ({fill = "#757B8A", stroke = "#E5E7EB"}) => (
      <svg
        width={14}
        height={16}
        style={{
          alignItems: stroke,
        }}
        viewBox="0 0 14 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_95_4099)">
          <path
            d="M7 8C5.93913 8 4.92172 7.57857 4.17157 6.82843C3.42143 6.07828 3 5.06087 3 4C3 2.93913 3.42143 1.92172 4.17157 1.17157C4.92172 0.421427 5.93913 0 7 0C8.06087 0 9.07828 0.421427 9.82843 1.17157C10.5786 1.92172 11 2.93913 11 4C11 5.06087 10.5786 6.07828 9.82843 6.82843C9.07828 7.57857 8.06087 8 7 8ZM6.53438 11.225L5.95312 10.2563C5.75313 9.92188 5.99375 9.5 6.38125 9.5H7H7.61562C8.00313 9.5 8.24375 9.925 8.04375 10.2563L7.4625 11.225L8.50625 15.0969L9.63125 10.5063C9.69375 10.2531 9.9375 10.0875 10.1906 10.1531C12.3813 10.7031 14 12.6844 14 15.0406C14 15.5719 13.5687 16 13.0406 16H8.92188C8.85625 16 8.79688 15.9875 8.74063 15.9656L8.75 16H5.25L5.25938 15.9656C5.20312 15.9875 5.14062 16 5.07812 16H0.959375C0.43125 16 0 15.5687 0 15.0406C0 12.6812 1.62188 10.7 3.80938 10.1531C4.0625 10.0906 4.30625 10.2563 4.36875 10.5063L5.49375 15.0969L6.5375 11.225H6.53438Z"
            fill={fill}
          />
        </g>
        <defs>
          <clipPath id="clip0_95_4099">
            <path d="M0 0H14V16H0V0Z" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    value: "trainers",
  },
  {
    to: "/admin/transactions",
    text: "Transactions",
    icon: ({fill = "#757B8A", stroke = "#E5E7EB"}) => (
      <svg
        width={18}
        height={16}
        viewBox="0 0 18 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M18 16H0V0H18V16Z" stroke={stroke} />
        <path
          d="M2 1C0.896875 1 0 1.89688 0 3V4H18V3C18 1.89688 17.1031 1 16 1H2ZM18 7H0V13C0 14.1031 0.896875 15 2 15H16C17.1031 15 18 14.1031 18 13V7ZM3.5 11H5.5C5.775 11 6 11.225 6 11.5C6 11.775 5.775 12 5.5 12H3.5C3.225 12 3 11.775 3 11.5C3 11.225 3.225 11 3.5 11ZM7 11.5C7 11.225 7.225 11 7.5 11H11.5C11.775 11 12 11.225 12 11.5C12 11.775 11.775 12 11.5 12H7.5C7.225 12 7 11.775 7 11.5Z"
          fill={fill}
        />
      </svg>
    ),
    value: "transactions",
  },
  {
    to: "/admin/refunds",
    text: "Refund",
    icon: ({fill = "#757B8A", stroke = "#E5E7EB"}) => (
      <svg
        width={20}
        height={16}
        style={{
          alignItems: stroke,
        }}
        viewBox="0 0 20 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_95_4109)">
          <path
            d="M16.7188 1.28145C16.425 0.987697 16.425 0.512697 16.7188 0.222072C17.0125 -0.0685531 17.4875 -0.0716781 17.7781 0.222072L19.7781 2.22207C19.9188 2.3627 19.9969 2.55332 19.9969 2.75332C19.9969 2.95332 19.9188 3.14395 19.7781 3.28457L17.7781 5.28457C17.4844 5.57832 17.0094 5.57832 16.7188 5.28457C16.4281 4.99082 16.425 4.51582 16.7188 4.2252L17.4375 3.50645L12 3.5002C11.5844 3.5002 11.25 3.16582 11.25 2.7502C11.25 2.33457 11.5844 2.0002 12 2.0002H17.4406L16.7188 1.28145ZM3.28125 11.7814L2.5625 12.5002H8C8.41562 12.5002 8.75 12.8346 8.75 13.2502C8.75 13.6658 8.41562 14.0002 8 14.0002H2.55938L3.27813 14.7189C3.57188 15.0127 3.57188 15.4877 3.27813 15.7783C2.98438 16.0689 2.50938 16.0721 2.21875 15.7783L0.21875 13.7814C0.078125 13.6408 0 13.4502 0 13.2502C0 13.0502 0.078125 12.8596 0.21875 12.7189L2.21875 10.7189C2.5125 10.4252 2.9875 10.4252 3.27813 10.7189C3.56875 11.0127 3.57188 11.4877 3.27813 11.7783L3.28125 11.7814ZM3 2.0002H10.5594C10.4437 2.2252 10.375 2.47832 10.375 2.7502C10.375 3.64707 11.1031 4.3752 12 4.3752H15.6687C15.5437 4.90645 15.6875 5.48457 16.1 5.9002C16.7344 6.53457 17.7625 6.53457 18.3969 5.9002L19 5.29707V12.0002C19 13.1033 18.1031 14.0002 17 14.0002H9.44063C9.55625 13.7752 9.625 13.5221 9.625 13.2502C9.625 12.3533 8.89688 11.6252 8 11.6252H4.33125C4.45625 11.0939 4.3125 10.5158 3.9 10.1002C3.26562 9.46582 2.2375 9.46582 1.60312 10.1002L1 10.7033V4.0002C1 2.89707 1.89688 2.0002 3 2.0002ZM5 4.0002H3V6.0002C4.10313 6.0002 5 5.10332 5 4.0002ZM17 10.0002C15.8969 10.0002 15 10.8971 15 12.0002H17V10.0002ZM10 11.0002C10.7956 11.0002 11.5587 10.6841 12.1213 10.1215C12.6839 9.55891 13 8.79585 13 8.0002C13 7.20455 12.6839 6.44149 12.1213 5.87888C11.5587 5.31627 10.7956 5.0002 10 5.0002C9.20435 5.0002 8.44129 5.31627 7.87868 5.87888C7.31607 6.44149 7 7.20455 7 8.0002C7 8.79585 7.31607 9.55891 7.87868 10.1215C8.44129 10.6841 9.20435 11.0002 10 11.0002Z"
            fill={fill}
          />
        </g>
        <defs>
          <clipPath id="clip0_95_4109">
            <path d="M0 0H20V16H0V0Z" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    value: "refunds",
  },
  {
    to: "/admin/content-libraries",
    text: "Content Libraries",
    icon: ({fill = "#757B8A", stroke = "#E5E7EB"}) => (
      <svg
        width={18}
        height={16}
        viewBox="0 0 18 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M18 16H0V0H18V16Z" stroke={stroke} />
        <path
          d="M7.8 14.7344C8.1375 14.8531 8.5 14.6062 8.5 14.25V2.45625C8.5 2.325 8.45 2.19375 8.34375 2.1125C7.73125 1.625 6.325 1 4.5 1C2.92188 1 1.44688 1.41562 0.565625 1.75312C0.2125 1.89062 0 2.24063 0 2.61875V14.1906C0 14.5625 0.4 14.8219 0.753125 14.7063C1.7375 14.3781 3.29688 14 4.5 14C5.55937 14 6.96875 14.4375 7.8 14.7344ZM10.2 14.7344C11.0312 14.4375 12.4406 14 13.5 14C14.7031 14 16.2625 14.3781 17.2469 14.7063C17.6 14.825 18 14.5625 18 14.1906V2.61875C18 2.24063 17.7875 1.89062 17.4344 1.75625C16.5531 1.41563 15.0781 1 13.5 1C11.675 1 10.2688 1.625 9.65625 2.1125C9.55313 2.19375 9.5 2.325 9.5 2.45625V14.25C9.5 14.6062 9.86562 14.8531 10.2 14.7344Z"
          fill={fill}
        />
      </svg>
    ),
    value: "content libraries",
  },
  {
    to: "/admin/profile/view",
    text: "Settings",
    icon: ({fill = "#757B8A", stroke = "#E5E7EB"}) => (
      <svg
        width={16}
        height={16}
        viewBox="0 0 16 16"
        style={{
          alignItems: stroke,
        }}
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_95_4119)">
          <path
            d="M15.4969 5.20625C15.5969 5.47813 15.5126 5.78125 15.2969 5.975L13.9438 7.20625C13.9782 7.46563 13.9969 7.73125 13.9969 8C13.9969 8.26875 13.9782 8.53438 13.9438 8.79375L15.2969 10.025C15.5126 10.2188 15.5969 10.5219 15.4969 10.7937C15.3594 11.1656 15.1938 11.5219 15.0032 11.8656L14.8563 12.1187C14.6501 12.4625 14.4188 12.7875 14.1657 13.0938C13.9813 13.3188 13.6751 13.3937 13.4001 13.3062L11.6594 12.7531C11.2407 13.075 10.7782 13.3438 10.2844 13.5469L9.89381 15.3313C9.83131 15.6156 9.61256 15.8406 9.32506 15.8875C8.89381 15.9594 8.45006 15.9969 7.99693 15.9969C7.54381 15.9969 7.10006 15.9594 6.66881 15.8875C6.38131 15.8406 6.16256 15.6156 6.10006 15.3313L5.70943 13.5469C5.21568 13.3438 4.75318 13.075 4.33443 12.7531L2.59693 13.3094C2.32193 13.3969 2.01568 13.3188 1.83131 13.0969C1.57818 12.7906 1.34693 12.4656 1.14068 12.1219L0.993807 11.8687C0.803182 11.525 0.637557 11.1687 0.500057 10.7969C0.400057 10.525 0.484432 10.2219 0.700057 10.0281L2.05318 8.79688C2.01881 8.53438 2.00006 8.26875 2.00006 8C2.00006 7.73125 2.01881 7.46563 2.05318 7.20625L0.700057 5.975C0.484432 5.78125 0.400057 5.47813 0.500057 5.20625C0.637557 4.83438 0.803182 4.47813 0.993807 4.13438L1.14068 3.88125C1.34693 3.5375 1.57818 3.2125 1.83131 2.90625C2.01568 2.68125 2.32193 2.60625 2.59693 2.69375L4.33756 3.24688C4.75631 2.925 5.21881 2.65625 5.71256 2.45312L6.10318 0.66875C6.16568 0.384375 6.38443 0.159375 6.67193 0.1125C7.10318 0.0375 7.54693 0 8.00006 0C8.45318 0 8.89693 0.0375 9.32818 0.109375C9.61568 0.15625 9.83443 0.38125 9.89693 0.665625L10.2876 2.45C10.7813 2.65313 11.2438 2.92188 11.6626 3.24375L13.4032 2.69062C13.6782 2.60312 13.9844 2.68125 14.1688 2.90313C14.4219 3.20938 14.6532 3.53437 14.8594 3.87812L15.0063 4.13125C15.1969 4.475 15.3626 4.83125 15.5001 5.20312L15.4969 5.20625ZM8.00006 10.5C8.6631 10.5 9.29898 10.2366 9.76782 9.76777C10.2367 9.29893 10.5001 8.66304 10.5001 8C10.5001 7.33696 10.2367 6.70107 9.76782 6.23223C9.29898 5.76339 8.6631 5.5 8.00006 5.5C7.33702 5.5 6.70113 5.76339 6.23229 6.23223C5.76345 6.70107 5.50006 7.33696 5.50006 8C5.50006 8.66304 5.76345 9.29893 6.23229 9.76777C6.70113 10.2366 7.33702 10.5 8.00006 10.5Z"
            fill={fill}
          />
        </g>
        <defs>
          <clipPath id="clip0_95_4119">
            <path d="M0 0H16V16H0V0Z" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    value: "settings",
  },
];

const AdminHeader = () => {
  const {
    globalState: { isOpen, path },
    authDispatch,
  } = useContexts();

  const navigate = useNavigate();
  const { profile } = useProfile();
  const { state } = useTheme();
  const mode = state?.theme;

  const sidebarStyles = {
    backgroundColor: isOpen
      ? THEME_COLORS[mode].BACKGROUND_ACTIVE
      : THEME_COLORS[mode].BACKGROUND_DARK,
    borderColor: THEME_COLORS[mode].BORDER,
    color: isOpen
      ? THEME_COLORS[mode].TEXT_SECONDARY
      : THEME_COLORS[mode].TEXT_ON_DARK,
  };

  const activeNavStyles = {
    backgroundColor: mode === "light" ? "#E6F4EC" : "#2D2D2D", 
    // backgroundColor: mode === "light" ? "#E6F4EC" : "#1F3A2E", 
    // Light: #E6F4EC, Dark: darker green equivalent
    // color: THEME_COLORS[mode].TEXT,
    color: mode === "light" ? "#4CBF6D" : "#F1F5F9",
    border: "0px solid #E5E7EB",
  };

  const inactiveNavStyles = {
    backgroundColor: "transparent",
    color: mode === "light" ? "#757B8A" : "#F1F5F9",
    // color: THEME_COLORS[mode].TEXT,
  };

  return (
    <div
      className={`min-h-full max-h-full bg-background h-full z-50 grid grid-rows-[auto_1fr_auto] border border-border pb-4 transition-all duration-200 ${
        isOpen
          ? "fixed w-[15rem] min-w-[15rem] max-w-[15rem] md:relative"
          : "relative w-[4.2rem] min-w-[4.2rem] max-w-[4.2rem]"
      }`}
      // style={sidebarStyles}
    >
      <HeaderLogo />

      <div className="h-full min-full max-full overflow-auto w-auto py-2">
        <div className="w-auto">
          <ul className="flex  flex-wrap px-2 text-sm">
            {NAV_ITEMS.map((item: INavItem) => (
              <li className="block w-full list-none" key={item.value}>
                <NavLink
                  aria-label={item?.to}
                  to={item.to}
                  id={item.to}
                  className={`flex items-start transition-all duration-200 hover:opacity-80 ${
                    path === item.value && isOpen
                      ? "w-[13.9375rem] h-[3rem] rounded border-0 border-[#E5E7EB]"
                      : "p-[0.75rem] rounded-[0.375rem]"
                  }`}
                  style={{
                    ...(path === item.value
                      ? activeNavStyles
                      : inactiveNavStyles),
                    ...(path === item.value && isOpen
                      ? {
                          padding: "0.75rem 7.07031rem 0.75rem 0.75rem",
                          alignItems: "flex-start",
                          gap: "0.75rem",
                          flexShrink: 0,
                          borderRadius: "0.25rem",
                          textWrap: "nowrap",
                          color: THEME_COLORS[mode].PRIMARY,
                        }
                      : {}),
                  }}
                >
                  <div
                    className={`flex items-center gap-3 ${
                      !isOpen ? "justify-center" : ""
                    }`}
                  >
                    {item.icon({
                      fill: path === item.value ? "#4CBF6D" : "#757B8A",
                      stroke: "none",
                    })}

                    {isOpen && (
                      <span
                        style={
                          path === item.value ? { color: activeNavStyles.color } : { color: inactiveNavStyles.color }
                        }
                      >
                        {item.text}
                      </span>
                    )}
                  </div>
                </NavLink>
              </li>
            ))}
          </ul>
        </div>
      </div>

      <div className="flex w-full max-w-full min-w-full flex-col items-center space-y-3">
        <Menu
          as={"div"}
          className={"relative z-[9999999] w-full space-y-3 px-2 text-text"}
        >
          <Menu.Button
            arial-label="menu-toggle-btn"
            type="button"
            name="menu-toggle-btn"
            id="menu-toggle-btn"
            className="flex w-full items-center justify-center gap-[.3125rem]"
          >
            {profile?.photo ? (
              <img
                className={`${
                  isOpen ? "h-10 w-10" : "h-5 w-5 xl:h-6 xl:w-6"
                } rounded-[50%] object-cover`}
                src={profile?.photo ?? "/default.png"}
                alt=""
              />
            ) : (
              <div
                className="min-h-[2.5rem] min-w-[2.5rem] rounded-lg shadow-md transition-colors duration-200"
                style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND_HOVER }}
              ></div>
            )}
            {isOpen ? (
              <>
                <div
                  className="text-left transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  <p className="w-32 text-sm font-medium truncate">
                    {profile?.first_name} {profile?.last_name}
                  </p>
                  <p className="w-[9.375rem] truncate text-xs">
                    {profile?.email}
                  </p>
                </div>
                <BiExpandVertical
                  className="min-h-5 min-w-5 transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                />
              </>
            ) : null}
          </Menu.Button>
          <Menu.Items
            className={`absolute ${
              isOpen
                ? "-bottom-1 -right-full md:-right-[170px]"
                : "-bottom-1 -right-[170px]"
            } mb-8 w-[160px] origin-top-right divide-y rounded-md border font-bold shadow-lg ring-1 ring-opacity-5 focus:outline-none transition-colors duration-200`}
            style={{
              backgroundColor: THEME_COLORS[mode].BACKGROUND,
              borderColor: THEME_COLORS[mode].BORDER,
              color: THEME_COLORS[mode].TEXT,
            }}
          >
            <div className="px-1 py-1">
              <Menu.Item>
                {({ active }) => (
                  <button
                    type="button"
                    arial-label="profile-btn"
                    name="profile-btn"
                    id="profile-btn"
                    className="group flex w-full items-center px-3 py-3 text-sm border-b transition-all duration-200 hover:opacity-80"
                    style={{
                      borderBottomColor: active
                        ? `${THEME_COLORS[mode].BORDER}80`
                        : "transparent",
                      color: THEME_COLORS[mode].TEXT,
                    }}
                    onClick={() => {
                      navigate("/admin/profile/view");
                    }}
                  >
                    <LazyLoad>
                      {/* <AccountIcon className="mr-2 w-5 h-5" /> */}
                      Account
                    </LazyLoad>
                  </button>
                )}
              </Menu.Item>

              <Menu.Item>
                {({ active }) => (
                  <button
                    type="button"
                    arial-label="logout-btn"
                    name="logout-btn"
                    id="logout-btn"
                    className="group flex w-full items-center px-3 py-3 text-sm border-b transition-all duration-200 hover:opacity-80"
                    style={{
                      borderBottomColor: active
                        ? `${THEME_COLORS[mode].BORDER}80`
                        : "transparent",
                      color: "#CE0000", // Keep logout button red
                    }}
                    onClick={() => {
                      authDispatch({ type: "LOGOUT" });
                      navigate(`/admin/login`);
                    }}
                  >
                    <LazyLoad>
                      {/* <LogoutIcon className="mr-2 w-5 h-5" /> */}
                      {/* <ArrowUpTrayIcon className="mr-2 h-5 w-5 rotate-90 text-[#CE0000]" /> */}
                      Logout
                    </LazyLoad>
                  </button>
                )}
              </Menu.Item>
            </div>
          </Menu.Items>
        </Menu>
      </div>
    </div>
  );
};

export default AdminHeader;
