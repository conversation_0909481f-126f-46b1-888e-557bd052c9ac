import React, { useState, useEffect } from "react";
import { Modal } from "@/components/Modal/Modal";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";

interface PayoutSettings {
  id?: number;
  trainer_payout_time_hours: number;
  split_company_percentage: number;
  split_trainer_percentage: number;
  affiliate_company_percentage: number;
  affiliate_trainer_percentage: number;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

interface PayoutSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const PayoutSettingsModal: React.FC<PayoutSettingsModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const { showToast } = useContexts();
  const { mutateAsync: customModelQuery } = useCustomModelQuery();

  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState<PayoutSettings>({
    trainer_payout_time_hours: 24,
    split_company_percentage: 30,
    split_trainer_percentage: 70,
    affiliate_company_percentage: 20,
    affiliate_trainer_percentage: 80,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch current payout settings when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchPayoutSettings();
    }
  }, [isOpen]);

  const fetchPayoutSettings = async () => {
    setLoading(true);
    try {
      const response = await customModelQuery({
        endpoint: "/v2/api/kanglink/custom/super_admin/payout-settings",
        method: "GET",
      });

      if (response.data) {
        setFormData(response.data);
      }
    } catch (error: any) {
      // If no settings found (404), keep default values
      if (error?.response?.status !== 404) {
        showToast(
          error?.response?.data?.message || "Failed to fetch payout settings",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate required fields
    if (
      !formData.trainer_payout_time_hours ||
      Number(formData.trainer_payout_time_hours) <= 0
    ) {
      newErrors.trainer_payout_time_hours =
        "Payout time must be greater than 0";
    }

    // Validate split percentages add up to 100
    if (
      Number(formData.split_company_percentage) +
      Number(formData.split_trainer_percentage) !==
      100
    ) {
      newErrors.split_percentages =
        "Company and Trainer percentages must add up to 100%";
    }

    // Validate affiliate percentages add up to 100
    if (
      Number(formData.affiliate_company_percentage) +
      Number(formData.affiliate_trainer_percentage) !==
      100
    ) {
      newErrors.affiliate_percentages =
        "Affiliate Company and Trainer percentages must add up to 100%";
    }

    // Validate percentage ranges
    if (
      Number(formData.split_company_percentage) < 0 ||
      Number(formData.split_company_percentage) > 100
    ) {
      newErrors.split_company_percentage =
        "Percentage must be between 0 and 100";
    }
    if (
      Number(formData.split_trainer_percentage) < 0 ||
      Number(formData.split_trainer_percentage) > 100
    ) {
      newErrors.split_trainer_percentage =
        "Percentage must be between 0 and 100";
    }
    if (
      Number(formData.affiliate_company_percentage) < 0 ||
      Number(formData.affiliate_company_percentage) > 100
    ) {
      newErrors.affiliate_company_percentage =
        "Percentage must be between 0 and 100";
    }
    if (
      Number(formData.affiliate_trainer_percentage) < 0 ||
      Number(formData.affiliate_trainer_percentage) > 100
    ) {
      newErrors.affiliate_trainer_percentage =
        "Percentage must be between 0 and 100";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setSubmitting(true);
    try {
      const response = await customModelQuery({
        endpoint: "/v2/api/kanglink/custom/super_admin/payout-settings",
        method: "POST",
        body: {
          trainer_payout_time_hours: formData.trainer_payout_time_hours,
          split_company_percentage: formData.split_company_percentage,
          split_trainer_percentage: formData.split_trainer_percentage,
          affiliate_company_percentage: formData.affiliate_company_percentage,
          affiliate_trainer_percentage: formData.affiliate_trainer_percentage,
        },
      });

      showToast(
        "Payout settings updated successfully",
        3000,
        ToastStatusEnum.SUCCESS
      );
      onSuccess?.();
      onClose();
    } catch (error: any) {
      showToast(
        error?.response?.data?.message || "Failed to update payout settings",
        5000,
        ToastStatusEnum.ERROR
      );
    } finally {
      setSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof PayoutSettings, value: number) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear related errors
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }

    // Auto-calculate complementary percentages
    if (field === "split_company_percentage") {
      setFormData((prev) => ({
        ...prev,
        split_trainer_percentage: 100 - value,
      }));
      setErrors((prev) => ({ ...prev, split_percentages: "" }));
    } else if (field === "split_trainer_percentage") {
      setFormData((prev) => ({
        ...prev,
        split_company_percentage: 100 - value,
      }));
      setErrors((prev) => ({ ...prev, split_percentages: "" }));
    } else if (field === "affiliate_company_percentage") {
      setFormData((prev) => ({
        ...prev,
        affiliate_trainer_percentage: 100 - value,
      }));
      setErrors((prev) => ({ ...prev, affiliate_percentages: "" }));
    } else if (field === "affiliate_trainer_percentage") {
      setFormData((prev) => ({
        ...prev,
        affiliate_company_percentage: 100 - value,
      }));
      setErrors((prev) => ({ ...prev, affiliate_percentages: "" }));
    }
  };

  const handleCancel = () => {
    setErrors({});
    onClose();
  };

  if (loading) {
    return (
      <Modal
        isOpen={isOpen}
        title="Payout Settings"
        modalHeader={true}
        modalCloseClick={handleCancel}
        classes={{
          modalDialog: "max-w-2xl",
          modal: "",
          modalContent: "",
        }}
      >
        <div className="flex justify-center items-center py-8">
          <div className="text-text">Loading payout settings...</div>
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      isOpen={isOpen}
      title="Payout Settings"
      modalHeader={true}
      modalCloseClick={handleCancel}
      classes={{
        modalDialog: "w-full max-w-[28rem]",
        modal: "",
        modalContent: "",
      }}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Trainer Payout Time */}
        <div>
          <label className="block text-sm font-medium text-text mb-2">
            Trainer Payout Time
          </label>
          <div className="flex items-center space-x-2">
            <input
              type="number"
              min="1"
              value={formData.trainer_payout_time_hours}
              onChange={(e) =>
                handleInputChange(
                  "trainer_payout_time_hours",
                  parseInt(e.target.value) || 0
                )
              }
              className="flex-1 px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text"
              placeholder="24"
            />
            <span className="text-text-secondary">Hours</span>
          </div>
          {errors.trainer_payout_time_hours && (
            <p className="mt-1 text-sm text-red-600">
              {errors.trainer_payout_time_hours}
            </p>
          )}
        </div>

        {/* Split Percentages */}
        <div>
          <h3 className="text-lg font-medium text-text mb-4">Split</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Company
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={formData.split_company_percentage}
                  onChange={(e) =>
                    handleInputChange(
                      "split_company_percentage",
                      parseFloat(e.target.value) || 0
                    )
                  }
                  className="flex-1 px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text"
                />
                <span className="text-text-secondary">%</span>
              </div>
              {errors.split_company_percentage && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.split_company_percentage}
                </p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Trainer
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={formData.split_trainer_percentage}
                  onChange={(e) =>
                    handleInputChange(
                      "split_trainer_percentage",
                      parseFloat(e.target.value) || 0
                    )
                  }
                  className="flex-1 px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text"
                />
                <span className="text-text-secondary">%</span>
              </div>
              {errors.split_trainer_percentage && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.split_trainer_percentage}
                </p>
              )}
            </div>
          </div>
          {errors.split_percentages && (
            <p className="mt-2 text-sm text-red-600">
              {errors.split_percentages}
            </p>
          )}
        </div>

        {/* Affiliate Split Percentages */}
        <div>
          <h3 className="text-lg font-medium text-text mb-4">
            Affiliate Split
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Company
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={formData.affiliate_company_percentage}
                  onChange={(e) =>
                    handleInputChange(
                      "affiliate_company_percentage",
                      parseFloat(e.target.value) || 0
                    )
                  }
                  className="flex-1 px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text"
                />
                <span className="text-text-secondary">%</span>
              </div>
              {errors.affiliate_company_percentage && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.affiliate_company_percentage}
                </p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Trainer
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={formData.affiliate_trainer_percentage}
                  onChange={(e) =>
                    handleInputChange(
                      "affiliate_trainer_percentage",
                      parseFloat(e.target.value) || 0
                    )
                  }
                  className="flex-1 px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text"
                />
                <span className="text-text-secondary">%</span>
              </div>
              {errors.affiliate_trainer_percentage && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.affiliate_trainer_percentage}
                </p>
              )}
            </div>
          </div>
          {errors.affiliate_percentages && (
            <p className="mt-2 text-sm text-red-600">
              {errors.affiliate_percentages}
            </p>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={handleCancel}
            disabled={submitting}
            className="px-4 py-2 text-sm font-medium text-text-secondary bg-background border border-border rounded-md hover:bg-background-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={submitting}
            className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
          >
            {submitting ? "Updating..." : "Update"}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default PayoutSettingsModal;
