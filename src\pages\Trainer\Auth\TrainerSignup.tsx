import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useState, useEffect } from "react";
import { Container } from "@/components/Container";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { MkdPasswordInput } from "@/components/MkdPasswordInput";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useTheme } from "@/hooks/useTheme";
import { LazyLoad } from "@/components/LazyLoad";
import { Modal } from "@/components/Modal";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import { useLocation, useNavigate } from "react-router";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faFacebook,
  faGoogle,
  faInstagram,
  faLinkedin,
} from "@fortawesome/free-brands-svg-icons";
import { faUser } from "@fortawesome/free-solid-svg-icons";
import { THEME_COLORS } from "@/context/Theme";

// Options for dropdowns and checkboxes
const yearsOfExperienceOptions = [
  "Less than 1 year",
  "1-2 years",
  "3-5 years",
  "6-10 years",
  "10+ years",
];
const genderOptions = [
  "Man",
  "Woman",
  "Non-Binary",
  "Transgender Woman",
  "Transgender Man",
  "Prefer not to say",
  "Other",
];
const qualificationsOptions = [
  "Certified Personal Trainer",
  "Group Fitness Instructor Certification",
  "Nutrition and Dietetics Certification",
  "Sports Medicine",
  "Corrective Exercise Specialist",
  "Youth Fitness",
  "Senior Fitness",
  "Functional Movement",
];
const specializationsOptions = [
  "Body Building",
  "Endurance Training",
  "Strength Training",
  "Functional Fitness",
  "Cross Fit",
  "Yoga",
  "Calisthenics",
  "Pilates",
];

const PlusIcon = () => {
  return (
    <svg
      width={15}
      height={16}
      viewBox="0 0 15 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_115_3228)">
        <path
          d="M8.79688 2.5C8.79688 1.94687 8.35 1.5 7.79688 1.5C7.24375 1.5 6.79688 1.94687 6.79688 2.5V7H2.29688C1.74375 7 1.29688 7.44688 1.29688 8C1.29688 8.55312 1.74375 9 2.29688 9H6.79688V13.5C6.79688 14.0531 7.24375 14.5 7.79688 14.5C8.35 14.5 8.79688 14.0531 8.79688 13.5V9H13.2969C13.85 9 14.2969 8.55312 14.2969 8C14.2969 7.44688 13.85 7 13.2969 7H8.79688V2.5Z"
          fill="#4CBF6D"
        />
      </g>
      <defs>
        <clipPath id="clip0_115_3228">
          <path d="M0.796875 0H14.7969V16H0.796875V0Z" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

// Password Strength Indicator Component
const PasswordStrengthIndicator = ({ validation }: { 
  validation: {
    hasLength: boolean | undefined;
    hasUppercase: boolean | undefined;
    hasLowercase: boolean | undefined;
    hasNumber: boolean | undefined;
    hasSpecial: boolean | undefined;
  }
}) => {
  const { state } = useTheme();
  const mode = state.theme;
  
  const getValidationColor = (isValid: boolean | undefined) => {
    // If no validation has been triggered yet (undefined), show gray
    if (isValid === undefined) {
      return '#6B7280'; // Gray for no input
    }
    
    return isValid ? '#4CBF6D' : '#FF0000'; // Green for valid, red for invalid with input
  };

  const getValidationIcon = (isValid: boolean | undefined) => {
    if (isValid === undefined) {
      return '○'; // Circle for no input
    }
    return isValid ? '✓' : '✗';
  };

  return (
    <div className="mt-2 space-y-1">
      <div className="text-xs font-medium text-text">Password Requirements:</div>
      <div className="grid grid-cols-1 gap-1 text-xs">
        <div className="flex items-center gap-2">
          <span style={{ color: getValidationColor(validation.hasLength) }}>
            {getValidationIcon(validation.hasLength)}
          </span>
          <span className="text-text">At least 8 characters</span>
        </div>
        <div className="flex items-center gap-2">
          <span style={{ color: getValidationColor(validation.hasUppercase) }}>
            {getValidationIcon(validation.hasUppercase)}
          </span>
          <span className="text-text">One uppercase letter</span>
        </div>
        <div className="flex items-center gap-2">
          <span style={{ color: getValidationColor(validation.hasLowercase) }}>
            {getValidationIcon(validation.hasLowercase)}
          </span>
          <span className="text-text">One lowercase letter</span>
        </div>
        <div className="flex items-center gap-2">
          <span style={{ color: getValidationColor(validation.hasNumber) }}>
            {getValidationIcon(validation.hasNumber)}
          </span>
          <span className="text-text">One number</span>
        </div>
        <div className="flex items-center gap-2">
          <span style={{ color: getValidationColor(validation.hasSpecial) }}>
            {getValidationIcon(validation.hasSpecial)}
          </span>
          <span className="text-text">One special character (@$!%*?&)</span>
        </div>
      </div>
    </div>
  );
};

// Confirm Password Indicator Component
const ConfirmPasswordIndicator = ({ validation }: { 
  validation: {
    matches: boolean | undefined;
    hasValue: boolean | undefined;
  }
}) => {
  const { state } = useTheme();
  const mode = state.theme;
  
  const getValidationColor = (isValid: boolean | undefined) => {
    // If no validation has been triggered yet (undefined), show gray
    if (isValid === undefined) {
      return '#6B7280'; // Gray for no input
    }
    
    return isValid ? '#4CBF6D' : '#FF0000'; // Green for valid, red for invalid with input
  };

  const getValidationIcon = (isValid: boolean | undefined) => {
    if (isValid === undefined) {
      return '○'; // Circle for no input
    }
    return isValid ? '✓' : '✗';
  };

  return (
    <div className="mt-2 space-y-1">
      <div className="text-xs font-medium text-text">Confirm Password:</div>
      <div className="grid grid-cols-1 gap-1 text-xs">
        <div className="flex items-center gap-2">
          <span style={{ color: getValidationColor(validation.hasValue) }}>
            {getValidationIcon(validation.hasValue)}
          </span>
          <span className="text-text">Password entered</span>
        </div>
        <div className="flex items-center gap-2">
          <span style={{ color: getValidationColor(validation.matches) }}>
            {getValidationIcon(validation.matches)}
          </span>
          <span className="text-text">Passwords match</span>
        </div>
      </div>
    </div>
  );
};

const schema = yup.object({
  fullName: yup.string().required("Full Name is required"),
  email: yup.string().email("Invalid email").required("Email is required"),
  // phone: yup
  //   .string()
  //   .required("Phone Number is required")
  //   .matches(
  //     /^[\+]?[1-9][\d]{0,15}$/,
  //     "Please enter a valid phone number (numbers only, max 15 digits)"
  //   )
  //   .min(10, "Phone number must be at least 10 digits")
  //   .max(15, "Phone number cannot exceed 15 digits"),
  profilePicture: yup.mixed().nullable(),
  password: yup
    .string()
    .required("Password is required")
    .min(8, "Password must be at least 8 characters")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
      "Password must contain at least one uppercase, at least one lowercase, at least one number, and at least one special character"
    ),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("password")], "Passwords must match")
    .required("Confirm Password is required"),
  yearsOfExperience: yup.string().required("Years of Experience is required"),
  gender: yup.string().required("Gender is required"),
  qualifications: yup
    .array()
    .of(yup.string())
    .min(1, "Select at least one qualification"),
  specializations: yup
    .array()
    .of(yup.string())
    .min(1, "Select at least one specialization"),
  bio: yup.string().required("Bio is required"),
  terms: yup.boolean().oneOf([true], "You must accept the terms"),
});

const TrainerSignup = () => {
  const { state } = useTheme();
  const mode = state.theme;
  const { sdk } = useSDK({ role: "trainer" });
  const { showToast } = useContexts();

  const [profilePreview, setProfilePreview] = useState<string | null>(null);
  const [loading, setLoading] = useState({
    submitting: false,
    qualifications: false,
    specializations: false,
  });
  const [modal, setModal] = useState({
    show: false,
    target: "",
    title: "",
    value: "",
  });

  const [content, setContent] = useState<{
    qualifications: string[];
    specializations: string[];
  }>({
    qualifications: [],
    specializations: [],
  });

  const location = useLocation();
  const navigate = useNavigate();
  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get("redirect_uri");

  const {
    register,
    handleSubmit,
    setValue,
    setError,
    clearErrors,
    getValues,
    formState: { errors },
    watch,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      qualifications: [],
      specializations: [],
      profilePicture: null,
    },
  });

  // Custom register for phone with validation
  // const phoneRegister = register('phone', {
  //   onChange: (e) => {
  //     let value = e.target.value;
      
  //     // Remove all non-digit characters except +
  //     value = value.replace(/[^\d+]/g, '');
      
  //     // Ensure only one + at the beginning
  //     if (value.startsWith('+')) {
  //       value = '+' + value.substring(1).replace(/\+/g, '');
  //     }
      
  //     // Limit to 15 characters (including +)
  //     if (value.length > 16) {
  //       value = value.substring(0, 16);
  //     }
      
  //     // Update the input value
  //     e.target.value = value;
  //     setValue('phone', value);
  //   }
  // });

  // Password validation state
  const [passwordValidation, setPasswordValidation] = useState<{
    hasLength: boolean | undefined;
    hasUppercase: boolean | undefined;
    hasLowercase: boolean | undefined;
    hasNumber: boolean | undefined;
    hasSpecial: boolean | undefined;
  }>({
    hasLength: undefined,
    hasUppercase: undefined,
    hasLowercase: undefined,
    hasNumber: undefined,
    hasSpecial: undefined,
  });

  // Confirm password validation state
  const [confirmPasswordValidation, setConfirmPasswordValidation] = useState<{
    matches: boolean | undefined;
    hasValue: boolean | undefined;
  }>({
    matches: undefined,
    hasValue: undefined,
  });

  // Custom register for password with real-time validation
  const passwordRegister = register('password');

  // Custom register for confirm password with real-time validation
  const confirmPasswordRegister = register('confirmPassword');

  // Watch for form data
  const trainerData = watch();
  const {
    password,
    confirmPassword,
  } = trainerData;


  const [_profilePicture, setProfilePicture] = useState<File | null>(null);
  const [socialAuthState, setSocialAuthState] = useState({
    loading: false,
    provider: "",
  });

  // Handle profile picture upload and preview
  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setProfilePicture(file);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => setProfilePreview(reader.result as string);
      reader.readAsDataURL(file);
    } else {
      setProfilePreview(null);
    }
  };



  const addContent = () => {
    setContent((prev) => ({
      ...prev,
      [modal.target as keyof typeof prev]: [
        ...prev[modal.target as keyof typeof prev],
        modal.value,
      ],
    }));

    setModal({ show: false, target: "", title: "", value: "" });
  };

  // Form submit handler
  const onSubmit = async (data: yup.InferType<typeof schema>) => {
    try {
      setLoading((prev) => ({ ...prev, submitting: true }));
      
      // Prepare all profile data to send with registration
      const profileData = {
        full_name: data.fullName,
        first_name: data.fullName.split(" ")[0] || "",
        last_name: data.fullName.split(" ")[1] || "",
        // phone: data.phone,
        gender: data.gender,
        bio: data.bio,
        specializations: data.specializations,
        years_of_experience: data.yearsOfExperience,
        qualifications: data.qualifications,
        terms: data.terms,
        profile_update: true,
        // profile_picture: profilePicture
      };

      const result: any = await sdk.request({
        endpoint: '/v1/api/kanglink/trainer/lambda/register',
        method: 'POST',
        body: {
          email: data.email,
          password: data.password,
          role: "trainer",
          data: profileData
        }
      });

      if (!result.error) {
        showToast("Registration successful! Please check your email to verify your account.", 4000, ToastStatusEnum.SUCCESS);
        navigate('/verification-sent');
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field as any, {
              type: "manual",
              message: result.validation[field],
            });
          }
        } else {
          showToast(result.message || "Registration failed", 4000, ToastStatusEnum.ERROR);
        }
      }
    } catch (error: any) {
      console.error("Registration error:", error);
      showToast(error?.response?.data?.message || error?.message || "Registration failed", 4000, ToastStatusEnum.ERROR);
    } finally {
      setLoading((prev) => ({ ...prev, submitting: false }));
    }
  };

  const socialLogin = async (type: string) => {
    try {
      setSocialAuthState((prev) => {
        return {
          ...prev,
          loading: true,
          provider: type,
        };
      });
      const result = await sdk.oauthLoginApi(type, "trainer");
      window.open(result, "_self");
    } catch (error: any) {
      showToast(
        error?.response?.data?.message
          ? error?.response?.data?.message
          : error?.message,
        4000,
        ToastStatusEnum.ERROR
      );
    } finally {
      setSocialAuthState((prev) => {
        return {
          ...prev,
          loading: false,
        };
      });
    }
  };

  // Theme-based classes using theme utility classes and CSS variables
  const bgClass = "bg-secondary";
  const textClass = "text-text";
  const inputBgClass = "bg-secondary border-primary";
  const inputTextClass = "text-text";

  
  // Update password and confirm password validation when either field changes
  useEffect(() => {
    const currentPassword = password || '';
    const currentConfirmPassword = confirmPassword || '';
    
    console.log('useEffect triggered:', { 
      currentPassword, 
      currentConfirmPassword, 
      matches: currentConfirmPassword === currentPassword 
    });
    
    // Update password validation
    if (currentPassword.length > 0) {
      const passwordValidation = {
        hasLength: currentPassword.length >= 8,
        hasUppercase: /[A-Z]/.test(currentPassword),
        hasLowercase: /[a-z]/.test(currentPassword),
        hasNumber: /\d/.test(currentPassword),
        hasSpecial: /[@$!%*?&]/.test(currentPassword),
      };
      setPasswordValidation(passwordValidation);
      
      // Clear password errors if all validations pass
      if (Object.values(passwordValidation).every(Boolean)) {
        clearErrors('password');
      }
    }
    
    // Update confirm password validation
    if (currentConfirmPassword.length > 0) {
      const confirmValidation = {
        hasValue: currentConfirmPassword.length > 0,
        matches: currentConfirmPassword === currentPassword && currentConfirmPassword.length > 0,
      };
      setConfirmPasswordValidation(confirmValidation);
      
      // Clear confirm password errors if validation passes
      if (confirmValidation.matches) {
        clearErrors('confirmPassword');
      } else if (currentConfirmPassword.length > 0 && currentPassword.length > 0) {
        // Set error if passwords don't match and both fields have values
        setError('confirmPassword', { 
          type: 'manual', 
          message: 'Passwords do not match' 
        });
      }
    }
  }, [password, confirmPassword, clearErrors, setError]);

  return (
    <>
      <Container className={`${bgClass} `}>
        <div className="w-full max-w-6xl border-border border mx-auto p-6 rounded-lg shadow-lg relative">
          <h1
            className={`text-2xl md:text-3xl font-bold text-center mb-8 ${textClass}`}
          >
            Signup as Trainer
          </h1>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="flex flex-col justify-between md:flex-row gap-6">
              <div className="md:max-w-1/2 md:w-1/2  space-y-6">
                {/* Full Name */}
                <MkdInputV2
                  name="fullName"
                  type="text"
                  register={register}
                  errors={errors}
                  required
                  placeholder="Enter Full Name"
                  className={inputBgClass}
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className={textClass}>
                      Full Name
                    </MkdInputV2.Label>
                    <MkdInputV2.Field className={inputTextClass} />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
                {/* Email */}
                <MkdInputV2
                  name="email"
                  type="email"
                  register={register}
                  errors={errors}
                  required
                  placeholder="Enter Email Address"
                  className={inputBgClass}
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className={textClass}>
                      Email
                    </MkdInputV2.Label>
                    <MkdInputV2.Field className={inputTextClass} />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
                {/* Phone Number */}
                {/* <MkdInputV2
                  // name="phone"
                  type="tel"
                  // register={phoneRegister}
                  {...phoneRegister}
                  errors={errors}
                  required
                  placeholder="Enter Phone Number (e.g., +1234567890)"
                  className={inputBgClass}
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className={textClass}>
                      Phone Number
                    </MkdInputV2.Label>
                    <MkdInputV2.Field 
                      className={inputTextClass}
                    />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2> */}
              </div>
              {/* Profile Picture Upload */}
              <div className="flex flex-col items-center gap-4 w-full md:w-auto md:grow">
                <div className="flex flex-col items-center gap-4 w-56">
                  <div
                    className={`rounded-full overflow-hidden size-40 flex items-center justify-center ${
                      mode === "dark" ? "bg-neutral-800" : "bg-gray-50"
                    }`}
                  >
                    {profilePreview ? (
                      <img
                        src={profilePreview}
                        alt="Profile Preview"
                        className="object-cover w-full h-full"
                      />
                    ) : (
                      <div className="flex items-center justify-center w-full h-full bg-gray-100 dark:bg-gray-700">
                        <FontAwesomeIcon
                          icon={faUser}
                          className="w-12 h-12 text-gray-400 dark:text-gray-500"
                        />
                      </div>
                    )}
                  </div>
                  <label className="w-full">
                    <input
                      type="file"
                      id="profile-input"
                      accept="image/*"
                      className="hidden"
                      onChange={handleProfileChange}
                    />
                    <InteractiveButton
                      type="button"
                      onClick={() =>
                        document.getElementById("profile-input")?.click()
                      }
                      className="w-full border text-primary border-green-400 text-green-400 bg-transparent hover:bg-green-50 hover:!text-white h-[2.875rem]"
                    >
                      <PlusIcon />
                      Add Profile Picture
                    </InteractiveButton>
                  </label>
                </div>
              </div>
            </div>
            {/* Passwords */}
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex-1">
                <MkdPasswordInput
                  name="password"
                  label="Password"
                  register={register}
                  errors={errors}
                  required
                  containerClassName={inputBgClass}
                  inputClassName={inputTextClass}
                />
                <PasswordStrengthIndicator validation={passwordValidation} />
              </div>
              <div className="flex-1">
                <MkdPasswordInput
                  name="confirmPassword"
                  label="Confirm Password"
                  register={register}
                  errors={errors}
                  required
                  containerClassName={inputBgClass}
                  inputClassName={inputTextClass}
                />
                {/* <ConfirmPasswordIndicator validation={confirmPasswordValidation} /> */}
              </div>
            </div>
            {/* Years of Experience & Gender */}
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex-1">
                <MkdInputV2
                  name="yearsOfExperience"
                  type="select"
                  register={register}
                  errors={errors}
                  required
                  options={yearsOfExperienceOptions}
                  placeholder="Select Years of Experience"
                  className={inputBgClass}
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className={textClass}>
                      Years of Experience
                    </MkdInputV2.Label>
                    <MkdInputV2.Field className={inputTextClass} />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>
              <div className="flex-1">
                <MkdInputV2
                  name="gender"
                  type="select"
                  register={register}
                  errors={errors}
                  required
                  options={genderOptions}
                  placeholder="Select Gender"
                  className={inputBgClass}
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className={textClass}>
                      Gender
                    </MkdInputV2.Label>
                    <MkdInputV2.Field className={inputTextClass} />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>
            </div>
            {/* Qualifications */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-text">
                Qualifications
              </label>
              <div className="flex md:flex-row flex-col gap-3">
                <div className="flex-1">
                  <div
                    className="grid grid-cols-1 md:grid-cols-2 gap-2 p-4 rounded-md border"
                    style={{
                      borderColor: THEME_COLORS[mode].BORDER,
                      backgroundColor: THEME_COLORS[mode].BACKGROUND,
                    }}
                  >
                    {[...qualificationsOptions, ...content.qualifications].map(
                      (qualification) => (
                        <LazyLoad key={qualification}>
                          <MkdInputV2
                            name="qualifications"
                            type="checkbox"
                            register={register}
                            errors={errors}
                            value={qualification}
                            required
                          >
                            <MkdInputV2.Container className="flex items-center gap-3">
                              <MkdInputV2.Field />
                              <MkdInputV2.Label className="text-[0.875rem] font-normal text-wrap">
                                {qualification}
                              </MkdInputV2.Label>
                            </MkdInputV2.Container>
                          </MkdInputV2>
                        </LazyLoad>
                      )
                    )}
                  </div>
                  {errors.qualifications && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.qualifications.message as string}
                    </p>
                  )}
                </div>
                <div className="flex justify-center items-start">
                  <InteractiveButton
                    type="button"
                    onClick={() =>
                      setModal((prev) => ({
                        ...prev,
                        show: true,
                        target: "qualifications",
                        title: "Add Qualification",
                      }))
                    }
                    className="w-full md:!min-w-fit md:w-fit px-2 border text-primary border-green-400 bg-transparent hover:bg-green-50 h-[2.875rem] flex items-center justify-center gap-2"
                    style={{
                      borderColor: THEME_COLORS[mode].PRIMARY,
                      color: THEME_COLORS[mode].PRIMARY,
                    }}
                  >
                    <PlusIcon />
                    Add New
                  </InteractiveButton>
                </div>
              </div>
            </div>

            {/* Specializations */}
            <div className="space-y-2 mt-2">
              <label className="text-sm font-medium text-text">
                Specializations
              </label>
              <div className="flex gap-3 flex-col md:flex-row">
                <div className="flex-1">
                  <div
                    className="grid grid-cols-1 md:grid-cols-2 gap-2 p-4 rounded-md border"
                    style={{
                      borderColor: THEME_COLORS[mode].BORDER,
                      backgroundColor: THEME_COLORS[mode].BACKGROUND,
                    }}
                  >
                    {[
                      ...specializationsOptions,
                      ...content.specializations,
                    ].map((specialization) => (
                      <LazyLoad key={specialization}>
                        <MkdInputV2
                          name="specializations"
                          type="checkbox"
                          register={register}
                          errors={errors}
                          value={specialization}
                          required
                        >
                          <MkdInputV2.Container className="flex items-center gap-3">
                            <MkdInputV2.Field />
                            <MkdInputV2.Label className="text-[0.875rem] font-normal text-wrap">
                              {specialization}
                            </MkdInputV2.Label>
                          </MkdInputV2.Container>
                        </MkdInputV2>
                      </LazyLoad>
                    ))}
                  </div>
                  {errors.specializations && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.specializations.message as string}
                    </p>
                  )}
                </div>
                <div className="flex justify-center items-start">
                  <InteractiveButton
                    type="button"
                    onClick={() =>
                      setModal((prev) => ({
                        ...prev,
                        show: true,
                        target: "specializations",
                        title: "Add Specialization",
                      }))
                    }
                    className="w-full md:!min-w-fit md:w-fit px-2 border text-primary border-green-400 bg-transparent hover:bg-green-50 h-[2.875rem] flex items-center justify-center gap-2"
                    style={{
                      borderColor: THEME_COLORS[mode].PRIMARY,
                      color: THEME_COLORS[mode].PRIMARY,
                    }}
                  >
                    <PlusIcon />
                    Add New
                  </InteractiveButton>
                </div>
              </div>
            </div>
            {/* Bio */}
            <MkdInputV2
              name="bio"
              type="textarea"
              value={undefined}
              register={register}
              errors={errors}
              required
              placeholder="Tell us about yourself..."
              className={inputBgClass}
            >
              <MkdInputV2.Container>
                <MkdInputV2.Label className={textClass}>Bio</MkdInputV2.Label>
                <MkdInputV2.Field className={inputTextClass} rows="4" />
                <MkdInputV2.Error />
              </MkdInputV2.Container>
            </MkdInputV2>
            {/* Terms and Conditions */}
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                {...register("terms")}
                className="accent-green-400"
                id="terms"
              />
              <label htmlFor="terms" className={textClass}>
                I agree to the{" "}
                <span className="text-green-400 font-medium cursor-pointer">
                  Terms of Use
                </span>{" "}
                and{" "}
                <span className="text-green-400 font-medium cursor-pointer">
                  Privacy Policy
                </span>
              </label>
            </div>
            {errors.terms && (
              <p className="text-red-500 text-sm mt-1">
                {errors.terms.message as string}
              </p>
            )}
            {/* Submit Button */}
            <InteractiveButton
              type="submit"
              loading={loading.submitting}
              disabled={loading.submitting}
              className="w-full h-[2.87rem] bg-green-400 text-white font-semibold text-base rounded-md mt-2"
            >
              Sign Up as Trainer
            </InteractiveButton>
            {/* Social Signup */}
            <div className="flex items-center my-6">
              <div className="flex-1 h-px bg-gray-300 dark:bg-[#3a3a3a]" />
              <span className="mx-4 text-gray-500 dark:text-gray-400">
                Or sign up with
              </span>
              <div className="flex-1 h-px bg-gray-300 dark:bg-[#3a3a3a]" />
            </div>
            <div className="grid grid-cols-1 gap-4">
              <InteractiveButton
                type="button"
                onClick={() => socialLogin("facebook")}
                disabled={socialAuthState.loading}
                loading={
                  socialAuthState.loading &&
                  socialAuthState.provider === "facebook"
                }
                className="w-full border h-12 !text-text !border-border bg-white dark:bg-neutral-800 hover:!text-white flex items-center justify-center gap-2"
              >
                <FontAwesomeIcon
                  icon={faFacebook}
                  className="w-5 h-5 text-[#1877F2]"
                />
                {socialAuthState.loading &&
                socialAuthState.provider === "facebook" ? null : (
                  <>Facebook</>
                )}
              </InteractiveButton>
              <InteractiveButton
                type="button"
                onClick={() => socialLogin("google")}
                disabled={socialAuthState.loading}
                loading={
                  socialAuthState.loading &&
                  socialAuthState.provider === "google"
                }
                className="w-full border h-12 !text-text !border-border bg-white dark:bg-neutral-800 hover:!text-white flex items-center justify-center gap-2"
              >
                <FontAwesomeIcon
                  icon={faGoogle}
                  className="w-5 h-5 text-red-600"
                />
                {socialAuthState.loading &&
                socialAuthState.provider === "google" ? null : (
                  <>Google</>
                )}
              </InteractiveButton>
              <InteractiveButton
                type="button"
                onClick={() => socialLogin("instagram")}
                disabled={true}
                loading={
                  socialAuthState.loading &&
                  socialAuthState.provider === "instagram"
                }
                className="w-full hidden border h-12 !text-text !border-border bg-white dark:bg-neutral-800 hover:!text-white flex items-center justify-center gap-2"
              >
                <FontAwesomeIcon
                  icon={faInstagram}
                  className="w-5 h-5 text-[#E4405F]"
                />
                {socialAuthState.loading &&
                socialAuthState.provider === "instagram" ? null : (
                  <>Instagram</>
                )}
              </InteractiveButton>
              <InteractiveButton
                type="button"
                onClick={() => socialLogin("linkedin")}
                disabled={socialAuthState.loading}
                loading={
                  socialAuthState.loading &&
                  socialAuthState.provider === "linkedin"
                }
                className="w-full border h-12 !text-text !border-border bg-white dark:bg-neutral-800 hover:!text-white flex items-center justify-center gap-2"
              >
                <FontAwesomeIcon
                  icon={faLinkedin}
                  className="w-5 h-5 text-[#0A66C2]"
                />
                {socialAuthState.loading &&
                socialAuthState.provider === "linkedin" ? null : (
                  <>LinkedIn</>
                )}
              </InteractiveButton>
            </div>
          </form>
        </div>
      </Container>
      <Modal
        isOpen={modal.show}
        title={modal.title}
        modalCloseClick={() => {
          setModal({ show: false, target: "", title: "", value: "" });
        }}
        modalHeader={true}
        classes={{
          modal: "w-full  ",
          modalContent: "border-b border-gray-200 dark:border-[#3a3a3a]",
          modalDialog: "max-w-xl",
        }}
        modalFooter={
          <div className="flex justify-end pt-5 px-5 gap-2 w-full">
            <InteractiveButton
              type="button"
              onClick={() => {
                setModal({ show: false, target: "", title: "", value: "" });
              }}
              className="bg-transparent text-text px-4 py-2 rounded-md"
            >
              Cancel
            </InteractiveButton>

            <InteractiveButton
              type="button"
              onClick={() => {
                addContent();
              }}
              className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/80"
            >
              Add
            </InteractiveButton>
          </div>
        }
      >
        <div className="py-5">
          <MkdInputV2
            name={modal.target}
            onChange={(e) => {
              setModal((prev) => ({
                ...prev,
                value: e.target.value,
              }));
            }}
            value={modal.value}
            required
            className={inputBgClass}
          >
            <MkdInputV2.Container>
              <MkdInputV2.Label
                className={`font-normal capitalize ${textClass}`}
              >
                {modal.target}
              </MkdInputV2.Label>
              <MkdInputV2.Field className={inputTextClass} />
              <MkdInputV2.Error />
            </MkdInputV2.Container>
          </MkdInputV2>
        </div>
      </Modal>
    </>
  );
};

export default TrainerSignup;
