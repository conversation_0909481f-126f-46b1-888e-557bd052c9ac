# Trainer Athletes Management Endpoint

## Overview
This endpoint allows trainers to view and manage their enrolled athletes with comprehensive filtering, sorting, and pagination capabilities. It provides detailed information about athlete enrollment, progress, and payment status.

## Endpoint Details

**URL:** `GET /v2/api/kanglink/custom/trainer/athletes`

**Authentication:** Required (Trainer role only)

**Authorization Header:** `Bearer {trainer_token}`

## Query Parameters

### Pagination
- `page` (integer, optional): Page number (default: 1)
- `limit` (integer, optional): Number of results per page (default: 20, max: 50)

### Sorting
- `sort_by` (string, optional): Field to sort by
  - Options: `enrollment_date`, `athlete_name`, `program_name`, `progress`
  - Default: `enrollment_date`
- `sort_order` (string, optional): Sort direction
  - Options: `asc`, `desc`
  - Default: `desc`

### Filtering
- `athlete_name` (string, optional): Filter by athlete name (partial match)
- `payment_type` (string, optional): Filter by payment type
  - Options: `subscription`, `one_time`, `all`
- `program_name` (string, optional): Filter by program name (partial match)
- `progress` (string, optional): Filter by progress level
  - Options: `below_10`, `below_50`, `above_50`, `completed`

## Example Requests

### Basic Request
```bash
GET /v2/api/kanglink/custom/trainer/athletes
Authorization: Bearer {trainer_token}
```

### With Filters and Sorting
```bash
GET /v2/api/kanglink/custom/trainer/athletes?athlete_name=John&payment_type=subscription&sort_by=progress&sort_order=desc&page=1&limit=10
Authorization: Bearer {trainer_token}
```

### Filter by Progress
```bash
GET /v2/api/kanglink/custom/trainer/athletes?progress=above_50
Authorization: Bearer {trainer_token}
```

## Response Format

### Success Response (200)
```json
{
  "error": false,
  "message": "Trainer athletes retrieved successfully",
  "data": [
    {
      "enrollment_id": 123,
      "athlete_id": 456,
      "athlete_name": "John Doe",
      "athlete_email": "<EMAIL>",
      "athlete_photo": "https://example.com/photo.jpg",
      "program_id": 789,
      "program_name": "Strength Training Program",
      "split_id": 101,
      "split_title": "Beginner Split",
      "payment_type": "subscription",
      "payment_type_display": "Subscription",
      "amount": 29.99,
      "currency": "USD",
      "enrollment_date": "2024-01-15T10:30:00Z",
      "enrollment_status": "active",
      "payment_status": "paid",
      "progress_percentage": 67.5,
      "progress_status": "On Track",
      "total_days_completed": 15,
      "total_exercises_completed": 45,
      "last_activity_date": "2024-01-20T14:22:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 25,
    "num_pages": 2,
    "has_next": true,
    "has_prev": false
  },
  "filters": {
    "athlete_name": "",
    "payment_type": "",
    "program_name": "",
    "progress": ""
  },
  "sorting": {
    "sort_by": "enrollment_date",
    "sort_order": "desc"
  }
}
```

### Error Responses

#### 400 - Bad Request
```json
{
  "error": true,
  "message": "Invalid sort_by parameter. Valid options: enrollment_date, athlete_name, program_name, progress"
}
```

#### 401 - Unauthorized
```json
{
  "error": true,
  "message": "Authentication required"
}
```

#### 500 - Internal Server Error
```json
{
  "error": true,
  "message": "Internal server error while fetching trainer athletes"
}
```

## Data Fields Explanation

### Athlete Information
- `athlete_id`: Unique identifier for the athlete
- `athlete_name`: Full name of the athlete (from user.data JSON field)
- `athlete_email`: Athlete's email address
- `athlete_photo`: URL to athlete's profile photo

### Enrollment Information
- `enrollment_id`: Unique identifier for the enrollment record
- `enrollment_date`: Date when athlete enrolled in the program
- `enrollment_status`: Current status (active, expired, cancelled, pending, refund)
- `payment_status`: Payment status (paid, pending, failed, refunded)

### Program Information
- `program_id`: Unique identifier for the program
- `program_name`: Name of the program
- `split_id`: Unique identifier for the specific split
- `split_title`: Title of the split

### Payment Information
- `payment_type`: Type of payment (subscription or one_time)
- `payment_type_display`: Human-readable payment type
- `amount`: Amount paid for the enrollment
- `currency`: Currency code (e.g., USD)

### Progress Information
- `progress_percentage`: Completion percentage (0-100)
- `progress_status`: Human-readable progress status
  - "Not Started": 0% progress
  - "Behind": 1-49% progress
  - "On Track": 50-99% progress
  - "Completed": 100% progress
- `total_days_completed`: Number of workout days completed
- `total_exercises_completed`: Number of exercises completed
- `last_activity_date`: Date of last activity

## Progress Filter Options

- `below_10`: Athletes with less than 10% progress
- `below_50`: Athletes with less than 50% progress
- `above_50`: Athletes with 50-99% progress
- `completed`: Athletes with 100% progress

## Use Cases

1. **Athlete Management Dashboard**: Display all enrolled athletes with their progress
2. **Progress Monitoring**: Filter athletes by progress level to identify those who need attention
3. **Payment Tracking**: Filter by payment type to manage subscriptions vs one-time purchases
4. **Search Functionality**: Find specific athletes by name or program
5. **Performance Analytics**: Sort by progress to identify top performers

## Notes

- Only trainers can access this endpoint
- The endpoint automatically filters to show only athletes enrolled in the authenticated trainer's programs
- Progress data comes from the `athlete_progress` table
- Athlete personal information is extracted from the JSON `data` field in the user table
- All dates are returned in ISO 8601 format
- Pagination limits are enforced (max 50 per page) to ensure good performance
