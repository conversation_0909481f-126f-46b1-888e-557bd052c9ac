import React, { createContext, useContext } from "react";

// Define the possible input types
export type StandardInputType =
  | React.InputHTMLAttributes<HTMLInputElement>["type"]
  | "textarea"
  | "toggle"
  | "custom_date";
export type DropdownType = "dropdown";
export type SelectType = "select";
export type MappingType = "mapping";
export type MkdInputV2Type =
  | StandardInputType
  | DropdownType
  | MappingType
  | SelectType;
export type TSelectOptions =
  | string[]
  | number[]
  | boolean[]
  | { [key: string | number]: any }[];
export type TDropdownOptions = { [key: string | number]: any }[]; // Array of objects with key-value pairs

// Context for the input field
export type MkdInputV2ContextType = {
  id: string;
  name?: string;
  type: MkdInputV2Type;
  value: any;
  onChange: (e: any) => void;
  register?: any;
  errors?: any;
  disabled?: boolean;
  required?: boolean;
  placeholder?: string;
  options?: TSelectOptions | TDropdownOptions;
  mapping?: Record<string | number, string>;
  customField?: boolean;
  displaySeparator: string;
  uniqueKey: string;
  display: string | number | any[] | { and: string[]; or: string[] };
  showNone?: boolean;
};

export const MkdInputV2Context = createContext<
  MkdInputV2ContextType | undefined
>(undefined);

// Hook to use the context
export const useMkdInputV2Context = () => {
  const context = useContext(MkdInputV2Context);
  if (!context) {
    throw new Error("MkdInputV2.* components must be used within MkdInputV2");
  }
  return context;
};
