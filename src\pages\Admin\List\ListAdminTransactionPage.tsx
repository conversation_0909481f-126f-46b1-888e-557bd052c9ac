import React, { useState, useEffect, useMemo } from "react";
import { useContexts } from "@/hooks/useContexts";
import {
  TransactionFilters,
  TransactionTable,
} from "@/components/Transactions";
import { Transaction } from "@/interfaces/model.interface";
import { useGetPaginateQuery } from "@/query/shared/listModel";
import { Models } from "@/utils/baas/models";
import { useCustomModelQuery } from "@/query/shared";
import { ToastStatusEnum } from "@/utils/Enums";
import { generateTransactionsPDF } from "@/utils/pdfGenerator";

const ListAdminTransactionPage: React.FC = () => {
  const { globalDispatch, showToast } = useContexts();
  const { mutateAsync: customModelQuery } = useCustomModelQuery();

  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [dateFilter, setDateFilter] = useState("Today");
  const [statusFilter, setStatusFilter] = useState("Show All");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10); // Match the design showing 10 items

  // Set path in global state for navigation highlighting
  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "transactions",
      },
    });
  }, [globalDispatch]);

  // Build query options for API call
  const queryOptions = useMemo(() => {
    const options: any = {
      page: currentPage,
      limit: pageSize,
      filter: [],
      join: ["user|trainer_id", "program", "split"],
      size: pageSize,
    };

    // Add search filter
    if (searchTerm.trim()) {
      // Search in trainer name through join
      options.filter.push(`trainer.full_name,like,%${searchTerm}%`);
    }

    // Add status filter if not "Show All"
    if (statusFilter !== "Show All") {
      // Map admin status to database status
      const statusMap: Record<string, string> = {
        Completed: "paid",
        Pending: "pending",
        Refunded: "refunded",
      };
      const dbStatus = statusMap[statusFilter] || statusFilter.toLowerCase();
      options.filter.push(`payment_status,eq,${dbStatus}`);
    }

    // Add date filter logic here if needed
    // For now, we'll skip date filtering

    return options;
  }, [currentPage, pageSize, searchTerm, statusFilter]);

  // Fetch transactions data using pagination
  const {
    data: transactionsData,
    isLoading,
    error,
    refetch,
    isFetching,
  } = useGetPaginateQuery(Models.ENROLLMENT, queryOptions, {
    enabled: true,
  });

  // Transform Transaction data to match UI expectations
  const transactions: Transaction[] = useMemo(() => {
    if (!transactionsData?.data) return [];

    return transactionsData.data.map((transaction: Transaction) => ({
      ...transaction,
      trainer_name:
        JSON.parse(transaction.user?.data || "{}")?.full_name ||
        JSON.parse(transaction.user?.data || "{}")?.first_name +
          " " +
          JSON.parse(transaction.user?.data || "{}")?.last_name ||
        "Unknown",
      program_name: transaction.program?.program_name || "Unknown Program",
      split_title: transaction.split?.title || "Unknown Split",
      total_amount: transaction.original_amount || transaction.amount || 0,
      currency: transaction.program?.currency || "USD",
      discount_amount: transaction.discount_amount || 0,
      discount_details: transaction.discount_details || "",
      affiliate_code: transaction.affiliate_code || "",
      affiliate_user_id: transaction.affiliate_user_id || "",
      payment_status: transaction.payment_status || "pending",
      // Calculate coach revenue and platform earning based on commission structure
      // Use original amount if available, otherwise fall back to amount
      coach_revenue: (() => {
        const baseAmount =
          transaction.original_amount || transaction.amount || 0;
        return baseAmount * 0.7; // 70% to coach
      })(),
      platform_earning: (() => {
        const baseAmount =
          transaction.original_amount || transaction.amount || 0;
        return baseAmount * 0.3; // 30% platform fee
      })(),
    }));
  }, [transactionsData?.data]);

  // Pagination info
  const totalPages = transactionsData?.num_pages || 1;

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, dateFilter, statusFilter]);

  // Handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownloadPDF = () => {
    try {
      // Show loading toast
      showToast("Generating PDF report...", 2000, ToastStatusEnum.INFO);

      // Generate PDF with current transaction data and filters
      generateTransactionsPDF(transactions, {
        title: "Transaction Report",
        subtitle: "Admin Transaction Summary",
        includeFilters: {
          searchTerm: searchTerm.trim() || undefined,
          dateFilter: dateFilter !== "Show All" ? dateFilter : undefined,
          statusFilter: statusFilter !== "Show All" ? statusFilter : undefined,
        },
        companyInfo: {
          name: "KangLink",
          address: "Fitness Training Platform",
          phone: "+****************",
          email: "<EMAIL>",
        },
      });

      // Show success toast
      setTimeout(() => {
        showToast(
          "PDF report generated successfully!",
          3000,
          ToastStatusEnum.SUCCESS
        );
      }, 500);
    } catch (error: any) {
      console.error("Error generating PDF:", error);
      showToast(
        "Failed to generate PDF report. Please try again.",
        5000,
        ToastStatusEnum.ERROR
      );
    }
  };

  const handlePayoutSettings = () => {
    
  };

  const handleDeleteTransaction = async (transactionId: number) => {
    try {
      await customModelQuery({
        endpoint: `/v2/api/kanglink/custom/${Models.TRANSACTION}/${transactionId}`,
        method: "DELETE",
      });

      showToast(
        "Transaction deleted successfully",
        3000,
        ToastStatusEnum.SUCCESS
      );
      refetch(); // Refresh the data
    } catch (error: any) {
      showToast(
        error?.response?.data?.message || "Failed to delete transaction",
        5000,
        ToastStatusEnum.ERROR
      );
    }
  };

  const handleStatusChange = async (
    transactionId: number,
    newStatus: string
  ) => {
    try {
      // Map UI status to database status
      const statusMap: Record<string, string> = {
        paid: "paid",
        pending: "pending",
        refunded: "refunded",
        failed: "failed",
      };
      const dbStatus = statusMap[newStatus] || newStatus.toLowerCase();

      await customModelQuery({
        endpoint: `/v2/api/kanglink/custom/${Models.ENROLLMENT}/${transactionId}`,
        method: "PUT",
        body: {
          payment_status: dbStatus,
        },
      });

      showToast(
        "Transaction status updated successfully",
        3000,
        ToastStatusEnum.SUCCESS
      );
      refetch(); // Refresh the data
    } catch (error: any) {
      showToast(
        error?.response?.data?.message || "Failed to update transaction status",
        5000,
        ToastStatusEnum.ERROR
      );
    }
  };

  const handleApplyFilter = () => {
    // Reset to first page when applying filters
    setCurrentPage(1);
  };

  return (
    <div className="w-full bg-background p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h1 className="text-2xl font-bold text-text">Transactions</h1>
        </div>

        {/* Filters Section */}
        <TransactionFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          dateFilter={dateFilter}
          setDateFilter={setDateFilter}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          onApplyFilter={handleApplyFilter}
        />

        {/* Transactions Table Section */}
        <TransactionTable
          transactions={transactions}
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          isLoading={isLoading || isFetching}
          error={error}
          onPageChange={handlePageChange}
          onDownloadPDF={handleDownloadPDF}
          onPayoutSettings={handlePayoutSettings}
          onDeleteTransaction={handleDeleteTransaction}
          onStatusChange={handleStatusChange}
          onRefresh={refetch}
        />
      </div>
    </div>
  );
};

export default ListAdminTransactionPage;
