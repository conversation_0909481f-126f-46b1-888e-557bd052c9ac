import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useLocation, useNavigate } from "react-router-dom";
import { MkdInputV2 } from "@/components/MkdInputV2";

import { InteractiveButton } from "@/components/InteractiveButton";
import { useContexts } from "@/hooks/useContexts";
import { useSDK } from "@/hooks/useSDK";
import { ToastStatusEnum } from "@/utils/Enums";
import { RoleMap } from "@/utils";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { FaFacebook, FaGoogle, FaInstagram, FaLinkedin } from "react-icons/fa";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import { useQueryClient } from "@tanstack/react-query";
import TwoFactorLoginModal from "@/components/Profile/TwoFactorLoginModal";
import EmailVerificationModal from "@/components/Profile/EmailVerificationModal";

interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}
type LoginType = "athlete" | "trainer";
const loginSchema = yup.object({
  email: yup
    .string()
    .email("Please enter a valid email address")
    .required("Email is required"),
  password: yup
    .string()
    .min(6, "Password must be at least 6 characters")
    .required("Password is required"),
  rememberMe: yup.boolean().required(),
});

const LoginPage = () => {
  const navigate = useNavigate();
  const { sdk } = useSDK();
  const { state } = useTheme();
  const mode = state?.theme;

  const { authDispatch, showToast } = useContexts();
  const queryClient = useQueryClient();

  const [loginType, setLoginType] = useState<LoginType>("athlete");
  const [submitLoading, setSubmitLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [socialAuthState, setSocialAuthState] = useState({
    loading: false,
    provider: "",
  });

  // 2FA state
  const [show2FAModal, setShow2FAModal] = useState(false);
  const [twoFAData, setTwoFAData] = useState({
    qrCodeUrl: "",
    oneTimeToken: "",
    role: "",
  });

  // Verification state
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [verificationData, setVerificationData] = useState({
    email: "",
    user_id: "",
    role: "",
  });

  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get("redirect_uri");

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  });

  // Theme styles
  const containerStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
    borderColor: THEME_COLORS[mode].BORDER,
    color: THEME_COLORS[mode].TEXT,
  };

  const buttonStyles = {
    backgroundColor: THEME_COLORS[mode].PRIMARY,
    color: THEME_COLORS[mode].TEXT_ON_PRIMARY || THEME_COLORS[mode].BACKGROUND,
  };

  const buttonHoverStyles = {
    backgroundColor: THEME_COLORS[mode].PRIMARY_HOVER,
  };

  const socialButtonStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderColor: THEME_COLORS[mode].BORDER,
    color: THEME_COLORS[mode].TEXT,
  };

  const socialButtonHoverStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_HOVER,
  };

  // Social icons using React Icons
  const socialIcons = [
    {
      icon: FaFacebook,
      alt: "Facebook",
      label: "Facebook",
      provider: "facebook",
      color: "#1877F2",
    },
    {
      icon: FaGoogle,
      alt: "Google",
      label: "Google",
      provider: "google",
      color: "#DB4437",
    },
    // {
    //   icon: FaInstagram,
    //   alt: "Instagram",
    //   label: "Instagram",
    //   provider: "instagram",
    //   color: "#E4405F",
    // },
    {
      icon: FaLinkedin,
      alt: "LinkedIn",
      label: "LinkedIn",
      provider: "linkedin",
      color: "#0A66C2",
    },
  ];

  const onSubmit = async (data: LoginFormData) => {
    try {
      setSubmitLoading(true);
      const result = await sdk.login(
        data.email,
        data.password,
        RoleMap[loginType],
        data.rememberMe ?? false
      );

      if (!result.error) {
        localStorage.setItem("role", result.role);
        
        // Check if 2FA is enabled
        if (result.two_factor_authentication) {
          // Get 2FA setup data
          const twoFAResult = await sdk.get2FALoginSetup(
            data.email,
            data.password,
            RoleMap[loginType]
          );

          if (!twoFAResult.error) {
            setTwoFAData({
              qrCodeUrl: twoFAResult.qr_code,
              oneTimeToken: twoFAResult.one_time_token,
              role: RoleMap[loginType],
            });
            setShow2FAModal(true);
            setSubmitLoading(false);
            return;
          } else {
            showToast(
              twoFAResult.message || "Failed to setup 2FA",
              4000,
              ToastStatusEnum.ERROR
            );
            setSubmitLoading(false);
            return;
          }
        }

        // No 2FA required, proceed with normal login
        authDispatch({
          type: "LOGIN",
          payload: { ...result, remember_me: data.rememberMe } as any,
        });
        showToast("Succesfully Logged In", 4000, ToastStatusEnum.SUCCESS);
        localStorage.setItem("token", result.token);
        // Invalidate all queries
        await queryClient.invalidateQueries();

        if (loginType === "athlete") {
          navigate(redirect_uri ?? `/`);
        } else {
          navigate(redirect_uri ?? `/trainer/dashboard`);
        }
      } else {
        setSubmitLoading(false);
        console.log(result);
        // Check if user needs verification
        if (result.requires_verification) {
          setVerificationData({
            email: result.email,
            user_id: result.user_id,
            role: RoleMap[loginType],
          });
          setShowVerificationModal(true);
          return;
        }
        
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field as "email" | "password", {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error: any) {
      setSubmitLoading(false);
      showToast(
        error?.response?.data?.message
          ? error?.response?.data?.message
          : error?.message,
        4000,
        ToastStatusEnum.ERROR
      );
      setError("email", {
        type: "manual",
        message: error?.response?.data?.message
          ? error?.response?.data?.message
          : error?.message,
      });
    }
  };

  const socialLogin = async (type: string) => {
    try {
      setSocialAuthState((prev) => {
        return {
          ...prev,
          loading: true,
          provider: type,
        };
      });
      const result = await sdk.oauthLoginApi(type, RoleMap[loginType]);
      window.open(result, "_self");
    } catch (error: any) {
      showToast(
        error?.response?.data?.message
          ? error?.response?.data?.message
          : error?.message,
        4000,
        ToastStatusEnum.ERROR
      );
    } finally {
      setSocialAuthState((prev) => {
        return {
          ...prev,
          loading: false,
        };
      });
    }
  };

  const handleForgotPassword = () => {
    navigate(`/forgot-password?role=${RoleMap[loginType]}`);
  };
  const onRoleChange = (role: "athlete" | "trainer") => {
    // set localStorage
    localStorage.setItem("login_type", role);

    setLoginType(role);
  };

  // Handle 2FA success
  const handle2FASuccess = (data: any) => {
    authDispatch({
      type: "LOGIN",
      payload: { ...data, remember_me: false } as any,
    });
    showToast("Succesfully Logged In", 4000, ToastStatusEnum.SUCCESS);
    // Invalidate all queries
    queryClient.invalidateQueries();

    if (loginType === "athlete") {
      navigate(redirect_uri ?? `/`);
    } else {
      navigate(redirect_uri ?? `/trainer/dashboard`);
    }
  };

  // Handle 2FA modal close
  const handle2FAModalClose = () => {
    setShow2FAModal(false);
    setTwoFAData({
      qrCodeUrl: "",
      oneTimeToken: "",
      role: "",
    });
  };

  // load initial role from localStorage
  useEffect(() => {
    const role = localStorage.getItem("login_type") as LoginType;
    if (role) {
      setLoginType(role);
    } else {
      localStorage.setItem("login_type", "athlete");
      setLoginType("athlete");
    }
  }, []);

  return (
    <div
      className="min-h-full grid grid-cols-1 grid-rows-1 h-full max-h-full transition-colors duration-200"
      style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND }}
    >
      {/* Main Content */}
      <main className="flex h-full items-center justify-center px-2 py-8">
        <div className="w-full max-w-md sm:max-w-lg flex justify-center">
          {/* Login Container */}
          <div
            className="relative w-full shadow-lg rounded-lg px-8 py-10 sm:px-10 sm:py-12 transition-colors duration-200"
            style={containerStyles}
          >
            {/* Title */}
            <div className="text-center mb-8">
              <h1
                className="text-3xl font-bold transition-colors duration-200"
                style={{ color: THEME_COLORS[mode].TEXT }}
              >
                Login
              </h1>
            </div>

            {/* Login Type Toggle */}
            <div
              className="flex mb-8 rounded-md overflow-hidden border transition-colors duration-200"
              style={{ borderColor: THEME_COLORS[mode].BORDER }}
            >
              <button
                onClick={() => onRoleChange("athlete")}
                className="flex-1 py-2 px-4 text-sm font-semibold transition-colors duration-200"
                style={
                  loginType === "athlete"
                    ? {
                        backgroundColor: THEME_COLORS[mode].PRIMARY,
                        color: THEME_COLORS[mode].BACKGROUND,
                      }
                    : {
                        backgroundColor:
                          THEME_COLORS[mode].BACKGROUND_SECONDARY,
                        color: THEME_COLORS[mode].PRIMARY,
                      }
                }
                onMouseEnter={(e) => {
                  if (loginType !== "athlete") {
                    e.currentTarget.style.backgroundColor =
                      THEME_COLORS[mode].BACKGROUND_HOVER;
                  }
                }}
                onMouseLeave={(e) => {
                  if (loginType !== "athlete") {
                    e.currentTarget.style.backgroundColor =
                      THEME_COLORS[mode].BACKGROUND_SECONDARY;
                  }
                }}
                aria-pressed={loginType === "athlete"}
              >
                Login as Athlete
              </button>
              <button
                onClick={() => onRoleChange("trainer")}
                className="flex-1 py-2 px-4 text-sm font-semibold transition-colors duration-200"
                style={
                  loginType === "trainer"
                    ? {
                        backgroundColor: THEME_COLORS[mode].PRIMARY,
                        color: THEME_COLORS[mode].BACKGROUND,
                      }
                    : {
                        backgroundColor:
                          THEME_COLORS[mode].BACKGROUND_SECONDARY,
                        color: THEME_COLORS[mode].PRIMARY,
                      }
                }
                onMouseEnter={(e) => {
                  if (loginType !== "trainer") {
                    e.currentTarget.style.backgroundColor =
                      THEME_COLORS[mode].BACKGROUND_HOVER;
                  }
                }}
                onMouseLeave={(e) => {
                  if (loginType !== "trainer") {
                    e.currentTarget.style.backgroundColor =
                      THEME_COLORS[mode].BACKGROUND_SECONDARY;
                  }
                }}
                aria-pressed={loginType === "trainer"}
              >
                Login as Trainer
              </button>
            </div>

            {/* Login Form */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Email Field */}
              <MkdInputV2
                name="email"
                type="email"
                register={register}
                errors={errors}
                required
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Label
                    className="transition-colors duration-200"
                    style={{ color: THEME_COLORS[mode].TEXT }}
                  >
                    Email:
                  </MkdInputV2.Label>
                  <MkdInputV2.Field
                    placeholder="Enter Email Address"
                    className="transition-colors duration-200"
                    style={{
                      backgroundColor: THEME_COLORS[mode].INPUT,
                      borderColor: THEME_COLORS[mode].BORDER,
                      color: THEME_COLORS[mode].TEXT,
                    }}
                  />
                  <MkdInputV2.Error />
                </MkdInputV2.Container>
              </MkdInputV2>

              {/* Password Field */}
              <MkdInputV2
                name="password"
                type={showPassword ? "text" : "password"}
                register={register}
                errors={errors}
                required
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Label
                    className="transition-colors duration-200"
                    style={{ color: THEME_COLORS[mode].TEXT }}
                  >
                    Password:
                  </MkdInputV2.Label>
                  <div className="relative">
                    <MkdInputV2.Field
                      placeholder="Enter Password"
                      className="pr-12 transition-colors duration-200"
                      style={{
                        backgroundColor: THEME_COLORS[mode].INPUT,
                        borderColor: THEME_COLORS[mode].BORDER,
                        color: THEME_COLORS[mode].TEXT,
                      }}
                    />

                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      {showPassword ? (
                        <AiOutlineEyeInvisible
                          className="h-5 w-5 cursor-pointer transition-colors duration-200"
                          style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                          onClick={() => setShowPassword(false)}
                        />
                      ) : (
                        <AiOutlineEye
                          className="h-5 w-5 cursor-pointer transition-colors duration-200"
                          style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                          onClick={() => setShowPassword(true)}
                        />
                      )}
                    </div>
                  </div>
                  <MkdInputV2.Error />
                </MkdInputV2.Container>
              </MkdInputV2>

              {/* Remember Me & Forgot Password */}
              <div className="flex items-center justify-between">
                <MkdInputV2
                  name="rememberMe"
                  type="checkbox"
                  register={register}
                  errors={errors}
                  required
                >
                  <MkdInputV2.Container className="flex items-center space-x-2">
                    <MkdInputV2.Field
                      className="transition-colors duration-200"
                      style={{
                        backgroundColor: THEME_COLORS[mode].INPUT,
                        borderColor: THEME_COLORS[mode].BORDER,
                        color: THEME_COLORS[mode].TEXT,
                      }}
                    />
                    <MkdInputV2.Label
                      className="transition-colors duration-200"
                      style={{ color: THEME_COLORS[mode].TEXT }}
                    >
                      Remember me
                    </MkdInputV2.Label>
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
                <button
                  type="button"
                  onClick={handleForgotPassword}
                  className="text-sm font-medium transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].PRIMARY }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.color =
                      THEME_COLORS[mode].PRIMARY_HOVER;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.color = THEME_COLORS[mode].PRIMARY;
                  }}
                >
                  Forgot Password?
                </button>
              </div>

              {/* Login Button */}
              <InteractiveButton
                type="submit"
                loading={submitLoading}
                className="w-full h-12 mt-2 rounded font-semibold text-base transition-colors duration-200"
                style={buttonStyles}
                disabled={submitLoading}
                onMouseEnter={(e) => {
                  if (!submitLoading) {
                    e.currentTarget.style.backgroundColor =
                      buttonHoverStyles.backgroundColor;
                  }
                }}
                onMouseLeave={(e) => {
                  if (!submitLoading) {
                    e.currentTarget.style.backgroundColor =
                      buttonStyles.backgroundColor;
                  }
                }}
              >
                {!submitLoading ? (
                  <>
                    Login as{" "}
                    {loginType.charAt(0).toUpperCase() + loginType.slice(1)}
                  </>
                ) : null}
              </InteractiveButton>
            </form>

            {/* Social Login Section */}
            <div className="mt-10">
              <div
                className="text-center mb-4 text-sm transition-colors duration-200"
                style={{ color: THEME_COLORS[mode].TEXT }}
              >
                Login using your account with
              </div>
              <div className="grid grid-cols-1 gap-4">
                {socialIcons.map((icon) => {
                  const IconComponent = icon.icon;
                  return (
                    <InteractiveButton
                      key={icon.provider}
                      type="button"
                      onClick={() => socialLogin(icon.provider)}
                      className="flex items-center justify-center gap-2 h-10 rounded border transition-colors duration-200 w-full"
                      style={socialButtonStyles}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor =
                          socialButtonHoverStyles.backgroundColor;
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor =
                          socialButtonStyles.backgroundColor;
                      }}
                      disabled={
                        socialAuthState.loading || icon.provider == "instagram"
                      }
                      loading={
                        socialAuthState.loading &&
                        socialAuthState.provider === icon.provider
                      }
                    >
                      <IconComponent
                        className="h-4 w-4"
                        style={{ color: icon.color }}
                      />
                      {socialAuthState.loading &&
                      socialAuthState.provider === icon.provider ? null : (
                        <span
                          className="text-sm font-medium transition-colors duration-200"
                          style={{ color: THEME_COLORS[mode].TEXT }}
                        >
                          {icon.label}
                        </span>
                      )}
                    </InteractiveButton>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* 2FA Login Modal */}
      <TwoFactorLoginModal
        isOpen={show2FAModal}
        onClose={handle2FAModalClose}
        onSuccess={handle2FASuccess}
        qrCodeUrl={twoFAData.qrCodeUrl}
        oneTimeToken={twoFAData.oneTimeToken}
        role={twoFAData.role}
      />

      {/* Email Verification Modal */}
      <EmailVerificationModal
        isOpen={showVerificationModal}
        onClose={() => setShowVerificationModal(false)}
        email={verificationData.email}
        user_id={verificationData.user_id}
        role={verificationData.role}
      />
    </div>
  );
};

export default LoginPage;
