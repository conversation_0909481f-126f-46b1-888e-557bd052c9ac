import React from "react";
import { StarRating } from "@/components/StarRating";
import { MkdButton } from "@/components/MkdButton";
import { useEnrollment } from "@/hooks/useEnrollment";
import { useUserReview } from "@/hooks/useUserReview";
import { useContexts } from "@/hooks/useContexts";

interface ProgramHeaderProps {
  courseName: string;
  trainerName: string;
  rating: number;
  programId?: number | string;
  onWriteReview?: () => void;
  onViewProfile?: () => void;
}

const ProgramHeader: React.FC<ProgramHeaderProps> = ({
  courseName,
  trainerName,
  rating,
  programId,
  onWriteReview,
  onViewProfile,
}) => {
  const { authState } = useContexts();
  const { useCheckProgramSubscription } = useEnrollment();
  const { existingReview } = useUserReview(programId || "");
  
  // Check if user is subscribed to this program
  const subscriptionStatus = useCheckProgramSubscription(programId || "");
  
  // Determine if user can write a review
  const canWriteReview = authState.isAuthenticated && 
    !subscriptionStatus.isLoading && 
    subscriptionStatus.isSubscribed;
  
  console.log(programId);
  
  return (
    <div className="w-full bg-background">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 lg:gap-6 p-4 lg:p-6">
        {/* Left Section - Course Info */}
        <div className="flex-1">
          <h1 className="text-xl lg:text-2xl font-bold text-text mb-2">
            {courseName}
          </h1>
          <p className="text-sm text-primary font-medium">By {trainerName}</p>
        </div>

        {/* Right Section - Rating and Actions */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
          {/* Rating and Actions Container */}
          <div className="flex flex-col gap-3 w-full sm:w-auto">
            {/* Star Rating */}
            <div className="flex items-center justify-start sm:justify-end">
              <StarRating rating={rating} size="md" />
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
              {canWriteReview ? (
                <MkdButton
                  onClick={onWriteReview}
                  className="w-full sm:w-auto bg-primary hover:bg-primary-hover text-white px-4 py-2 text-sm font-semibold"
                >
                  {existingReview ? "Edit Review" : "Write a Review"}
                </MkdButton>
              ) : authState.isAuthenticated ? (
                <MkdButton
                  disabled
                  className="w-full sm:w-auto bg-gray-300 text-gray-500 px-4 py-2 text-sm font-semibold cursor-not-allowed"
                  title="You must be enrolled in this program to write a review"
                >
                  Write a Review
                </MkdButton>
              ) : (
                <MkdButton
                  disabled
                  className="w-full sm:w-auto bg-gray-300 text-gray-500 px-4 py-2 text-sm font-semibold cursor-not-allowed"
                  title="You must be logged in to write a review"
                >
                  Write a Review
                </MkdButton>
              )}
              <MkdButton
                onClick={onViewProfile}
                className="w-full sm:w-auto bg-primary hover:bg-primary-hover text-white px-4 py-2 text-sm font-semibold"
              >
                View Profile
              </MkdButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgramHeader;
