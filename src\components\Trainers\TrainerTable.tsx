import React, { useState } from "react";
import { ChevronDown } from "lucide-react";
import { EditIcon } from "@/assets/svgs";
import { PaginationBar } from "@/components/PaginationBar";
import { TrainerEnrollmentModal } from "@/components/Trainers";
import EditExpertiseModal from "./EditExpertiseModal";
import EditCertificationsModal from "./EditCertificationsModal";
import { Trainer } from "@/interfaces";

interface TrainerTableProps {
  trainers: Trainer[];
  currentPage: number;
  totalPages: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onViewList: (trainerId: number) => void;
  onEditTrainer: (trainerId: number) => void;
  onStatusChange: (trainerId: number, newStatus: Trainer["status"]) => void;
}

const TrainerTable: React.FC<TrainerTableProps> = ({
  trainers,
  currentPage,
  totalPages,
  pageSize,
  onPageChange,
  onViewList,
  onEditTrainer,
  onStatusChange,
}) => {
  // Enrollment modal state
  const [showEnrollmentModal, setShowEnrollmentModal] = useState<{
    trainerId: number;
    trainerName: string;
  } | null>(null);

  // Modal states for expertise and certifications
  const [showExpertiseModal, setShowExpertiseModal] = useState(false);
  const [showCertificationsModal, setShowCertificationsModal] = useState(false);
  const getStatusColor = (status: Trainer["status"]) => {
    switch (status) {
      case 1:
        return "text-green-600 bg-green-50 border-green-200";
      case 0:
      default:
        return "text-gray-600 bg-gray-50 border-gray-200 dark:bg-[#262626] dark:border-[#3A3A3A] dark:text-[#F3F4F6]";
    }
  };

  return (
    <div className="bg-background border border-border rounded-lg shadow-sm overflow-hidden">
      {/* Header with Title and Action Buttons */}
      <div className="px-4 sm:px-6 py-4 border-b border-border flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h2 className="text-2xl font-bold text-text">Trainer</h2>
        <div className="flex flex-col sm:flex-row gap-2">
          <button
            onClick={() => setShowExpertiseModal(true)}
            className="px-4 py-2 text-sm font-semibold text-white bg-primary rounded-md hover:bg-primary-hover transition-colors duration-200"
          >
            Edit Expertise
          </button>
          <button
            onClick={() => setShowCertificationsModal(true)}
            className="px-4 py-2 text-sm font-semibold text-white bg-primary rounded-md hover:bg-primary-hover transition-colors duration-200"
          >
            Edit Certifications
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Table Header */}
          <thead className="bg-background-secondary">
            <tr>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-base font-medium text-text">Name</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-base font-medium text-text">
                    Date joined
                  </span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-base font-medium text-text">Status</span>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-base font-medium text-text">
                  Enrollments
                </span>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-base font-medium text-text">Actions</span>
              </th>
            </tr>
          </thead>

          {/* Table Body */}
          <tbody className="divide-y divide-border">
            {trainers.map((trainer) => (
              <tr
                key={trainer.id}
                className="hover:bg-background-hover transition-colors duration-200"
              >
                <td className="px-4 sm:px-6 py-4 text-base text-text whitespace-nowrap">
                  {trainer?.name}
                </td>
                <td className="px-4 sm:px-6 py-4 text-base text-text whitespace-nowrap">
                  {trainer?.dateAdded}
                </td>
                <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                  <div className="relative">
                    <select
                      value={trainer.status}
                      onChange={(e) =>
                        onStatusChange(
                          trainer.id as number,
                          e.target.value as unknown as Trainer["status"]
                        )
                      }
                      className={`px-3 py-2 pr-8 bg-none rounded-md border text-sm font-medium appearance-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${getStatusColor(trainer.status)}`}
                    >
                      <option value={1}>Active</option>
                      <option value={0}>Inactive</option>
                    </select>
                    {/* <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 pointer-events-none" /> */}
                  </div>
                </td>
                <td className="px-4 sm:px-6 py-4 text-base text-text whitespace-nowrap">
                  <button
                    title="View Enrollments"
                    onClick={() => {
                      setShowEnrollmentModal({
                        trainerId: trainer.id as number,
                        trainerName: trainer.name || "Unknown Trainer",
                      });
                      onViewList(trainer.id as number);
                    }}
                    disabled={!trainer.programs}
                    className="disbaled:opacity-50 disabled:border-none disabled:hover:bg-transparent disabled:text-text px-3 py-1.5 border border-primary text-primary rounded-md hover:bg-primary hover:text-white transition-colors duration-200 text-xs font-medium"
                  >
                    {/* {trainer?.programs ? `View ${trainer.programs}` : 0} */}
                    View
                  </button>
                </td>
                <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => onEditTrainer(trainer.id as number)}
                    className="p-2 text-text-secondary hover:text-text transition-colors duration-200"
                    title="Edit Trainer"
                  >
                    <EditIcon className="w-4 h-4" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-4 sm:px-6 py-4 border-t border-border">
          <PaginationBar
            currentPage={currentPage}
            pageCount={totalPages}
            pageSize={pageSize}
            canPreviousPage={currentPage > 1}
            canNextPage={currentPage < totalPages}
            updatePageSize={() => {}} // Not needed for this implementation
            updateCurrentPage={onPageChange}
            startSize={pageSize}
            multiplier={1}
            showLimit={false}
            canChangeLimit={false}
          />
        </div>
      )}

      {/* Trainer Enrollment Modal */}
      {showEnrollmentModal && (
        <TrainerEnrollmentModal
          isOpen={!!showEnrollmentModal}
          onClose={() => setShowEnrollmentModal(null)}
          trainerId={showEnrollmentModal.trainerId}
          trainerName={showEnrollmentModal.trainerName}
        />
      )}

      {/* Edit Expertise Modal */}
      <EditExpertiseModal
        isOpen={showExpertiseModal}
        onClose={() => setShowExpertiseModal(false)}
      />

      {/* Edit Certifications Modal */}
      <EditCertificationsModal
        isOpen={showCertificationsModal}
        onClose={() => setShowCertificationsModal(false)}
      />
    </div>
  );
};

export default TrainerTable;
