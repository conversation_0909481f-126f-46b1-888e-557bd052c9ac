import { useState, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import {
  useGetPaginateQuery,
  useCreateModelMutation,
  useUpdateModelMutation,
  useDeleteModelMutation,
  useCustomModelQuery,
} from "@/query/shared";
import { useContexts } from "../useContexts";
import { useProfile } from "../useProfile";
import { Exercise, } from "@/interfaces/model.interface";
import { ToastStatusEnum } from "@/utils/Enums";
import { TreeSDKOptions } from "@/utils/TreeSDK";

export type LibraryType = "exercise" | "video";
export type LibraryItem = Exercise;

interface LibraryFilters {
  type?: 1 | 2; // 1 = admin created, 2 = trainer created
  user_id?: number | string;
  search?: string;
}

interface PaginationOptions extends TreeSDKOptions {
  page?: number;
  limit?: number;
  filters?: LibraryFilters;
  sort?: string;
  join?: string | string[];
}

interface UseLibraryProps {
  libraryType: LibraryType;
  autoFetch?: boolean;
  initialPagination?: Partial<PaginationOptions>;
  disable?: {
    type_one?: boolean;
    type_two?: boolean;
    all?: boolean;
  };
  role?: string;
}

export const useLibrary = ({
  libraryType,
  autoFetch = true,
  initialPagination = {},
  disable = {
    type_one: true,
    type_two: true,
    all: true,
  },
  role,
}: UseLibraryProps) => {
  const { showToast, tokenExpireError } = useContexts();
  const { profile } = useProfile();
  const queryClient = useQueryClient();

  const [paginationOptions, setPaginationOptions] = useState<PaginationOptions>(
    {
      page: 1,
      limit: 10,
      filters: {},
      sort: "created_at:desc",
      join: [],
      ...initialPagination,
    }
  );

  // Table name based on library type
  const tableName = libraryType;

  // Build query options for the SDK
  const buildQueryOptions = useCallback(
    (options: PaginationOptions): TreeSDKOptions => {
      const { page = 1, limit = 10, filters = {}, sort, join } = options;

      const queryOptions: TreeSDKOptions = {
        page,
        size: limit, // TreeSDK uses 'size' instead of 'limit'
        filter: [], // TreeSDK uses filter array
        join,
      };

      // Handle sorting
      if (sort) {
        const [field, direction] = sort.split(":");
        queryOptions.order = field;
        queryOptions.direction = direction as "asc" | "desc";
      } else {
        queryOptions.order = "created_at";
        queryOptions.direction = "desc";
      }

      // Apply filters using TreeSDK filter format
      const filterArray: string[] = [];

      if (filters.type) {
        filterArray.push(`kanglink_${tableName}.type,eq,${filters.type}`);
      }

      if (filters.user_id) {
        filterArray.push(`kanglink_${tableName}.user_id,eq,${filters.user_id}`);
      }

      if (filters.search) {
        filterArray.push(`kanglink_${tableName}.name,cs,${filters.search}`); // cs = contains (case-sensitive)
      }

      queryOptions.filter = filterArray;

      return queryOptions;
    },
    []
  );

  // Get paginated library items
  const {
    data: libraryData,
    isLoading: isLoadingLibrary,
    error: libraryError,
    refetch: refetchLibrary,
  } = useGetPaginateQuery(
    tableName,
    buildQueryOptions(paginationOptions),
    role,
    {
      enabled: autoFetch && !disable.all,
      // staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 3,
    }
  );

  // Get admin libraries (type 1)
  const {
    data: adminLibraryData,
    isLoading: isLoadingAdminLibrary,
    refetch: refetchAdminLibrary,
  } = useGetPaginateQuery(
    tableName,
    buildQueryOptions({
      ...paginationOptions,
      filters: { ...paginationOptions.filters, type: 1 },
      join: paginationOptions.join,
    }),
    role,
    {
      enabled: autoFetch && !disable.type_one,
      // staleTime: 5 * 60 * 1000,
      retry: 3,
    }
  );

  // Get current trainer's library (type 2)
  const {
    data: trainerLibraryData,
    isLoading: isLoadingTrainerLibrary,
    refetch: refetchTrainerLibrary,
  } = useGetPaginateQuery(
    tableName,
    buildQueryOptions({
      ...paginationOptions,
      filters: {
        ...paginationOptions.filters,
        type: 2,
        user_id: profile?.id,
      },
      join: paginationOptions.join,
    }),
    role,
    {
      enabled: autoFetch && !disable?.type_two && !!profile?.id,
      // staleTime: 5 * 60 * 1000,
      retry: 3,
    }
  );

  // Create mutation
  const createMutation = useCreateModelMutation(tableName, role, {
    showToast: false,
  });

  // Update mutation
  const updateMutation = useUpdateModelMutation(tableName, role, {
    showToast: false,
  });

  // Delete mutation
  const deleteMutation = useDeleteModelMutation(tableName, role, {
    showToast: false,
  });

  // Custom query for complex operations
  const { mutate: customMutate, isPending: isCustomPending } =
    useCustomModelQuery({ role });

  // Create library item
  const createLibraryItem = useCallback(
    async (data: Partial<LibraryItem>) => {
      try {
        const payload = {
          ...data,
          user_id: profile?.id,
        };

        const result = await createMutation.mutateAsync(payload);

        // Invalidate and refetch queries
        queryClient.invalidateQueries({ queryKey: [tableName], refetchType: "all", exact: false });

        showToast(
          `${libraryType} created successfully`,
          5000,
          ToastStatusEnum.SUCCESS
        );
        return result;
      } catch (error: any) {
        const message = error?.message || `Failed to create ${libraryType}`;
        showToast(message, 5000, ToastStatusEnum.ERROR);
        tokenExpireError(message);
        throw error;
      }
    },
    [
      createMutation,
      profile?.id,
      libraryType,
      queryClient,
      showToast,
      tokenExpireError,
      tableName,
    ]
  );

  // Update library item
  const updateLibraryItem = useCallback(
    async (id: string | number, data: Partial<LibraryItem>) => {
      try {
        const result = await updateMutation.mutateAsync({ id, payload: data });

        // Invalidate and refetch queries
        queryClient.invalidateQueries({ queryKey: [tableName], refetchType: "all", exact: false });

        showToast(
          `${libraryType} updated successfully`,
          5000,
          ToastStatusEnum.SUCCESS
        );
        return result;
      } catch (error: any) {
        const message = error?.message || `Failed to update ${libraryType}`;
        showToast(message, 5000, ToastStatusEnum.ERROR);
        tokenExpireError(message);
        throw error;
      }
    },
    [
      updateMutation,
      libraryType,
      queryClient,
      showToast,
      tokenExpireError,
      tableName,
    ]
  );

  // Delete library item
  const deleteLibraryItem = useCallback(
    async (id: string | number) => {
      try {
        const result = await deleteMutation.mutateAsync(id);

        // Invalidate and refetch queries
        queryClient.invalidateQueries({ queryKey: [tableName], refetchType: "all", exact: false });

        showToast(
          `${libraryType} deleted successfully`,
          5000,
          ToastStatusEnum.SUCCESS
        );
        return result;
      } catch (error: any) {
        const message = error?.message || `Failed to delete ${libraryType}`;
        showToast(message, 5000, ToastStatusEnum.ERROR);
        tokenExpireError(message);
        throw error;
      }
    },
    [
      deleteMutation,
      libraryType,
      queryClient,
      showToast,
      tokenExpireError,
      tableName,
    ]
  );

  // Update pagination options
  const updatePagination = useCallback(
    (newOptions: Partial<PaginationOptions>) => {
      setPaginationOptions((prev) => ({
        ...prev,
        ...newOptions,
      }));
    },
    []
  );

  // Search library items
  const searchLibraryItems = useCallback(
    (searchTerm: string) => {
      updatePagination({
        page: 1,
        filters: {
          ...paginationOptions.filters,
          search: searchTerm,
        },
      });
    },
    [paginationOptions.filters, updatePagination]
  );

  // Filter by type (admin or trainer created)
  const filterByType = useCallback(
    (type: 1 | 2) => {
      updatePagination({
        page: 1,
        filters: {
          ...paginationOptions.filters,
          type,
        },
      });
    },
    [paginationOptions.filters, updatePagination]
  );

  // Clear filters
  const clearFilters = useCallback(() => {
    updatePagination({
      page: 1,
      filters: {},
    });
  }, [updatePagination]);

  // Refresh all data
  const refreshAll = useCallback(() => {
    refetchLibrary();
    refetchAdminLibrary();
    refetchTrainerLibrary();
  }, [refetchLibrary, refetchAdminLibrary, refetchTrainerLibrary]);

  return {
    // Data
    libraryData: libraryData?.data || [],
    adminLibraryData: adminLibraryData?.data || [],
    trainerLibraryData: trainerLibraryData?.data || [],

    adminPagination: {
      total: adminLibraryData?.total || 0,
      page: adminLibraryData?.page || 1,
      limit: adminLibraryData?.limit || 10,
      num_pages: adminLibraryData?.num_pages || 0,
    },
    trainerPagination: {
      total: trainerLibraryData?.total || 0,
      page: trainerLibraryData?.page || 1,
      limit: trainerLibraryData?.limit || 10,
      num_pages: trainerLibraryData?.num_pages || 0,
    },

    // Pagination info
    pagination: {
      total: libraryData?.total || 0,
      page: libraryData?.page || 1,
      limit: libraryData?.limit || 10,
      num_pages: libraryData?.num_pages || 0,
    },

    // Loading states
    isLoading: isLoadingLibrary,
    isLoadingAdmin: isLoadingAdminLibrary,
    isLoadingTrainer: isLoadingTrainerLibrary,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    isCustomPending,

    // Error states
    error: libraryError,

    // Actions
    createLibraryItem,
    updateLibraryItem,
    deleteLibraryItem,

    // Pagination & filtering
    updatePagination,
    searchLibraryItems,
    filterByType,
    clearFilters,

    // Utilities
    refreshAll,
    paginationOptions,

    // Custom operations
    customMutate,
  };
};
