import { ChevronDown } from "lucide-react";

interface SaleDiscountData {
  type: "fixed" | "percentage";
  value: number | null;
  apply_to_all: boolean;
}

interface SaleDiscountSectionProps {
  saleDiscount?: SaleDiscountData;
  onChange?: (saleDiscount: SaleDiscountData) => void;
}

const SaleDiscountSection = ({
  saleDiscount = {
    type: "fixed",
    value: null,
    apply_to_all: false,
  },
  onChange,
}: SaleDiscountSectionProps) => {
  return (
    <div className="bg-background border border-border rounded-md p-6 shadow-sm">
      <div className="space-y-4">
        <h3 className="text-sm font-medium text-text">Sale Discount</h3>

        <div className="flex gap-4 items-center">
          {/* Currency/Percentage Selector */}
          <div className="relative">
            <select
              value={saleDiscount.type}
              onChange={(e) =>
                onChange?.({
                  ...saleDiscount,
                  type: e.target.value as "fixed" | "percentage",
                })
              }
              className="appearance-none px-3 py-2 pr-8 border border-border rounded-md bg-input text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              style={{
                backgroundImage: "none",
                WebkitAppearance: "none",
                MozAppearance: "none",
              }}
            >
              <option value="fixed">$</option>
              <option value="percentage">%</option>
            </select>
            <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text pointer-events-none" />
          </div>

          {/* Value Input */}
          <input
            type="number"
            value={Number(saleDiscount.value) || ""}
            onChange={(e) =>
              onChange?.({
                ...saleDiscount,
                value: Number(e.target.value),
              })
            }
            className="w-16 px-3 bg-none appearance-none py-2 border border-border rounded-md bg-input text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder=""
          />

          {/* Apply to All Button */}
          <button
            onClick={() =>
              onChange?.({
                ...saleDiscount,
                apply_to_all: !saleDiscount.apply_to_all,
              })
            }
            className={`px-4 py-2 border border-border rounded-md text-sm transition-colors duration-200 ${
              saleDiscount.apply_to_all
                ? "bg-primary text-white"
                : "bg-input text-text hover:bg-background-hover"
            }`}
          >
            Apply to all
          </button>
        </div>
      </div>
    </div>
  );
};

export default SaleDiscountSection;
