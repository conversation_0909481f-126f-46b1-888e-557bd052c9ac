import React from "react";

interface ContainerProps {
  children: React.ReactNode;
  className?: string;
}

const Container = ({ children, className }: ContainerProps) => {
  return (
    <div
      className={`min-h-full h-full max-h-full overflow-y-auto w-full items-center space-y-5 ${className}`}
    >
      {children}

      <div className="h-5"></div>
    </div>
  );
};

export default Container;
