# Page Title Implementation

## Overview
Implemented dynamic page title updates that change the browser tab title based on the content being viewed. This improves user experience and SEO by showing relevant page titles.

## Implementation

### 1. Custom Hook: usePageTitle
**Location**: `src/hooks/usePageTitle/usePageTitle.tsx`

A flexible hook for managing document titles:

```typescript
export const usePageTitle = ({
  title,
  suffix = "KSL Fitness",
  fallback = "KSL Fitness"
}: UsePageTitleOptions) => {
  useEffect(() => {
    if (title) {
      document.title = suffix ? `${title} - ${suffix}` : title;
    } else {
      document.title = fallback;
    }

    // Cleanup: Reset title when component unmounts
    return () => {
      document.title = "KSL Fitness";
    };
  }, [title, suffix, fallback]);
};
```

### 2. Specialized Hooks

#### useProgramPageTitle
For program detail pages:
```typescript
export const useProgramPageTitle = (programName?: string) => {
  usePageTitle({
    title: programName,
    suffix: "KSL Fitness",
    fallback: "Program Details - KSL Fitness"
  });
};
```

#### useTrainerPageTitle
For trainer profile pages:
```typescript
export const useTrainerPageTitle = (trainerName?: string) => {
  usePageTitle({
    title: trainerName,
    suffix: "Trainer Profile - KSL Fitness",
    fallback: "Trainer Profile - KSL Fitness"
  });
};
```

## Usage Examples

### Program Details Page
**File**: `src/pages/Athlete/View/ViewAthleteProgramPage.tsx`

```typescript
import { useProgramPageTitle } from "@/hooks/usePageTitle";

const ViewAthleteProgramPage = () => {
  const { data: programData } = useProgramDetails(programId);
  
  // Update page title when program data is loaded
  useProgramPageTitle(programData?.program_name);
  
  // ... rest of component
};
```

**Result**:
- Loading: "Program Details - KSL Fitness"
- Loaded: "Upper Body Strength Program - KSL Fitness"

### Trainer Details Page
**File**: `src/pages/Athlete/View/ViewAthleteTrainerDetailsPage.tsx`

```typescript
import { useTrainerPageTitle } from "@/hooks/usePageTitle";

const ViewAthleteTrainerDetailsPage = () => {
  const { data: trainerData } = useTrainerDetails(trainerId);
  
  // Update page title when trainer data is loaded
  useTrainerPageTitle(
    trainerData?.full_name || 
    (trainerData?.first_name && trainerData?.last_name 
      ? `${trainerData.first_name} ${trainerData.last_name}` 
      : undefined)
  );
  
  // ... rest of component
};
```

**Result**:
- Loading: "Trainer Profile - KSL Fitness"
- Loaded: "John Smith - Trainer Profile - KSL Fitness"

## Title Formats

### Program Pages
| State | Title Format |
|-------|-------------|
| Loading | "Program Details - KSL Fitness" |
| Loaded | "{Program Name} - KSL Fitness" |
| Error | "Program Details - KSL Fitness" |

### Trainer Pages
| State | Title Format |
|-------|-------------|
| Loading | "Trainer Profile - KSL Fitness" |
| Loaded | "{Trainer Name} - Trainer Profile - KSL Fitness" |
| Error | "Trainer Profile - KSL Fitness" |

### Default/Fallback
| Context | Title |
|---------|-------|
| Component unmount | "KSL Fitness" |
| No data | Respective fallback title |

## Benefits

1. **Better UX**: Users can identify tabs by content
2. **SEO Improvement**: Search engines get meaningful page titles
3. **Browser History**: Meaningful titles in browser history
4. **Bookmarks**: Descriptive bookmark names
5. **Accessibility**: Screen readers announce meaningful page titles

## Technical Features

- **Automatic Cleanup**: Resets title when component unmounts
- **Fallback Handling**: Shows appropriate fallback when data is loading
- **Reusable**: Can be used across different page types
- **Type Safe**: Full TypeScript support
- **Reactive**: Updates automatically when data changes

## Future Enhancements

1. **SEO Meta Tags**: Extend to manage meta descriptions and keywords
2. **Social Media**: Add Open Graph and Twitter Card support
3. **Breadcrumbs**: Integrate with breadcrumb navigation
4. **Analytics**: Track page title changes for analytics
5. **Internationalization**: Support for multiple languages

## Usage Guidelines

### Do's
- Use descriptive, meaningful titles
- Include the site name for branding
- Keep titles under 60 characters for SEO
- Use consistent formatting across pages

### Don'ts
- Don't use generic titles like "Page" or "Details"
- Don't forget fallback titles for loading states
- Don't make titles too long (affects SEO)
- Don't include sensitive information in titles

## Testing

To test the implementation:
1. Navigate to a program details page
2. Check browser tab title shows "Program Details - KSL Fitness" while loading
3. Verify title updates to "{Program Name} - KSL Fitness" when loaded
4. Navigate away and verify title resets
5. Test with different programs and trainers

## Browser Support

This implementation works in all modern browsers that support:
- `document.title` property (universal support)
- React hooks (React 16.8+)
- ES6 template literals
