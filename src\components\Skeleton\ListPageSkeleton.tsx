import React from "react";
import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

interface ListPageSkeletonProps {
  /** Show page header with title and subtitle */
  showHeader?: boolean;
  /** Show search and filter section */
  showFilters?: boolean;
  /** Show stats cards at the top */
  showStats?: boolean;
  /** Number of stat cards to show */
  statsCount?: number;
  /** Number of table rows to show */
  rows?: number;
  /** Number of table columns to show */
  columns?: number;
  /** Show pagination at the bottom */
  showPagination?: boolean;
  /** Custom className for the container */
  className?: string;
}

const ListPageSkeleton: React.FC<ListPageSkeletonProps> = ({
  showHeader = true,
  showFilters = true,
  showStats = false,
  statsCount = 4,
  rows = 8,
  columns = 6,
  showPagination = true,
  className = "",
}) => {
  const { state } = useTheme();
  const mode = state?.theme || "light";

  return (
    <div
      className={`min-h-screen bg-background-secondary py-6 px-2 sm:px-4 md:px-8 ${className}`}
    >
      <SkeletonTheme
        baseColor={THEME_COLORS[mode].TEXT_DISABLED}
        highlightColor={THEME_COLORS[mode].BACKGROUND_HOVER}
      >
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Page Header Skeleton */}
          {showHeader && (
            <div className="space-y-2">
              <Skeleton height={32} width="40%" />
              <Skeleton height={16} width="60%" />
            </div>
          )}

          {/* Stats Cards Skeleton */}
          {showStats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {Array.from({ length: statsCount }).map((_, index) => (
                <div
                  key={index}
                  className="bg-background rounded-lg border border-border p-4 space-y-3"
                >
                  <div className="flex items-center justify-between">
                    <Skeleton height={16} width="60%" />
                    <Skeleton height={24} width={24} />
                  </div>
                  <Skeleton height={28} width="40%" />
                  <Skeleton height={14} width="80%" />
                </div>
              ))}
            </div>
          )}

          {/* Search and Filters Skeleton */}
          {showFilters && (
            <div className="bg-background rounded-lg border border-border p-4 space-y-4">
              <div className="flex flex-col md:flex-row gap-4">
                {/* Search Input */}
                <div className="flex-1 space-y-2">
                  <Skeleton height={16} width="15%" />
                  <Skeleton height={40} width="100%" />
                </div>

                {/* Filter Dropdowns */}
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="space-y-2">
                    <Skeleton height={16} width="20%" />
                    <Skeleton height={40} width="150px" />
                  </div>
                  <div className="space-y-2">
                    <Skeleton height={16} width="15%" />
                    <Skeleton height={40} width="120px" />
                  </div>
                </div>

                {/* Filter Button */}
                <div className="flex items-end">
                  <Skeleton height={40} width="100px" />
                </div>
              </div>
            </div>
          )}

          {/* Table Skeleton */}
          <div className="bg-background rounded-lg border border-border overflow-hidden">
            {/* Table Header */}
            <div className="border-b border-border p-4">
              <div
                className="grid gap-4"
                style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
              >
                {Array.from({ length: columns }).map((_, index) => (
                  <Skeleton
                    key={index}
                    height={16}
                    width={`${Math.random() * 30 + 60}%`}
                  />
                ))}
              </div>
            </div>

            {/* Table Rows */}
            <div className="divide-y divide-border">
              {Array.from({ length: rows }).map((_, rowIndex) => (
                <div key={rowIndex} className="p-4">
                  <div
                    className="grid gap-4"
                    style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
                  >
                    {Array.from({ length: columns }).map((_, colIndex) => (
                      <div key={colIndex} className="flex items-center">
                        {colIndex === 0 ? (
                          // First column often has an avatar or icon
                          <div className="flex items-center space-x-3">
                            <Skeleton
                              height={32}
                              width={32}
                              style={{ borderRadius: "50%" }}
                            />
                            <Skeleton height={16} width="80%" />
                          </div>
                        ) : colIndex === columns - 1 ? (
                          // Last column often has action buttons
                          <div className="flex space-x-2">
                            <Skeleton height={32} width={32} />
                            <Skeleton height={32} width={32} />
                          </div>
                        ) : (
                          // Regular data columns
                          <Skeleton
                            height={16}
                            width={`${Math.random() * 40 + 50}%`}
                          />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Pagination Skeleton */}
          {showPagination && (
            <div className="flex items-center justify-between">
              <Skeleton height={16} width="20%" />
              <div className="flex items-center space-x-2">
                <Skeleton height={32} width={32} />
                <Skeleton height={32} width={32} />
                <Skeleton height={32} width={32} />
                <span className="mx-2">
                  <Skeleton height={16} width="10px" />
                </span>
                <Skeleton height={32} width={32} />
                <Skeleton height={32} width={32} />
              </div>
            </div>
          )}
        </div>
      </SkeletonTheme>
    </div>
  );
};

export default ListPageSkeleton;
