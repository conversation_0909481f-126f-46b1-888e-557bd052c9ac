import {
  Coupon,
  CouponUsage,
  Discount,
  ProgramDiscount,
} from "./model.interface";

export interface TransformedProgramData {
  id: string;
  courseName: string;
  trainerName: string;
  rating: number;
  description: string[];
  payment_plan: string[];
  trainer: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
    full_name: string;
    photo: string | null;
  };
  details: Array<{
    label: string;
    value: string;
  }>;
  splits: Array<{
    id: string;
    name: string;
    description: string;
    subscriptionPrice: number;
    buyPrice: number;
  }>;
  reviews: any[];
  currency: string;
  image: string | null;
  review_count: number;
  program_discount?: ProgramDiscount | null;
  discount?: Discount | null;
  coupon?: Coupon | null;
  coupon_usage_stats?: CouponUsage[] | [];
  days_for_preview?: number;
  approval_date?: string;
}
