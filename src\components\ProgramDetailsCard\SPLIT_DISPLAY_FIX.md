# Split Display Fix for Program Details Card

## Problem
The "Split" row in the Program Details Card was not properly handling long split names when there were multiple splits. Long split names like "Foundation Phase, Strength Building Phase, Peak Performance Phase" were overflowing and wrapping awkwardly, making the text hard to read.

## Root Cause
1. **Data Transformation**: All split names were being joined with commas regardless of length
2. **Layout Issues**: The card used a fixed horizontal layout that couldn't accommodate long text
3. **No Text Wrapping**: No proper handling for text that exceeded the available space

## Solution Implemented

### 1. Smart Data Formatting (ViewAthleteProgramPage.tsx)
Updated the split value transformation logic to handle different scenarios:

```typescript
{
  label: "Split",
  value: (() => {
    if (!programData.splits || programData.splits.length === 0) {
      return "Not specified";
    }
    
    const splitNames = programData.splits.map((s) => s.title);
    
    // If there's only one split, show it directly
    if (splitNames.length === 1) {
      return splitNames[0];
    }
    
    // If multiple splits but reasonable length, join them
    if (splitNames.join(", ").length <= 60) {
      return splitNames.join(", ");
    }
    
    // For many splits, show count + first split
    if (splitNames.length > 3) {
      return `${splitNames.length} splits: ${splitNames[0]}, ...`;
    }
    
    // For 2-3 long splits, return them all (card will format nicely)
    return splitNames.join(", ");
  })(),
}
```

### 2. Enhanced Display Logic (ProgramDetailsCard.tsx)
Added intelligent rendering for split values:

```typescript
const renderValue = (detail: ProgramDetail) => {
  if (detail.label === "Split") {
    // Handle "X splits: ..." format
    if (detail.value.includes(" splits: ")) {
      return <span className="...break-words">{detail.value}</span>;
    }
    
    // Handle multiple splits with long text
    if (detail.value.length > 50 && detail.value.includes(", ")) {
      const splits = detail.value.split(", ");
      return (
        <div className="space-y-1">
          {splits.map((split, idx) => (
            <div key={idx} className="break-words">
              • {split.trim()}
            </div>
          ))}
        </div>
      );
    }
  }
  
  return <span className="...break-words">{detail.value}</span>;
};
```

### 3. Adaptive Layout
Updated the layout to use different arrangements based on content:

```typescript
const isLongSplit = 
  detail.label === "Split" && 
  detail.value.length > 50 && 
  detail.value.includes(", ") &&
  !detail.value.includes(" splits: ");

// Use vertical layout for long splits, horizontal for others
className={
  isLongSplit
    ? "flex flex-col gap-2"
    : "flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1"
}
```

## Display Scenarios

### 1. Single Split
**Input**: `["Upper Body"]`
**Output**: `"Upper Body"`
**Layout**: Horizontal (label | value)

### 2. Multiple Short Splits
**Input**: `["Push", "Pull", "Legs"]`
**Output**: `"Push, Pull, Legs"`
**Layout**: Horizontal (label | value)

### 3. Multiple Long Splits (2-3 splits)
**Input**: `["Foundation Phase", "Strength Building Phase", "Peak Performance Phase"]`
**Output**: 
```
Split
• Foundation Phase
• Strength Building Phase  
• Peak Performance Phase
```
**Layout**: Vertical with bullet points

### 4. Many Splits (4+ splits)
**Input**: `["Phase 1", "Phase 2", "Phase 3", "Phase 4", "Phase 5"]`
**Output**: `"5 splits: Phase 1, ..."`
**Layout**: Horizontal (label | value)

## Benefits

1. **Better Readability**: Long split names are displayed in a clean, readable format
2. **Space Efficient**: Adapts layout based on content length
3. **Consistent Design**: Maintains the card's visual consistency
4. **Responsive**: Works well on different screen sizes
5. **User Friendly**: Clear indication when there are many splits

## Technical Features

- **Break-word CSS**: Prevents text overflow
- **Conditional Rendering**: Different display logic based on content
- **Adaptive Layout**: Vertical vs horizontal based on content length
- **Bullet Points**: Clear visual separation for multiple items
- **Truncation with Count**: Shows total count when there are many splits

## Testing Scenarios

1. **Single split**: Should display normally in horizontal layout
2. **2-3 short splits**: Should display as comma-separated in horizontal layout
3. **2-3 long splits**: Should display as bullet list in vertical layout
4. **4+ splits**: Should display count with first split name
5. **Very long single split**: Should wrap text properly

## Future Enhancements

- Add tooltip showing all splits when truncated
- Add expand/collapse functionality for many splits
- Consider showing split descriptions on hover
- Add visual indicators for different split types
