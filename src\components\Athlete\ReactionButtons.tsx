import { useState } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { 
  HeartIcon, 
  ChatBubbleOvalLeftIcon,
  ShareIcon,
  HandThumbUpIcon 
} from "@heroicons/react/24/outline";
import { 
  HeartIcon as HeartSolidIcon, 
  HandThumbUpIcon as HandThumbUpSolidIcon 
} from "@heroicons/react/24/solid";

interface Reaction {
  type: "like" | "love" | "comment" | "share";
  count: number;
  isActive?: boolean;
}

interface ReactionButtonsProps {
  reactions: Reaction[];
  onReactionClick?: (type: string, isActive: boolean) => void;
  size?: "sm" | "md" | "lg";
  layout?: "horizontal" | "vertical";
}

const ReactionButtons = ({
  reactions,
  onReactionClick,
  size = "md",
  layout = "horizontal",
}: ReactionButtonsProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [localReactions, setLocalReactions] = useState(reactions);

  const sizeClasses = {
    sm: "p-1.5 text-xs",
    md: "p-2 text-sm",
    lg: "p-3 text-base",
  };

  const iconSizes = {
    sm: "w-3 h-3",
    md: "w-4 h-4",
    lg: "w-5 h-5",
  };

  const handleReactionClick = (reactionType: string, currentlyActive: boolean) => {
    const newActiveState = !currentlyActive;
    
    setLocalReactions(prev =>
      prev.map(reaction =>
        reaction.type === reactionType
          ? {
              ...reaction,
              isActive: newActiveState,
              count: newActiveState ? reaction.count + 1 : Math.max(0, reaction.count - 1),
            }
          : reaction
      )
    );

    onReactionClick?.(reactionType, newActiveState);
  };

  const getReactionIcon = (type: string, isActive: boolean) => {
    const iconClass = iconSizes[size];
    
    switch (type) {
      case "like":
        return isActive ? (
          <HandThumbUpSolidIcon className={`${iconClass} text-blue-500`} />
        ) : (
          <HandThumbUpIcon className={iconClass} />
        );
      case "love":
        return isActive ? (
          <HeartSolidIcon className={`${iconClass} text-red-500`} />
        ) : (
          <HeartIcon className={iconClass} />
        );
      case "comment":
        return <ChatBubbleOvalLeftIcon className={iconClass} />;
      case "share":
        return <ShareIcon className={iconClass} />;
      default:
        return <HandThumbUpIcon className={iconClass} />;
    }
  };

  const getReactionColor = (type: string, isActive: boolean) => {
    if (isActive) {
      switch (type) {
        case "like":
          return "#3B82F6"; // blue-500
        case "love":
          return "#EF4444"; // red-500
        default:
          return THEME_COLORS[mode].PRIMARY;
      }
    }
    return THEME_COLORS[mode].TEXT_SECONDARY;
  };

  const containerClass = layout === "horizontal" 
    ? "flex items-center gap-1 sm:gap-2" 
    : "flex flex-col gap-1";

  return (
    <div className={containerClass}>
      {localReactions.map((reaction) => { 

        if(reaction.type !== "like") {
          return null;
        }

        return (
        <button
          key={reaction.type}
          onClick={() => handleReactionClick(reaction.type, reaction.isActive || false)}
          className={`inline-flex items-center gap-1.5 rounded-lg transition-all duration-200 hover:opacity-80 ${sizeClasses[size]}`}
          style={{
            backgroundColor: reaction.isActive 
              ? THEME_COLORS[mode].BACKGROUND_SECONDARY 
              : "transparent",
            color: getReactionColor(reaction.type, reaction.isActive || false),
          }}
        >
          {getReactionIcon(reaction.type, reaction.isActive || false)}
          {reaction.count > 0 && (
            <span className="font-medium min-w-[1ch]">
              {reaction.count}
            </span>
          )}
        </button>)
      })}
    </div>
  );
};

export default ReactionButtons;
