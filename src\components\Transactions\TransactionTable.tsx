import React, { useState } from "react";
import { ChevronDown } from "lucide-react";
import { PaginationBar } from "@/components/PaginationBar";
import { InteractiveButton } from "@/components/InteractiveButton";
import { Transaction } from "@/interfaces/model.interface";
import PayoutSettingsModal from "./PayoutSettingsModal";

interface TransactionTableProps {
  transactions: Transaction[];
  currentPage: number;
  totalPages: number;
  pageSize: number;
  isLoading?: boolean;
  error?: Error | null;
  onPageChange: (page: number) => void;
  onDownloadPDF: () => void;
  onPayoutSettings: () => void;
  onDeleteTransaction: (transactionId: number) => void;
  onStatusChange: (transactionId: number, newStatus: string) => void;
  onRefresh?: () => void;
}

const TransactionTable: React.FC<TransactionTableProps> = ({
  transactions,
  currentPage,
  totalPages,
  pageSize,
  isLoading = false,
  error = null,
  onPageChange,
  onDownloadPDF,
  onPayoutSettings,
  onDeleteTransaction: _onDeleteTransaction,
  onStatusChange,
  onRefresh,
}) => {
  const [isPayoutModalOpen, setIsPayoutModalOpen] = useState(false);

  const handlePayoutSettings = () => {
    setIsPayoutModalOpen(true);
  };

  const handlePayoutModalClose = () => {
    setIsPayoutModalOpen(false);
  };

  const handlePayoutModalSuccess = () => {
    // Optionally call the parent's onPayoutSettings callback
    onPayoutSettings?.();
  };
  const getStatusColor = (paymentStatus: string) => {
    console.log(paymentStatus)
    switch (paymentStatus) {
      case "paid":
        return "text-primary bg-background-secondary border-primary";
      case "pending":
        return "text-warning bg-background-secondary border-warning";
      case "refunded":
        return "text-text bg-background-secondary border-border";
      case "failed":
        return "text-error bg-background-secondary border-error";
      default:
        return "text-text bg-background-secondary border-border";
    }
  };

  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
  };


  return (
    <div className="bg-background border border-border rounded-lg shadow-sm overflow-hidden">
      {/* Header with Title and Action Buttons */}
      <div className="px-4 sm:px-6 py-4 border-b border-border flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h2 className="text-2xl font-bold text-text">Transactions</h2>

        <div className="flex flex-col sm:flex-row gap-2">
          <InteractiveButton
            onClick={onDownloadPDF}
            type="button"
            className="!h-9 px-4 bg-primary text-white text-sm font-semibold"
          >
            Download PDF
          </InteractiveButton>
          <InteractiveButton
            onClick={handlePayoutSettings}
            type="button"
            className="!h-9 px-4 bg-primary text-white text-sm font-semibold"
          >
            Payout Settings
          </InteractiveButton>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Table Header */}
          <thead className="bg-background-secondary">
            <tr>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">ID</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">Trainer</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">Date</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-sm font-medium text-text">Status</span>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-xs font-medium text-text">
                  Total Amount
                </span>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-xs font-medium text-text">
                  Coach Revenue
                </span>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-xs font-medium text-text">
                  Your Earning
                </span>
              </th>
              {/* <th className="px-4 sm:px-6 py-3 text-right">
                <span className="text-sm font-medium text-text">Actions</span>
              </th> */}
            </tr>
          </thead>

          {/* Table Body */}
          <tbody className="divide-y divide-border">
            {isLoading ? (
              <tr>
                <td colSpan={7} className="px-4 sm:px-6 py-12 text-center">
                  <div className="flex flex-col items-center space-y-3">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <div className="text-text-secondary">
                      Loading transactions...
                    </div>
                  </div>
                </td>
              </tr>
            ) : error ? (
              <tr>
                <td colSpan={7} className="px-4 sm:px-6 py-12 text-center">
                  <div className="flex flex-col items-center space-y-4">
                    <div className="text-red-600">
                      Error loading transactions: {error.message}
                    </div>
                    {onRefresh && (
                      <button
                        onClick={onRefresh}
                        className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      >
                        Retry
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ) : transactions.length === 0 ? (
              <tr>
                <td colSpan={7} className="px-4 sm:px-6 py-12 text-center">
                  <div className="text-text-secondary">
                    No transactions found
                  </div>
                </td>
              </tr>
            ) : (
              transactions.map((transaction) => (
                <tr
                  key={transaction.id}
                  className="hover:bg-background-hover transition-colors duration-200"
                >
                  <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                    {transaction.id}
                  </td>
                  <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                    {transaction.trainer_name || "Unknown"}
                  </td>
                  <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                    {formatDate(transaction.created_at || "")}
                  </td>
                  <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                    {/* {transaction.payment_status === "refunded" ? (
                      <div className="px-3 py-2 rounded-md bg-background border border-border">
                        <span className="text-xs font-normal text-text">
                          Refunded
                        </span>
                      </div>
                    ) : ( */}
                      <div className="relative">
                        <select
                          value={transaction?.payment_status || "pending"}
                          onChange={(e) => {
                            onStatusChange(
                              transaction.id as number,
                              e.target.value
                            );
                          }}
                          disabled
                          className={`bg-none px-3 py-2 rounded border text-xs font-normal appearance-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${getStatusColor(transaction.payment_status || "")}`}
                        >
                          <option value="pending">Pending</option>
                          <option value="paid">Completed</option>
                          <option value="refunded">Refunded</option>
                          <option value="failed">Failed</option>
                        </select>
                        {/* <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 pointer-events-none" /> */}
                      </div>
                    {/* )} */}
                  </td>
                  <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                    <div className="flex flex-col">
                      <span>
                        {formatCurrency(
                          transaction.total_amount || 0,
                          transaction.currency
                        )}
                      </span>
                      {transaction.discount_amount &&
                        transaction.discount_amount > 0 && (
                          <span
                            className="text-xs text-green-600"
                            title={
                              transaction.discount_details || "Discount applied"
                            }
                          >
                            -
                            {formatCurrency(
                              transaction.discount_amount,
                              transaction.currency
                            )}{" "}
                            discount
                          </span>
                        )}
                      {transaction.affiliate_code && (
                        <span
                          className="text-xs text-blue-600"
                          title="Affiliate referral"
                        >
                          Ref: {transaction.affiliate_code}
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                    {formatCurrency(
                      transaction.coach_revenue || 0,
                      transaction.currency
                    )}
                  </td>
                  <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                    {formatCurrency(
                      transaction.platform_earning || 0,
                      transaction.currency
                    )}
                  </td>
                  {/* <td className="px-4 sm:px-6 py-4 whitespace-nowrap text-right">
                  <button
                    onClick={() =>
                      onDeleteTransaction(transaction.id as number)
                    }
                    className="p-2 text-text-secondary hover:text-red-600 transition-colors duration-200"
                    title="Delete Transaction"
                  >
                    <TrashIcon className="w-3.5 h-3.5" />
                  </button>
                </td> */}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-4 sm:px-6 py-4 border-t border-border">
          <PaginationBar
            currentPage={currentPage}
            pageCount={totalPages}
            pageSize={pageSize}
            canPreviousPage={currentPage > 1}
            canNextPage={currentPage < totalPages}
            updatePageSize={() => {}} // Not needed for this implementation
            updateCurrentPage={onPageChange}
            startSize={pageSize}
            multiplier={1}
            canChangeLimit={false}
          />
        </div>
      )}

      {/* Payout Settings Modal */}
      <PayoutSettingsModal
        isOpen={isPayoutModalOpen}
        onClose={handlePayoutModalClose}
        onSuccess={handlePayoutModalSuccess}
      />
    </div>
  );
};

export default TransactionTable;
