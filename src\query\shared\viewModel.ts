import { useQuery } from "@tanstack/react-query";
import { queryKeys } from "../queryKeys";
import { useSDK } from "@/hooks/useSDK";
import { TreeSDKOptions } from "@/utils/TreeSDK";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";

export const useViewModelQuery = (
  table: string,
  id: string | number,
  options?: TreeSDKOptions
) => {
  const { tdk } = useSDK();
  const { tokenExpireError, showToast } = useContexts();

  const queryFn = async (table: string, id: string | number) => {
    const response = await tdk.getOne(table, id, options);
    if (response?.error) {
      const message = response?.message || "An error occurred";
      showToast(message, 5000, ToastStatusEnum.ERROR);
      tokenExpireError(message);
    }
    return response.data ?? response?.model;
  };

  return useQuery({
    queryKey: [(queryKeys as any)?.[table]?.byId, table, id],
    enabled: !!id && !!table,
    queryFn: () => queryFn(table, id),
  });
};

export const useGetOneFilterModelQuery = (
  table: string,
  options?: TreeSDKOptions
) => {
  const { tdk } = useSDK();
  const { tokenExpireError, showToast } = useContexts();

  const queryFn = async (table: string, options?: TreeSDKOptions) => {
    const response = await tdk.getOneFilter(table, options);
    if (response?.error) {
      const message = response?.message || "An error occurred";
      showToast(message, 5000, ToastStatusEnum.ERROR);
      tokenExpireError(message);
    }
    return response.data ?? response?.model;
  };

  return useQuery({
    queryKey: [(queryKeys as any)?.[table]?.byId, table, options],
    enabled: !!table,
    queryFn: () => queryFn(table, options),
  });
};
