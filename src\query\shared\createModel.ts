import { useMutation, useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "../queryKeys";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import { ApiResponse } from "@/utils/TreeSDK";

export const useCreateModelMutation = (
  table: string,
  role?: any,
  config?: any
) => {
  const { tdk } = useSDK({ role });
  const { showToast, tokenExpireError } = useContexts();
  const queryClient = useQueryClient();

  const mutationFn = async (
    payload: Record<string, any>
  ): Promise<ApiResponse> => {
    const response = await tdk.create(table, payload);
    return response;
  };

  return useMutation({
    mutationFn,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.all, table],
        exact: false,
        refetchType: "all",
      });
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.list, table],
        exact: false,
        refetchType: "all",
      });
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.many, table],
        exact: false,
        refetchType: "all",
      });
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.paginate, table],
        exact: false,
        refetchType: "all",
      });
      if (config?.showToast) {
        showToast("Created successfully", 5000, ToastStatusEnum.SUCCESS);
      }
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || error?.message;
      console.error(error);
      showToast(message, 5000, ToastStatusEnum.ERROR);
      tokenExpireError(message);
    },
  });
};
