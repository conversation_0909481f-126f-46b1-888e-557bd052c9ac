# Athlete Enrollment System

- programs have splits
- these splits are the plans for a trainer, we need to be able to create plans that are one time or subscription based using the same split and integrate with stripe

- an athlete subscribes or pays one time for a split
- a split can support both;
  so athlete <PERSON> can subscribe and keep being charged monthly
  and Athlete <PERSON> can decide that he wants to pay one time for the split and have it for a lifetime

- athletes that buy a split one time do not get updates if the trainer updates the split
- athletes that subscribe to a split get updates if the trainer updates the split
- athletes that subscribe to a split get charged monthly
- athletes that buy a split one time do not get charged monthly

# we already the enrollment schema

```
export interface Enrollment {
  id?: number | string;
  trainer_id?: number | string;
  athlete_id?: number | string;
  program_id?: number | string;
  split_id?: number | string;
  payment_type?: "subscription" | "one_time";
  amount?: number;
  currency?: string;
  enrollment_date?: string;
  expiry_date?: string | null;
  status?: "active" | "expired" | "cancelled" | "pending";
  payment_status?: "paid" | "pending" | "failed" | "refunded";
  created_at?: string;
  updated_at?: string;

  // Related entities
  trainer?: User;
  athlete?: User;
  program?: Program;
  split?: Split;
}
```

# below are the tables of program and its relationships

```
// User interface (matching backend specification and existing patterns)
export interface User {
  id?: number | string;
  email?: string;
  first_name?: string;
  last_name?: string;
  middle_name?: string; // Added for name parsing
  full_name?: string; // Alternative field name from backend spec
  photo?: string | null;
  role?: "trainer" | "member" | "super_admin";
  role_id?: "trainer" | "member" | "super_admin"; // Alternative field name
  // Status values: 0 = Inactive, 1 = Active, 2 = Suspended
  status?: 0 | 1 | 2;
  two_factor_enabled?: boolean; // Added for 2FA status
  data?: any | Record<string, any>; // For storing additional user data
  created_at?: string;
  updated_at?: string;
  program?: Array<Program>;
}

export interface Program {
  id?: number | string;
  user_id?: number | string;
  image?: string;
  program_name?: string;
  type_of_program?: string;
  program_description?: string;
  payment_plan?: string[];
  track_progress?: boolean;
  allow_comments?: boolean;
  allow_private_messages?: boolean;
  target_levels?: string[];
  split_program?: number;
  currency?: string;
  days_for_preview?: number;
  created_at?: string;
  updated_at?: string;
  status?: "draft" | "pending_approval" | "published" | "rejected" | "archived";
  splits?: Split[];
}

export interface Split {
  id?: string | number;
  program_id?: string | number;
  equipment_required?: string;
  title?: string;
  full_price?: number;
  subscription?: number;
  created_at?: string;
  updated_at?: string;
  weeks?: Week[];
}

export interface Week {
  id?: string | number;
  split_id?: string | number;
  title?: string;
  week_order?: number;
  equipment_required?: string;
  created_at?: string;
  updated_at?: string;
  days?: Day[];
}

export interface Day {
  id?: string | number;
  week_id?: string | number;
  title?: string;
  day_order?: number;
  is_rest_day?: boolean;
  created_at?: string;
  updated_at?: string;
  sessions?: Session[];
}

export interface Session {
  id?: string | number;
  day_id?: string | number;
  title?: string;
  session_order?: number;
  created_at?: string;
  updated_at?: string;
  exercises?: ExerciseInstance[];
}

export interface Video {
  id?: number | string;
  name?: string;
  type?: number;
  video_type?: string;
  url?: string;
  user_id?: number | null;
  created_at?: string;
  updated_at?: string;
}

export interface Exercise {
  id?: number | string;
  name?: string;
  type?: number;
  exercise_type?: string;
  user_id?: number | null;
  video_id?: number | null;
  video?: Video;
  created_at?: string;
  updated_at?: string;
}

export interface ExerciseInstance {
  id?: number | string;
  user_id?: number;
  session_id?: number | string;
  exercise_id?: number | string;
  video_id?: number | null;
  video?: Video;
  exercise?: Exercise;
  sets?: string | null;
  reps_or_time?: string | null;
  reps_time_type?: "reps" | "time";
  exercise_details?: string | null;
  rest_duration_minutes?: number;
  rest_duration_seconds?: number;
  label?: string | null;
  label_number?: string;
  is_linked?: boolean;
  exercise_order?: number;
  created_at?: string;
  updated_at?: string;
}
```

how do we save data when an athlete buys one time OR when they subscribe to a split?
we need to implement this
payment and subscriptions need to be handled using stripe
