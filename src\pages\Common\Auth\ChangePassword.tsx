import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { InteractiveButton } from "@/components/InteractiveButton";
import { MkdPasswordInput } from "@/components/MkdPasswordInput";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { useToast } from "@/hooks/useToast";
import { baseUrl } from "@/utils/config";

// Validation schema
const schema = yup.object({
  password: yup
    .string()
    .required("Password is required")
    .min(8, "Password must be at least 8 characters")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
      "Password must contain uppercase, lowercase, number, and special character"
    ),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("password")], "Passwords must match")
    .required("Confirm Password is required"),
});

interface FormData {
  password: string;
  confirmPassword: string;
}

const ChangePassword = () => {
  const { state } = useTheme();
  const mode = state?.theme;
  const navigate = useNavigate();
  const toast = useToast();
  const [searchParams] = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [resetToken, setResetToken] = useState<string | null>(null);
  const role = searchParams.get("role") || "trainer";

  // Check for reset token on component mount
  useEffect(() => {
    const token = localStorage.getItem("reset_token");
    if (!token) {
      toast.error(
        "Invalid access. Please start the password reset process again."
      );
      navigate(`/forgot-password?role=${role}`);
      return;
    }
    setResetToken(token);
  }, [navigate, toast, role]);

  // React Hook Form setup
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  // Theme styles
  const containerStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
  };

  const cardStyles = {
    backgroundColor: THEME_COLORS[mode].CARD_BG,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  // Handle form submission
  const onSubmit = async (data: FormData) => {
    if (!resetToken) {
      toast.error("Reset token not found. Please start the process again.");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(
        `${baseUrl}/v2/api/kanglink/custom/trainer/reset-password`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            password: data.password,
            confirm_password: data.confirmPassword,
            reset_token: resetToken,
          }),
        }
      );

      const result = await response.json();

      if (result.error) {
        toast.error(result.message || "Failed to reset password");
        return;
      }

      // Clear the reset token from localStorage
      localStorage.removeItem("reset_token");

      toast.success(
        "Password reset successfully! Please login with your new password."
      );
      navigate(`/login?role=${role}`);
    } catch (error) {
      console.error("Password reset error:", error);
      toast.error("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="min-h-screen bg-background-secondary transition-colors duration-200"
      style={containerStyles}
    >
      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
        <div
          className="w-full max-w-md bg-card-bg border border-border rounded-lg shadow-lg p-6 sm:p-8 transition-colors duration-200"
          style={cardStyles}
        >
          {/* Title */}
          <div className="text-center mb-6 sm:mb-8">
            <h2 className="text-xl sm:text-2xl font-bold text-text font-inter">
              Change password
            </h2>
          </div>

          {/* Form */}
          <form
            onSubmit={handleSubmit(onSubmit)}
            className={`${errors.password ? "space-y-10" : "space-y-6"}`}
          >
            {/* Password Field */}
            <MkdPasswordInput
              name="password"
              label="Enter Password"
              register={register}
              errors={errors}
              required
              placeholder="Enter your new password"
            />

            {/* Confirm Password Field */}
            <MkdPasswordInput
              name="confirmPassword"
              label="Confirm New Password"
              register={register}
              errors={errors}
              required
              placeholder="Confirm your new password"
            />

            {/* Submit Button */}
            <InteractiveButton
              type="submit"
              loading={isLoading}
              disabled={isLoading}
              className="w-full bg-primary hover:bg-primary-hover text-white py-3 px-4 rounded-md font-semibold"
            >
              {!isLoading && <>Reset Password</>}
            </InteractiveButton>
          </form>

          {/* Login Link */}
          <div className="mt-6 text-center">
            <Link
              to="/login"
              className="text-sm text-primary hover:text-primary-hover transition-colors duration-200"
            >
              Back to Sign In
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ChangePassword;
