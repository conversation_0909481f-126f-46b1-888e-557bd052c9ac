/**
 * Comprehensive URL validation utility
 * Validates URLs with proper protocol, domain, and path structure
 */

export const isValidUrl = (url: string): boolean => {
  if (!url || typeof url !== 'string') {
    return false;
  }

  const trimmedUrl = url.trim();
  if (!trimmedUrl) {
    return false;
  }

  try {
    // Use the URL constructor for basic validation
    const urlObj = new URL(trimmedUrl);
    
    // Check for valid protocol (http or https)
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return false;
    }

    // Check for valid hostname (domain)
    if (!urlObj.hostname || urlObj.hostname.length === 0) {
      return false;
    }

    // Additional regex validation for more specific requirements
    const urlRegex = /^https?:\/\/([\w-]+\.)+[\w-]+(\/[\w\-./?%&=]*)?$/;
    return urlRegex.test(trimmedUrl);
  } catch (error) {
    // URL constructor throws error for invalid URLs
    return false;
  }
};

/**
 * Video URL specific validation
 * Additional checks for common video file extensions and video hosting platforms
 */
export const isValidVideoUrl = (url: string): boolean => {
  if (!isValidUrl(url)) {
    return false;
  }

  const trimmedUrl = url.trim().toLowerCase();
  
  // Common video file extensions
  const videoExtensions = [
    '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v', '.3gp', '.ogv'
  ];
  
  // Common video hosting platforms
  const videoPlatforms = [
    'youtube.com', 'youtu.be', 'vimeo.com', 'dailymotion.com', 'twitch.tv',
    'facebook.com', 'instagram.com', 'tiktok.com', 'linkedin.com'
  ];

  // Check if URL contains video file extension
  const hasVideoExtension = videoExtensions.some(ext => trimmedUrl.includes(ext));
  
  // Check if URL is from a known video platform
  const isFromVideoPlatform = videoPlatforms.some(platform => trimmedUrl.includes(platform));

  return hasVideoExtension || isFromVideoPlatform;
};

/**
 * Get URL validation error message
 */
export const getUrlValidationError = (url: string): string | null => {
  if (!url.trim()) {
    return 'Video URL is required';
  }
  
  if (!isValidUrl(url)) {
    return 'Please enter a valid URL (e.g., https://example.com/video.mp4)';
  }
  
  if (!isValidVideoUrl(url)) {
    return 'Please enter a valid video URL (supports common video formats and platforms)';
  }
  
  return null;
}; 