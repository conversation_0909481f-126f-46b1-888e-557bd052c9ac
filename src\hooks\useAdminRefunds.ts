import { useQuery } from "@tanstack/react-query";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { RestAPIMethodEnum } from "@/utils/Enums";

// Admin Refund Request Interface (matching API response)
export interface AdminRefundRequest {
  id: number;
  enrollment_id: number;
  amount: number;
  currency: string;
  reason: string;
  status: "pending" | "approved" | "rejected" | "processed";
  requested_at: string;
  processed_at?: string;
  admin_notes?: string;
  refund_amount?: number;
  stripe_refund_id?: string;
  program: {
    name: string;
    split_name?: string;
  };
  athlete: {
    email: string;
    full_name: string;
  };
  trainer: {
    full_name: string;
  };
  enrollment: {
    enrollment_date: string;
    payment_status: string;
    stripe_payment_intent_id?: string;
  };
  processed_by?: number;
}

export interface AdminRefundListResponse {
  error: boolean;
  data: {
    refund_requests: AdminRefundRequest[];
    pagination: {
      current_page: number;
      per_page: number;
      total: number;
      total_pages: number;
    };
  };
}

export interface AdminRefundDetailResponse {
  error: boolean;
  data: AdminRefundRequest;
}

export interface RefundDecisionPayload {
  decision: "approve" | "reject";
  admin_notes?: string;
}

export interface RefundDecisionResponse {
  error: boolean;
  message: string;
  data: {
    id: number;
    status: string;
    processed_at: string;
    admin_notes?: string;
  };
}

export interface ProcessRefundPayload {
  refund_amount?: number;
}

export interface ProcessRefundResponse {
  error: boolean;
  message: string;
  data: {
    refund_request_id: number;
    stripe_refund_id: string;
    refund_amount: number;
    currency: string;
    status: string;
    processed_at: string;
  };
}

// Query parameters for fetching refund requests
export interface AdminRefundQueryParams {
  page?: number;
  limit?: number;
  status?: "pending" | "approved" | "rejected" | "processed";
  trainer_name?: string;
  date_from?: string;
  date_to?: string;
}

// Hook to fetch all refund requests with pagination and filters
export const useAdminRefundRequests = (params: AdminRefundQueryParams = {}) => {
  const { mutateAsync } = useCustomModelQuery({
    role: "super_admin",
    showToast: false,
  });

  return useQuery({
    queryKey: ["admin-refund-requests", params],
    queryFn: async (): Promise<AdminRefundListResponse> => {
      // Build query string from parameters
      const queryParams = new URLSearchParams();

      if (params.page) queryParams.append("page", params.page.toString());
      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (params.status) queryParams.append("status", params.status);
      if (params.trainer_name)
        queryParams.append("trainer_name", params.trainer_name);
      if (params.date_from) queryParams.append("date_from", params.date_from);
      if (params.date_to) queryParams.append("date_to", params.date_to);

      const queryString = queryParams.toString();
      const endpoint = `/v2/api/kanglink/custom/admin/refund/requests${queryString ? `?${queryString}` : ""}`;

      const response = await mutateAsync({
        method: RestAPIMethodEnum.GET,
        endpoint,
      });

      if (response.error) {
        throw new Error(response.message || "Failed to fetch refund requests");
      }

      return response as AdminRefundListResponse;
    },
    refetchInterval: 30000, // Refetch every 30 seconds for real-time updates
    staleTime: 10000, // Consider data stale after 10 seconds
  });
};

// Hook to fetch a specific refund request details
export const useAdminRefundDetail = (refundId: number | null) => {
  const { mutateAsync } = useCustomModelQuery({
    role: "super_admin",
    showToast: false,
  });

  return useQuery({
    queryKey: ["admin-refund-detail", refundId],
    queryFn: async (): Promise<AdminRefundDetailResponse> => {
      if (!refundId) {
        throw new Error("Refund ID is required");
      }

      const response = await mutateAsync({
        method: RestAPIMethodEnum.GET,
        endpoint: `/v2/api/kanglink/custom/admin/refund/request/${refundId}`,
      });

      if (response.error) {
        throw new Error(response.message || "Failed to fetch refund details");
      }

      return response as AdminRefundDetailResponse;
    },
    enabled: !!refundId, // Only run query if refundId is provided
  });
};

// Hook to approve or reject a refund request
export const useRefundDecision = () => {
  const { mutateAsync } = useCustomModelQuery({
    role: "super_admin",
    showToast: true,
  });

  return {
    makeDecision: async (
      refundId: number,
      payload: RefundDecisionPayload
    ): Promise<RefundDecisionResponse> => {
      const response = await mutateAsync({
        method: RestAPIMethodEnum.PUT,
        endpoint: `/v2/api/kanglink/custom/admin/refund/request/${refundId}/decision`,
        body: payload,
      });

      if (response.error) {
        throw new Error(
          response.message || "Failed to process refund decision"
        );
      }

      return response as RefundDecisionResponse;
    },
  };
};

// Hook to process an approved refund (execute the actual refund)
export const useProcessRefund = () => {
  const { mutateAsync } = useCustomModelQuery({
    role: "super_admin",
    showToast: true,
  });

  return {
    processRefund: async (
      refundId: number,
      payload: ProcessRefundPayload = {}
    ): Promise<ProcessRefundResponse> => {
      const response = await mutateAsync({
        method: RestAPIMethodEnum.POST,
        endpoint: `/v2/api/kanglink/custom/admin/refund/request/${refundId}/process`,
        body: payload,
      });

      if (response.error) {
        throw new Error(response.message || "Failed to process refund");
      }

      return response as ProcessRefundResponse;
    },
  };
};
