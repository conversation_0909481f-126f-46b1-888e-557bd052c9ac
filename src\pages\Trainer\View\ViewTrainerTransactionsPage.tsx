import { ThemeStyles } from "@/components/ThemeStyles";
import { useContexts } from "@/hooks/useContexts";
import { useEffect, useState } from "react";
import {
  TransactionStatsCards,
  EarningsGraph,
  StripeConnectSetup,
  WithdrawalModal,
  // TransactionHistory,
} from "@/components/Transactions";

const ViewTrainerTransactionsPage = () => {
  const { globalDispatch } = useContexts();
  const [isWithdrawalModalOpen, setIsWithdrawalModalOpen] = useState(false);

  const handleRefresh = () => {
    // This will trigger refresh in child components
  };

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "transaction",
      },
    });
  }, [globalDispatch]);

  const handleWithdrawFunds = () => {
    setIsWithdrawalModalOpen(true);
  };

  const handleWithdrawalSuccess = () => {
    handleRefresh();
  };

  return (
    <>
      <ThemeStyles />
      <div className="relative flex flex-col gap-6 px-4 py-6 w-full max-w-[1200px] mx-auto min-h-screen bg-background text-text">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h1 className="font-bold text-2xl sm:text-3xl text-text dark:text-gray-100">
            Transactions
          </h1>
          <button
            onClick={handleWithdrawFunds}
            className="flex w-full sm:w-auto justify-center items-center px-6 py-3 rounded bg-green-400 hover:bg-green-500 text-white font-semibold text-base transition-colors duration-200"
          >
            Withdraw Funds
          </button>
        </div>

        {/* Stats Cards */}
        <TransactionStatsCards onRefresh={handleRefresh} />

        {/* Main Content - Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Payment Settings Section */}
          <div className="rounded-lg shadow-sm border border-border bg-secondary p-6 dark:bg-neutral-800 dark:border-[#3a3a3a]">
            <StripeConnectSetup onRefresh={handleRefresh} />
          </div>

          {/* Earning Graph Section */}
          <EarningsGraph onRefresh={handleRefresh} />
        </div>

        {/* Transaction History Section */}
        {/* <TransactionHistory onRefresh={handleRefresh} /> */}

        {/* Withdrawal Modal */}
        <WithdrawalModal
          isOpen={isWithdrawalModalOpen}
          onClose={() => setIsWithdrawalModalOpen(false)}
          onSuccess={handleWithdrawalSuccess}
        />
      </div>
    </>
  );
};

export default ViewTrainerTransactionsPage;
