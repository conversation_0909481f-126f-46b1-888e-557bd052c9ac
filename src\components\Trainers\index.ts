import { lazy } from "react";

export const Trainers = lazy(() => import("./Trainers.tsx"));
export const TrainerFilters = lazy(() => import("./TrainerFilters"));
export const TrainerTable = lazy(() => import("./TrainerTable"));
export const TrainerEnrollmentModal = lazy(
  () => import("./TrainerEnrollmentModal")
);
export const EditExpertiseModal = lazy(() => import("./EditExpertiseModal"));
export const EditCertificationsModal = lazy(
  () => import("./EditCertificationsModal")
);

export * from "./types";
