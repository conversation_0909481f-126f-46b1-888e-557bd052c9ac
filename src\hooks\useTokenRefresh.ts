import { useCallback, useEffect, useState } from 'react';
import { tokenManager } from '@/utils/TokenManager';
import { useContexts } from '@/hooks/useContexts';

export interface UseTokenRefreshReturn {
  ensureValidToken: () => Promise<boolean>;
  tokenStatus: {
    hasToken: boolean;
    isExpired: boolean;
    canRefresh: boolean;
    expiresIn: number;
  };
  refreshToken: () => Promise<boolean>;
  isRefreshing: boolean;
}

/**
 * Hook for managing token refresh functionality
 * Provides methods to ensure valid tokens and get token status
 */
export const useTokenRefresh = (): UseTokenRefreshReturn => {
  const { tokenExpireError } = useContexts();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [tokenStatus, setTokenStatus] = useState(() => tokenManager.getTokenStatus());

  // Update token status periodically
  useEffect(() => {
    const updateStatus = () => {
      setTokenStatus(tokenManager.getTokenStatus());
    };

    // Update immediately
    updateStatus();

    // Update every 30 seconds
    const interval = setInterval(updateStatus, 30000);

    return () => clearInterval(interval);
  }, []);

  /**
   * Ensure the current token is valid, refresh if necessary
   * @returns Promise<boolean> indicating if token is valid
   */
  const ensureValidToken = useCallback(async (): Promise<boolean> => {
    try {
      setIsRefreshing(true);
      const isValid = await tokenManager.ensureValidToken();
      
      if (!isValid) {
        // Token is invalid and couldn't be refreshed
        tokenExpireError('TOKEN_EXPIRED');
        return false;
      }

      // Update token status after successful validation/refresh
      setTokenStatus(tokenManager.getTokenStatus());
      return true;
    } catch (error) {
      console.error('Error ensuring valid token:', error);
      tokenExpireError('TOKEN_EXPIRED');
      return false;
    } finally {
      setIsRefreshing(false);
    }
  }, [tokenExpireError]);

  /**
   * Manually refresh the token
   * @returns Promise<boolean> indicating success
   */
  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      setIsRefreshing(true);
      const result = await tokenManager.silentTokenRefresh();
      
      if (!result.success) {
        console.error('Manual token refresh failed:', result.error);
        tokenExpireError('TOKEN_EXPIRED');
        return false;
      }

      // Update token status after successful refresh
      setTokenStatus(tokenManager.getTokenStatus());
      return true;
    } catch (error) {
      console.error('Error during manual token refresh:', error);
      tokenExpireError('TOKEN_EXPIRED');
      return false;
    } finally {
      setIsRefreshing(false);
    }
  }, [tokenExpireError]);

  return {
    ensureValidToken,
    tokenStatus,
    refreshToken,
    isRefreshing,
  };
};

/**
 * Higher-order function that wraps API calls with automatic token refresh
 * @param apiCall The API function to wrap
 * @returns Wrapped API function that ensures valid token before execution
 */
export const withTokenRefresh = <T extends (...args: any[]) => Promise<any>>(
  apiCall: T
): T => {
  return (async (...args: Parameters<T>) => {
    const isValid = await tokenManager.ensureValidToken();
    
    if (!isValid) {
      throw new Error('TOKEN_EXPIRED');
    }

    return apiCall(...args);
  }) as T;
};

/**
 * Hook that provides a wrapped version of SDK methods with automatic token refresh
 */
export const useSDKWithTokenRefresh = () => {
  const { ensureValidToken } = useTokenRefresh();

  /**
   * Wrap any SDK method with automatic token refresh
   * @param sdkMethod The SDK method to wrap
   * @returns Wrapped method that ensures valid token before execution
   */
  const wrapSDKMethod = useCallback(<T extends (...args: any[]) => Promise<any>>(
    sdkMethod: T
  ): T => {
    return (async (...args: Parameters<T>) => {
      const isValid = await ensureValidToken();
      
      if (!isValid) {
        throw new Error('TOKEN_EXPIRED');
      }

      return sdkMethod(...args);
    }) as T;
  }, [ensureValidToken]);

  return { wrapSDKMethod, ensureValidToken };
};
