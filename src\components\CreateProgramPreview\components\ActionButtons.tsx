import React from "react";
import { InteractiveButton } from "@/components/InteractiveButton";

interface ActionButtonsProps {
  onCancel?: () => void;
  onSubmit?: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  onCancel,
  onSubmit,
}) => {
  return (
    <div className="flex justify-end gap-4 mt-8 pt-6">
      <InteractiveButton
        onClick={onCancel}
        className="bg-background border border-border text-text hover:bg-background-hover px-6 py-2.5"
      >
        Back
      </InteractiveButton>
      
      <InteractiveButton
        onClick={onSubmit}
        className="bg-primary text-white hover:bg-primary-hover px-6 py-2.5"
      >
        Done
      </InteractiveButton>
    </div>
  );
};

export default ActionButtons;
