---
description: 
globs: 
alwaysApply: false
---
using the figma selection of light and dark mode, make the design.

* update the page to fit design
* all screen responsiveness
* with light and dark mode implemented
* simply check [ThemeStyles.tsx](mdc:src/components/ThemeStyles/ThemeStyles.tsx), [ThemeConstants.ts](mdc:src/context/Theme/ThemeConstants.ts) and [tailwind.config.ts](mdc:tailwind.config.ts) for theme classes
* use [useTheme.tsx](mdc:src/hooks/useTheme/useTheme.tsx) only if classes can not be used



[ListTrainerProgramPage.tsx](mdc:src/pages/Trainer/List/ListTrainerProgramPage.tsx)
design:
light - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=89-1429&m=dev
dark - https://www.figma.com/design/lPW7WFhcrM3vP4MrOYPWPb/Kanga-Sportlink-Design?node-id=112-1770&m=dev
