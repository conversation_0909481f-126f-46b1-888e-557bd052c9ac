-- ============================================================================
-- Trainer Program Management Database Schema
-- ============================================================================
-- This file contains the database schema for trainer program creation and management
-- Supports multi-step program creation with draft/publish functionality
-- ============================================================================
-- CORE PROGRAM TABLES
-- ============================================================================
-- Main programs table
CREATE TABLE trainer_programs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  trainer_id BIGINT NOT NULL,
  program_name VARCHAR(255) NOT NULL,
  type_of_program VARCHAR(100) NOT NULL,
  program_description TEXT NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  days_for_preview INT DEFAULT 1,
  track_progress BOOLEAN DEFAULT FALSE,
  allow_comments BOOLEAN DEFAULT FALSE,
  allow_private_messages BOOLEAN DEFAULT FALSE,
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  published_at TIMESTAMP NULL,
  INDEX idx_trainer_id (trainer_id),
  INDEX idx_status (status),
  INDEX idx_type_of_program (type_of_program),
  INDEX idx_created_at (created_at),
  UNIQUE KEY unique_trainer_program_name (trainer_id, program_name)
);
-- Payment plans for programs
CREATE TABLE program_payment_plans (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  program_id BIGINT NOT NULL,
  payment_type ENUM('oneTime', 'monthly') NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (program_id) REFERENCES trainer_programs(id) ON DELETE CASCADE,
  UNIQUE KEY unique_program_payment_type (program_id, payment_type),
  INDEX idx_program_id (program_id)
);
-- Target levels for programs
CREATE TABLE program_target_levels (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  program_id BIGINT NOT NULL,
  level ENUM('beginner', 'intermediate', 'expert') NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (program_id) REFERENCES trainer_programs(id) ON DELETE CASCADE,
  UNIQUE KEY unique_program_level (program_id, level),
  INDEX idx_program_id (program_id)
);
-- Program splits (pricing tiers)
CREATE TABLE program_splits (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  program_id BIGINT NOT NULL,
  split_id VARCHAR(100) NOT NULL,
  -- UUID from frontend
  title VARCHAR(255) NOT NULL,
  full_price DECIMAL(10, 2) NULL,
  subscription_price DECIMAL(10, 2) NULL,
  sort_order INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (program_id) REFERENCES trainer_programs(id) ON DELETE CASCADE,
  UNIQUE KEY unique_program_split_id (program_id, split_id),
  INDEX idx_program_id (program_id),
  INDEX idx_split_id (split_id)
);
-- ============================================================================
-- PROGRAM STRUCTURE TABLES
-- ============================================================================
-- Program weeks
CREATE TABLE program_weeks (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  program_id BIGINT NOT NULL,
  split_id VARCHAR(100) NOT NULL,
  week_id VARCHAR(100) NOT NULL,
  -- UUID from frontend
  name VARCHAR(255) NOT NULL,
  sort_order INT DEFAULT 0,
  is_collapsed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (program_id) REFERENCES trainer_programs(id) ON DELETE CASCADE,
  INDEX idx_program_id (program_id),
  INDEX idx_split_id (split_id),
  INDEX idx_week_id (week_id),
  UNIQUE KEY unique_program_split_week (program_id, split_id, week_id)
);
-- Program days
CREATE TABLE program_days (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  week_id BIGINT NOT NULL,
  day_id VARCHAR(100) NOT NULL,
  -- UUID from frontend
  name VARCHAR(255) NOT NULL,
  is_rest_day BOOLEAN DEFAULT FALSE,
  sort_order INT DEFAULT 0,
  is_collapsed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (week_id) REFERENCES program_weeks(id) ON DELETE CASCADE,
  INDEX idx_week_id (week_id),
  INDEX idx_day_id (day_id),
  UNIQUE KEY unique_week_day (week_id, day_id)
);
-- Program sessions
CREATE TABLE program_sessions (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  day_id BIGINT NOT NULL,
  session_id VARCHAR(100) NOT NULL,
  -- UUID from frontend
  name VARCHAR(255) NOT NULL,
  session_letter VARCHAR(10) NOT NULL,
  session_number INT NOT NULL,
  linked_session_id VARCHAR(100) NULL,
  sort_order INT DEFAULT 0,
  is_collapsed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (day_id) REFERENCES program_days(id) ON DELETE CASCADE,
  INDEX idx_day_id (day_id),
  INDEX idx_session_id (session_id),
  INDEX idx_linked_session_id (linked_session_id),
  UNIQUE KEY unique_day_session (day_id, session_id)
);
-- Program exercises
CREATE TABLE program_exercises (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  session_id BIGINT NOT NULL,
  exercise_id VARCHAR(100) NOT NULL,
  -- UUID from frontend
  name VARCHAR(255) NOT NULL,
  sets VARCHAR(50),
  reps_or_time VARCHAR(50),
  reps_time_type ENUM('reps', 'time') DEFAULT 'reps',
  video_url TEXT,
  exercise_details TEXT,
  rest_duration_minutes INT DEFAULT 0,
  rest_duration_seconds INT DEFAULT 0,
  linked_exercise_id VARCHAR(100) NULL,
  is_linked BOOLEAN DEFAULT FALSE,
  sort_order INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES program_sessions(id) ON DELETE CASCADE,
  INDEX idx_session_id (session_id),
  INDEX idx_exercise_id (exercise_id),
  INDEX idx_linked_exercise_id (linked_exercise_id),
  UNIQUE KEY unique_session_exercise (session_id, exercise_id)
);
-- ============================================================================
-- EXERCISE AND VIDEO LIBRARY TABLES
-- ============================================================================
-- Exercise library
CREATE TABLE exercise_library (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(255) NOT NULL,
  created_by ENUM('admin', 'trainer') NOT NULL,
  trainer_id BIGINT NULL,
  -- NULL for admin exercises
  category VARCHAR(100),
  muscle_groups JSON,
  -- Array of muscle groups
  equipment JSON,
  -- Array of equipment needed
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_created_by (created_by),
  INDEX idx_trainer_id (trainer_id),
  INDEX idx_category (category),
  INDEX idx_name (name),
  INDEX idx_is_active (is_active)
);
-- Video library
CREATE TABLE video_library (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(255) NOT NULL,
  url TEXT NOT NULL,
  created_by ENUM('admin', 'trainer') NOT NULL,
  trainer_id BIGINT NULL,
  -- NULL for admin videos
  duration INT NULL,
  -- Duration in seconds
  thumbnail_url TEXT,
  file_size BIGINT NULL,
  -- File size in bytes
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_created_by (created_by),
  INDEX idx_trainer_id (trainer_id),
  INDEX idx_name (name),
  INDEX idx_is_active (is_active)
);
-- ============================================================================
-- CONFIGURATION TABLES
-- ============================================================================
-- Program types/categories
CREATE TABLE program_types (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  value VARCHAR(100) NOT NULL UNIQUE,
  label VARCHAR(255) NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  sort_order INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_value (value),
  INDEX idx_is_active (is_active)
);
-- Supported currencies
CREATE TABLE supported_currencies (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(3) NOT NULL UNIQUE,
  name VARCHAR(100) NOT NULL,
  symbol VARCHAR(10) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  sort_order INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_code (code),
  INDEX idx_is_active (is_active)
);
-- ============================================================================
-- PROGRAM METADATA TABLE
-- ============================================================================
-- Additional program metadata for step two
CREATE TABLE program_metadata (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  program_id BIGINT NOT NULL UNIQUE,
  equipment_required TEXT,
  program_description TEXT,
  -- Detailed description from step two
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (program_id) REFERENCES trainer_programs(id) ON DELETE CASCADE,
  INDEX idx_program_id (program_id)
);
-- ============================================================================
-- AUDIT AND TRACKING TABLES
-- ============================================================================
-- Program audit log
CREATE TABLE program_audit_log (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  program_id BIGINT NOT NULL,
  action_type ENUM(
    'CREATE',
    'UPDATE',
    'DELETE',
    'PUBLISH',
    'ARCHIVE',
    'DUPLICATE'
  ) NOT NULL,
  entity_type ENUM(
    'PROGRAM',
    'SPLIT',
    'WEEK',
    'DAY',
    'SESSION',
    'EXERCISE'
  ) NOT NULL,
  entity_id VARCHAR(100),
  -- Can be database ID or frontend UUID
  old_values JSON,
  -- Previous values (for updates)
  new_values JSON,
  -- New values
  changed_by BIGINT NOT NULL,
  -- trainer_id who made the change
  change_reason VARCHAR(500),
  -- Optional reason for the change
  ip_address VARCHAR(45),
  -- IP address of the user
  user_agent TEXT,
  -- Browser/client information
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (program_id) REFERENCES trainer_programs(id) ON DELETE CASCADE,
  INDEX idx_program_id (program_id),
  INDEX idx_changed_by (changed_by),
  INDEX idx_created_at (created_at),
  INDEX idx_action_type (action_type),
  INDEX idx_entity_type (entity_type)
);
-- Program access log (for tracking views, downloads, etc.)
CREATE TABLE program_access_log (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  program_id BIGINT NOT NULL,
  user_id BIGINT,
  -- NULL for anonymous access
  access_type ENUM('VIEW', 'DOWNLOAD', 'PREVIEW', 'PURCHASE') NOT NULL,
  ip_address VARCHAR(45),
  user_agent TEXT,
  referrer TEXT,
  accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (program_id) REFERENCES trainer_programs(id) ON DELETE CASCADE,
  INDEX idx_program_id (program_id),
  INDEX idx_user_id (user_id),
  INDEX idx_access_type (access_type),
  INDEX idx_accessed_at (accessed_at)
);
-- ============================================================================
-- VIEWS FOR EASY DATA RETRIEVAL
-- ============================================================================
-- Complete program overview with all basic information
CREATE VIEW program_overview AS
SELECT tp.id as program_id,
  tp.trainer_id,
  tp.program_name,
  tp.type_of_program,
  tp.program_description,
  tp.currency,
  tp.days_for_preview,
  tp.track_progress,
  tp.allow_comments,
  tp.allow_private_messages,
  tp.status,
  tp.created_at,
  tp.updated_at,
  tp.published_at,
  -- Payment plans (aggregated)
  GROUP_CONCAT(DISTINCT ppp.payment_type) as payment_plans,
  -- Target levels (aggregated)
  GROUP_CONCAT(DISTINCT ptl.level) as target_levels,
  -- Split count
  COUNT(DISTINCT ps.id) as split_count,
  -- Metadata
  pm.equipment_required,
  pm.program_description as detailed_description
FROM trainer_programs tp
  LEFT JOIN program_payment_plans ppp ON tp.id = ppp.program_id
  AND ppp.is_active = TRUE
  LEFT JOIN program_target_levels ptl ON tp.id = ptl.program_id
  LEFT JOIN program_splits ps ON tp.id = ps.program_id
  LEFT JOIN program_metadata pm ON tp.id = pm.program_id
GROUP BY tp.id;
-- Program structure summary
CREATE VIEW program_structure_summary AS
SELECT tp.id as program_id,
  tp.program_name,
  ps.split_id,
  ps.title as split_title,
  COUNT(DISTINCT pw.id) as week_count,
  COUNT(DISTINCT pd.id) as day_count,
  COUNT(DISTINCT pses.id) as session_count,
  COUNT(DISTINCT pe.id) as exercise_count,
  COUNT(
    DISTINCT CASE
      WHEN pd.is_rest_day = TRUE THEN pd.id
    END
  ) as rest_day_count
FROM trainer_programs tp
  LEFT JOIN program_splits ps ON tp.id = ps.program_id
  LEFT JOIN program_weeks pw ON tp.id = pw.program_id
  AND ps.split_id = pw.split_id
  LEFT JOIN program_days pd ON pw.id = pd.week_id
  LEFT JOIN program_sessions pses ON pd.id = pses.day_id
  LEFT JOIN program_exercises pe ON pses.id = pe.session_id
GROUP BY tp.id,
  ps.split_id;
-- Exercise library with trainer filter
CREATE VIEW exercise_library_view AS
SELECT el.id,
  el.name,
  el.created_by,
  el.trainer_id,
  el.category,
  el.muscle_groups,
  el.equipment,
  el.description,
  el.is_active,
  el.created_at,
  el.updated_at,
  -- Usage count in programs
  COUNT(DISTINCT pe.id) as usage_count
FROM exercise_library el
  LEFT JOIN program_exercises pe ON el.name = pe.name
WHERE el.is_active = TRUE
GROUP BY el.id;
-- Video library with trainer filter
CREATE VIEW video_library_view AS
SELECT vl.id,
  vl.name,
  vl.url,
  vl.created_by,
  vl.trainer_id,
  vl.duration,
  vl.thumbnail_url,
  vl.file_size,
  vl.is_active,
  vl.created_at,
  vl.updated_at,
  -- Usage count in programs
  COUNT(DISTINCT pe.id) as usage_count
FROM video_library vl
  LEFT JOIN program_exercises pe ON vl.url = pe.video_url
WHERE vl.is_active = TRUE
GROUP BY vl.id;
-- ============================================================================
-- SAMPLE DATA INSERTION
-- ============================================================================
-- Sample program types
INSERT INTO program_types (value, label, description, sort_order)
VALUES (
    'Body building',
    'Body Building',
    'Muscle building and strength training programs',
    1
  ),
  (
    'High Jump',
    'High Jump',
    'Athletic training for high jump performance',
    2
  ),
  (
    'Cross fit',
    'CrossFit',
    'High-intensity functional fitness programs',
    3
  ),
  (
    'Cardio',
    'Cardiovascular Training',
    'Heart health and endurance programs',
    4
  ),
  (
    'Flexibility',
    'Flexibility & Mobility',
    'Stretching and mobility improvement programs',
    5
  );
-- Sample currencies
INSERT INTO supported_currencies (code, name, symbol, sort_order)
VALUES ('USD', 'US Dollar', '$', 1),
  ('EUR', 'Euro', '€', 2),
  ('GBP', 'British Pound', '£', 3),
  ('CAD', 'Canadian Dollar', 'C$', 4),
  ('AUD', 'Australian Dollar', 'A$', 5);
-- Sample admin exercises
INSERT INTO exercise_library (
    name,
    created_by,
    category,
    muscle_groups,
    equipment,
    description
  )
VALUES (
    'Push-ups',
    'admin',
    'Bodyweight',
    '["Chest", "Triceps", "Shoulders"]',
    '["None"]',
    'Classic bodyweight exercise for upper body strength'
  ),
  (
    'Squats',
    'admin',
    'Bodyweight',
    '["Quadriceps", "Glutes", "Hamstrings"]',
    '["None"]',
    'Fundamental lower body exercise'
  ),
  (
    'Plank',
    'admin',
    'Core',
    '["Core", "Shoulders"]',
    '["None"]',
    'Isometric core strengthening exercise'
  ),
  (
    'Deadlift',
    'admin',
    'Strength',
    '["Hamstrings", "Glutes", "Back"]',
    '["Barbell"]',
    'Compound movement for posterior chain'
  ),
  (
    'Bench Press',
    'admin',
    'Strength',
    '["Chest", "Triceps", "Shoulders"]',
    '["Barbell", "Bench"]',
    'Upper body pressing movement'
  );
-- Sample admin videos
INSERT INTO video_library (name, url, created_by, duration, thumbnail_url)
VALUES (
    'Push-up Tutorial',
    'https://example.com/pushup.mp4',
    'admin',
    120,
    'https://example.com/pushup-thumb.jpg'
  ),
  (
    'Squat Form Guide',
    'https://example.com/squat.mp4',
    'admin',
    180,
    'https://example.com/squat-thumb.jpg'
  ),
  (
    'Plank Variations',
    'https://example.com/plank.mp4',
    'admin',
    240,
    'https://example.com/plank-thumb.jpg'
  ),
  (
    'Deadlift Technique',
    'https://example.com/deadlift.mp4',
    'admin',
    300,
    'https://example.com/deadlift-thumb.jpg'
  ),
  (
    'Bench Press Setup',
    'https://example.com/bench.mp4',
    'admin',
    200,
    'https://example.com/bench-thumb.jpg'
  );
-- ============================================================================
-- STORED PROCEDURES FOR COMMON OPERATIONS
-- ============================================================================
DELIMITER // -- Procedure to duplicate a program
CREATE PROCEDURE DuplicateProgram(
  IN source_program_id BIGINT,
  IN new_program_name VARCHAR(255),
  IN target_trainer_id BIGINT,
  IN copy_pricing BOOLEAN,
  IN copy_structure BOOLEAN,
  OUT new_program_id BIGINT
) BEGIN
DECLARE EXIT HANDLER FOR SQLEXCEPTION BEGIN ROLLBACK;
RESIGNAL;
END;
START TRANSACTION;
-- Create new program
INSERT INTO trainer_programs (
    trainer_id,
    program_name,
    type_of_program,
    program_description,
    currency,
    days_for_preview,
    track_progress,
    allow_comments,
    allow_private_messages
  )
SELECT target_trainer_id,
  new_program_name,
  type_of_program,
  program_description,
  currency,
  days_for_preview,
  track_progress,
  allow_comments,
  allow_private_messages
FROM trainer_programs
WHERE id = source_program_id;
SET new_program_id = LAST_INSERT_ID();
-- Copy payment plans
INSERT INTO program_payment_plans (program_id, payment_type)
SELECT new_program_id,
  payment_type
FROM program_payment_plans
WHERE program_id = source_program_id
  AND is_active = TRUE;
-- Copy target levels
INSERT INTO program_target_levels (program_id, level)
SELECT new_program_id,
  level
FROM program_target_levels
WHERE program_id = source_program_id;
-- Copy splits (with or without pricing)
IF copy_pricing THEN
INSERT INTO program_splits (
    program_id,
    split_id,
    title,
    full_price,
    subscription_price,
    sort_order
  )
SELECT new_program_id,
  CONCAT(split_id, '_copy'),
  title,
  full_price,
  subscription_price,
  sort_order
FROM program_splits
WHERE program_id = source_program_id;
ELSE
INSERT INTO program_splits (program_id, split_id, title, sort_order)
SELECT new_program_id,
  CONCAT(split_id, '_copy'),
  title,
  sort_order
FROM program_splits
WHERE program_id = source_program_id;
END IF;
-- Copy metadata
INSERT INTO program_metadata (
    program_id,
    equipment_required,
    program_description
  )
SELECT new_program_id,
  equipment_required,
  program_description
FROM program_metadata
WHERE program_id = source_program_id;
-- Log the duplication
INSERT INTO program_audit_log (
    program_id,
    action_type,
    entity_type,
    changed_by,
    change_reason,
    new_values
  )
VALUES (
    new_program_id,
    'DUPLICATE',
    'PROGRAM',
    target_trainer_id,
    'Program duplicated',
    JSON_OBJECT(
      'source_program_id',
      source_program_id,
      'copy_pricing',
      copy_pricing,
      'copy_structure',
      copy_structure
    )
  );
COMMIT;
END // -- Procedure to get program statistics
CREATE PROCEDURE GetProgramStatistics(IN target_program_id BIGINT) BEGIN
SELECT tp.id as program_id,
  tp.program_name,
  tp.status,
  COUNT(DISTINCT ps.id) as split_count,
  COUNT(DISTINCT pw.id) as week_count,
  COUNT(DISTINCT pd.id) as day_count,
  COUNT(DISTINCT pses.id) as session_count,
  COUNT(DISTINCT pe.id) as exercise_count,
  COUNT(
    DISTINCT CASE
      WHEN pd.is_rest_day = TRUE THEN pd.id
    END
  ) as rest_day_count,
  COUNT(
    DISTINCT CASE
      WHEN pe.video_url IS NOT NULL
      AND pe.video_url != '' THEN pe.id
    END
  ) as exercises_with_video,
  AVG(
    CASE
      WHEN pe.rest_duration_minutes > 0
      OR pe.rest_duration_seconds > 0 THEN pe.rest_duration_minutes * 60 + pe.rest_duration_seconds
    END
  ) as avg_rest_duration_seconds
FROM trainer_programs tp
  LEFT JOIN program_splits ps ON tp.id = ps.program_id
  LEFT JOIN program_weeks pw ON tp.id = pw.program_id
  LEFT JOIN program_days pd ON pw.id = pd.week_id
  LEFT JOIN program_sessions pses ON pd.id = pses.day_id
  LEFT JOIN program_exercises pe ON pses.id = pe.session_id
WHERE tp.id = target_program_id
GROUP BY tp.id;
END // -- Procedure to validate program structure
CREATE PROCEDURE ValidateProgramStructure(IN target_program_id BIGINT) BEGIN -- Check for splits without weeks
SELECT 'Splits without weeks' as issue_type,
  ps.split_id,
  ps.title
FROM program_splits ps
  LEFT JOIN program_weeks pw ON ps.program_id = pw.program_id
  AND ps.split_id = pw.split_id
WHERE ps.program_id = target_program_id
  AND pw.id IS NULL
UNION ALL
-- Check for weeks without days
SELECT 'Weeks without days' as issue_type,
  pw.week_id,
  pw.name
FROM program_weeks pw
  LEFT JOIN program_days pd ON pw.id = pd.week_id
WHERE pw.program_id = target_program_id
  AND pd.id IS NULL
UNION ALL
-- Check for non-rest days without sessions
SELECT 'Non-rest days without sessions' as issue_type,
  pd.day_id,
  pd.name
FROM program_days pd
  JOIN program_weeks pw ON pd.week_id = pw.id
  LEFT JOIN program_sessions pses ON pd.id = pses.day_id
WHERE pw.program_id = target_program_id
  AND pd.is_rest_day = FALSE
  AND pses.id IS NULL
UNION ALL
-- Check for sessions without exercises
SELECT 'Sessions without exercises' as issue_type,
  pses.session_id,
  pses.name
FROM program_sessions pses
  JOIN program_days pd ON pses.day_id = pd.id
  JOIN program_weeks pw ON pd.week_id = pw.id
  LEFT JOIN program_exercises pe ON pses.id = pe.session_id
WHERE pw.program_id = target_program_id
  AND pe.id IS NULL;
END // DELIMITER;