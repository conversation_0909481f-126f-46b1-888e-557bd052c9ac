import React from "react";
import { PostActionsProps } from "./types";
import ReactionButton from "./ReactionButton";

const PostActions: React.FC<PostActionsProps> = ({
  likes,
  comments,
  current_reaction,
  on_reaction_toggle,
  on_toggle_comments,
  is_loading_reaction = false,
}) => {
  return (
    <div className="flex items-center space-x-8 mb-6 pb-4 border-b border-border">
      <ReactionButton
        current_reaction={current_reaction}
        reaction_count={likes}
        on_reaction_toggle={on_reaction_toggle}
        is_loading={is_loading_reaction}
      />
      <button
        onClick={on_toggle_comments}
        className="flex items-center space-x-2 text-text-disabled hover:text-primary transition-colors duration-200"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="17"
          height="16"
          viewBox="0 0 17 16"
          fill="none"
        >
          <g clipPath="url(#clip0_94_247)">
            <path
              d="M4.56577 12.2281C4.9689 11.9344 5.49077 11.8594 5.95952 12.0281C6.78765 12.3281 7.71577 12.5 8.70327 12.5C12.6001 12.5 15.2033 9.98438 15.2033 7.5C15.2033 5.01562 12.6001 2.5 8.70327 2.5C4.8064 2.5 2.20327 5.01562 2.20327 7.5C2.20327 8.5 2.59077 9.4625 3.3189 10.2875C3.58765 10.5906 3.7189 10.9906 3.68765 11.3969C3.6439 11.9625 3.50952 12.4812 3.33452 12.9406C3.86577 12.6938 4.3064 12.4187 4.56577 12.2312V12.2281ZM1.36577 13.4969C1.42202 13.4125 1.47515 13.3281 1.52515 13.2437C1.83765 12.725 2.13452 12.0437 2.1939 11.2781C1.2564 10.2125 0.70327 8.90938 0.70327 7.5C0.70327 3.90937 4.28452 1 8.70327 1C13.122 1 16.7033 3.90937 16.7033 7.5C16.7033 11.0906 13.122 14 8.70327 14C7.5439 14 6.4439 13.8 5.45015 13.4406C5.07827 13.7125 4.47202 14.0844 3.75327 14.3969C3.2814 14.6031 2.7439 14.7906 2.18765 14.9C2.16265 14.9062 2.13765 14.9094 2.11265 14.9156C1.97515 14.9406 1.84077 14.9625 1.70015 14.975C1.6939 14.975 1.68452 14.9781 1.67827 14.9781C1.5189 14.9937 1.35952 15.0031 1.20015 15.0031C0.99702 15.0031 0.81577 14.8813 0.737645 14.6938C0.65952 14.5063 0.70327 14.2937 0.843895 14.15C0.97202 14.0187 1.08765 13.8781 1.19702 13.7281C1.25015 13.6562 1.30015 13.5844 1.34702 13.5125C1.35015 13.5062 1.35327 13.5031 1.3564 13.4969H1.36577Z"
              fill="#757B8A"
            />
          </g>
          <defs>
            <clipPath id="clip0_94_247">
              <path d="M0.703125 0H16.7031V16H0.703125V0Z" fill="white" />
            </clipPath>
          </defs>
        </svg>
        <span className="text-sm font-medium">{comments}</span>
      </button>
    </div>
  );
};

export default PostActions;
