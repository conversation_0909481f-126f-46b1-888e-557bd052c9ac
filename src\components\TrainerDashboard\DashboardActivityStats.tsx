import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faDumbbell,
  faCheckCircle,
  faTrophy,
  faStar,
  faUsers,
} from "@fortawesome/free-solid-svg-icons";
import { useTrainerActivities } from "@/hooks/useTrainerActivities";
import { useState, useEffect } from "react";

interface DashboardActivityStatsProps {
  className?: string;
}

interface ActivityStats {
  activity_type_stats: Array<{
    activity_type: string;
    count: number;
  }>;
  recent_activity_count: number;
  top_athletes: Array<{
    user_id: number;
    user_name: string;
    user_email: string;
    activity_count: number;
  }>;
}

const DashboardActivityStats = ({ className = "" }: DashboardActivityStatsProps) => {
  const [stats, setStats] = useState<ActivityStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { fetchActivityStats } = useTrainerActivities();

  useEffect(() => {
    const loadStats = async () => {
      setIsLoading(true);
      try {
        const response = await fetchActivityStats();
        if (response?.data) {
          setStats(response.data);
        }
      } catch (error) {
        console.error("Error loading activity stats:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadStats();
  }, [fetchActivityStats]);

  const getActivityTypeIcon = (activityType: string) => {
    switch (activityType) {
      case "workout_completed":
        return faDumbbell;
      case "day_completed":
        return faCheckCircle;
      case "program_completed":
        return faTrophy;
      case "milestone_reached":
        return faStar;
      default:
        return faDumbbell;
    }
  };

  const getActivityTypeLabel = (activityType: string) => {
    switch (activityType) {
      case "workout_completed":
        return "Workouts";
      case "day_completed":
        return "Days";
      case "week_completed":
        return "Weeks";
      case "program_completed":
        return "Programs";
      case "milestone_reached":
        return "Milestones";
      default:
        return activityType.replace("_", " ").replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  if (isLoading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
        {[1, 2, 3, 4].map((index) => (
          <div key={index} className="bg-secondary dark:bg-neutral-800 rounded-lg p-4 animate-pulse">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!stats) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-gray-500 dark:text-gray-400">No activity statistics available</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Activity Type Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.activity_type_stats.map((stat) => (
          <div
            key={stat.activity_type}
            className="bg-secondary dark:bg-neutral-800 rounded-lg p-4 border border-border"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {getActivityTypeLabel(stat.activity_type)}
                </p>
                <p className="text-2xl font-bold text-text dark:text-gray-100">
                  {stat.count}
                </p>
              </div>
              <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center">
                <FontAwesomeIcon
                  icon={getActivityTypeIcon(stat.activity_type)}
                  className="w-5 h-5 text-white"
                />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity Count */}
      <div className="bg-secondary dark:bg-neutral-800 rounded-lg p-4 border border-border">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Recent Activity</p>
            <p className="text-2xl font-bold text-text dark:text-gray-100">
              {stats.recent_activity_count}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Last 7 days
            </p>
          </div>
          <div className="w-10 h-10 rounded-full bg-green-500 flex items-center justify-center">
            <FontAwesomeIcon
              icon={faCheckCircle}
              className="w-5 h-5 text-white"
            />
          </div>
        </div>
      </div>

      {/* Top Athletes */}
      {stats.top_athletes.length > 0 && (
        <div className="bg-secondary dark:bg-neutral-800 rounded-lg p-4 border border-border">
          <div className="flex items-center gap-2 mb-4">
            <FontAwesomeIcon
              icon={faUsers}
              className="w-4 h-4 text-primary"
            />
            <h3 className="text-lg font-semibold text-text dark:text-gray-100">
              Top Active Athletes
            </h3>
          </div>
          <div className="space-y-3">
            {stats.top_athletes.slice(0, 5).map((athlete) => (
              <div
                key={athlete.user_id}
                className="flex items-center justify-between p-3 bg-background dark:bg-neutral-700 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {athlete.user_name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-text dark:text-gray-100">
                      {athlete.user_name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {athlete.user_email}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-bold text-primary">
                    {athlete.activity_count}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    activities
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardActivityStats; 