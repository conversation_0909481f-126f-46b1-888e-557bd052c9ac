# Skeleton Loading System

This document explains the skeleton loading system implemented in the application. The system provides realistic loading states that match the actual page structure, improving user experience during data fetching.

## Overview

The skeleton system consists of several specialized components:

1. **Skeleton** - Basic skeleton loader (existing component using react-loading-skeleton)
2. **DiscountPageSkeleton** - Specific skeleton for discount management pages
3. **FormPageSkeleton** - Generic skeleton for form-based pages
4. **ListPageSkeleton** - Generic skeleton for list/table pages

## Components

### DiscountPageSkeleton

A specialized skeleton that matches the exact structure of the discount management page.

```typescript
import { DiscountPageSkeleton } from '@/components/Skeleton';

if (isLoading) {
  return <DiscountPageSkeleton />;
}
```

**Features:**

- Two-column layout matching the discount page
- Affiliate link section skeleton
- Promo code form skeleton
- Sale discount section skeleton
- Subscription/full price discount sections
- Action buttons skeleton

### FormPageSkeleton

A flexible skeleton for form-based pages with customizable layout.

```typescript
import { FormPageSkeleton } from '@/components/Skeleton';

if (isLoading) {
  return (
    <FormPageSkeleton
      showHeader={true}
      columns={2}
      sections={4}
      fieldsPerSection={3}
      showActions={true}
    />
  );
}
```

**Props:**

- `showHeader` - Show page header with title and subtitle
- `columns` - Number of columns (1 or 2)
- `sections` - Number of form sections
- `fieldsPerSection` - Number of fields per section
- `showActions` - Show action buttons at bottom
- `className` - Custom CSS classes

**Use Cases:**

- Create/Edit program pages
- Profile forms
- Settings pages
- Any form-heavy pages

### ListPageSkeleton

A flexible skeleton for list and table pages with optional components.

```typescript
import { ListPageSkeleton } from '@/components/Skeleton';

if (isLoading) {
  return (
    <ListPageSkeleton
      showHeader={true}
      showFilters={true}
      showStats={true}
      statsCount={4}
      rows={8}
      columns={6}
      showPagination={true}
    />
  );
}
```

**Props:**

- `showHeader` - Show page header
- `showFilters` - Show search and filter section
- `showStats` - Show stats cards at top
- `statsCount` - Number of stat cards
- `rows` - Number of table rows
- `columns` - Number of table columns
- `showPagination` - Show pagination controls
- `className` - Custom CSS classes

**Use Cases:**

- Admin list pages
- Data tables
- Dashboard pages
- Search results pages

## Implementation Examples

### Replace Text Loading with Skeleton

**Before:**

```typescript
if (isLoading) {
  return (
    <div className="flex items-center justify-center h-64">
      <div className="text-text">Loading...</div>
    </div>
  );
}
```

**After:**

```typescript
if (isLoading) {
  return <FormPageSkeleton columns={2} sections={4} />;
}
```

### Customize for Different Page Types

**Single Column Form (Profile Page):**

```typescript
<FormPageSkeleton
  columns={1}
  sections={3}
  fieldsPerSection={4}
  className="max-w-2xl mx-auto"
/>
```

**Admin List with Stats:**

```typescript
<ListPageSkeleton
  showStats={true}
  statsCount={4}
  rows={10}
  columns={7}
/>
```

**Simple List without Filters:**

```typescript
<ListPageSkeleton
  showFilters={false}
  showStats={false}
  rows={6}
  columns={4}
/>
```

## Best Practices

### 1. Match Page Structure

Choose the skeleton that best matches your page structure:

- Use `FormPageSkeleton` for forms and create/edit pages
- Use `ListPageSkeleton` for tables and list views
- Create custom skeletons for unique layouts

### 2. Configure Appropriately

Adjust skeleton props to match your actual content:

- Set correct number of columns and sections
- Match the number of table columns
- Include/exclude optional components (stats, filters, pagination)

### 3. Consistent Styling

All skeletons use the same CSS custom properties for theming:

- `--skeleton-color` - Base skeleton color (uses text-disabled-color for better contrast)
- `--skeleton-highlight-color` - Animation highlight color (uses background-hover-color)

### 4. Performance

Skeletons are lightweight and render quickly:

- No data fetching required
- Minimal DOM elements
- CSS animations only

## Theme Integration

Skeletons automatically adapt to your theme using CSS custom properties:

```css
:root {
  --skeleton-color: var(--text-disabled-color);
  --skeleton-highlight-color: var(--background-hover-color);
}
```

This ensures skeletons match your application's color scheme in both light and dark modes.

## Migration Guide

To migrate existing loading states to skeletons:

1. **Identify the page type** (form, list, or custom)
2. **Choose appropriate skeleton component**
3. **Replace loading JSX** with skeleton component
4. **Configure props** to match page structure
5. **Test in both light and dark themes**

## Future Enhancements

Potential improvements to the skeleton system:

1. **More specialized skeletons** for specific page types
2. **Animation customization** options
3. **Dynamic skeleton generation** based on actual component structure
4. **Skeleton composition** for complex layouts
5. **Loading state transitions** with fade effects

## Troubleshooting

**Skeleton doesn't match page layout:**

- Adjust `columns`, `sections`, and `fieldsPerSection` props
- Consider creating a custom skeleton for unique layouts

**Skeleton appears too fast/slow:**

- Skeleton should appear immediately when `isLoading` is true
- Ensure loading state is properly managed in your data fetching logic

**Theme colors don't match:**

- Check CSS custom properties are properly defined
- Verify theme variables are available in skeleton context
