# Athlete Enrollment Program Endpoint

## Overview

The athlete enrollment program endpoint provides a comprehensive view of a specific enrollment with complete program structure including weeks, days, sessions, exercises, and progress tracking. This endpoint is designed to support the athlete workout screen UI with all necessary data for displaying the complete program structure with progress.

## Endpoint

```
GET /v2/api/kanglink/custom/athlete/enrollment/:enrollment_id/program
```

## Authentication

- **Required**: Yes
- **Roles**: `member`
- **Token**: Bearer token in Authorization header

## Parameters

- `enrollment_id` (path parameter) - The ID of the enrollment to retrieve

## Response Structure

The endpoint returns a complete program structure with nested data and progress tracking:

```json
{
  "error": false,
  "data": {
    "enrollment": {
      "id": 1,
      "trainer_id": 2,
      "athlete_id": 3,
      "program_id": 4,
      "split_id": 5,
      "payment_type": "one_time",
      "amount": 100.00,
      "currency": "USD",
      "enrollment_date": "2024-01-15T10:30:00Z",
      "status": "active",
      "payment_status": "paid"
    },
    "program": {
      "id": 4,
      "name": "Strength Training Program",
      "description": "Complete strength training program",
      "image_url": "https://example.com/program.jpg"
    },
    "split": {
      "id": 5,
      "title": "Beginner Split",
      "description": "Perfect for beginners",
      "duration_weeks": 8,
      "pricing": {
        "full_price": 100.00,
        "subscription_price": 20.00,
        "price": 20.00,
        "currency": "USD"
      }
    },
    "trainer": {
      "id": 2,
      "email": "<EMAIL>",
      "full_name": "John Trainer",
      "first_name": "John",
      "last_name": "Trainer",
      "photo": "https://example.com/photo.jpg"
    },
    "weeks": [
      {
        "id": 1,
        "title": "Week 1",
        "description": "Introduction week",
        "week_order": 1,
        "split_id": 5,
        "days": [
          {
            "id": 1,
            "title": "Day 1",
            "description": "Upper Body Strength",
            "day_order": 1,
            "equipment_required": "Dumbbells, resistance bands, pull-up bar, exercise ball",
            "week_id": 1,
            "sessions": [
              {
                "id": 1,
                "title": "Session 1 - Compound Movements",
                "description": "Focus on compound movements",
                "session_order": 1,
                "day_id": 1,
                "exercise_instances": [
                  {
                    "id": 1,
                    "label": "Push-ups",
                    "sets": 4,
                    "reps_or_time": "12-15",
                    "weight": null,
                    "rest_time_seconds": 60,
                    "notes": "Keep core tight",
                    "exercise_order": 1,
                    "session_id": 1,
                    "exercise": {
                      "id": 1,
                      "name": "Push-ups",
                      "description": "Standard push-up exercise",
                      "muscle_groups": "Chest, Triceps, Shoulders",
                      "equipment": "Body weight",
                      "difficulty_level": "Beginner"
                    },
                    "video": {
                      "id": 1,
                      "url": "https://example.com/video.mp4",
                      "thumbnail_url": "https://example.com/thumbnail.jpg",
                      "duration_seconds": 120
                    },
                    "progress": {
                      "is_completed": true,
                      "sets_completed": 4,
                      "reps_completed": "15,14,13,12",
                      "weight_used": "bodyweight",
                      "time_taken_seconds": 300,
                      "difficulty_rating": 7,
                      "notes": "Felt good",
                      "completed_at": "2024-01-15T11:00:00Z"
                    }
                  }
                ]
              }
            ],
            "progress": {
              "is_completed": true,
              "total_exercises": 5,
              "completed_exercises": 5,
              "completed_at": "2024-01-15T12:00:00Z",
              "notes": "Great workout"
            }
          }
        ],
        "progress": {
          "total_days": 7,
          "completed_days": 1,
          "is_completed": false
        }
      }
    ],
    "overall_progress": {
      "current_week_id": 1,
      "current_day_id": 2,
      "total_days_completed": 1,
      "total_exercises_completed": 5,
      "progress_percentage": 12.5,
      "last_activity_date": "2024-01-15T12:00:00Z"
    }
  }
}
```

## Key Features

### 1. Complete Program Structure

The endpoint provides a fully nested structure:
- **Weeks** - Ordered by `week_order`
- **Days** - Ordered by `day_order` within each week
- **Sessions** - Ordered by `session_order` within each day
- **Exercise Instances** - Ordered by `exercise_order` within each session

### 2. Progress Tracking at Multiple Levels

- **Exercise Level**: Individual exercise completion with detailed metrics
- **Day Level**: Day completion status and exercise counts
- **Week Level**: Week completion based on completed days
- **Overall Level**: Total program progress and current position

### 3. Rich Exercise Data

Each exercise instance includes:
- Exercise details (name, description, muscle groups, equipment)
- Video content (URL, thumbnail, duration)
- Workout parameters (sets, reps, weight, rest time)
- Progress tracking (completion status, actual performance)

### 4. Equipment Information

- Equipment required for each day
- Equipment needed for individual exercises
- Helps athletes prepare for workouts

### 5. Pricing Information

- Multiple price types (full price, subscription price)
- Minimum price calculation
- Currency information

## UI Integration

### Workout Screen Display

The response structure directly supports the workout screen shown in the design:

- **Day Header**: Use `day.title` and `day.description`
- **Equipment Section**: Display `day.equipment_required`
- **Sessions**: Loop through `day.sessions`
- **Exercises**: Display `session.exercise_instances` with video and progress
- **Progress Buttons**: Use `exercise.progress.is_completed` for "Mark Complete" state

### Navigation

- **Week Navigation**: Use `weeks` array with `week.progress` for completion indicators
- **Day Navigation**: Use `week.days` with `day.progress` for day completion status
- **Current Position**: Use `overall_progress.current_week_id` and `current_day_id`

## Error Responses

### 404 Not Found
```json
{
  "error": true,
  "message": "Enrollment not found or access denied"
}
```

### 500 Internal Server Error
```json
{
  "error": true,
  "message": "Failed to get enrollment program data",
  "details": "Error details"
}
```

## Database Requirements

### Required Tables
- `kanglink_enrollment` - Core enrollment data
- `kanglink_split` - Split details and pricing
- `kanglink_program` - Program information
- `kanglink_user` - Trainer information from `data` JSON field
- `kanglink_week` - Week structure
- `kanglink_day` - Day structure with equipment requirements
- `kanglink_session` - Session structure
- `kanglink_exercise_instance` - Exercise instances with parameters
- `kanglink_exercise` - Exercise master data
- `kanglink_video` - Video content
- `kanglink_exercise_progress` - Exercise completion tracking
- `kanglink_day_progress` - Day completion tracking
- `kanglink_athlete_progress` - Overall progress tracking

## Performance Considerations

- Single complex query for program structure to minimize database calls
- Progress data fetched separately for optimal performance
- Data structured in memory to avoid N+1 query problems
- Proper indexing on foreign keys for join performance

## Testing

Run the test suite:
```bash
cd mtpbk/custom/ksl_be/tests
node ../../../test_runner.js athlete_enrollment_program.test.js
```

The test covers:
- Complete program structure retrieval
- Progress data integration
- Enrollment ownership verification
- Error handling for non-existent enrollments
- Data structure validation

## Usage Example

```javascript
// Frontend usage
const response = await fetch('/v2/api/kanglink/custom/athlete/enrollment/123/program', {
  headers: {
    'Authorization': 'Bearer ' + token,
    'x-project': btoa('kanglink:secret')
  }
});

const { data } = await response.json();

// Access program structure
const currentWeek = data.weeks.find(w => w.id === data.overall_progress.current_week_id);
const currentDay = currentWeek?.days.find(d => d.id === data.overall_progress.current_day_id);

// Display workout for current day
currentDay?.sessions.forEach(session => {
  session.exercise_instances.forEach(exercise => {
    // Render exercise with video and progress tracking
  });
});
```
