import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useCustomModelQuery } from "@/query/shared/customModel";
import {
  AthleteFavoriteProgramsResponse,
  AthleteFavoriteTrainersResponse,
  FavoriteActionResponse,
} from "@/interfaces/model.interface";
import { AthleteEndpoints } from "@/utils/baas/athlete";

// Hook for fetching favorite programs
export const useAthleteFavoritePrograms = (options?: {
  enabled?: boolean;
  refetchInterval?: number;
}) => {
  const customQuery = useCustomModelQuery();

  return useQuery({
    queryKey: ["athlete-favorite-programs"],
    queryFn: async (): Promise<AthleteFavoriteProgramsResponse> => {
      const response = await customQuery.mutateAsync({
        endpoint: AthleteEndpoints.ATHLETE_FAVORITE_PROGRAMS.GET_ALL.url,
        method: AthleteEndpoints.ATHLETE_FAVORITE_PROGRAMS.GET_ALL.method,
        requiresAuth: true,
      });

      // Handle API error responses
      if (response.error || !response.data) {
        throw new Error(
          response.message || "Failed to fetch favorite programs"
        );
      }

      return response.data;
    },
    enabled: options?.enabled ?? true,
    refetchInterval: options?.refetchInterval,
    staleTime: 30000, // Consider data stale after 30 seconds
    retry: 3,
  });
};

// Hook for fetching favorite trainers
export const useAthleteFavoriteTrainers = (options?: {
  enabled?: boolean;
  refetchInterval?: number;
}) => {
  const customQuery = useCustomModelQuery();

  return useQuery({
    queryKey: ["athlete-favorite-trainers"],
    queryFn: async (): Promise<AthleteFavoriteTrainersResponse> => {
      const response = await customQuery.mutateAsync({
        endpoint: AthleteEndpoints.ATHLETE_FAVORITE_TRAINERS.GET_ALL.url,
        method: AthleteEndpoints.ATHLETE_FAVORITE_TRAINERS.GET_ALL.method,
        requiresAuth: true,
      });

      // Handle API error responses
      if (response.error || !response.data) {
        throw new Error(
          response.message || "Failed to fetch favorite trainers"
        );
      }

      return response.data;
    },
    enabled: options?.enabled ?? true,
    refetchInterval: options?.refetchInterval,
    staleTime: 30000, // Consider data stale after 30 seconds
    retry: 3,
  });
};

// Hook for managing program favorites
export const useAthleteProgramFavorites = () => {
  const queryClient = useQueryClient();
  const customQuery = useCustomModelQuery();

  const addProgramToFavorites = useMutation({
    mutationFn: async (
      programId: string | number
    ): Promise<FavoriteActionResponse> => {
      const response = await customQuery.mutateAsync({
        endpoint: AthleteEndpoints.ATHLETE_FAVORITE_PROGRAMS.ADD.url.replace(
          "{programId}",
          String(programId)
        ),
        method: AthleteEndpoints.ATHLETE_FAVORITE_PROGRAMS.ADD.method,
        requiresAuth: true,
      });

      // Handle API error responses
      if (response.error || !response.data) {
        throw new Error(
          response.message || "Failed to add program to favorites"
        );
      }

      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch favorite programs
      queryClient.invalidateQueries({
        queryKey: ["athlete-favorite-programs"],
      });
    },
  });

  const removeProgramFromFavorites = useMutation({
    mutationFn: async (
      programId: string | number
    ): Promise<FavoriteActionResponse> => {
      const response = await customQuery.mutateAsync({
        endpoint: AthleteEndpoints.ATHLETE_FAVORITE_PROGRAMS.REMOVE.url.replace(
          "{programId}",
          String(programId)
        ),
        method: AthleteEndpoints.ATHLETE_FAVORITE_PROGRAMS.REMOVE.method,
        requiresAuth: true,
      });

      // Handle API error responses
      if (response.error || !response.data) {
        throw new Error(
          response.message || "Failed to remove program from favorites"
        );
      }

      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch favorite programs
      queryClient.invalidateQueries({
        queryKey: ["athlete-favorite-programs"],
      });
    },
  });

  const toggleProgramFavorite = async (
    programId: string | number,
    isFavorite: boolean
  ): Promise<FavoriteActionResponse> => {
    if (isFavorite) {
      return removeProgramFromFavorites.mutateAsync(programId);
    } else {
      return addProgramToFavorites.mutateAsync(programId);
    }
  };

  return {
    addProgramToFavorites,
    removeProgramFromFavorites,
    toggleProgramFavorite,
    isAddingProgram: addProgramToFavorites.isPending,
    isRemovingProgram: removeProgramFromFavorites.isPending,
    isToggling:
      addProgramToFavorites.isPending || removeProgramFromFavorites.isPending,
  };
};

// Hook for managing trainer favorites
export const useAthleteTrainerFavorites = () => {
  const queryClient = useQueryClient();
  const customQuery = useCustomModelQuery();

  const addTrainerToFavorites = useMutation({
    mutationFn: async (
      trainerId: string | number
    ): Promise<FavoriteActionResponse> => {
      const response = await customQuery.mutateAsync({
        endpoint: AthleteEndpoints.ATHLETE_FAVORITE_TRAINERS.ADD.url.replace(
          "{trainerId}",
          String(trainerId)
        ),
        method: AthleteEndpoints.ATHLETE_FAVORITE_TRAINERS.ADD.method,
        requiresAuth: true,
      });

      // Handle API error responses
      if (response.error || !response.data) {
        throw new Error(
          response.message || "Failed to add trainer to favorites"
        );
      }

      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch favorite trainers
      queryClient.invalidateQueries({
        queryKey: ["athlete-favorite-trainers"],
      });
    },
  });

  const removeTrainerFromFavorites = useMutation({
    mutationFn: async (
      trainerId: string | number
    ): Promise<FavoriteActionResponse> => {
      const response = await customQuery.mutateAsync({
        endpoint: AthleteEndpoints.ATHLETE_FAVORITE_TRAINERS.REMOVE.url.replace(
          "{trainerId}",
          String(trainerId)
        ),
        method: AthleteEndpoints.ATHLETE_FAVORITE_TRAINERS.REMOVE.method,
        requiresAuth: true,
      });

      // Handle API error responses
      if (response.error || !response.data) {
        throw new Error(
          response.message || "Failed to remove trainer from favorites"
        );
      }

      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch favorite trainers
      queryClient.invalidateQueries({
        queryKey: ["athlete-favorite-trainers"],
      });
    },
  });

  const toggleTrainerFavorite = async (
    trainerId: string | number,
    isFavorite: boolean
  ): Promise<FavoriteActionResponse> => {
    if (isFavorite) {
      return removeTrainerFromFavorites.mutateAsync(trainerId);
    } else {
      return addTrainerToFavorites.mutateAsync(trainerId);
    }
  };

  return {
    addTrainerToFavorites,
    removeTrainerFromFavorites,
    toggleTrainerFavorite,
    isAddingTrainer: addTrainerToFavorites.isPending,
    isRemovingTrainer: removeTrainerFromFavorites.isPending,
    isToggling:
      addTrainerToFavorites.isPending || removeTrainerFromFavorites.isPending,
  };
};
