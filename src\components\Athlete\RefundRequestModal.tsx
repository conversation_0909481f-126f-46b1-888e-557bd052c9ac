import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Modal } from "@/components/Modal";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import {
  useSubmitRefundRequest,
  useRefundEligibility,
} from "@/hooks/useAthleteRefunds";
import { useQueryClient } from "@tanstack/react-query";

// Validation schema
const refundRequestSchema = yup.object().shape({
  reason: yup
    .string()
    .required("Reason is required")
    .min(10, "Reason must be at least 10 characters long")
    .max(500, "Reason cannot exceed 500 characters"),
});

interface RefundRequestFormData {
  reason: string;
}

interface RefundRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  enrollmentId: number;
  programName: string;
  amount: number;
  currency: string;
  onSuccess?: () => void;
}

export const RefundRequestModal: React.FC<RefundRequestModalProps> = ({
  isOpen,
  onClose,
  enrollmentId,
  programName,
  amount,
  currency,
  onSuccess,
}) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const queryClient = useQueryClient();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const { submitRefundRequest } = useSubmitRefundRequest();
  const {
    isEligible,
    timeRemainingHours,
    reasons: ineligibilityReasons,
  } = useRefundEligibility(enrollmentId);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
  } = useForm<RefundRequestFormData>({
    resolver: yupResolver(refundRequestSchema),
  });

  const reasonValue = watch("reason", "");

  const handleClose = () => {
    reset();
    setShowConfirmation(false);
    onClose();
  };

  const onSubmit = async (_data: RefundRequestFormData) => {
    if (!isEligible) {
      return;
    }

    setShowConfirmation(true);
  };

  const handleConfirmSubmit = async () => {
    const formData = watch();
    setIsSubmitting(true);

    try {
      await submitRefundRequest({
        enrollment_id: enrollmentId,
        reason: formData.reason,
      });

      // Invalidate related queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ["refund-status", enrollmentId],
      });
      queryClient.invalidateQueries({ queryKey: ["refund-history"] });
      queryClient.invalidateQueries({ queryKey: ["athlete-enrollments"] });

      onSuccess?.();
      handleClose();
    } catch (error) {
      console.error("Failed to submit refund request:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatTimeRemaining = (hours: number) => {
    if (hours < 1) {
      const minutes = Math.floor(hours * 60);
      return `${minutes} minute${minutes !== 1 ? "s" : ""}`;
    }
    return `${Math.floor(hours)} hour${Math.floor(hours) !== 1 ? "s" : ""}`;
  };

  if (!isEligible && ineligibilityReasons.length > 0) {
    return (
      <Modal
        isOpen={isOpen}
        modalCloseClick={handleClose}
        title="Refund Not Available"
        modalHeader
        classes={{
          modalDialog: "max-w-md",
          modal: "h-full",
        }}
      >
        <div className="p-6">
          <div className="text-center mb-4">
            <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
              <svg
                className="w-8 h-8 text-red-600 dark:text-red-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>
            <h3
              className="text-lg font-semibold mb-2"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              This enrollment is not eligible for a refund
            </h3>
          </div>

          <div className="space-y-2 mb-6">
            {ineligibilityReasons.map((reason, index) => (
              <div key={index} className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                <p
                  className="text-sm"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  {reason}
                </p>
              </div>
            ))}
          </div>

          <button
            onClick={handleClose}
            className="w-full px-4 py-2 rounded-lg text-white transition-colors"
            style={{
              backgroundColor: THEME_COLORS[mode].PRIMARY,
            }}
          >
            Close
          </button>
        </div>
      </Modal>
    );
  }

  if (showConfirmation) {
    return (
      <Modal
        isOpen={isOpen}
        modalCloseClick={handleClose}
        title="Confirm Refund Request"
        modalHeader
        classes={{
          modalDialog: "max-w-md",
          modal: "h-full",
        }}
      >
        <div className="p-6">
          <div className="text-center mb-6">
            <div className="w-16 h-16 mx-auto mb-4 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center">
              <svg
                className="w-8 h-8 text-yellow-600 dark:text-yellow-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h3
              className="text-lg font-semibold mb-2"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              Submit Refund Request?
            </h3>
            <p
              className="text-sm opacity-70 mb-4"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              You are requesting a refund for:
            </p>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span
                  className="font-medium"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  Program:
                </span>
                <span style={{ color: THEME_COLORS[mode].TEXT }}>
                  {programName}
                </span>
              </div>
              <div className="flex justify-between">
                <span
                  className="font-medium"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  Amount:
                </span>
                <span style={{ color: THEME_COLORS[mode].TEXT }}>
                  {currency} {amount}
                </span>
              </div>
            </div>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={() => setShowConfirmation(false)}
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirmSubmit}
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 rounded-lg text-white transition-colors disabled:opacity-50"
              style={{
                backgroundColor: THEME_COLORS[mode].PRIMARY,
              }}
            >
              {isSubmitting ? "Submitting..." : "Submit Request"}
            </button>
          </div>
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={handleClose}
      title="Request Refund"
      modalHeader
      classes={{
        modalDialog: "max-w-lg",
        modal: "h-full",
      }}
    >
      <form
        onSubmit={handleSubmit(onSubmit, (error) => {
          console.log("ERROR >>", error);
        })}
        className="p-6"
      >
        <div className="mb-6">
          <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
            <div className="flex items-start space-x-3">
              <div className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5">
                <svg fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div>
                <p className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                  Time Remaining: {formatTimeRemaining(timeRemainingHours)}
                </p>
                <p className="text-xs text-blue-600 dark:text-blue-300">
                  Refund requests must be submitted within the payout window.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-2 mb-4">
            <div className="flex justify-between">
              <span
                className="font-medium"
                style={{ color: THEME_COLORS[mode].TEXT }}
              >
                Program:
              </span>
              <span style={{ color: THEME_COLORS[mode].TEXT }}>
                {programName}
              </span>
            </div>
            <div className="flex justify-between">
              <span
                className="font-medium"
                style={{ color: THEME_COLORS[mode].TEXT }}
              >
                Amount:
              </span>
              <span style={{ color: THEME_COLORS[mode].TEXT }}>
                {currency} {amount}
              </span>
            </div>
          </div>
        </div>

        <div className="mb-6">
          <MkdInputV2
            name="reason"
            type="textarea"
            placeholder="Please provide a detailed reason for your refund request (minimum 10 characters)"
            register={register}
            errors={errors}
            required
          >
            <MkdInputV2.Container>
              <MkdInputV2.Label>Reason for Refund</MkdInputV2.Label>
              <MkdInputV2.Field rows={4} />

              <MkdInputV2.Error />
            </MkdInputV2.Container>
            <div className="text-xs text-gray-500 mt-5">
              {reasonValue.length}/500 characters
            </div>
          </MkdInputV2>
        </div>

        <div className="flex space-x-3">
          <button
            type="button"
            onClick={handleClose}
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={!reasonValue || reasonValue.length < 10}
            className="flex-1 px-4 py-2 rounded-lg text-white transition-colors disabled:opacity-50"
            style={{
              backgroundColor: THEME_COLORS[mode].PRIMARY,
            }}
          >
            Continue
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default RefundRequestModal;
