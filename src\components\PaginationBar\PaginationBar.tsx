import { useCallback } from "react";
import { useTheme } from "@/hooks/useTheme";
import { Theme } from "@/utils/Enums/theme.enums";

interface PaginationBarProps {
  currentPage: number;
  pageCount: number;
  pageSize: number;
  canPreviousPage: boolean;
  canNextPage: boolean;
  updatePageSize: (pageSize: number) => void;
  previousPage?: () => void;
  nextPage?: () => void;
  startSize: number;
  multiplier: number;
  updateCurrentPage: (page: number) => void;
  canChangeLimit: boolean;
  showLimit?: boolean;
}
const PaginationBar = ({
  currentPage,
  pageCount,
  pageSize: _pageSize,
  canPreviousPage,
  canNextPage,
  updatePageSize: _updatePageSize,
  // previousPage,
  // nextPage,
  startSize: _startSize = 500,
  multiplier: _multiplier = 100,
  updateCurrentPage,
  canChangeLimit: _canChangeLimit = true,
  showLimit: _showLimit = false,
}: PaginationBarProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const chevronStroke = mode === Theme.LIGHT ? "#0F172A" : "#FFF";

  const nextPage = useCallback(() => {
    if (canNextPage && currentPage + 1 <= pageCount) {
      updateCurrentPage(currentPage + 1);
    }
  }, [canNextPage, currentPage, pageCount, updateCurrentPage]);

  const previousPage = useCallback(() => {
    if (canPreviousPage && currentPage - 1 > 0) {
      updateCurrentPage(currentPage - 1);
    }
  }, [canPreviousPage, currentPage, updateCurrentPage]);

  return (
    <div className="flex w-full items-center justify-center py-4">
      <div className="flex items-center space-x-1">
        {/* First page button (double left chevron) */}
        <button
          type="button"
          onClick={() => updateCurrentPage(1)}
          disabled={currentPage === 1}
          className={`w-8 h-8 flex items-center justify-center rounded transition-colors duration-200 font-medium border border-[#3A3A3A] bg-transparent text-white hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed`}
          aria-label="First page"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M11 13L6 8L11 3"
              stroke={chevronStroke}
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M8 13L3 8L8 3"
              stroke={chevronStroke}
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
        {/* Previous page button (single left chevron) */}
        <button
          type="button"
          onClick={previousPage}
          disabled={!canPreviousPage}
          className={`w-8 h-8 flex items-center justify-center rounded transition-colors duration-200 font-medium border border-[#3A3A3A] bg-transparent text-black hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed`}
          aria-label="Previous page"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10 13L5 8L10 3"
              stroke={chevronStroke}
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
        {/* Page numbers */}
        {Array.from({ length: Math.min(pageCount, 10) }).map((_, idx) => {
          const page = idx + 1;
          return (
            <button
              key={page}
              type="button"
              onClick={() => updateCurrentPage(page)}
              disabled={page === currentPage}
              className={`w-8 h-8 flex items-center justify-center rounded transition-colors duration-200 font-medium border ${
                page === currentPage
                  ? "bg-[#22C55E] text-white border-[#22C55E] dark:text-black"
                  : "bg-transparent text-black border-[#3A3A3A] hover:bg-gray-100 dark:text-white"
              } ${page === currentPage ? "" : "disabled:opacity-50 disabled:cursor-not-allowed"}`}
              aria-label={`Page ${page}`}
            >
              {page}
            </button>
          );
        })}
        {/* Next page button (single right chevron) */}
        <button
          type="button"
          onClick={nextPage}
          disabled={!canNextPage}
          className={`w-8 h-8 flex items-center justify-center rounded transition-colors duration-200 font-medium border border-[#3A3A3A] bg-transparent text-white hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed`}
          aria-label="Next page"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6 3L11 8L6 13"
              stroke={chevronStroke}
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
        {/* Last page button (double right chevron) */}
        <button
          type="button"
          onClick={() => updateCurrentPage(pageCount)}
          disabled={currentPage === pageCount}
          className={`w-8 h-8 flex items-center justify-center rounded transition-colors duration-200 font-medium border border-[#3A3A3A] bg-[transparent] text-white hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed`}
          aria-label="Last page"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M5 3L10 8L5 13"
              stroke={chevronStroke}
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M8 3L13 8L8 13"
              stroke={chevronStroke}
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default PaginationBar;
// <div>
//   <button
//     type="button"
//     onClick={previousPage}
//     disabled={!canPreviousPage}
//     className={`h-10 w-10 font-bold`}
//   >
//     &#x02190;
//   </button>{" "}
//   <button
//     type="button"
//     onClick={nextPage}
//     disabled={!canNextPage}
//     className={`h-10 w-10 font-bold `}
//   >
//     &#x02192;
//   </button>{" "}
// </div>
