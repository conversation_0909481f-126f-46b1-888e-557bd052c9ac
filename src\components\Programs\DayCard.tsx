import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { ExerciseItem } from "./index";
import { ExerciseInstance } from "@/interfaces";

interface DayCardProps {
  dayNumber: number;
  weekDay: string;
  exercises: ExerciseInstance[];
}

const DayCard = ({ dayNumber, weekDay, exercises }: DayCardProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <div
      className="w-full max-w-sm mx-auto lg:max-w-none lg:w-72 rounded-md shadow-lg border transition-all duration-200 hover:shadow-xl"
      style={{
        backgroundColor: THEME_COLORS[mode].SECONDARY,
        borderColor: THEME_COLORS[mode].BORDER,
      }}
    >
      {/* Day header */}
      <div className="p-4 pb-2">
        <h3
          className="text-sm font-bold font-inter mb-1 transition-colors duration-200"
          style={{ color: THEME_COLORS[mode].TEXT }}
        >
          Day {dayNumber}
        </h3>
        <p
          className="text-xs font-normal font-inter transition-colors duration-200"
          style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
        >
          {weekDay}
        </p>
      </div>

      {/* Exercises list */}
      <div className="px-4 pb-4">
        {exercises.map((exercise, index) => (
          <ExerciseItem
            key={`${exercise.exercise?.name}-${index}`}
            exerciseLetter={exercise.label || String.fromCharCode(65 + index)}
            exerciseName={exercise.exercise?.name || ""}
            reps={exercise.reps_or_time || ""}
            videoUrl={exercise.video_url || ""}
            thumbnailUrl={exercise.video_url || ""}
            isLast={index === exercises.length - 1}
          />
        ))}
      </div>
    </div>
  );
};

export default DayCard;
