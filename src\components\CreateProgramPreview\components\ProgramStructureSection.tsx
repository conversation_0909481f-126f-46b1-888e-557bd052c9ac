import React, { useState } from "react";
import { WeekSection } from "./index";
import { MkdInputV2 } from "@/components/MkdInputV2";

interface ProgramStructureSectionProps {
  stepTwoData?: any;
  stepOneData?: any;
  selectedSplit?: string;
  onSplitChange?: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  availableSplits?: Array<{
    value: string;
    label: string;
    split_id: string;
  }>;
  weeks: any[];
}

const ProgramStructureSection: React.FC<ProgramStructureSectionProps> = ({
  stepTwoData: _stepTwoData,
  stepOneData: _stepOneData,
  selectedSplit,
  onSplitChange,
  availableSplits = [],
  weeks,
}) => {
  const [collapsedStates, setCollapsedStates] = useState<{
    [key: string]: boolean;
  }>({});

  const toggleCollapse = (id: string) => {
    setCollapsedStates((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Handle split selection change
  const handleSplitChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    onSplitChange?.(event);
    // Reset collapsed states when switching splits
    setCollapsedStates({});
  };

  if (!availableSplits || availableSplits.length === 0) {
    return (
      <div className="space-y-6">
        <h2 className="text-lg font-medium text-text">Program</h2>
        <div className="text-center py-8">
          <p className="text-text-secondary">
            No program structure defined yet.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h2 className="text-lg font-medium text-text">Program</h2>

        {availableSplits.length > 1 && (
          <div className="w-full sm:w-64">
            <MkdInputV2
              name="selectedSplit"
              type="dropdown"
              uniqueKey="split_id"
              display="label"
              options={availableSplits}
              value={selectedSplit}
              onChange={handleSplitChange}
            >
              <MkdInputV2.Container>
                <MkdInputV2.Label className="text-sm font-medium text-text">
                  View Split
                </MkdInputV2.Label>
                <MkdInputV2.Field
                  placeholder="Select split to view..."
                  className="mt-1"
                />
                <MkdInputV2.Error />
              </MkdInputV2.Container>
            </MkdInputV2>
          </div>
        )}
      </div> */}

      {/* Display current split title if multiple splits exist */}
      {availableSplits.length > 1 && selectedSplit && (
        <div className="bg-background-secondary rounded-lg p-3 border border-border">
          <p className="text-sm text-text-secondary">
            Currently viewing:{" "}
            <span className="font-medium text-text">
              {
                availableSplits.find((split) => split.value === selectedSplit)
                  ?.label
              }
            </span>
          </p>
        </div>
      )}

      <div className="space-y-4">
        {weeks && weeks.length > 0 ? (
          weeks.map((week: any, weekIndex: number) => (
            <WeekSection
              key={week.id || `week-${weekIndex}`}
              week={week}
              weekIndex={weekIndex}
              isCollapsed={collapsedStates[`week-${week.id}`] || false}
              onToggleCollapse={() => toggleCollapse(`week-${week.id}`)}
              collapsedStates={collapsedStates}
              onToggleItemCollapse={toggleCollapse}
            />
          ))
        ) : (
          <div className="text-center py-8">
            <p className="text-text-secondary">
              No program structure defined for this split yet.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProgramStructureSection;
