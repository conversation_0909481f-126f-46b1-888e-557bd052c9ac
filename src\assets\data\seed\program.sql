-- MySQL Schema for Complete Program Structure
-- Based on FormData interface from src/components/CreateProgramStepOne/types.ts
-- and ProgramFormData interface from src/components/CreateProgramStepTwo/types.ts

-- Create programs table
CREATE TABLE kanglink_program (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    program_name VARCHAR(255) NOT NULL,
    type_of_program VARCHAR(100) NOT NULL,
    program_description TEXT,
    payment_plan JSON, -- Store array of payment plans as JSON
    track_progress BOOLEAN DEFAULT FALSE,
    allow_comments BOOLEAN DEFAULT FALSE,
    allow_private_messages BOOLEAN DEFAULT FALSE,
    target_levels JSON, -- Store array of target levels as <PERSON><PERSON><PERSON>
    split_program INT DEFAULT 0,
    currency VARCHAR(10) DEFAULT 'USD',
    days_for_preview INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
);

-- <PERSON><PERSON> splits table
CREATE TABLE kanglink_split (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    program_id INT NOT NULL,
    split_id VARCHAR(255) NOT NULL, -- Corresponds to split_id from interface
    title VARCHAR(255) NOT NULL,
    full_price DECIMAL(10, 2) NULL, -- Optional field
    subscription DECIMAL(10, 2) NULL, -- Optional field
    program_split VARCHAR(255), -- From ProgramFormData
    description TEXT, -- From ProgramFormData
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
);

-- Create weeks table
CREATE TABLE kanglink_week (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    split_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    equipment_required VARCHAR(255),
    is_collapsed BOOLEAN DEFAULT FALSE,
    week_order INT NOT NULL, -- To maintain order of weeks
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
);

-- Create days table
CREATE TABLE kanglink_day (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    week_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    is_rest_day BOOLEAN DEFAULT FALSE,
    is_collapsed BOOLEAN DEFAULT FALSE,
    day_order INT NOT NULL, -- To maintain order of days
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
);

-- Create sessions table
CREATE TABLE kanglink_session (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    day_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    session_letter VARCHAR(10),
    session_number INT,
    is_collapsed BOOLEAN DEFAULT FALSE,
    session_order INT NOT NULL, -- To maintain order of sessions
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
);

-- Create videos resource table
CREATE TABLE kanglink_video (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type TINYINT NOT NULL CHECK (type IN (1, 2)), -- 1 or 2 as specified
    url TEXT NOT NULL,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
);

-- Create exercises resource table
CREATE TABLE kanglink_exercise (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type TINYINT NOT NULL CHECK (type IN (1, 2)), -- 1 or 2 as specified
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
);

CREATE TABLE kanglink_qualification (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type TINYINT NOT NULL CHECK (type IN (1, 2)), -- 1 or 2 as specified
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
);
CREATE TABLE kanglink_specialization (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type TINYINT NOT NULL CHECK (type IN (1, 2)), -- 1 or 2 as specified
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
);

-- Create exercise instances table (renamed from exercises)
CREATE TABLE kanglink_exercise_instance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_id INT NOT NULL,
    exercise_id INT NOT NULL, -- References exercises table
    video_id INT NULL, -- References videos table (optional)
    sets VARCHAR(100),
    reps_or_time VARCHAR(100),
    reps_time_type ENUM('reps', 'time') NOT NULL,
    exercise_details TEXT,
    rest_duration_minutes INT DEFAULT 0,
    rest_duration_seconds INT DEFAULT 0,
    linked_exercise_id VARCHAR(255) NULL, -- For linking exercises together
    is_linked BOOLEAN DEFAULT FALSE,
    exercise_order INT NOT NULL, -- To maintain order of exercises
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

);

-- Create exercise_links table for managing exercise instance relationships
CREATE TABLE kanglink_exercise_link (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    primary_exercise_instance_id INT NOT NULL,
    linked_exercise_instance_id INT NOT NULL,
    link_type VARCHAR(50) DEFAULT 'superset', -- Could be 'superset', 'circuit', etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create enrollment table for trainer-athlete-program relationships
CREATE TABLE kanglink_enrollment (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trainer_id INT NOT NULL, -- User ID of the trainer
    athlete_id INT NOT NULL, -- User ID of the athlete
    program_id INT NOT NULL, -- References kanglink_program
    split_id INT NOT NULL, -- References kanglink_split
    payment_type ENUM('subscription', 'one_time') NOT NULL,
    amount DECIMAL(10, 2) NOT NULL, -- Amount paid
    currency VARCHAR(10) DEFAULT 'USD',
    enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expiry_date TIMESTAMP NULL, -- For subscriptions or time-limited access
    status ENUM('active', 'expired', 'cancelled', 'pending') DEFAULT 'active',
    payment_status ENUM('paid', 'pending', 'failed', 'refunded') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Schema Summary:
-- 1. Hierarchical structure: programs -> splits -> weeks -> days -> sessions -> exercise_instances
-- 2. Resource tables: videos and exercises (with user_id and type fields)
-- 3. Exercise instances reference both exercise and video resources
-- 4. Exercise instances can be linked together via exercise_links table
-- 5. Enrollment table manages trainer-athlete-program relationships with payment details
-- 6. All tables include proper foreign key relationships with CASCADE DELETE
-- 7. Order fields maintain sequence of items within their parent containers
-- 8. JSON fields store arrays from the TypeScript interfaces
-- 9. Proper indexing for performance on commonly queried fields
-- 10. Videos and exercises are reusable resources that can be referenced by multiple instances
-- 2. Enrollment supports both subscription and one-time payment models
