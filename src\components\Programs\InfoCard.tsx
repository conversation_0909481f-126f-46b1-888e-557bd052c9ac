import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

interface InfoCardProps {
  title: string;
  content: string[];
}

const InfoCard = ({ title, content }: InfoCardProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <div 
      className="rounded-md shadow-lg border p-6 transition-all duration-200"
      style={{
        backgroundColor: THEME_COLORS[mode].SECONDARY,
        borderColor: THEME_COLORS[mode].BORDER,
      }}
    >
      <h2 
        className="text-xl font-bold font-inter mb-4 transition-colors duration-200"
        style={{ color: THEME_COLORS[mode].TEXT }}
      >
        {title}
      </h2>
      <div className="space-y-3">
        {content.map((paragraph, index) => (
          <p 
            key={index}
            className="text-sm font-normal font-inter leading-relaxed transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
          >
            {paragraph}
          </p>
        ))}
      </div>
    </div>
  );
};

export default InfoCard;
