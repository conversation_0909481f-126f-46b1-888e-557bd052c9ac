import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Modal } from "@/components/Modal";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useCreateModelMutation, useUpdateModelMutation } from "@/query/shared";
import { useContexts } from "@/hooks/useContexts";
import { useProfile } from "@/hooks/useProfile";
import { Models } from "@/utils/baas/models";
import { Exercise } from "@/interfaces/model.interface";
import { ToastStatusEnum } from "@/utils/Enums";

interface ExerciseModalProps {
  isOpen: boolean;
  onClose: () => void;
  exercise?: Exercise | null; // For editing
  onSuccess?: (exercise: Exercise) => void;
}

interface ExerciseFormData {
  name: string;
  // exercise_type: string;
  video_url: string;
}

const ExerciseModal: React.FC<ExerciseModalProps> = ({
  isOpen,
  onClose,
  exercise,
  onSuccess,
}) => {
  const { showToast } = useContexts();
  const { profile } = useProfile();



  // Validation schema
  const schema = yup.object({
    name: yup.string().trim().required("Exercise name is required").max(150, "Exercise name must be less than 150 characters"),
    video_url: yup
      .string()
      .trim()
      .max(255, "Video URL must be less than 255 characters")
      .required("Video URL is required")
      .test("is-valid-url", "Please enter a valid URL", (value) => {
        if (!value) return false;
        try {
          const url = new URL(value);
          return ['http:', 'https:'].includes(url.protocol);
        } catch {
          return false;
        }
      })
      .test("is-valid-url", "Please enter a valid URL", (value) => {
        if (!value) return false;
        try {
          const url = new URL(value);
          return ['http:', 'https:'].includes(url.protocol);
        } catch {
          return false;
        }
      }),
  });

  // Form setup
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<ExerciseFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      name: "",
      // exercise_type: "",
      video_url: "",
    },
  });

  // API mutations
  const { mutateAsync: createExercise, isPending: isCreating } =
    useCreateModelMutation(Models.EXERCISE);
  const { mutateAsync: updateExercise, isPending: isUpdating } =
    useUpdateModelMutation(Models.EXERCISE);

  const isLoading = isCreating || isUpdating;
  const isEditMode = !!exercise;

  // Populate form when editing
  useEffect(() => {
    if (isEditMode && exercise) {
      setValue("name", exercise.name || "");
      // setValue("exercise_type", exercise.exercise_type || "");
      setValue("video_url", exercise.video_url || "");
    } else {
      reset();
    }
  }, [exercise, isEditMode, setValue, reset]);

  // Handle form submission
  const onSubmit = async (data: ExerciseFormData) => {
    try {
      const payload = {
        name: data.name,
        // exercise_type: data.exercise_type,
        video_url: data.video_url,
        type: 1, // Admin created
        user_id: profile?.id,
      };

      let result;
      if (isEditMode && exercise?.id) {
        result = await updateExercise({
          id: exercise.id,
          payload,
        });
      } else {
        result = await createExercise(payload);
      }

      showToast(
        `Exercise ${isEditMode ? "updated" : "created"} successfully`,
        5000,
        ToastStatusEnum.SUCCESS
      );

      onSuccess?.(result);
      handleClose();
    } catch (error) {
      console.error("Error saving exercise:", error);
      showToast(
        `Failed to ${isEditMode ? "update" : "create"} exercise`,
        5000,
        ToastStatusEnum.ERROR
      );
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      title={isEditMode ? "Edit Exercise" : "Add Exercise"}
      modalCloseClick={handleClose}
      modalHeader={true}
      classes={{
        modal: "h-full",
        modalDialog: "w-full max-w-md h-auto",
        modalContent: "",
      }}
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Exercise Name */}
        <MkdInputV2
          name="name"
          type="text"
          register={register}
          errors={errors}
          required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label>Exercise Name</MkdInputV2.Label>
            <MkdInputV2.Field placeholder="Enter exercise name" />
            <MkdInputV2.Error />
          </MkdInputV2.Container>
        </MkdInputV2>

        {/* Exercise Type */}
        {/* <MkdInputV2
          name="exercise_type"
          type="select"
          register={register}
          errors={errors}
          required
          options={[
            { value: "", label: "Select exercise type" },
            { value: "strength", label: "Strength" },
            { value: "cardio", label: "Cardio" },
            { value: "flexibility", label: "Flexibility" },
            { value: "balance", label: "Balance" },
            { value: "endurance", label: "Endurance" },
            { value: "plyometric", label: "Plyometric" },
            { value: "other", label: "Other" },
          ]}
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label>Exercise Type</MkdInputV2.Label>
            <MkdInputV2.Field />
            <MkdInputV2.Error />
          </MkdInputV2.Container>
        </MkdInputV2> */}

        {/* Video URL */}
        <MkdInputV2
          name="video_url"
          type="text"
          register={register}
          errors={errors}
          required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label>Video URL</MkdInputV2.Label>
            <MkdInputV2.Field placeholder="Enter video URL" />
            <MkdInputV2.Error />
          </MkdInputV2.Container>
        </MkdInputV2>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4">
          <InteractiveButton
            type="button"
            onClick={handleClose}
            className="px-4 py-2 border border-border text-text hover:bg-background-hover transition-colors"
            disabled={isLoading}
          >
            Cancel
          </InteractiveButton>
          <InteractiveButton
            type="submit"
            className="px-4 py-2 bg-primary text-white hover:bg-primary-dark transition-colors"
            disabled={isLoading}
          >
            {isLoading
              ? isEditMode
                ? "Updating..."
                : "Creating..."
              : isEditMode
                ? "Update Exercise"
                : "Create Exercise"}
          </InteractiveButton>
        </div>
      </form>
    </Modal>
  );
};

export default ExerciseModal;
