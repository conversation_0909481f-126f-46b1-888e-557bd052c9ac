import { Exercise } from "../types";

/**
 * Updates rest durations for linked exercises
 * Linked exercises should have no rest duration except for the last exercise in each group
 */
export const updateLinkedExerciseRestDurations = (exercises: Exercise[]) => {
  // Group exercises by their link labels
  const linkedGroups: { [label: string]: Exercise[] } = {};

  exercises.forEach((exercise) => {
    if (exercise.is_linked && exercise.label) {
      if (!linkedGroups[exercise.label]) {
        linkedGroups[exercise.label] = [];
      }
      linkedGroups[exercise.label].push(exercise);
    }
  });

  // For each linked group, set rest duration to 0 for all except the last exercise
  Object.values(linkedGroups).forEach((group) => {
    if (group.length > 1) {
      // Sort by exercise_order to ensure proper sequence
      group.sort((a, b) => a.exercise_order - b.exercise_order);

      // Set rest duration to 0 for all exercises except the last one
      group.forEach((exercise, index) => {
        if (index < group.length - 1) {
          // Not the last exercise in the group - no rest
          exercise.rest_duration_minutes = 0;
          exercise.rest_duration_seconds = 0;
        }
        // The last exercise keeps its rest duration (or can be set by user)
      });
    }
  });
};

/**
 * Updates exercise orders and reassigns labels for all exercises in a session
 * This ensures proper sequential ordering and label assignment after any changes
 */
export const updateExerciseOrdersAndLabels = (exercises: Exercise[]) => {
  // First, update exercise_order for all exercises based on their position
  exercises.forEach((ex, index) => {
    ex.exercise_order = index + 1;
  });

  // Create a list of exercise groups (linked groups and individual exercises)
  // Each group will get a sequential letter label (A, B, C, etc.)
  const exerciseGroups: Exercise[][] = [];
  const processedExercises = new Set<string>();

  // Process exercises in order to maintain their sequence
  exercises.forEach((ex) => {
    if (processedExercises.has(ex.id)) return;

    if (ex.is_linked && ex.label) {
      // Find all exercises in this linked group
      const linkedGroup = exercises.filter(
        (otherEx) =>
          otherEx.is_linked &&
          otherEx.label === ex.label &&
          !processedExercises.has(otherEx.id)
      );

      // Sort the linked group by exercise_order
      linkedGroup.sort((a, b) => a.exercise_order - b.exercise_order);

      // Add the group and mark all exercises as processed
      exerciseGroups.push(linkedGroup);
      linkedGroup.forEach((groupEx) => processedExercises.add(groupEx.id));
    } else {
      // Individual unlinked exercise forms its own group
      exerciseGroups.push([ex]);
      processedExercises.add(ex.id);
    }
  });

  // Now assign labels to each group sequentially
  exerciseGroups.forEach((group, groupIndex) => {
    const groupLabel = String.fromCharCode(65 + groupIndex); // A, B, C, etc.

    if (group.length === 1) {
      // Single exercise (unlinked) - still gets a label but is not linked
      const exercise = group[0];
      exercise.is_linked = false;
      exercise.label = groupLabel;
      exercise.label_number = "1";
    } else {
      // Linked group
      group.forEach((exercise, exerciseIndex) => {
        exercise.is_linked = true;
        exercise.label = groupLabel;
        exercise.label_number = (exerciseIndex + 1).toString();
      });
    }
  });

  // Update rest durations for linked exercises after label assignment
  updateLinkedExerciseRestDurations(exercises);
};
