import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { ClockIcon } from "@heroicons/react/24/outline";

interface SessionHeaderProps {
  sessionTitle: string;
  duration: string;
}

const SessionHeader = ({ sessionTitle, duration }: SessionHeaderProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const headerStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderColor: THEME_COLORS[mode].BORDER,
    color: THEME_COLORS[mode].TEXT,
  };

  return (
    <div 
      className="bg-background border-b border-border p-4 sm:p-6 transition-colors duration-200"
      style={headerStyles}
    >
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        {/* Session Title */}
        <div className="flex items-center gap-3">
          <div className="w-4 h-4 bg-primary rounded-sm flex-shrink-0"></div>
          <h2 className="text-xl sm:text-2xl font-bold text-text">
            {sessionTitle}
          </h2>
        </div>

        {/* Duration */}
        <div className="flex items-center gap-2 text-text-secondary">
          <ClockIcon className="w-4 h-4" />
          <span className="text-sm font-normal">
            {duration}
          </span>
        </div>
      </div>
    </div>
  );
};

export default SessionHeader;
