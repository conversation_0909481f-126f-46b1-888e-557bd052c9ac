/**
 * Trainer Program Management API Specifications
 *
 * This file contains all API endpoints, request/response schemas, and data structures
 * needed for the Add Trainer Program page functionality.
 *
 * The program creation process involves:
 * 1. Step One: Basic program info, payment plans, settings, target levels, splits, and pricing
 * 2. Step Two: Program structure with weeks, days, sessions, and exercises
 * 3. Preview: Final review before saving as draft or publishing
 */

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface ProgramSplit {
  split_id: string;
  title: string;
  full_price?: number;
  subscription?: number;
}

export interface StepOneFormData {
  program_name: string;
  type_of_program: string;
  program_description: string;
  payment_plan: string[]; // ["oneTime", "monthly"]
  track_progress: boolean;
  allow_comments: boolean;
  allow_private_messages: boolean;
  target_levels: string[]; // ["beginner", "intermediate", "expert"]
  split_program: number;
  splits: ProgramSplit[];
  currency: string;
  days_for_preview: number;
}

export interface RestDuration {
  minutes: number;
  seconds: number;
}

export interface Exercise {
  id: string;
  name: string;
  sets: string;
  repsOrTime: string;
  repsTimeType: "reps" | "time";
  videoUrl: string;
  exerciseDetails: string;
  restDuration: RestDuration;
  linkedExerciseId: string | null;
  isLinked: boolean;
}

export interface Session {
  id: string;
  name: string;
  sessionLetter: string;
  sessionNumber: number;
  isCollapsed: boolean;
  exercises: Exercise[];
  linkedSessionId?: string | null;
}

export interface Day {
  id: string;
  name: string;
  isRestDay: boolean;
  isCollapsed: boolean;
  sessions: Session[];
}

export interface Week {
  id: string;
  name: string;
  isCollapsed: boolean;
  days: Day[];
}

export interface StepTwoFormData {
  programSplit: string;
  description: string;
  equipmentRequired: string;
  weeks: Week[];
  splitConfigurations: {
    [splitId: string]: Week[];
  };
  status: "draft" | "published";
}

export interface CompleteProgram extends StepOneFormData, StepTwoFormData {
  id?: number;
  trainerId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ExerciseLibraryItem {
  id: string;
  name: string;
  createdBy: "admin" | "trainer";
  trainerId?: string;
  category?: string;
  muscleGroups?: string[];
  equipment?: string[];
}

export interface VideoLibraryItem {
  id: string;
  name: string;
  url: string;
  createdBy: "admin" | "trainer";
  trainerId?: string;
  duration?: number;
  thumbnailUrl?: string;
}

// ============================================================================
// API ENDPOINT SPECIFICATIONS
// ============================================================================

export const TrainerProgramAPIs = {
  // 1. CREATE PROGRAM (SAVE AS DRAFT)
  CREATE_PROGRAM_DRAFT: {
    url: "/v2/api/kanglink/custom/trainer/programs/draft",
    method: "POST",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    body: {
      stepOneData: "StepOneFormData",
      stepTwoData: "StepTwoFormData",
    },
    response: {
      success: "boolean",
      message: "string",
      data: {
        programId: "number",
        status: "draft",
        createdAt: "string (ISO date)",
        validationErrors: "string[] | null",
      },
    },
  },

  // 2. PUBLISH PROGRAM
  PUBLISH_PROGRAM: {
    url: "/v2/api/kanglink/custom/trainer/programs/publish",
    method: "POST",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    body: {
      stepOneData: "StepOneFormData",
      stepTwoData: "StepTwoFormData",
    },
    response: {
      success: "boolean",
      message: "string",
      data: {
        programId: "number",
        status: "published",
        publishedAt: "string (ISO date)",
        programUrl: "string",
        validationErrors: "string[] | null",
      },
    },
  },

  // 3. UPDATE EXISTING PROGRAM
  UPDATE_PROGRAM: {
    url: "/v2/api/kanglink/custom/trainer/programs/{programId}",
    method: "PUT",
    params: {
      programId: "number",
    },
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    body: {
      stepOneData: "StepOneFormData",
      stepTwoData: "StepTwoFormData",
      status: "'draft' | 'published'",
    },
    response: {
      success: "boolean",
      message: "string",
      data: {
        programId: "number",
        status: "string",
        updatedAt: "string (ISO date)",
        validationErrors: "string[] | null",
      },
    },
  },

  // 4. GET PROGRAM BY ID (FOR EDITING)
  GET_PROGRAM: {
    url: "/v2/api/kanglink/custom/trainer/programs/{programId}",
    method: "GET",
    params: {
      programId: "number",
    },
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    response: {
      success: "boolean",
      data: {
        program: "CompleteProgram",
        lastModified: "string (ISO date)",
        canEdit: "boolean",
      },
    },
  },

  // 5. GET EXERCISE LIBRARY
  GET_EXERCISE_LIBRARY: {
    url: "/v2/api/kanglink/custom/trainer/exercises",
    method: "GET",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    queryParams: {
      search: "string (optional)",
      category: "string (optional)",
      muscleGroup: "string (optional)",
      page: "number (optional, default: 1)",
      limit: "number (optional, default: 50)",
    },
    response: {
      success: "boolean",
      data: {
        exercises: "ExerciseLibraryItem[]",
        pagination: {
          page: "number",
          limit: "number",
          total: "number",
          totalPages: "number",
        },
      },
    },
  },

  // 6. ADD CUSTOM EXERCISE
  ADD_CUSTOM_EXERCISE: {
    url: "/v2/api/kanglink/custom/trainer/exercises",
    method: "POST",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    body: {
      name: "string",
      category: "string (optional)",
      muscleGroups: "string[] (optional)",
      equipment: "string[] (optional)",
      saveToLibrary: "boolean",
    },
    response: {
      success: "boolean",
      message: "string",
      data: {
        exercise: "ExerciseLibraryItem",
        savedToLibrary: "boolean",
      },
    },
  },

  // 7. GET VIDEO LIBRARY
  GET_VIDEO_LIBRARY: {
    url: "/v2/api/kanglink/custom/trainer/videos",
    method: "GET",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    queryParams: {
      search: "string (optional)",
      page: "number (optional, default: 1)",
      limit: "number (optional, default: 50)",
    },
    response: {
      success: "boolean",
      data: {
        videos: "VideoLibraryItem[]",
        pagination: {
          page: "number",
          limit: "number",
          total: "number",
          totalPages: "number",
        },
      },
    },
  },

  // 8. UPLOAD VIDEO
  UPLOAD_VIDEO: {
    url: "/v2/api/kanglink/custom/trainer/videos/upload",
    method: "POST",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "multipart/form-data",
    },
    body: {
      file: "File",
      name: "string",
      saveToLibrary: "boolean",
    },
    response: {
      success: "boolean",
      message: "string",
      data: {
        video: "VideoLibraryItem",
        uploadUrl: "string",
        savedToLibrary: "boolean",
      },
    },
  },

  // 9. ADD VIDEO BY URL
  ADD_VIDEO_BY_URL: {
    url: "/v2/api/kanglink/custom/trainer/videos",
    method: "POST",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    body: {
      name: "string",
      url: "string",
      saveToLibrary: "boolean",
    },
    response: {
      success: "boolean",
      message: "string",
      data: {
        video: "VideoLibraryItem",
        savedToLibrary: "boolean",
      },
    },
  },

  // 10. VALIDATE PROGRAM DATA
  VALIDATE_PROGRAM: {
    url: "/v2/api/kanglink/custom/trainer/programs/validate",
    method: "POST",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    body: {
      stepOneData: "StepOneFormData",
      stepTwoData: "StepTwoFormData",
    },
    response: {
      success: "boolean",
      data: {
        isValid: "boolean",
        errors: "string[]",
        warnings: "string[]",
      },
    },
  },

  // 11. GET PROGRAM TYPES/CATEGORIES
  GET_PROGRAM_TYPES: {
    url: "/v2/api/kanglink/custom/trainer/program-types",
    method: "GET",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    response: {
      success: "boolean",
      data: {
        programTypes: [
          {
            value: "string",
            label: "string",
            description: "string (optional)",
          },
        ],
      },
    },
  },

  // 12. GET CURRENCIES
  GET_CURRENCIES: {
    url: "/v2/api/kanglink/custom/trainer/currencies",
    method: "GET",
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    response: {
      success: "boolean",
      data: {
        currencies: [
          {
            code: "string",
            name: "string",
            symbol: "string",
          },
        ],
      },
    },
  },

  // 13. DELETE PROGRAM
  DELETE_PROGRAM: {
    url: "/v2/api/kanglink/custom/trainer/programs/{programId}",
    method: "DELETE",
    params: {
      programId: "number",
    },
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    response: {
      success: "boolean",
      message: "string",
      data: {
        programId: "number",
        deletedAt: "string (ISO date)",
      },
    },
  },

  // 14. DUPLICATE PROGRAM
  DUPLICATE_PROGRAM: {
    url: "/v2/api/kanglink/custom/trainer/programs/{programId}/duplicate",
    method: "POST",
    params: {
      programId: "number",
    },
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json",
    },
    body: {
      newProgramName: "string",
      copyPricing: "boolean (optional, default: true)",
      copyStructure: "boolean (optional, default: true)",
    },
    response: {
      success: "boolean",
      message: "string",
      data: {
        originalProgramId: "number",
        newProgramId: "number",
        newProgramName: "string",
        createdAt: "string (ISO date)",
      },
    },
  },
};
