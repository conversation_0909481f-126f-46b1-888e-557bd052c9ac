import React, { useState } from "react";

interface ReactionButtonProps {
  current_reaction?: string | null;
  reaction_count: number;
  on_reaction_toggle: (reaction_type: "like" | "love" | "fire" | "strong") => void;
  is_loading?: boolean;
}

const ReactionButton: React.FC<ReactionButtonProps> = ({
  current_reaction,
  reaction_count,
  on_reaction_toggle,
  is_loading = false,
}) => {
  const [show_reactions, setShowReactions] = useState(false);

  const reactions = [
    { type: "like", emoji: "👍", label: "Like" },
    { type: "love", emoji: "❤️", label: "Love" },
    { type: "fire", emoji: "🔥", label: "Fire" },
    { type: "strong", emoji: "💪", label: "Strong" },
  ] as const;

  const current_reaction_data = reactions.find(r => r.type === current_reaction);

  const handleReactionClick = (reaction_type: "like" | "love" | "fire" | "strong") => {
    on_reaction_toggle(reaction_type);
    setShowReactions(false);
  };

  return (
    <div className="relative">
      {/* Main reaction button */}
      <button
        onClick={() => setShowReactions(!show_reactions)}
        disabled={is_loading}
        className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 text-sm font-medium disabled:opacity-50 ${
          current_reaction
            ? "bg-primary/10 text-primary hover:bg-primary/20"
            : "text-text-secondary hover:bg-background-secondary hover:text-text"
        }`}
      >
        <span className="text-lg">
          {current_reaction_data?.emoji || "👍"}
        </span>
        <span>{reaction_count}</span>
        {is_loading && (
          <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin"></div>
        )}
      </button>

      {/* Reaction picker */}
      {show_reactions && (
        <div className="absolute bottom-full left-0 mb-2 bg-background border border-border rounded-lg shadow-lg p-2 flex space-x-1 z-10">
          {reactions.map((reaction) => (
            <button
              key={reaction.type}
              onClick={() => handleReactionClick(reaction.type)}
              disabled={is_loading}
              className={`p-2 rounded-lg hover:bg-background-secondary transition-all duration-200 disabled:opacity-50 ${
                current_reaction === reaction.type ? "bg-primary/10" : ""
              }`}
              title={reaction.label}
            >
              <span className="text-lg">{reaction.emoji}</span>
            </button>
          ))}
        </div>
      )}

      {/* Backdrop to close reaction picker */}
      {show_reactions && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowReactions(false)}
        />
      )}
    </div>
  );
};

export default ReactionButton;
