import React, { useState } from "react";
import { MkdButton } from "@/components/MkdButton";
import { useCustomerCards, StripeCard } from "@/hooks/useCustomerCards";
import { TransformedProgramData } from "@/interfaces";
import { DiscountPreviewResponse } from "@/hooks/useEnrollment";

interface CardConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUseExistingCard: (card: StripeCard, couponCode?: string) => void;
  onAddNewCard: (couponCode?: string) => void;
  splitName: string;
  paymentType: "subscription" | "one_time";
  amount: number;
  currency: string;
  splitId: number | string;
  programData?: TransformedProgramData | null;
  // Centralized discount preview props
  discountPreview: DiscountPreviewResponse["data"] | null;
  isLoadingDiscount: boolean;
  couponError: string | null;
  onCouponCodeChange: (code: string) => void;
  initialCouponCode?: string;
}

const CardConfirmationModal: React.FC<CardConfirmationModalProps> = ({
  isOpen,
  onClose,
  onUseExistingCard,
  onAddNewCard,
  splitName,
  paymentType,
  amount,
  currency,
  splitId: _splitId,
  programData,
  discountPreview,
  isLoadingDiscount,
  couponError,
  onCouponCodeChange,
  initialCouponCode = "",
}) => {
  const [selectedCardId, setSelectedCardId] = useState<string>("");
  const [couponCode, setCouponCode] = useState(initialCouponCode);

  const { useGetCustomerCards } = useCustomerCards();
  const { data: cardsData, isLoading } = useGetCustomerCards(isOpen);

  const cards = cardsData?.payment_methods || [];
  // Note: The new API doesn't provide is_default, so we'll use the first card as default
  const defaultCard = cards.length > 0 ? cards[0] : null;
  const selectedCard =
    cards.find((card) => card.id === selectedCardId) || defaultCard;

  // Check if coupons should be shown for this payment type
  const shouldShowCoupons = () => {
    if (!programData?.coupon) return false;

    const coupon = programData.coupon;
    if (!coupon.is_active) return false;

    // Check if coupon applies to the current payment type
    const appliesTo = coupon.applies_to;
    if (appliesTo === "both") return true;
    if (appliesTo === "subscription" && paymentType === "subscription")
      return true;
    if (appliesTo === "full_payment" && paymentType === "one_time") return true;

    return false;
  };

  // Handle coupon code input changes - delegate to parent
  const handleCouponCodeChange = (code: string) => {
    setCouponCode(code);
    onCouponCodeChange(code);
  };

  // Auto-select default card if available
  React.useEffect(() => {
    if (defaultCard && !selectedCardId) {
      setSelectedCardId(defaultCard.id);
    }
  }, [defaultCard, selectedCardId]);

  const handleUseExistingCard = () => {
    if (selectedCard) {
      onUseExistingCard(selectedCard, couponCode.trim() || undefined);
    }
  };

  const handleAddNewCard = () => {
    onAddNewCard(couponCode.trim() || undefined);
  };

  const getCardBrandIcon = (brand: string) => {
    switch (brand.toLowerCase()) {
      case "visa":
        return "💳";
      case "mastercard":
        return "💳";
      case "amex":
        return "💳";
      case "discover":
        return "💳";
      default:
        return "💳";
    }
  };

  const formatCardDisplay = (card: StripeCard) => {
    return `${getCardBrandIcon(card.card.brand)} •••• ${card.card.last4} (${card.card.exp_month}/${card.card.exp_year})`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-background rounded-lg p-6 w-full max-w-md mx-4 border border-border">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-text">
            {paymentType === "subscription" ? "Subscribe to" : "Purchase"}{" "}
            {splitName}
          </h2>
          <button
            onClick={onClose}
            className="text-text-secondary hover:text-text"
          >
            ✕
          </button>
        </div>

        <div className="mb-6">
          {/* Pricing Display */}
          {discountPreview && discountPreview.total_discount_amount > 0 ? (
            <div className="mb-2">
              <div className="text-lg text-text-secondary line-through">
                ${amount} {currency}
                {paymentType === "subscription" && (
                  <span className="text-sm font-normal">/month</span>
                )}
              </div>
              <div className="text-2xl font-bold text-primary">
                ${discountPreview.final_amount} {currency}
                {paymentType === "subscription" && (
                  <span className="text-sm font-normal">/month</span>
                )}
              </div>
              <div className="text-sm text-green-600 dark:text-green-400">
                You save ${discountPreview.total_discount_amount}!
              </div>
            </div>
          ) : (
            <div className="text-2xl font-bold text-primary mb-2">
              ${amount} {currency}
              {paymentType === "subscription" && (
                <span className="text-sm font-normal">/month</span>
              )}
            </div>
          )}
          <p className="text-sm text-text-secondary">
            {paymentType === "subscription"
              ? "Monthly billing, access with automatic updates"
              : "Lifetime access, no updates when trainer modifies split"}
          </p>
        </div>

        {/* Coupon Code Input */}
        {shouldShowCoupons() && (
          <div className="mb-6">
            <label
              htmlFor="coupon-code"
              className="block text-sm font-medium text-text mb-2"
            >
              Coupon Code (Optional)
            </label>
            <input
              id="coupon-code"
              type="text"
              value={couponCode}
              onChange={(e) => handleCouponCodeChange(e.target.value)}
              placeholder="Enter coupon code"
              className="w-full uppercase px-3 py-2 border border-border rounded-md bg-input text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
            {isLoadingDiscount && (
              <div className="mt-2 text-sm text-text-secondary">
                Checking discount...
              </div>
            )}
            {couponError && (
              <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                <div className="text-sm text-red-800 dark:text-red-200">
                  ✗ {couponError}
                </div>
              </div>
            )}
            {discountPreview &&
              discountPreview.applied_discounts &&
              discountPreview.applied_discounts.length > 0 && (
                <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
                  <div className="text-sm text-green-800 dark:text-green-200">
                    {discountPreview.applied_discounts.map(
                      (discount, index: number) => (
                        <div key={index}>
                          ✓ {discount.type === "coupon" ? "Coupon" : "Discount"}{" "}
                          applied: ${discount.discount_amount} off
                        </div>
                      )
                    )}
                  </div>
                </div>
              )}
          </div>
        )}

        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-text-secondary">Loading payment methods...</p>
          </div>
        ) : cards.length > 0 ? (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-text">
              Choose Payment Method
            </h3>

            {/* Existing Cards */}
            <div className="space-y-2">
              {cards.map((card) => (
                <label
                  key={card.id}
                  className={`flex items-center p-3 border rounded-md cursor-pointer transition-colors ${
                    selectedCardId === card.id
                      ? "border-primary bg-primary/5"
                      : "border-border hover:border-primary/50"
                  }`}
                >
                  <input
                    type="radio"
                    name="payment-method"
                    value={card.id}
                    checked={selectedCardId === card.id}
                    onChange={(e) => setSelectedCardId(e.target.value)}
                    className="mr-3"
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="text-text font-medium">
                        {formatCardDisplay(card)}
                      </span>
                      {card === defaultCard && (
                        <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                          Default
                        </span>
                      )}
                    </div>
                    <div className="text-xs text-text-secondary capitalize">
                      {card.card.brand} • {card.card.funding || "card"}
                    </div>
                  </div>
                </label>
              ))}
            </div>

            {/* Add New Card Option */}
            <label className="flex items-center p-3 border border-border rounded-md cursor-pointer hover:border-primary/50 transition-colors">
              <input
                type="radio"
                name="payment-method"
                value="new-card"
                checked={selectedCardId === "new-card"}
                onChange={(e) => setSelectedCardId(e.target.value)}
                className="mr-3"
              />
              <div className="flex items-center">
                <span className="text-2xl mr-3">➕</span>
                <span className="text-text font-medium">Add new card</span>
              </div>
            </label>

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <MkdButton
                onClick={onClose}
                className="flex-1 !bg-transparent px-4 py-2 border !border-border rounded-md !text-text-secondary hover:text-text hover:border-primary/50 transition-colors"
              >
                Cancel
              </MkdButton>
              <MkdButton
                onClick={
                  selectedCardId === "new-card"
                    ? handleAddNewCard
                    : handleUseExistingCard
                }
                disabled={!selectedCardId}
                className="flex-1"
              >
                {selectedCardId === "new-card"
                  ? "Add New Card"
                  : `Pay $${discountPreview ? discountPreview.final_amount : amount} ${currency}`}
              </MkdButton>
            </div>
          </div>
        ) : (
          <div className="text-center space-y-4">
            <div className="text-6xl mb-4">💳</div>
            <h3 className="text-lg font-medium text-text">
              No Payment Methods Found
            </h3>
            <p className="text-text-secondary mb-6">
              You need to add a payment method to continue with your{" "}
              {paymentType === "subscription" ? "subscription" : "purchase"}.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-border rounded-md text-text-secondary hover:text-text hover:border-primary/50 transition-colors"
              >
                Cancel
              </button>
              <MkdButton onClick={onAddNewCard} className="flex-1">
                Add Payment Method
              </MkdButton>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CardConfirmationModal;

// {
//   "id": "evt_1RhxqxBgOlWo0lDUlyJ4zvac",
//   "object": "event",
//   "api_version": "2022-08-01",
//   "created": 1751828635,
//   "data": {
//     "object": {
//       "id": "di_1RhxqwBgOlWo0lDU0c8ouGrz",
//       "object": "discount",
//       "checkout_session": null,
//       "coupon": {
//         "id": "HPx8tnp3",
//         "object": "coupon",
//         "amount_off": null,
//         "created": 1751828633,
//         "currency": "usd",
//         "duration": "forever",
//         "duration_in_months": null,
//         "livemode": false,
//         "max_redemptions": null,
//         "metadata": {
//         },
//         "name": "Discount for Split 129 - 19% off",
//         "percent_off": 19,
//         "redeem_by": null,
//         "times_redeemed": 1,
//         "valid": true
//       },
//       "customer": "cus_ScO9bzD0YvmpQY",
//       "end": null,
//       "invoice": null,
//       "invoice_item": null,
//       "promotion_code": null,
//       "start": 1751828634,
//       "subscription": "sub_1RhxqwBgOlWo0lDUnad4nrgb",
//       "subscription_item": null
//     }
//   },
//   "livemode": false,
//   "pending_webhooks": 2,
//   "request": {
//     "id": "req_wxChuJqFZSMIFe",
//     "idempotency_key": "be464e9f-fac4-4b34-88aa-1433f2842ff2"
//   },
//   "type": "customer.discount.created"
// }
