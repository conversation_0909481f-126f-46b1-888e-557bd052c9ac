import React from "react";
import { UseFormRegister, FieldErrors } from "react-hook-form";
import { FormData } from "../types";

interface TargetLevelsSectionProps {
  register: UseFormRegister<FormData>;
  errors: FieldErrors<FormData>;
}

const TargetLevelsSection: React.FC<TargetLevelsSectionProps> = ({
  register,
  errors,
}) => {
  return (
    <div className="space-y-3">
      <label className="text-sm font-medium text-text">Target Levels</label>
      <div className="space-y-2">
        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            value="beginner"
            {...register("target_levels")}
            className="w-4 h-4 text-primary border-border focus:ring-primary focus:ring-2"
          />
          <span className="text-sm text-text">Beginner</span>
        </label>
        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            value="intermediate"
            {...register("target_levels")}
            className="w-4 h-4 text-primary border-border focus:ring-primary focus:ring-2"
          />
          <span className="text-sm text-text">Intermediate</span>
        </label>
        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            value="expert"
            {...register("target_levels")}
            className="w-4 h-4 text-primary border-border focus:ring-primary focus:ring-2"
          />
          <span className="text-sm text-text">Expert</span>
        </label>
      </div>
      {errors.target_levels && (
        <p className="text-sm text-red-500">{errors.target_levels.message}</p>
      )}
    </div>
  );
};

export default TargetLevelsSection;
