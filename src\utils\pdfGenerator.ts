import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { Transaction } from "@/interfaces/model.interface";

export interface PDFGenerationOptions {
  title?: string;
  subtitle?: string;
  includeFilters?: {
    searchTerm?: string;
    dateFilter?: string;
    statusFilter?: string;
  };
  companyInfo?: {
    name?: string;
    address?: string;
    phone?: string;
    email?: string;
  };
}

/**
 * Generate a PDF report from transaction data
 */
export const generateTransactionsPDF = (
  transactions: Transaction[],
  options: PDFGenerationOptions = {}
): void => {
  try {
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;

    // Default options
    const {
      title = "Transaction Report",
      subtitle = "Admin Transaction Summary",
      includeFilters,
      companyInfo = {
        name: "KangLink",
        address: "123 Business Street, City, State 12345",
        phone: "+****************",
        email: "<EMAIL>",
      },
    } = options;

    let yPosition = 20;

    // Header with company info
    doc.setFontSize(20);
    doc.setFont("helvetica", "bold");
    doc.text(companyInfo.name || "KangLink", 20, yPosition);

    yPosition += 10;
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");
    doc.text(companyInfo.address || "", 20, yPosition);

    yPosition += 5;
    doc.text(
      `Phone: ${companyInfo.phone || ""} | Email: ${companyInfo.email || ""}`,
      20,
      yPosition
    );

    // Add line separator
    yPosition += 10;
    doc.line(20, yPosition, pageWidth - 20, yPosition);

    // Title
    yPosition += 15;
    doc.setFontSize(16);
    doc.setFont("helvetica", "bold");
    doc.text(title, 20, yPosition);

    yPosition += 8;
    doc.setFontSize(12);
    doc.setFont("helvetica", "normal");
    doc.text(subtitle, 20, yPosition);

    // Date generated
    yPosition += 8;
    doc.setFontSize(10);
    doc.text(
      `Generated on: ${new Date().toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      })}`,
      20,
      yPosition
    );

    // Applied filters (if any)
    if (includeFilters) {
      yPosition += 10;
      doc.setFont("helvetica", "bold");
      doc.text("Applied Filters:", 20, yPosition);

      yPosition += 6;
      doc.setFont("helvetica", "normal");

      if (includeFilters.searchTerm) {
        doc.text(`• Search: "${includeFilters.searchTerm}"`, 25, yPosition);
        yPosition += 5;
      }

      if (
        includeFilters.dateFilter &&
        includeFilters.dateFilter !== "Show All"
      ) {
        doc.text(`• Date: ${includeFilters.dateFilter}`, 25, yPosition);
        yPosition += 5;
      }

      if (
        includeFilters.statusFilter &&
        includeFilters.statusFilter !== "Show All"
      ) {
        doc.text(`• Status: ${includeFilters.statusFilter}`, 25, yPosition);
        yPosition += 5;
      }
    }

    // Summary statistics
    yPosition += 10;
    const totalTransactions = transactions.length;
    const totalAmount = transactions.reduce(
      (sum, t) => sum + (Number(t.total_amount) || 0),
      0
    );
    const totalCoachRevenue = transactions.reduce(
      (sum, t) => sum + (Number(t.coach_revenue) || 0),
      0
    );
    const totalPlatformEarning = transactions.reduce(
      (sum, t) => sum + (Number(t.platform_earning) || 0),
      0
    );

    doc.setFont("helvetica", "bold");
    doc.text("Summary:", 20, yPosition);

    yPosition += 8;
    doc.setFont("helvetica", "normal");
    doc.text(`Total Transactions: ${totalTransactions}`, 20, yPosition);

    yPosition += 5;
    doc.text(`Total Amount: $${totalAmount.toFixed(2)}`, 20, yPosition);

    yPosition += 5;
    doc.text(
      `Total Coach Revenue: $${totalCoachRevenue.toFixed(2)}`,
      20,
      yPosition
    );

    yPosition += 5;
    doc.text(
      `Total Platform Earnings: $${totalPlatformEarning.toFixed(2)}`,
      20,
      yPosition
    );

    // Prepare table data
    const tableData = transactions.map((transaction) => [
      transaction.id?.toString() || "N/A",
      transaction.trainer_name || "Unknown",
      transaction.created_at
        ? new Date(transaction.created_at).toLocaleDateString()
        : "N/A",
      transaction.payment_status || "N/A",
      `$${(Number(transaction.total_amount) || 0).toFixed(2)}`,
      `$${(Number(transaction.coach_revenue) || 0).toFixed(2)}`,
      `$${(Number(transaction.platform_earning) || 0).toFixed(2)}`,
    ]);

    // Generate table
    yPosition += 15;

    autoTable(doc, {
      head: [
        [
          "ID",
          "Trainer",
          "Date",
          "Status",
          "Total Amount",
          "Coach Revenue",
          "Platform Earning",
        ],
      ],
      body: tableData,
      startY: yPosition,
      styles: {
        fontSize: 8,
        cellPadding: 3,
      },
      headStyles: {
        fillColor: [66, 139, 202], // Bootstrap primary blue
        textColor: 255,
        fontStyle: "bold",
      },
      alternateRowStyles: {
        fillColor: [245, 245, 245],
      },
      columnStyles: {
        0: { cellWidth: 15 }, // ID
        1: { cellWidth: 30 }, // Trainer
        2: { cellWidth: 25 }, // Date
        3: { cellWidth: 20 }, // Status
        4: { cellWidth: 25 }, // Total Amount
        5: { cellWidth: 25 }, // Coach Revenue
        6: { cellWidth: 25 }, // Platform Earning
      },
      margin: { left: 20, right: 20 },
      didDrawPage: (_data) => {
        // Footer
        const pageNumber = (doc as any).internal.getCurrentPageInfo()
          .pageNumber;
        const totalPages = (doc as any).internal.pages.length - 1;

        doc.setFontSize(8);
        doc.setFont("helvetica", "normal");
        doc.text(
          `Page ${pageNumber} of ${totalPages}`,
          pageWidth - 40,
          pageHeight - 10
        );

        doc.text("Generated by KangLink Admin Panel", 20, pageHeight - 10);
      },
    });

    // Save the PDF
    const fileName = `transactions_report_${new Date().toISOString().split("T")[0]}.pdf`;
    doc.save(fileName);
  } catch {
    throw new Error("Failed to generate PDF report");
  }
};
