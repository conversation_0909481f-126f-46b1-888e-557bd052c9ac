import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useTheme } from "@/hooks/useTheme";
import { useSDK } from "@/hooks/useSDK";
import {
  ProgramWeekSection,
  ProgramSetupSection,
  FormActionButtons,
} from "./components";
import {
  CreateProgramStepTwoProps,
  Week,
  Day,
  Session,
  Exercise,
} from "./types";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import { updateExerciseOrdersAndLabels } from "./utils/exerciseUtils";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { useQueryClient } from "@tanstack/react-query";
import { Models } from "@/utils/baas";
import { queryKeys } from "@/query/queryKeys";

// Form validation schema - simplified for form handling
const schema = yup.object({
  program_split: yup.string().required("Program split is required"),
  description: yup.string().required("Description is required"),
  weeks: yup.array().required("Weeks are required"),
});

type FormData = yup.InferType<typeof schema>;

const CreateProgramStepTwo: React.FC<CreateProgramStepTwoProps> = ({
  onSubmit,
  onCancel: _onCancel,
  onBack,
  onPreview,
  stepOneData,
  initialData,
}) => {
  const { state: _state } = useTheme();
  const { sdk } = useSDK();
  const { showToast } = useContexts();

  const queryClient = useQueryClient();

  // Image upload state
  const [programImage, setProgramImage] = useState<File | null>(null);
  const [programImagePreview, setProgramImagePreview] = useState<string | null>(
    initialData?.image || null
  );
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  // Handle image upload
  const handleImageChange = (file: File | null, previewUrl: string | null) => {
    setProgramImage(file);
    setProgramImagePreview(previewUrl);
  };

  // Upload image to server and return URL
  const uploadImageToServer = async (file: File): Promise<string | null> => {
    try {
      setIsUploadingImage(true);
      const formData = new FormData();
      formData.append("file", file);

      const result = await sdk?.uploadImage(formData);

      if (result && !result.error && result.url) {
        return result.url;
      } else {
        throw new Error("Upload failed");
      }
    } catch (error) {
      console.error("Image upload error:", error);
      showToast("Failed to upload image", 3000, ToastStatusEnum.ERROR);
      return null;
    } finally {
      setIsUploadingImage(false);
    }
  };

  // Use custom model query for API calls
  const { mutate: saveProgramMutation, isPending: isSaving } =
    useCustomModelQuery();
  const { mutate: publishProgramMutation, isPending: isPublishing } =
    useCustomModelQuery();

  // Initialize default weeks structure
  const getDefaultWeeks = (): Week[] => {
    const defaultWeeks: Week[] = [
      {
        id: "week-1",
        title: "Week 1",
        week_order: 1,
        days: [
          {
            id: "day-1",
            title: "Day 1",
            day_order: 1,
            is_rest_day: false,
            sessions: [
              {
                id: "session-1",
                title: "Session 1",
                exercises: [
                  {
                    id: "exercise-1",
                    sets: "",
                    reps_or_time: "",
                    exercise_details: "",
                    rest_duration_minutes: 0,
                    rest_duration_seconds: 0,
                    label: null, // Will be updated by updateExerciseOrdersAndLabels
                    label_number: "1", // Will be updated by updateExerciseOrdersAndLabels
                    is_linked: false,
                    exercise_order: 1, // Will be updated by updateExerciseOrdersAndLabels
                    user_id: 0, 
                    time_minutes: 0,
                    time_seconds: 0,
                    session_id: "",
                    exercise_id: "",
                    video_url: null,
                    reps_time_type: "reps",
                  },
                ],
                session_order: 1,
              },
            ],
          },
        ],
      },
    ];

    // Initialize labels for all exercises in the default structure
    defaultWeeks.forEach((week) => {
      week.days.forEach((day) => {
        day.sessions.forEach((session) => {
          updateExerciseOrdersAndLabels(session.exercises);
        });
      });
    });

    return defaultWeeks;
  };
  // Initialize state with initialData if provided
  const getInitialSplitConfigurations = () => {
    if (initialData?.splitConfigurations) {
      return initialData.splitConfigurations;
    }
    return {};
  };

  const getInitialCurrentSplit = () => {
    if (initialData?.program_split) {
      return initialData.program_split;
    }
    return stepOneData?.splits[0]?.split_id || "split-0";
  };

  const getInitialCurrentWeeks = () => {
    const currentSplitId = getInitialCurrentSplit();
    if (initialData?.splitConfigurations?.[currentSplitId]) {
      const weeks: Week[] = initialData.splitConfigurations[currentSplitId];
      // Ensure labels are properly initialized for loaded data
      weeks.forEach((week: Week) => {
        week.days.forEach((day: Day) => {
          day.sessions.forEach((session: Session) => {
            updateExerciseOrdersAndLabels(session.exercises);
          });
        });
      });
      return weeks;
    }
    return getDefaultWeeks();
  };

  // State to track configurations for each split
  const [splitConfigurations, setSplitConfigurations] = useState<{
    [key: string]: Week[];
  }>(getInitialSplitConfigurations());
  const [currentSplit, setCurrentSplit] = useState<string>(
    getInitialCurrentSplit()
  );
  const [currentWeeks, setCurrentWeeks] = useState<Week[]>(
    getInitialCurrentWeeks()
  );

  // State to track equipment for each split
  const [splitEquipment, setSplitEquipment] = useState<{
    [key: string]: string;
  }>(() => {
    // Initialize from stepOneData splits if available
    if (stepOneData?.splits) {
      const equipmentMap: { [key: string]: string } = {};
      stepOneData.splits.forEach((split: any) => {
        if (split.equipment_required) {
          equipmentMap[split?.id || split.split_id] = split.equipment_required;
        }
      });
      return equipmentMap;
    }
    return {};
  });

  const {
    register,
    handleSubmit: _handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      program_split: currentSplit,
      description:
        initialData?.description || stepOneData?.program_description || "",
      weeks: currentWeeks,
    },
  });

  // Initialize form with step one data and initial data
  useEffect(() => {
    if (stepOneData || initialData) {
      setValue(
        "description",
        initialData?.description || stepOneData?.program_description || ""
      );

      setValue("program_split", currentSplit);
    }
  }, [stepOneData, initialData, setValue, currentSplit]);

  // Initialize on mount
  useEffect(() => {
    setValue("weeks", currentWeeks);
  }, [setValue, currentWeeks]);

  // Validation function to check if split has minimum required structure
  const validateSplitStructure = (
    weeks: Week[],
    isDraft: boolean = false
  ): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // If draft status, skip structure validation
    if (isDraft) {
      return { isValid: true, errors: [] };
    }

    // Check if there's at least one week
    if (!weeks || weeks.length === 0) {
      errors.push("At least one week is required");
      return { isValid: false, errors };
    }

    // Check each week
    for (const week of weeks) {
      // Check if week has at least one day
      if (!week.days || week.days.length === 0) {
        errors.push(`Week "${week.title}" must have at least one day`);
        continue;
      }

      // Check if week has more than 7 days
      if (week.days.length > 7) {
        errors.push(`Week "${week.title}" cannot have more than 7 days`);
        continue;
      }

      // Check each day
      for (const day of week.days) {
        // Skip rest days
        if (day.is_rest_day) continue;

        // Check if day has at least one session
        if (!day.sessions || day.sessions.length === 0) {
          errors.push(
            `Day "${day.title}" in week "${week.title}" must have at least one session (or be marked as rest day)`
          );
          continue;
        }

        // Check each session
        for (const session of day.sessions) {
          // Check if session has at least one exercise
          if (!session.exercises || session.exercises.length === 0) {
            errors.push(
              `Session "${session.title}" in day "${day.title}" of week "${week.title}" must have at least one exercise`
            );
          }
        }
      }
    }

    return { isValid: errors.length === 0, errors };
  };

  // Validate all splits before submission
  const validateAllSplits = (isDraft: boolean = false): { isValid: boolean; errors: string[] } => {
    const allErrors: string[] = [];

    // Get all split configurations including current one
    const allSplitConfigurations = {
      ...splitConfigurations,
      [currentSplit]: currentWeeks,
    };

    // console.log("allSplitConfigurations >>", allSplitConfigurations);
    // Validate each split
    stepOneData?.splits?.forEach((split) => {
      const weeks = allSplitConfigurations[split?.split_id];

      const validation = validateSplitStructure(weeks, isDraft);

      if (!validation.isValid) {
        allErrors.push(`${split?.title}:`);
        validation.errors.forEach((error) => allErrors.push(`  - ${error}`));
      }
    });

    return { isValid: allErrors.length === 0, errors: allErrors };
  };

  const validateAndPrepareData = (isDraft: boolean = false) => {
    // Validate all splits before submission
    const validation = validateAllSplits(isDraft);

    if (!validation.isValid) {
      // Show validation errors
      showToast(
        `Please fix the following issues before submitting:\n\n${validation.errors.join(
          "\n"
        )}`,
        20000,
        ToastStatusEnum.ERROR
      );
      return null;
    }

    // Get current form data
    const currentFormData = {
      program_split: currentSplit,
      description: watch("description"),
      weeks: currentWeeks,
    };

    // Include all split configurations and equipment in the submission
    const formDataWithSplits = {
      ...currentFormData,
      splitConfigurations: {
        ...splitConfigurations,
        [currentSplit]: currentWeeks, // Make sure current split has the latest data
      },
      splitEquipment: splitEquipment,
    };

    return formDataWithSplits;
  };

  const handleSaveAsDraft = async () => {
    const data = validateAndPrepareData(true); // Skip validation for draft
    if (data) {
      const message = `Saving program as ${initialData?.status ?? "draft"}...`;
      showToast(message, 3000, ToastStatusEnum.INFO);

      // Handle image upload if new image was selected
      let imageUrl = programImagePreview;
      if (programImage) {
        showToast("Uploading image...", 3000, ToastStatusEnum.INFO);
        imageUrl = await uploadImageToServer(programImage);
        if (!imageUrl) {
          // If image upload fails, don't proceed with save
          return;
        }
      }

      // Check if this is an update or new program
      const programId = initialData?.programId;

      // Enhance stepOneData with equipment information
      const enhancedStepOneData = {
        ...stepOneData,
        splits:
          stepOneData?.splits?.map((split: any) => ({
            ...split,
            equipment_required: splitEquipment[split.split_id] || "",
          })) || [],
      };

      const payload = {
        stepOneData: enhancedStepOneData,
        image: imageUrl,
        stepTwoData: data,
      };

      if (programId) {
        // Update existing program
        saveProgramMutation(
          {
            endpoint: `/v2/api/kanglink/custom/trainer/programs/${programId}`,
            method: "PUT",
            body: { ...payload },
          },
          {
            onSuccess: async (response: any) => {
              // Call onSubmit with the response data for parent component handling
              await queryClient.invalidateQueries({
                queryKey: [queryKeys.program.paginate, Models.PROGRAM],
                exact: false,
                refetchType: "all",
              });
              showToast("Program saved successfully!", 3000, ToastStatusEnum.SUCCESS);
              onSubmit?.({
                ...data,
                status:
                  initialData?.status &&
                  !["draft"].includes(initialData?.status)
                    ? initialData?.status
                    : "draft",
                programId: response?.data || programId,
                apiResponse: response,
                image: imageUrl,
              });
            },
            onError: (error: any) => {
              console.error("Save program error:", error);
              showToast(
                error?.message || "Failed to save program. Please try again.",
                5000,
                ToastStatusEnum.ERROR
              );
            },
          }
        );
      } else {
        // Create new program as draft
        saveProgramMutation(
          {
            endpoint: "/v2/api/kanglink/custom/trainer/programs/draft",
            method: "POST",
            body: payload,
          },
          {
            onSuccess: async (response: any) => {
              // Call onSubmit with the response data for parent component handling
              await queryClient.invalidateQueries({
                queryKey: [queryKeys.program.paginate, Models.PROGRAM],
                exact: false,
                refetchType: "all",
              });
              showToast("Program saved successfully!", 3000, ToastStatusEnum.SUCCESS);
              onSubmit?.({
                ...data,
                status: "draft",
                programId: response?.data,
                apiResponse: response,
                image: imageUrl,
              });
            },
            onError: (error: any) => {
              console.error("Save program error:", error);
              showToast(
                error?.message || "Failed to save program. Please try again.",
                5000,
                ToastStatusEnum.ERROR
              );
            },
          }
        );
      }
    }
  };

  const handlePublish = async () => {
    const data = validateAndPrepareData();
    if (data) {
      showToast("Publishing program...", 3000, ToastStatusEnum.INFO);

      // Handle image upload if new image was selected
      let imageUrl = programImagePreview;
      if (programImage) {
        showToast("Uploading image...", 3000, ToastStatusEnum.INFO);
        imageUrl = await uploadImageToServer(programImage);
        if (!imageUrl) {
          // If image upload fails, don't proceed with publish
          return;
        }
      }

      // Check if this is an update or new program
      const programId = initialData?.programId;

      // Enhance stepOneData with equipment information
      const enhancedStepOneData = {
        ...stepOneData,
        splits:
          stepOneData?.splits?.map((split: any) => ({
            ...split,
            equipment_required: splitEquipment[split.split_id] || "",
          })) || [],
      };

      const payload = {
        stepOneData: enhancedStepOneData,
        image: imageUrl,
        stepTwoData: data,
      };

      if (programId) {
        // Update existing program and publish
        publishProgramMutation(
          {
            endpoint: `/v2/api/kanglink/custom/trainer/programs/${programId}`,
            method: "PUT",
            body: { ...payload, status: "pending_approval" },
          },
          {
            onSuccess: (response: any) => {
              // Call onSubmit with the response data for parent component handling
              queryClient.invalidateQueries({
                queryKey: ["program"],
                exact: false,
                refetchType: "all",
              });
              showToast("Program published successfully! Awaiting approval.", 3000, ToastStatusEnum.SUCCESS);
              onSubmit?.({
                ...data,
                status: !["draft"].includes(initialData?.status)
                  ? initialData?.status
                  : "pending_approval",
                programId: response?.data || programId,
                apiResponse: response,
                image: imageUrl,
              });
            },
            onError: (error: any) => {
              console.error("Publish program error:", error);
              showToast(
                error?.message || "Failed to publish program. Please try again.",
                5000,
                ToastStatusEnum.ERROR
              );
            },
          }
        );
      } else {
        // Create new program and publish
        publishProgramMutation(
          {
            endpoint:
              "/v2/api/kanglink/custom/trainer/programs/pending_approval",
            method: "POST",
            body: { ...payload, status: "pending_approval" },
          },
          {
            onSuccess: (response: any) => {
              // Call onSubmit with the response data for parent component handling
              queryClient.invalidateQueries({
                queryKey: ["program"],
                exact: false,
                refetchType: "all",
              });
              showToast("Program published successfully! Awaiting approval.", 3000, ToastStatusEnum.SUCCESS);
              onSubmit?.({
                ...data,
                status: "pending_approval",
                programId: response?.data?.programId,
                apiResponse: response,
                image: imageUrl,
              });
            },
            onError: (error: any) => {
              console.error("Publish program error:", error);
              showToast(
                error?.message || "Failed to publish program. Please try again.",
                5000,
                ToastStatusEnum.ERROR
              );
            },
          }
        );
      }
    }
  };

  const handleBack = () => {
    onBack?.();
  };

  // Helper function to update weeks for current split
  const updateCurrentSplitWeeks = (updatedWeeks: Week[]) => {
    // Update the split configurations
    setSplitConfigurations((prev) => ({
      ...prev,
      [currentSplit]: updatedWeeks,
    }));
    // Update the current weeks state
    setCurrentWeeks(updatedWeeks);
    // Update the form value
    setValue("weeks", updatedWeeks);
  };

  // Helper functions for collapsing are now managed locally in each component

  // Helper function to update weeks
  const updateWeeks = (updatedWeeks: Week[]) => {
    updateCurrentSplitWeeks(updatedWeeks);
  };

  // Handle equipment change for current split
  const handleEquipmentChange = (splitId: string, equipment: string) => {
    setSplitEquipment((prev) => ({
      ...prev,
      [splitId]: equipment,
    }));
  };

  // Handle split change
  const handleSplitChange = (selectedSplit: string) => {
    // Save current split's data before switching
    const updatedConfigurations = {
      ...splitConfigurations,
      [currentSplit]: currentWeeks,
    };

    // Get weeks for the new split
    let newSplitWeeks =
      updatedConfigurations[selectedSplit] || getDefaultWeeks();

    // Ensure labels are properly initialized for the new split
    newSplitWeeks.forEach((week: Week) => {
      week.days.forEach((day: Day) => {
        day.sessions.forEach((session: Session) => {
          updateExerciseOrdersAndLabels(session.exercises);
        });
      });
    });

    // Update all states
    setSplitConfigurations(updatedConfigurations);
    setCurrentSplit(selectedSplit);
    setCurrentWeeks(newSplitWeeks);

    // Update form values
    setValue("program_split", selectedSplit);
    setValue("weeks", newSplitWeeks);
  };

  // Action button functions
  const addWeek = () => {
    const newWeek: Week = {
      id: `week-${currentWeeks.length + 1}`,
      title: `Week ${currentWeeks.length + 1}`,
      days: [
        {
          id: `day-${currentWeeks.length + 1}-1`,
          title: "Day 1",
          is_rest_day: false,
          sessions: [],
          day_order: 1,
        },
      ],
      week_order: currentWeeks.length + 1,
    };
    const updatedWeeks = [...currentWeeks, newWeek];
    updateCurrentSplitWeeks(updatedWeeks);
  };

  const addDay = (weekIndex: number) => {
    const updatedWeeks = [...currentWeeks];
    const week = updatedWeeks[weekIndex];
    
    // Check if week already has 7 days
    if (week.days.length >= 7) {
      showToast("Cannot add more than 7 days to a week", 3000, ToastStatusEnum.ERROR);
      return;
    }
    
    const newDay: Day = {
      id: `day-${weekIndex + 1}-${week.days.length + 1}`,
      title: `Day ${week.days.length + 1}`,
      is_rest_day: false,
      sessions: [],
      day_order: week.days.length + 1,
    };
    updatedWeeks[weekIndex].days.push(newDay);
    updateCurrentSplitWeeks(updatedWeeks);
  };

  const addSession = (weekIndex: number, dayIndex: number) => {
    const updatedWeeks = [...currentWeeks];
    const day = updatedWeeks[weekIndex].days[dayIndex];
    const sessionCount = day.sessions.length;
    const newSession: Session = {
      id: `session-${weekIndex + 1}-${dayIndex + 1}-${sessionCount + 1}`,
      title: `Session ${sessionCount + 1}`,
      session_order: sessionCount + 1,
      exercises: [
        {
          id: `exercise-${weekIndex + 1}-${dayIndex + 1}-${sessionCount + 1}-1`,
          sets: "",
          reps_or_time: "",
          reps_time_type: "reps" as const,
          exercise_details: "",
          rest_duration_minutes: 0,
          rest_duration_seconds: 0,
          label: null, // Will be updated by updateExerciseOrdersAndLabels
          label_number: "1", // Will be updated by updateExerciseOrdersAndLabels
          is_linked: false,
          exercise_order: 1, // Will be updated by updateExerciseOrdersAndLabels
          user_id: 0,
          session_id: "",
          time_minutes: 0,
          time_seconds: 0,
          exercise_id: "",
          video_url: null,
        },
      ],
    };
    updatedWeeks[weekIndex].days[dayIndex].sessions.push(newSession);

    // Update exercise orders and reassign labels for the new session
    updateExerciseOrdersAndLabels(newSession.exercises);

    updateCurrentSplitWeeks(updatedWeeks);
  };

  const addExercise = (
    weekIndex: number,
    dayIndex: number,
    sessionIndex: number
  ) => {
    const updatedWeeks = [...currentWeeks];
    const session =
      updatedWeeks[weekIndex].days[dayIndex].sessions[sessionIndex];
    const exerciseCount = session.exercises.length;
    const newExercise: Exercise = {
      id: `exercise-${weekIndex + 1}-${dayIndex + 1}-${sessionIndex + 1}-${exerciseCount + 1}`,
      sets: "",
      reps_or_time: "",
      reps_time_type: "reps" as const,
      exercise_details: "",
      rest_duration_minutes: 0,
      rest_duration_seconds: 0,
      label: null,
      label_number: "1", // Will be updated by updateExerciseOrdersAndLabels
      is_linked: false,
      exercise_order: exerciseCount + 1, // Will be updated by updateExerciseOrdersAndLabels
      user_id: 0,
      session_id: "",
      time_minutes: 0,
      time_seconds: 0,
      exercise_id: "",
      video_url: null,
    };
    updatedWeeks[weekIndex].days[dayIndex].sessions[
      sessionIndex
    ].exercises.push(newExercise);

    // Update exercise orders and reassign labels for the entire session
    updateExerciseOrdersAndLabels(session.exercises);

    updateCurrentSplitWeeks(updatedWeeks);
  };

  // Delete functions
  const deleteWeek = (weekIndex: number) => {
    const updatedWeeks = [...currentWeeks];
    updatedWeeks.splice(weekIndex, 1);

    // Update week orders for remaining weeks
    updatedWeeks.forEach((week, index) => {
      week.week_order = index + 1;
      week.title = `Week ${index + 1}`;
    });

    updateCurrentSplitWeeks(updatedWeeks);
  };

  const deleteDay = (weekIndex: number, dayIndex: number) => {
    const updatedWeeks = [...currentWeeks];
    updatedWeeks[weekIndex].days.splice(dayIndex, 1);

    // Update day orders for remaining days in the week
    updatedWeeks[weekIndex].days.forEach((day, index) => {
      day.day_order = index + 1;
      day.title = `Day ${index + 1}`;
    });

    updateCurrentSplitWeeks(updatedWeeks);
  };

  const deleteSession = (
    weekIndex: number,
    dayIndex: number,
    sessionIndex: number
  ) => {
    const updatedWeeks = [...currentWeeks];
    updatedWeeks[weekIndex].days[dayIndex].sessions.splice(sessionIndex, 1);

    // Update session orders for remaining sessions in the day
    updatedWeeks[weekIndex].days[dayIndex].sessions.forEach(
      (session, index) => {
        session.session_order = index + 1;
        session.title = `Session ${index + 1}`;
      }
    );

    updateCurrentSplitWeeks(updatedWeeks);
  };

  // console.log("splitConfigurations >>", splitConfigurations);

  return (
    <div className="w-full h-full p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-text font-['Inter']">
          Create Program (2/2)
        </h1>
      </div>

      {/* Main Form Container */}
      <div className="w-full bg-background rounded-md shadow-sm border border-border p-6">
        <div className="space-y-8">
          {/* Program Setup Section */}
          <ProgramSetupSection
            register={register}
            errors={errors}
            stepOneData={stepOneData}
            onSplitChange={handleSplitChange}
            currentSplit={currentSplit}
            onAddWeek={addWeek}
            onImageChange={handleImageChange}
            currentImage={programImagePreview}
            splitEquipment={splitEquipment}
            onEquipmentChange={handleEquipmentChange}
          />

          {/* Program Structure */}
          <div className="space-y-6">
            {currentWeeks.map((week, weekIndex) => (
              <ProgramWeekSection
                key={week.id}
                week={week}
                weekIndex={weekIndex}
                onUpdateWeeks={updateWeeks}
                weeks={currentWeeks}
                onAddWeek={addWeek}
                onAddDay={addDay}
                onAddSession={addSession}
                onAddExercise={addExercise}
                onDeleteWeek={deleteWeek}
                onDeleteDay={deleteDay}
                onDeleteSession={deleteSession}
              />
            ))}
          </div>

          {/* Final Action Buttons */}
          <FormActionButtons
            program={initialData}
            onSaveAsDraft={handleSaveAsDraft}
            onPublish={handlePublish}
            onBack={handleBack}
            onPreview={onPreview}
            showBackButton={true}
            isSaving={isSaving || isUploadingImage}
            isPublishing={isPublishing || isUploadingImage}
          />
        </div>
      </div>
    </div>
  );
};

export default CreateProgramStepTwo;
