import { useQueryClient } from "@tanstack/react-query";
import { useCreateModelMutation } from "@/query/shared/createModel";
import { useContexts } from "@/hooks/useContexts";
import { ProgramReview, CreatePostFeed } from "@/interfaces/model.interface";
import { Models } from "@/utils/baas/models";
import { useProfile } from "@/hooks/useProfile";

export interface ReviewSubmissionData {
  program_id: number | string;
  split_id?: number | string | null;
  content: string;
  rating: number;
  post_type: CreatePostFeed["post_type"];
}

export interface ReviewSubmissionResponse {
  error: boolean;
  message: string;
  data?: ProgramReview;
}

export const useReviewSubmission = () => {
  const { authState } = useContexts();
  const queryClient = useQueryClient();
  const { profile } = useProfile();
  // Check if user is logged in
  const isLoggedIn = authState.isAuthenticated;

  const { mutateAsync: createPost, isPending: isCreatingPost } =
    useCreateModelMutation(Models.POST_FEED, {
      showToast: true,
    });

  const submitReview = async (
    reviewData: ReviewSubmissionData
  ): Promise<ReviewSubmissionResponse> => {
    if (!isLoggedIn) {
      throw new Error("User must be logged in to submit a review");
    }

    // Validate rating
    if (reviewData.rating < 1 || reviewData.rating > 5) {
      throw new Error("Rating must be between 1 and 5 stars");
    }

    // Validate content
    if (!reviewData.content.trim()) {
      throw new Error("Review content cannot be empty");
    }

    if (reviewData.content.trim().length < 10) {
      throw new Error("Review must be at least 10 characters long");
    }

    if (reviewData.content.trim().length > 1000) {
      throw new Error("Review cannot exceed 1000 characters");
    }

    const payload: CreatePostFeed = {
      program_id: reviewData.program_id,
      split_id: reviewData.split_id || null,
      post_type: reviewData.post_type,
      content: reviewData.content.trim(),
      rating: reviewData.rating,
      is_private: false,
      visibility_scope: "public",
      user_id: profile?.id,
    };

    const response = await createPost(payload);

    if (response?.error) {
      throw new Error(response.message || "Failed to submit review");
    }

    // Invalidate and refetch program reviews
    queryClient.invalidateQueries({
      queryKey: ["program-reviews", reviewData.program_id.toString()],
    });

    // Invalidate user review
    queryClient.invalidateQueries({
      queryKey: ["user-review", reviewData.program_id, profile?.id],
    });

    // Invalidate program details to update review count
    queryClient.invalidateQueries({
      queryKey: ["program-details", reviewData.program_id.toString()],
    });

    return response as ReviewSubmissionResponse;
  };

  return {
    submitReview,
    isSubmitting: isCreatingPost,
    isLoggedIn,
  };
};

export default useReviewSubmission;
