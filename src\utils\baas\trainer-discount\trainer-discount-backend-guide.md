# Trainer Discount Backend Implementation Guide

## Overview
This guide provides implementation details for building the backend APIs for trainer discount management. Each discount is linked to a specific trainer's program and integrates with <PERSON>e for payment processing.

## Technology Stack Recommendations
- **Framework**: Node.js with Express.js or NestJS
- **Database**: MySQL or PostgreSQL
- **Payment Processing**: Stripe API
- **Authentication**: JWT tokens
- **Validation**: <PERSON><PERSON> or Yu<PERSON>
- **ORM**: Prisma, TypeORM, or Sequelize

---

## Implementation Checklist

### 1. Database Setup
- [ ] Create database tables using the provided schema
- [ ] Set up database migrations
- [ ] Create database indexes for performance
- [ ] Set up foreign key constraints
- [ ] Create database views for complex queries

### 2. Authentication & Authorization
- [ ] Implement JWT token validation middleware
- [ ] Verify trainer ownership of programs
- [ ] Add role-based access control
- [ ] Implement rate limiting

### 3. API Endpoints Implementation
- [ ] GET /programs/{programId}/discounts
- [ ] GET /programs/{programId}/pricing
- [ ] PUT /programs/{programId}/discounts
- [ ] POST /programs/{programId}/promo-codes/validate
- [ ] POST /programs/{programId}/discounts/preview
- [ ] DELETE /programs/{programId}/promo-codes/{codeId}

### 4. Business Logic
- [ ] Discount calculation algorithms
- [ ] Promo code validation logic
- [ ] Revenue impact calculations
- [ ] Price override logic

### 5. Stripe Integration
- [ ] Create/update Stripe products
- [ ] Create/update Stripe prices
- [ ] Create/manage Stripe coupons
- [ ] Sync discount changes with Stripe

### 6. Validation & Error Handling
- [ ] Input validation schemas
- [ ] Business rule validation
- [ ] Error response formatting
- [ ] Logging and monitoring

---

## Key Implementation Details

### Authentication Middleware
```javascript
const authenticateTrainer = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Verify trainer exists and is active
    const trainer = await Trainer.findById(decoded.trainerId);
    if (!trainer || !trainer.isActive) {
      return res.status(401).json({ success: false, error: 'Unauthorized' });
    }
    
    req.trainer = trainer;
    next();
  } catch (error) {
    return res.status(401).json({ success: false, error: 'Invalid token' });
  }
};
```

### Program Ownership Validation
```javascript
const validateProgramOwnership = async (req, res, next) => {
  try {
    const { programId } = req.params;
    const program = await Program.findById(programId);
    
    if (!program) {
      return res.status(404).json({ success: false, error: 'Program not found' });
    }
    
    if (program.trainerId !== req.trainer.id) {
      return res.status(403).json({ success: false, error: 'Access denied' });
    }
    
    req.program = program;
    next();
  } catch (error) {
    return res.status(500).json({ success: false, error: 'Server error' });
  }
};
```

### Discount Calculation Logic
```javascript
const calculateDiscountedPrice = (originalPrice, discountType, discountValue) => {
  if (discountType === 'fixed') {
    return Math.max(0, originalPrice - discountValue);
  } else if (discountType === 'percentage') {
    return originalPrice * (1 - discountValue / 100);
  }
  return originalPrice;
};

const calculateRevenueImpact = (originalPrices, discountedPrices) => {
  const originalTotal = originalPrices.reduce((sum, price) => sum + price, 0);
  const discountedTotal = discountedPrices.reduce((sum, price) => sum + price, 0);
  
  return {
    amount: discountedTotal - originalTotal,
    percentage: ((discountedTotal - originalTotal) / originalTotal) * 100
  };
};
```

### Promo Code Validation
```javascript
const validatePromoCode = async (programId, code) => {
  // Check if code already exists for this program
  const existingCode = await PromoCode.findOne({
    where: { programId, code, isActive: true }
  });
  
  if (existingCode) {
    // Generate suggestions
    const suggestions = [
      `${code}1`,
      `${code}2024`,
      `NEW${code}`
    ];
    
    return {
      isValid: false,
      isAvailable: false,
      conflicts: [existingCode.code],
      suggestions
    };
  }
  
  // Additional validation rules
  if (code.length < 3 || code.length > 20) {
    return {
      isValid: false,
      isAvailable: true,
      conflicts: [],
      suggestions: []
    };
  }
  
  return {
    isValid: true,
    isAvailable: true,
    conflicts: [],
    suggestions: []
  };
};
```

### Stripe Integration
```javascript
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

const createStripeCoupon = async (promoCode) => {
  try {
    const coupon = await stripe.coupons.create({
      id: `${promoCode.programId}_${promoCode.code}`,
      name: promoCode.code,
      percent_off: promoCode.discountType === 'percentage' ? promoCode.discountValue : undefined,
      amount_off: promoCode.discountType === 'fixed' ? Math.round(promoCode.discountValue * 100) : undefined,
      currency: promoCode.discountType === 'fixed' ? 'usd' : undefined,
      duration: 'once',
      redeem_by: promoCode.expiryDate ? Math.floor(new Date(promoCode.expiryDate).getTime() / 1000) : undefined,
      max_redemptions: promoCode.usageLimit
    });
    
    return coupon.id;
  } catch (error) {
    console.error('Stripe coupon creation failed:', error);
    throw new Error('Failed to create Stripe coupon');
  }
};

const updateStripePrice = async (tier, discountedPrice) => {
  try {
    // Create new price object (Stripe prices are immutable)
    const price = await stripe.prices.create({
      product: tier.stripeProductId,
      unit_amount: Math.round(discountedPrice * 100), // Convert to cents
      currency: 'usd',
      recurring: tier.billingCycle ? {
        interval: tier.billingCycle === 'monthly' ? 'month' : 
                 tier.billingCycle === 'quarterly' ? 'month' : 'year',
        interval_count: tier.billingCycle === 'quarterly' ? 3 : 1
      } : undefined
    });
    
    // Archive old price
    if (tier.stripePriceId) {
      await stripe.prices.update(tier.stripePriceId, { active: false });
    }
    
    return price.id;
  } catch (error) {
    console.error('Stripe price update failed:', error);
    throw new Error('Failed to update Stripe price');
  }
};
```

### Input Validation Schemas
```javascript
const Joi = require('joi');

const updateDiscountSchema = Joi.object({
  affiliateLink: Joi.string().uri().allow('').optional(),
  saleDiscount: Joi.object({
    type: Joi.string().valid('fixed', 'percentage').required(),
    value: Joi.number().min(0).required(),
    applyToAll: Joi.boolean().required()
  }).optional(),
  promoCode: Joi.object({
    code: Joi.string().min(3).max(20).alphanum().required(),
    discountType: Joi.string().valid('fixed', 'percentage').required(),
    discountValue: Joi.number().min(0).required(),
    appliesTo: Joi.object({
      subscription: Joi.boolean().required(),
      fullPayment: Joi.boolean().required()
    }).required(),
    expiryDate: Joi.date().iso().min('now').optional(),
    usageLimit: Joi.number().integer().min(1).optional()
  }).optional(),
  subscriptionDiscounts: Joi.array().items(
    Joi.object({
      tierId: Joi.number().integer().required(),
      discountType: Joi.string().valid('fixed', 'percentage').required(),
      discountValue: Joi.number().min(0).required()
    })
  ).optional(),
  fullPriceDiscounts: Joi.array().items(
    Joi.object({
      tierId: Joi.number().integer().required(),
      discountType: Joi.string().valid('fixed', 'percentage').required(),
      discountValue: Joi.number().min(0).required()
    })
  ).optional()
});
```

### Error Response Formatting
```javascript
const formatErrorResponse = (error, req) => {
  const response = {
    success: false,
    error: {
      code: error.code || 'UNKNOWN_ERROR',
      message: error.message || 'An unexpected error occurred'
    },
    timestamp: new Date().toISOString(),
    path: req.path
  };
  
  // Add validation details for Joi errors
  if (error.isJoi) {
    response.error.details = error.details.reduce((acc, detail) => {
      acc[detail.path.join('.')] = detail.message;
      return acc;
    }, {});
  }
  
  return response;
};
```

### Audit Logging
```javascript
const logDiscountChange = async (programId, actionType, entityType, entityId, oldValues, newValues, changedBy, reason) => {
  await DiscountAuditLog.create({
    programId,
    actionType,
    entityType,
    entityId,
    oldValues: oldValues ? JSON.stringify(oldValues) : null,
    newValues: newValues ? JSON.stringify(newValues) : null,
    changedBy,
    changeReason: reason
  });
};
```

---

## Testing Strategy

### Unit Tests
- [ ] Discount calculation functions
- [ ] Promo code validation logic
- [ ] Revenue impact calculations
- [ ] Input validation schemas

### Integration Tests
- [ ] Database operations
- [ ] Stripe API integration
- [ ] Authentication middleware
- [ ] Complete API endpoints

### End-to-End Tests
- [ ] Full discount creation flow
- [ ] Promo code usage flow
- [ ] Error handling scenarios
- [ ] Performance under load

---

## Performance Considerations

### Database Optimization
- Use appropriate indexes on frequently queried columns
- Implement database connection pooling
- Consider read replicas for heavy read operations
- Use database views for complex queries

### Caching Strategy
- Cache program pricing data (Redis)
- Cache discount calculations for common scenarios
- Implement cache invalidation on updates

### API Performance
- Implement request rate limiting
- Use pagination for list endpoints
- Optimize database queries (avoid N+1 problems)
- Implement response compression

---

## Security Considerations

### Data Protection
- Validate all input data
- Sanitize user inputs
- Use parameterized queries
- Implement proper error handling (don't expose sensitive data)

### Access Control
- Verify trainer ownership of programs
- Implement role-based permissions
- Use HTTPS for all communications
- Secure API keys and secrets

### Audit Trail
- Log all discount changes
- Track promo code usage
- Monitor for suspicious activities
- Implement data retention policies

---

## Deployment Checklist

### Environment Setup
- [ ] Configure environment variables
- [ ] Set up database connections
- [ ] Configure Stripe API keys
- [ ] Set up logging and monitoring

### Production Readiness
- [ ] Implement health check endpoints
- [ ] Set up error monitoring (Sentry, etc.)
- [ ] Configure load balancing
- [ ] Set up backup and recovery procedures

### Monitoring
- [ ] API response time monitoring
- [ ] Database performance monitoring
- [ ] Error rate tracking
- [ ] Business metrics tracking (discount usage, revenue impact)
