import React, { useState, useEffect } from "react";
import { FeedPostProps } from "./types";
import PostActions from "./PostActions";
import CommentInput from "./CommentInput";
import { usePostInteractions } from "@/hooks/usePostInteractions";
import { useProfile } from "@/hooks/useProfile";

const TrainerFeedPost: React.FC<Omit<FeedPostProps, "likes" | "comments">> = ({
  id,
  author_name,
  author_avatar,
  content,
  timestamp,
  is_private = false,
  author_id: _author_id,
  user_reaction: _user_reaction,
  can_delete: _can_delete = false,
}) => {
  const [new_comment, setNewComment] = useState("");
  const [show_replies, setShowReplies] = useState(false);

  const { profile } = useProfile();

  // Post interactions hook
  const {
    addComment,
    deleteComment,
    toggleReaction,
    loading,
    fetchComments,
    fetchReactions,
    comments,
    reactions,
    current_user_reaction,
  } = usePostInteractions({
    post_id: id,
    target_type: "post",
  });

  const handlePostComment = async () => {
    if (new_comment.trim()) {
      await addComment(new_comment.trim(), false);
      setNewComment("");
    }
  };

  const handleReactionToggle = async (
    reaction_type: "like" | "love" | "fire" | "strong"
  ) => {
    await toggleReaction(reaction_type);
  };

  const handleToggleComments = () => {
    setShowReplies(!show_replies);
  };

  const handleDeleteComment = async (comment_id: string | number) => {
    await deleteComment(comment_id);
  };

  useEffect(() => {
    fetchComments();
    fetchReactions();
  }, []);

  return (
    <div className="bg-background rounded-[0.375rem] shadow-sm border border-border p-6 mb-6 hover:shadow-md transition-shadow duration-200">
      {/* Post Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <img
            src={author_avatar}
            alt={author_name}
            className="w-12 h-12 rounded-full object-cover ring-2 ring-border"
          />
          <div>
            <h3 className="font-semibold text-text">{author_name}</h3>
            <p className="text-sm text-text-disabled">{timestamp}</p>
          </div>
        </div>
        {is_private && (
          <div className="flex items-center space-x-2 bg-primary/10 px-3 py-1.5 rounded-full">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="11"
              height="13"
              viewBox="0 0 11 13"
              fill="none"
            >
              <path
                d="M3.82812 3.875V5H7.57812V3.875C7.57812 2.83906 6.73906 2 5.70312 2C4.66719 2 3.82812 2.83906 3.82812 3.875ZM2.32812 5V3.875C2.32812 2.01172 3.83984 0.5 5.70312 0.5C7.56641 0.5 9.07812 2.01172 9.07812 3.875V5H9.45312C10.2805 5 10.9531 5.67266 10.9531 6.5V11C10.9531 11.8273 10.2805 12.5 9.45312 12.5H1.95312C1.12578 12.5 0.453125 11.8273 0.453125 11V6.5C0.453125 5.67266 1.12578 5 1.95312 5H2.32812Z"
                fill="#4CBF6D"
              />
            </svg>
            <span className="text-sm font-medium text-primary">Private</span>
          </div>
        )}
      </div>

      <div className="pl-12">
        {/* Post Content */}
        <div className="mb-6">
          <p className="text-text leading-relaxed text-[15px]">{content}</p>
        </div>

        {/* Post Actions */}
        <PostActions
          likes={reactions?.length || 0}
          comments={comments?.length || 0}
          current_reaction={current_user_reaction?.reaction_type}
          on_reaction_toggle={handleReactionToggle}
          on_toggle_comments={handleToggleComments}
          is_loading_reaction={loading?.is_toggling_reaction}
        />

        {/* Comments Section */}
        {show_replies && (
          <div className="mt-4 space-y-3">
            {comments && comments?.length > 0 ? (
              comments?.map((comment) => (
                <div
                  key={comment.id}
                  className="flex space-x-3 p-3 bg-background-secondary rounded-lg"
                >
                  <img
                    src={comment.user?.photo || "https://placehold.co/32x32"}
                    alt={comment.user?.data?.full_name || "User"}
                    className="w-8 h-8 rounded-full object-cover flex-shrink-0"
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-text text-sm">
                        {comment.user?.data?.full_name ||
                          comment.user?.data?.first_name +
                            " " +
                            comment.user?.data?.last_name ||
                          "Unknown User"}
                      </h4>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-text-disabled">
                          {comment.created_at
                            ? new Date(comment.created_at).toLocaleString()
                            : ""}
                        </span>
                        {profile?.id === comment.user_id && (
                          <button
                            onClick={() => handleDeleteComment(comment.id!)}
                            disabled={loading?.is_deleting_comment}
                            className="text-red-500 hover:text-red-700 text-xs disabled:opacity-50"
                          >
                            {loading?.is_deleting_comment
                              ? "Deleting..."
                              : "Delete"}
                          </button>
                        )}
                      </div>
                    </div>
                    <p className="text-text-secondary text-sm mt-1">
                      {comment.content}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-text-disabled text-sm text-center py-4">
                No comments yet
              </p>
            )}
          </div>
        )}
      </div>

      {/* Comment Input */}
      <CommentInput
        user_avatar={profile?.photo || "https://placehold.co/40x40"}
        value={new_comment}
        on_change={setNewComment}
        on_submit={handlePostComment}
        is_loading={loading?.is_adding_comment}
      />
    </div>
  );
};

export default TrainerFeedPost;
