import React, { useMemo } from "react";
import { Modal } from "@/components/Modal/Modal";
import { useGetListQuery } from "@/query/shared/listModel";
import { Models } from "@/utils/baas/models";
import { Enrollment } from "@/interfaces/model.interface";
import {
  User,
  Calendar,
  CreditCard,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  BookOpen,
  Target,
} from "lucide-react";

interface AthleteEnrollmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  athleteId: number;
  athleteName: string;
}

interface EnrollmentWithDetails extends Enrollment {
  athlete?: {
    id: number;
    full_name: string;
    email: string;
    photo?: string;
  };
  trainer?: {
    id: number;
    full_name: string;
    email: string;
    photo?: string;
  };
  program?: {
    id: number;
    program_name: string;
    type_of_program: string;
    program_description?: string;
    image?: string;
  };
  split?: {
    id: number;
    title: string;
    full_price: number;
    subscription: number;
  };
}

const AthleteEnrollmentModal: React.FC<AthleteEnrollmentModalProps> = ({
  isOpen,
  onClose,
  athleteId,
  athleteN<PERSON>,
}) => {
  // Build query options for fetching enrollments with program, split, athlete, and trainer details
  const queryOptions = useMemo(() => {
    if (!athleteId) return undefined;

    return {
      filter: [`athlete_id,eq,${athleteId}`],
      join: ["program", "split|split_id", "user|trainer_id"],
    };
  }, [athleteId]);

  // Fetch enrollment details using useGetListQuery
  const {
    data: enrollmentData,
    isLoading: loading,
    error,
  } = useGetListQuery(Models.ENROLLMENT, queryOptions, undefined, {
    enabled: isOpen && !!queryOptions,
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });

  // Transform the data to include proper athlete, trainer, program, and split information
  const enrichedEnrollments: EnrollmentWithDetails[] = useMemo(() => {
    if (!enrollmentData?.data) return [];

    return enrollmentData.data.map((enrollment: any) => {
      // Handle joined data - the API might return joined data differently
      const athleteData = enrollment.athlete || enrollment.user;
      const trainerData = enrollment.trainer || enrollment.user;

      const athleteInfo = athleteData
        ? {
            id: athleteData.id as number,
            full_name:
              JSON.parse(athleteData.data || "{}")?.full_name ||
              `${athleteData.first_name || ""} ${athleteData.last_name || ""}`.trim() ||
              "Unknown Athlete",
            email: athleteData.email || "<EMAIL>",
            photo: athleteData.photo,
          }
        : {
            id: enrollment.athlete_id as number,
            full_name: "Unknown Athlete",
            email: "<EMAIL>",
          };

      const trainerInfo = trainerData
        ? {
            id: trainerData.id as number,
            full_name:
              JSON.parse(trainerData.data || "{}")?.full_name ||
              `${trainerData.first_name || ""} ${trainerData.last_name || ""}`.trim() ||
              "Unknown Trainer",
            email: trainerData.email || "<EMAIL>",
            photo: trainerData.photo,
          }
        : {
            id: enrollment.trainer_id as number,
            full_name: "Unknown Trainer",
            email: "<EMAIL>",
          };

      const programInfo = enrollment.program
        ? {
            id: enrollment.program.id as number,
            program_name: enrollment.program.program_name || "Unknown Program",
            type_of_program:
              enrollment.program.type_of_program || "Unknown Type",
            program_description: enrollment.program.program_description,
            image: enrollment.program.image,
          }
        : {
            id: enrollment.program_id as number,
            program_name: "Unknown Program",
            type_of_program: "Unknown Type",
          };

      return {
        ...enrollment,
        athlete: athleteInfo,
        trainer: trainerInfo,
        program: programInfo,
        split: enrollment.split || {
          id: enrollment.split_id as number,
          title: "Unknown Split",
          full_price: 0,
          subscription: 0,
        },
      };
    });
  }, [enrollmentData?.data]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "text-green-600 bg-green-50 border-green-200";
      case "expired":
        return "text-gray-600 bg-gray-50 border-gray-200";
      case "cancelled":
        return "text-red-600 bg-red-50 border-red-200";
      case "pending":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="w-4 h-4" />;
      case "expired":
        return <Clock className="w-4 h-4" />;
      case "cancelled":
        return <XCircle className="w-4 h-4" />;
      case "pending":
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  const getPaymentStatusColor = (paymentStatus: string) => {
    switch (paymentStatus) {
      case "paid":
        return "text-green-600 bg-green-50 border-green-200";
      case "pending":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "failed":
        return "text-red-600 bg-red-50 border-red-200";
      case "refunded":
        return "text-blue-600 bg-blue-50 border-blue-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      modalHeader={true}
      title={`${athleteName}'s Enrollments`}
      classes={{
        modal: "h-full w-full",
        modalDialog: "h-[90%] max-w-5xl w-full",
        modalContent: "max-h-[calc(90vh-120px)] w-full overflow-y-auto",
      }}
    >
      <div className="space-y-6">
        {loading && (
          <div className="flex items-center justify-center py-8">
            <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-3 text-text">Loading enrollments...</span>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">
              {error?.message || "Failed to load enrollment details"}
            </p>
          </div>
        )}

        {!loading && !error && enrichedEnrollments.length === 0 && (
          <div className="text-center py-8">
            <BookOpen className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <p className="text-text-secondary">
              No enrollments found for this athlete.
            </p>
          </div>
        )}

        {!loading && !error && enrichedEnrollments.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-text">
                {enrichedEnrollments.length} Enrollment
                {enrichedEnrollments.length > 1 ? "s" : ""}
              </h3>
              <div className="text-sm text-text-secondary">
                Athlete ID: {athleteId}
              </div>
            </div>

            <div className="grid gap-6">
              {enrichedEnrollments.map((enrollment) => (
                <div
                  key={enrollment.id}
                  className="bg-background border border-border rounded-lg p-6 hover:shadow-md transition-shadow duration-200"
                >
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Program Information */}
                    <div className="lg:col-span-2 space-y-4">
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                          <BookOpen className="w-6 h-6 text-primary" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-text text-lg">
                            {enrollment.program?.program_name ||
                              "Unknown Program"}
                          </h4>
                          <p className="text-sm text-text-secondary mb-2">
                            {enrollment.program?.type_of_program}
                          </p>
                          {enrollment.program?.program_description && (
                            <p className="text-sm text-text line-clamp-2">
                              {enrollment.program.program_description}
                            </p>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-text-secondary">
                            Split
                          </label>
                          <p className="text-text">
                            {enrollment.split?.title || "Unknown Split"}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-text-secondary">
                            Trainer
                          </label>
                          <div className="flex items-center space-x-2">
                            <User className="w-4 h-4 text-text-secondary" />
                            <span className="text-text">
                              {enrollment.trainer?.full_name ||
                                "Unknown Trainer"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Enrollment Details */}
                    <div className="space-y-4">
                      <div className="space-y-3">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium text-text-secondary">
                              Payment Type
                            </label>
                            <p className="text-text capitalize">
                              {enrollment.payment_type}
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-text-secondary">
                              Amount
                            </label>
                            <p className="text-text font-medium">
                              {enrollment.amount
                                ? formatCurrency(
                                    enrollment.amount,
                                    enrollment.currency
                                  )
                                : "N/A"}
                            </p>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Calendar className="w-4 h-4 text-text-secondary" />
                            <span className="text-sm text-text">
                              Enrolled:{" "}
                              {enrollment.enrollment_date
                                ? formatDate(enrollment.enrollment_date)
                                : "Unknown"}
                            </span>
                          </div>

                          {enrollment.expiry_date && (
                            <div className="flex items-center space-x-2">
                              <Clock className="w-4 h-4 text-text-secondary" />
                              <span className="text-sm text-text">
                                Expires: {formatDate(enrollment.expiry_date)}
                              </span>
                            </div>
                          )}
                        </div>

                        <div className="grid grid-cols-1 gap-3">
                          <div>
                            <label className="text-sm font-medium text-text-secondary">
                              Status
                            </label>
                            <div
                              className={`inline-flex items-center space-x-1 px-2 py-1 rounded-md border text-xs font-medium ${getStatusColor(enrollment.status || "pending")}`}
                            >
                              {getStatusIcon(enrollment.status || "pending")}
                              <span className="capitalize">
                                {enrollment.status}
                              </span>
                            </div>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-text-secondary">
                              Payment Status
                            </label>
                            <div
                              className={`inline-flex items-center space-x-1 px-2 py-1 rounded-md border text-xs font-medium ${getPaymentStatusColor(enrollment.payment_status || "pending")}`}
                            >
                              <CreditCard className="w-3 h-3" />
                              <span className="capitalize">
                                {enrollment.payment_status}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default AthleteEnrollmentModal;
