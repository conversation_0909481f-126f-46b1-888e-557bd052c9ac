# Athlete Endpoints Implementation Guide

## Overview

This document provides a comprehensive guide to the athlete endpoints implementation, including API configuration, custom hooks, and frontend integration for the ViewAthleteLibraryPage.

## Architecture

The implementation follows a layered architecture:

1. **API Configuration Layer** (`src/utils/baas/athlete.ts`)
2. **Data Interface Layer** (`src/interfaces/model.interface.ts`)
3. **Custom Hooks Layer** (`src/hooks/useAthleteEnrollments/`, `src/hooks/useAthleteFavorites/`)
4. **UI Component Layer** (`src/pages/Athlete/View/ViewAthleteLibraryPage.tsx`)
5. **Testing Layer** (`src/test/hooks/`, `src/test/pages/`)

## API Endpoints

### Athlete Enrollments

**Endpoint**: `GET /v2/api/kanglink/custom/athlete/enrollments`

**Purpose**: Retrieves all enrollments for the authenticated athlete, categorized by status.

**Response Categories**:

- `owned`: One-time payments that are active and paid
- `subscribed`: Subscription payments (active or with billing issues)
- `pending_refund`: Enrollments with refund requested but not processed
- `refunded`: Enrollments with completed refunds

### Athlete Favorites

#### Programs

- `GET /v2/api/kanglink/custom/athlete/favorite/programs` - Get favorite programs
- `POST /v2/api/kanglink/custom/athlete/favorite/programs/{id}` - Add program to favorites
- `DELETE /v2/api/kanglink/custom/athlete/favorite/programs/{id}` - Remove program from favorites

#### Trainers

- `GET /v2/api/kanglink/custom/athlete/favorite/trainers` - Get favorite trainers
- `POST /v2/api/kanglink/custom/athlete/favorite/trainers/{id}` - Add trainer to favorites
- `DELETE /v2/api/kanglink/custom/athlete/favorite/trainers/{id}` - Remove trainer from favorites

## Custom Hooks

### useAthleteEnrollments

```typescript
import { useAthleteEnrollments } from "@/hooks/useAthleteEnrollments";

const {
  data: enrollmentsData,
  isLoading: enrollmentsLoading,
  error: enrollmentsError,
} = useAthleteEnrollments({
  enabled: true,
  refetchInterval: 30000, // Optional: auto-refresh every 30 seconds
});
```

**Features**:

- Automatic data fetching with React Query
- Built-in error handling and retry logic
- Configurable refetch intervals
- TypeScript support with full type safety

### useAthleteFavorites

```typescript
import {
  useAthleteFavoritePrograms,
  useAthleteFavoriteTrainers,
  useAthleteProgramFavorites,
  useAthleteTrainerFavorites,
} from "@/hooks/useAthleteFavorites";

// Fetch favorite data
const { data: favoritePrograms } = useAthleteFavoritePrograms();
const { data: favoriteTrainers } = useAthleteFavoriteTrainers();

// Manage favorites
const { toggleProgramFavorite } = useAthleteProgramFavorites();
const { toggleTrainerFavorite } = useAthleteTrainerFavorites();

// Usage
await toggleProgramFavorite("123", false); // Add to favorites
await toggleTrainerFavorite("456", true); // Remove from favorites
```

**Features**:

- Separate hooks for fetching and managing favorites
- Optimistic updates with automatic cache invalidation
- Loading states for UI feedback
- Error handling with retry logic

## Data Transformation

The implementation includes data transformation functions to convert API responses to component-compatible formats:

```typescript
// Transform enrollment data for CourseCard component
const transformEnrollmentToCourse = (enrollment: EnrollmentItem) => ({
  id: enrollment.id.toString(),
  name: enrollment.program.name,
  trainer: enrollment.trainer.full_name,
  description: enrollment.program.description,
  image: enrollment.program.image_url || "https://placehold.co/366x192",
  status:
    enrollment.category === "owned"
      ? "owned"
      : enrollment.category === "subscribed"
        ? "subscribed"
        : "refund-requested",
  // ... additional fields
});
```

## UI Integration

### ViewAthleteLibraryPage

The page component demonstrates best practices for:

1. **Loading States**: Shows skeleton loading while data is being fetched
2. **Error Handling**: Displays user-friendly error messages
3. **Empty States**: Shows helpful messages when no data is available
4. **Conditional Rendering**: Only shows sections with data
5. **Real-time Updates**: Automatically refreshes data at intervals

### Key Features

- **Responsive Design**: Works across all screen sizes
- **Theme Support**: Integrates with the app's theme system
- **Accessibility**: Proper ARIA labels and semantic HTML
- **Performance**: Optimized with React Query caching and pagination

## Testing

### Unit Tests

```bash
# Run hook tests
npm test src/test/hooks/useAthleteEnrollments.test.tsx
npm test src/test/hooks/useAthleteFavorites.test.tsx

# Run component tests
npm test src/test/pages/ViewAthleteLibraryPage.test.tsx
```

### Test Coverage

- ✅ Hook functionality and error handling
- ✅ API endpoint calls with correct parameters
- ✅ Data transformation functions
- ✅ Component rendering with different states
- ✅ User interactions and event handlers

### Mock Data

Test utilities are provided in `src/test/setup/athlete-endpoints.setup.ts` for consistent mock data across tests.

## Error Handling

The implementation includes comprehensive error handling:

1. **Network Errors**: Automatic retry with exponential backoff
2. **Authentication Errors**: Token refresh and re-authentication
3. **Validation Errors**: User-friendly error messages
4. **Loading States**: Skeleton loading and spinner indicators

## Performance Optimizations

1. **React Query Caching**: Reduces unnecessary API calls
2. **Stale-While-Revalidate**: Shows cached data while fetching updates
3. **Conditional Rendering**: Only renders sections with data
4. **Lazy Loading**: Components load only when needed
5. **Debounced Updates**: Prevents excessive API calls

## Security Considerations

1. **Authentication**: All endpoints require valid JWT tokens
2. **Authorization**: Users can only access their own data
3. **Input Validation**: All user inputs are validated
4. **XSS Prevention**: Proper data sanitization

## Future Enhancements

1. **Offline Support**: Cache data for offline viewing
2. **Real-time Updates**: WebSocket integration for live updates
3. **Advanced Filtering**: Search and filter capabilities
4. **Pagination**: Handle large datasets efficiently
5. **Analytics**: Track user interactions and preferences

## Troubleshooting

### Common Issues

1. **Endpoints Not Being Called**:
   - Ensure you're using `endpoint` instead of `url` in the customQuery.mutateAsync call
   - Add `requiresAuth: true` for authenticated endpoints
   - Check that the hook is properly enabled

2. **Authentication Errors**: Ensure valid JWT token in localStorage

3. **CORS Issues**: Verify API endpoint configuration

4. **Loading States**: Check network connectivity and API availability

5. **Data Not Updating**: Clear React Query cache or check refetch intervals

### Fixed Issues

**✅ Hook Parameter Fix**: Updated all hooks to use correct parameters:

```typescript
// ❌ Wrong (old implementation)
const response = await customQuery.mutateAsync({
  url: endpoint,
  method: "GET",
});

// ✅ Correct (fixed implementation)
const response = await customQuery.mutateAsync({
  endpoint: endpoint,
  method: "GET",
  requiresAuth: true,
});
```

**✅ Error Response Handling**: Added proper error handling for API responses:

```typescript
// ❌ Wrong (causes app to break)
const response = await customQuery.mutateAsync({...});
return response.data; // Crashes if response.error = true

// ✅ Correct (handles errors gracefully)
const response = await customQuery.mutateAsync({...});

// Handle API error responses
if (response.error || !response.data) {
  throw new Error(response.message || "Failed to fetch data");
}

return response.data;
```

**Why This Matters**: When the API returns `{"error": true, "message": "..."}`, the old code tried to access `response.data` which doesn't exist, causing the entire application to crash. The new code properly checks for errors and throws meaningful error messages that React Query can handle gracefully.

### Debug Mode

Enable debug logging by setting:

```typescript
const queryClient = new QueryClient({
  logger: {
    log: console.log,
    warn: console.warn,
    error: console.error,
  },
});
```

## Quick Start Guide

### 1. Basic Implementation

```typescript
import { useAthleteEnrollments } from "@/hooks/useAthleteEnrollments";
import { useAthleteFavoritePrograms } from "@/hooks/useAthleteFavorites";

function MyComponent() {
  const { data: enrollments, isLoading } = useAthleteEnrollments();
  const { data: favoritePrograms } = useAthleteFavoritePrograms();

  if (isLoading) return <div>Loading...</div>;

  return (
    <div>
      <h2>My Enrollments ({enrollments?.meta.total_enrollments})</h2>
      {enrollments?.data.owned.map(enrollment => (
        <div key={enrollment.id}>{enrollment.program.name}</div>
      ))}
    </div>
  );
}
```

### 2. Managing Favorites

```typescript
import { useAthleteProgramFavorites } from "@/hooks/useAthleteFavorites";

function ProgramCard({ program }) {
  const { toggleProgramFavorite, isToggling } = useAthleteProgramFavorites();

  const handleFavoriteClick = async () => {
    try {
      await toggleProgramFavorite(program.id, program.isFavorite);
    } catch (error) {
      console.error("Failed to toggle favorite:", error);
    }
  };

  return (
    <div>
      <h3>{program.name}</h3>
      <button onClick={handleFavoriteClick} disabled={isToggling}>
        {program.isFavorite ? "Remove from Favorites" : "Add to Favorites"}
      </button>
    </div>
  );
}
```

### 3. Error Handling

```typescript
function MyComponent() {
  const { data, isLoading, error, refetch } = useAthleteEnrollments();

  if (error) {
    return (
      <div>
        <p>Error loading data: {error.message}</p>
        <button onClick={() => refetch()}>Try Again</button>
      </div>
    );
  }

  // ... rest of component
}
```

## Support

For issues or questions:

1. Check the test files for usage examples
2. Review the API documentation in the `docs/` folder
3. Consult the TypeScript interfaces for data structures
4. Use the browser developer tools for debugging API calls
