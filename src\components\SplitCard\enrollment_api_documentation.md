# Enrollment Payment Authentication - Frontend Integration Guide

## Overview

The enrollment endpoint properly handles payment authentication for both one-time payments and subscriptions. This document explains how to integrate with the updated API responses.

**Note**: The endpoint URL has been corrected to match the implementation.

## API Endpoint

```
POST /v2/api/kanglink/custom/athlete/enrollment
```

**Legacy endpoint** (deprecated): `/v2/api/kanglink/custom/athlete/enroll` - returns 301 redirect

## Request Body

```json
{
  "split_id": 123,
  "payment_type": "one_time" | "subscription",
  "payment_method_id": "pm_xxxxxxxxxx",
  "affiliate_code": "TRAINER123",
  "coupon_code": "SAVE20"
}
```

### Required Parameters

- `split_id` (number): The ID of the split to enroll in
- `payment_type` (string): Either "one_time" or "subscription"
- `payment_method_id` (string): Stripe payment method ID

### Optional Parameters

- `affiliate_code` (string): Affiliate code for commission tracking
- `coupon_code` (string): Coupon code for discounts

## Response Types

### 1. Successful Enrollment (200 OK)

**For successful payments (one-time succeeded or subscription created):**

```json
{
  "error": false,
  "message": "Enrollment successful",
  "data": {
    "enrollment_id": 456,
    "status": "active",
    "payment_status": "paid",
    "payment_type": "one_time",
    "amount": 85.0,
    "currency": "USD",
    "original_amount": 100.0,
    "discount_amount": 15.0,
    "applied_discounts": [
      {
        "type": "coupon",
        "coupon_code": "SAVE20",
        "discount_amount": 15.0,
        "source": "coupon"
      }
    ],
    "affiliate_code": "TRAINER123",
    "commission_tracking": true
  }
}
```

**For pending payments (one-time requiring processing):**

```json
{
  "error": false,
  "message": "Enrollment created, payment processing",
  "data": {
    "enrollment_id": 456,
    "status": "pending",
    "payment_status": "pending",
    "payment_type": "one_time",
    "amount": 85.0,
    "currency": "USD",
    "original_amount": 100.0,
    "discount_amount": 15.0,
    "applied_discounts": [
      {
        "type": "coupon",
        "coupon_code": "SAVE20",
        "discount_amount": 15.0,
        "source": "coupon"
      }
    ],
    "affiliate_code": "TRAINER123"
  }
}
```

**Action**:

- For `status: "active"`: Redirect user to program page immediately
- For `status: "pending"`: Show loading state and poll enrollment status

### 2. Authentication Required (400 Bad Request)

```json
{
  "error": true,
  "message": "Payment requires additional authentication",
  "requires_action": true,
  "payment_intent": {
    "id": "pi_xxxxxxxxxx",
    "client_secret": "pi_xxxxxxxxxx_secret_xxxxxxxxxx"
  },
  "subscription_id": "sub_xxxxxxxxxx"
}
```

**Action**: Use Stripe.js to handle authentication (see implementation below).

### 3. Affiliate/Coupon Error (400 Bad Request)

```json
{
  "error": true,
  "message": "Invalid affiliate code"
}
```

```json
{
  "error": true,
  "message": "Coupon has expired"
}
```

```json
{
  "error": true,
  "message": "You have already used this coupon"
}
```

```json
{
  "error": true,
  "message": "Affiliate code is not valid for this program"
}
```

**Action**: Show specific error message to user and allow them to correct the affiliate/coupon code.

### 4. Payment Error (400 Bad Request)

```json
{
  "error": true,
  "message": "Payment failed: insufficient funds"
}
```

**Action**: Show error message to user.

## API Response Data Structure

### Successful Enrollment Response Fields

| Field                 | Type    | Description                            |
| --------------------- | ------- | -------------------------------------- |
| `enrollment_id`       | number  | Unique enrollment identifier           |
| `status`              | string  | "active" or "pending"                  |
| `payment_status`      | string  | "paid" or "pending"                    |
| `payment_type`        | string  | "one_time" or "subscription"           |
| `amount`              | number  | Final amount charged (after discounts) |
| `currency`            | string  | Currency code (e.g., "USD")            |
| `original_amount`     | number  | Original price before discounts        |
| `discount_amount`     | number  | Total discount applied                 |
| `applied_discounts`   | array   | Array of discount objects              |
| `affiliate_code`      | string  | Affiliate code used (if any)           |
| `commission_tracking` | boolean | Whether commission will be tracked     |

### Applied Discounts Object Structure

```json
{
  "type": "coupon",
  "coupon_code": "SAVE20",
  "discount_amount": 15.0,
  "source": "coupon"
}
```

## Additional API Endpoints

### Affiliate Code Validation

**Endpoint:** `POST /v2/api/kanglink/custom/affiliate/validate`

**Request Body:**

```json
{
  "affiliate_code": "TRAINER123",
  "program_id": 456
}
```

**Response:**

```json
{
  "error": false,
  "valid": true,
  "trainer_id": 789,
  "message": "Valid affiliate code"
}
```

### Coupon Code Validation

**Endpoint:** `POST /v2/api/kanglink/custom/coupon/validate`

**Request Body:**

```json
{
  "coupon_code": "SAVE20",
  "program_id": 456,
  "split_id": 123,
  "payment_type": "one_time",
  "amount": 100.0
}
```

**Response:**

```json
{
  "error": false,
  "valid": true,
  "discount_amount": 20.0,
  "coupon": {
    "id": 1,
    "code": "SAVE20",
    "discount_type": "percentage",
    "discount_value": 20
  }
}
```

### Discount Preview

**Endpoint:** `GET /v2/api/kanglink/custom/discount/preview`

**Query Parameters:**

- `split_id` (required): Split ID
- `payment_type` (required): "one_time" or "subscription"
- `coupon_code` (optional): Coupon code to apply

**Response:**

```json
{
  "error": false,
  "data": {
    "original_amount": 100.0,
    "discount_amount": 20.0,
    "final_amount": 80.0,
    "applied_discounts": [
      {
        "type": "coupon",
        "coupon_code": "SAVE20",
        "discount_amount": 20.0,
        "source": "coupon"
      }
    ]
  }
}
```

### Enrollment Status Check

**Endpoint:** `GET /v2/api/kanglink/custom/athlete/enrollment/status/:subscription_id`

**Response:**

```json
{
  "error": false,
  "status": "active",
  "payment_status": "paid",
  "enrollment_id": 456
}
```

## Payment Type Differences

### One-Time Payments

- **Usually succeed immediately** without authentication
- **Rare authentication cases** are handled the same way as subscriptions
- **No ongoing webhook events** after initial payment

### Subscriptions

- **More likely to require authentication** (3D Secure, bank verification)
- **Ongoing invoice payments** will be handled automatically
- **Authentication only required** for the initial subscription setup

## Affiliate and Discount Validation Rules

### Affiliate Codes

- **Format**: Alphanumeric characters, typically 6-20 characters
- **Case**: Case-insensitive validation
- **Scope**: Must be valid for the specific program being enrolled in
- **Trainer**: Affiliate code must belong to an active trainer
- **Self-referral**: Athletes cannot use their own affiliate code (if they are also trainers)

### Coupon Codes

- **Format**: Alphanumeric, hyphens, underscores (3-50 characters)
- **Uniqueness**: Must be unique across all active coupons
- **Expiry**: Must not be expired (if expiry date is set)
- **Usage Limit**: Must not exceed usage limit (if set)
- **User Limit**: Each user can only use a coupon once per program
- **Payment Type**: Must apply to the selected payment type (one_time or subscription)
- **Program Scope**: Must be valid for the specific program

### Discount Types

- **Fixed Amount**: Positive dollar amounts (e.g., $10 off)
- **Percentage**: 0-100% range (e.g., 20% off)
- **Maximum Discount**: Cannot exceed the original amount
- **Minimum Amount**: Final amount cannot be less than $0

## Error Handling Best Practices

1. **Always check for `requires_action`** in error responses
2. **Use Stripe.js** for authentication - never handle manually
3. **Show loading states** during authentication and webhook processing
4. **Implement timeouts** for enrollment activation (max 20 seconds)
5. **Provide clear error messages** for different failure scenarios
6. **Validate affiliate/coupon codes** before submitting payment
7. **Show discount preview** to users before final confirmation
8. **Handle discount calculation errors** gracefully

## Testing Scenarios

### Test Cards for Authentication

```javascript
// 3D Secure authentication required
const testCard3DS = "pm_card_authenticationRequired";

// 3D Secure authentication fails
const testCard3DSFail = "pm_card_authenticationRequiredOnSetup";

// Insufficient funds
const testCardDeclined = "pm_card_chargeDeclined";
```

## Testing Data

### Test Affiliate Codes

- `TRAINER123` - Valid affiliate code
- `INVALID123` - Invalid affiliate code
- `OTHERPROGRAM456` - Valid code but wrong program

### Test Coupon Codes

- `SAVE20` - 20% discount coupon
- `EXPIRED10` - Expired coupon
- `MAXED50` - Usage limit reached
- `USED30` - User already used this coupon
- `SUBONLY` - Only applies to subscriptions
- `NOTFOUND` - Invalid coupon code

### Test Payment Methods

- `pm_card_visa` - Standard successful payment
- `pm_card_authenticationRequired` - Requires 3D Secure
- `pm_card_authenticationRequiredOnSetup` - 3D Secure fails
- `pm_card_chargeDeclined` - Insufficient funds

## API Endpoints Summary

### Enrollment

- `POST /v2/api/kanglink/custom/athlete/enrollment` - Create enrollment with affiliate/coupon support

### Validation

- `POST /v2/api/kanglink/custom/affiliate/validate` - Validate affiliate codes
- `POST /v2/api/kanglink/custom/coupon/validate` - Validate coupon codes
- `GET /v2/api/kanglink/custom/discount/preview` - Preview discount calculations

### Status Checking

- `GET /v2/api/kanglink/custom/athlete/enrollment/status/:id` - Check enrollment status

## Support Contact

If enrollment fails after authentication or times out, direct users to contact support with:

- Enrollment attempt timestamp
- Split ID
- Payment method type
- Affiliate code used (if any)
- Coupon code used (if any)
- Original amount and final amount
- Error message received
- Browser and device information

## Related Documentation

- [Discount System API](./discount_system_api.md) - Complete discount management
- [Discount API Quick Reference](./discount_api_quick_reference.md) - API endpoints
- [Frontend Discount Implementation](./frontend_discount_implementation.md) - UI components
- [Payout System Documentation](./payout_system_documentation.md) - Commission tracking

---

**Note**: This authentication flow only applies to subscriptions and rare one-time payment cases. Most one-time payments will succeed immediately without requiring additional authentication. Affiliate codes and coupon codes are processed during enrollment and affect the final payment amount charged to Stripe.
