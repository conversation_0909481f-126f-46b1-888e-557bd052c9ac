import React, { useState } from "react";
import { ChevronDown } from "lucide-react";
import { EditIcon } from "@/assets/svgs";
import { PaginationBar } from "@/components/PaginationBar";
import { Athlete } from "./types";
import { AthleteEnrollmentModal } from "@/components/Athletes";

interface AthleteTableProps {
  athletes: Athlete[];
  currentPage: number;
  totalPages: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onViewList: (athleteId: number) => void;
  onEditAthlete: (athleteId: number) => void;
  onStatusChange: (athleteId: number, newStatus: Athlete["status"]) => void;
}

const AthleteTable: React.FC<AthleteTableProps> = ({
  athletes,
  currentPage,
  totalPages,
  pageSize,
  onPageChange,
  onViewList,
  onEditAthlete: _onEditAthlete,
  onStatusChange,
}) => {
  // Enrollment modal state
  const [showEnrollmentModal, setShowEnrollmentModal] = useState<{
    athleteId: number;
    athleteName: string;
  } | null>(null);

  const getStatusColor = (status: Athlete["status"]) => {
    switch (status) {
      case 1:
        return "text-green-600 bg-green-50 border-green-200 dark:bg-[#262626] dark:border-[#3A3A3A] dark:text-[#F3F4F6]";
      case 0:
        return "text-gray-600 bg-gray-50 border-gray-200 dark:bg-[#262626] dark:border-[#3A3A3A] dark:text-[#F3F4F6]";
      case 2:
        return "text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-[#262626] dark:border-[#3A3A3A] dark:text-[#F3F4F6]";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200 dark:bg-[#262626] dark:border-[#3A3A3A] dark:text-[#F3F4F6]";
    }
  };

  return (
    <div className="bg-background border border-border rounded-lg shadow-sm overflow-hidden">
      <div className="px-4 sm:px-6 py-4 border-b border-border">
        <h2 className="text-2xl font-bold text-text">Athlete</h2>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Table Header */}
          <thead className="bg-background-secondary">
            <tr>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-base font-medium text-text">Name</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-base font-medium text-text">
                    Date joined
                  </span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-base font-medium text-text">Status</span>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-base font-medium text-text">
                  Enrollments
                </span>
              </th>
              {/* <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-base font-medium text-text">Actions</span>
              </th> */}
            </tr>
          </thead>

          {/* Table Body */}
          <tbody className="divide-y divide-border">
            {athletes.map((athlete) => (
              <tr
                key={athlete.id}
                className="hover:bg-background-hover transition-colors duration-200"
              >
                <td className="px-4 sm:px-6 py-4 text-base text-text whitespace-nowrap">
                  {athlete?.name}
                </td>
                <td className="px-4 sm:px-6 py-4 text-base text-text whitespace-nowrap">
                  {athlete?.dateJoined}
                </td>
                <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                  <div className="relative">
                    <select
                      value={athlete.status}
                      onChange={(e) =>
                        onStatusChange(
                          athlete.id as number,
                          e.target.value as unknown as Athlete["status"]
                        )
                      }
                      // disabled
                      className={`px-3 py-2 pr-8 bg-none rounded-md border text-sm font-medium appearance-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${getStatusColor(athlete.status as unknown as Athlete["status"])}`}
                    >
                      <option value={1}>Active</option>
                      <option value={0}>Inactive</option>
                      {/* <option value={2}>Suspended</option> */}
                    </select>
                    {/* <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 pointer-events-none" /> */}
                  </div>
                </td>
                <td className="px-4 sm:px-6 py-4 text-base text-text whitespace-nowrap">
                  <button
                    title="View Enrollments"
                    onClick={() => {
                      setShowEnrollmentModal({
                        athleteId: athlete.id as number,
                        athleteName: athlete.name || "Unknown Athlete",
                      });
                      onViewList(athlete.id as number);
                    }}
                    disabled={!athlete.enrollments?.length}
                    className="disbaled:opacity-50 disabled:border-none disabled:hover:bg-transparent disabled:text-text px-3 py-1.5 border border-primary text-primary rounded-md hover:bg-primary hover:text-white transition-colors duration-200 text-xs font-medium"
                  >
                    {/* {athlete?.enrollments?.length
                      ? `View ${athlete.enrollments?.length}`
                      : 0} */}
                    View
                  </button>
                </td>
                {/* <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => onEditAthlete(athlete.id as number)}
                    className="p-2 text-text-secondary hover:text-text transition-colors duration-200"
                    title="Edit Athlete"
                  >
                    <EditIcon className="w-4 h-4" />
                  </button>
                </td> */}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-4 sm:px-6 py-4 border-t border-border">
          <PaginationBar
            currentPage={currentPage}
            pageCount={totalPages}
            pageSize={pageSize}
            canPreviousPage={currentPage > 1}
            canNextPage={currentPage < totalPages}
            updatePageSize={() => {}} // Not needed for this implementation
            updateCurrentPage={onPageChange}
            startSize={pageSize}
            multiplier={1}
            canChangeLimit={false}
          />
        </div>
      )}

      {/* Athlete Enrollment Modal */}
      {showEnrollmentModal && (
        <AthleteEnrollmentModal
          isOpen={!!showEnrollmentModal}
          onClose={() => setShowEnrollmentModal(null)}
          athleteId={showEnrollmentModal.athleteId}
          athleteName={showEnrollmentModal.athleteName}
        />
      )}
    </div>
  );
};

export default AthleteTable;
