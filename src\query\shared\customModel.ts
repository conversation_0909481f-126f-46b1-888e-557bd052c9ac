import { useMutation } from "@tanstack/react-query";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import { MethodConfig } from "@/utils/MkdSDK";

export const useCustomModelQuery = (config?: any) => {
  const { sdk } = useSDK({ role: config?.role });
  const { showToast, tokenExpireError } = useContexts();

  const mutationFn = async (options: MethodConfig) => {
    const response = await sdk.request(options);

    return response;
  };

  return useMutation({
    mutationFn,
    onError: (error: any) => {
      const message = error?.response?.data?.message || error?.message;
      if (config?.showToast) {
        showToast(message, 5000, ToastStatusEnum.ERROR);
      }
      tokenExpireError(message);
      console.error(error);
    },
    // ...config,
  });
};
