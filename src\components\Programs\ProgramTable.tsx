import React, { useState } from "react";
import { ChevronDown, Eye, Check, X, Trash2 } from "lucide-react";
// import { EditIcon } from "@/assets/svgs";
import { PaginationBar } from "@/components/PaginationBar";
import { AdminProgram } from "./types";
import { useContexts } from "@/hooks/useContexts";
import { RoleEnum } from "@/utils/Enums";
import { EnrollmentDetailsModal } from "@/components/Programs";
import { Modal } from "@/components/Modal";

interface ProgramTableProps {
  programs: AdminProgram[];
  currentPage: number;
  totalPages: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onViewList: (programId: number, enrollmentIds: number[]) => void;
  onEditProgram: (programId: number) => void;
  onPreviewProgram: (programId: number) => void;
  onApproveProgram: (programId: number) => Promise<void>;
  onRejectProgram: (programId: number, reason?: string) => Promise<void>;
  onDeleteProgram: (programId: number, reason?: string) => Promise<void>;
  onStatusChange: (
    programId: number,
    newStatus: AdminProgram["status"]
  ) => void;
}

const ProgramTable: React.FC<ProgramTableProps> = ({
  programs,
  currentPage,
  totalPages,
  pageSize,
  onPageChange,
  onViewList,
  onEditProgram: _onEditProgram,
  onPreviewProgram,
  onApproveProgram,
  onRejectProgram,
  onDeleteProgram,
  onStatusChange,
}) => {
  const { authState } = useContexts();
  const [rejectionReason, setRejectionReason] = useState<string>("");
  const [deleteReason, setDeleteReason] = useState<string>("");
  const [showRejectModal, setShowRejectModal] = useState<number | null>(null);
  const [showApproveModal, setShowApproveModal] = useState<number | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState<number | null>(null);
  const [isApproving, setIsApproving] = useState<boolean>(false);
  const [isRejecting, setIsRejecting] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  // Enrollment modal state
  const [showEnrollmentModal, setShowEnrollmentModal] = useState<{
    programId: number;
    enrollmentIds: number[];
  } | null>(null);

  // Check if user is admin or super_admin
  const isAdmin =
    authState.role === RoleEnum.ADMIN ||
    authState.role === RoleEnum.SUPER_ADMIN;

  const handleApproveClick = (programId: number) => {
    setShowApproveModal(programId);
  };

  const handleApproveConfirm = async () => {
    if (showApproveModal && !isApproving) {
      setIsApproving(true);
      try {
        await onApproveProgram(showApproveModal);
        setShowApproveModal(null);
      } finally {
        setIsApproving(false);
      }
    }
  };

  const handleApproveCancel = () => {
    if (!isApproving) {
      setShowApproveModal(null);
    }
  };

  const handleRejectClick = (programId: number) => {
    setShowRejectModal(programId);
    setRejectionReason("");
  };

  const handleRejectConfirm = async () => {
    if (showRejectModal && rejectionReason.trim() && !isRejecting) {
      setIsRejecting(true);
      try {
        await onRejectProgram(showRejectModal, rejectionReason);
        setShowRejectModal(null);
        setRejectionReason("");
      } finally {
        setIsRejecting(false);
      }
    }
  };

  const handleRejectCancel = () => {
    if (!isRejecting) {
      setShowRejectModal(null);
      setRejectionReason("");
    }
  };

  const handleDeleteClick = (programId: number) => {
    setShowDeleteModal(programId);
    setDeleteReason("");
  };

  const handleDeleteConfirm = async () => {
    if (showDeleteModal && deleteReason.trim() && !isDeleting) {
      setIsDeleting(true);
      try {
        await onDeleteProgram(showDeleteModal, deleteReason);
        setShowDeleteModal(null);
        setDeleteReason("");
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const handleDeleteCancel = () => {
    if (!isDeleting) {
      setShowDeleteModal(null);
      setDeleteReason("");
    }
  };

  const getStatusColor = (status: AdminProgram["status"]) => {
    switch (status) {
      case "Live":
        return "text-primary bg-background-secondary border-primary";
      case "Completed":
        return "text-blue-600 bg-background-secondary border-blue-200";
      case "Pending Approval":
          return "text-warning bg-background-secondary border-warning";
      case "Rejected":
        return "text-red-600 bg-red-50 border-red-200 dark:bg-[#262626] dark:border-[#3A3A3A] dark:text-[#F3F4F6]";
      case "Draft":
      case "Archived":
      case "Deleted":
      default:
        return "text-text bg-background-secondary border-border";
    }
  };

  return (
    <div className="bg-background border border-border rounded-lg shadow-sm overflow-hidden">
      <div className="px-4 sm:px-6 py-4 border-b border-border">
        <h2 className="text-2xl font-bold text-text">Program</h2>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Table Header */}
          <thead className="bg-background-secondary">
            <tr>
              <th className="px-4 sm:px-6 py-3 text-left text-base font-medium text-text whitespace-nowrap">
                <div className="flex items-center space-x-2">
                  <span>Program Name</span>
                  <ChevronDown className="w-3 h-3 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left text-base font-medium text-text whitespace-nowrap">
                <div className="flex items-center space-x-2">
                  <span>Trainer</span>
                  <ChevronDown className="w-3 h-3 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left text-base font-medium text-text whitespace-nowrap">
                <div className="flex items-center space-x-2">
                  <span>Date Added</span>
                  <ChevronDown className="w-3 h-3 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left text-base font-medium text-text whitespace-nowrap">
                Status
              </th>
              <th className="px-4 sm:px-6 py-3 text-left text-base font-medium text-text whitespace-nowrap">
                Enrollments
              </th>
              <th className="px-4 sm:px-6 py-3 text-left text-base font-medium text-text whitespace-nowrap">
                Actions
              </th>
            </tr>
          </thead>

          {/* Table Body */}
          <tbody className="divide-y divide-border">
            {programs.map((program) => (
              <tr
                key={program.id}
                className="hover:bg-background-hover transition-colors duration-200"
              >
                <td className="px-4 sm:px-6 py-4 text-base text-text whitespace-nowrap">
                  {program.program_name}
                </td>
                <td className="px-4 sm:px-6 py-4 text-base text-text whitespace-nowrap">
                  {program.trainer_name}
                </td>
                <td className="px-4 sm:px-6 py-4 text-base text-text whitespace-nowrap">
                  {program.dateCreated}
                </td>
                <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                  <div className="relative">
                    <select
                      value={program.status}
                      onChange={(e) =>
                        onStatusChange(
                          program.id as number,
                          e.target.value as AdminProgram["status"]
                        )
                      }
                      disabled
                      className={`px-3 !w-[100px] py-2 text-center border rounded-md text-sm font-medium bg-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${getStatusColor(program.status)}`}
                      style={{
                        backgroundImage: "none",
                        WebkitAppearance: "none",
                        MozAppearance: "none",
                      }}
                    >
                      <option value="Draft">Draft</option>
                      <option value="Pending Approval">Pending</option>
                      <option value="Live">Live</option>
                      <option value="Rejected">Rejected</option>
                      <option value="Archived">Archived</option>
                      <option value="Deleted">Deleted</option>
                    </select>
                    {/* <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none" /> */}
                  </div>
                </td>
                <td className="px-4 sm:px-6 py-4 text-base text-text whitespace-nowrap">
                  <button
                    onClick={() => {
                      const enrollmentIds =
                        program.enrollments?.map((e) => e.id as number) || [];
                      setShowEnrollmentModal({
                        programId: program.id as number,
                        enrollmentIds: enrollmentIds,
                      });
                      onViewList(program.id as number, enrollmentIds);
                    }}
                    title="View Enrollments"
                    disabled={!program.enrollments?.length}
                    className="disbaled:opacity-50 disabled:border-none disabled:hover:bg-transparent disabled:text-text px-3 py-1.5 border border-primary text-primary rounded-md hover:bg-primary hover:text-white transition-colors duration-200 text-xs font-medium"
                  >
                    {/* {program?.enrollments?.length
                      ? `View ${program.enrollments?.length}`
                      : 0} */}
                      View
                  </button>
                </td>
                <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => onPreviewProgram(program.id as number)}
                      className="p-1.5 text-text-secondary hover:text-text transition-colors duration-200"
                      title="Preview Program"
                    >
                      <Eye className="w-4 h-4" />
                    </button>

                    {/* Admin-only approve/reject/delete buttons */}
                    {isAdmin && (
                      <>
                        {program.status === "Pending Approval" && (
                          <>
                            <button
                              onClick={() =>
                                handleApproveClick(program.id as number)
                              }
                              className="p-1.5 text-green-600 hover:text-green-700 hover:bg-green-50 rounded transition-colors duration-200"
                              title="Approve Program"
                            >
                              <Check className="w-4 h-4" />
                            </button>

                            <button
                              onClick={() =>
                                handleRejectClick(program.id as number)
                              }
                              className="p-1.5 text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors duration-200"
                              title="Reject Program"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </>
                        )}

                        {/* Delete button for all programs */}
                        {program.status !== "Deleted" && program.status !== "Archived" && <button
                          onClick={() =>
                            handleDeleteClick(program.id as number)
                          }
                          className="p-1.5 text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors duration-200"
                          title="Delete Program"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>}
                      </>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-4 sm:px-6 py-4 border-t border-border">
          <PaginationBar
            currentPage={currentPage}
            pageCount={totalPages}
            pageSize={pageSize}
            canPreviousPage={currentPage > 1}
            canNextPage={currentPage < totalPages}
            updatePageSize={() => {}} // Not needed for this implementation
            updateCurrentPage={onPageChange}
            startSize={pageSize}
            multiplier={1}
            canChangeLimit={false}
          />
        </div>
      )}

      {/* Approve Confirmation Modal */}
      <Modal
        isOpen={!!showApproveModal}
        title="Approve Program"
        modalCloseClick={handleApproveCancel}
        modalHeader={true}
        classes={{ modal: "h-full", modalDialog: "h-fit w-full md:max-w-[25rem]", modalContent: "" }}
        disableCancel={isApproving}
      >
        <div className="space-y-4">
          <p className="text-text-secondary">
            Are you sure you want to approve this program? This action will
            make the program live and available to athletes.
          </p>
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleApproveCancel}
              disabled={isApproving}
              className="px-4 py-2 border border-border text-text-secondary rounded-md hover:bg-background-hover disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              onClick={handleApproveConfirm}
              disabled={isApproving}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center space-x-2"
            >
              {isApproving && (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              )}
              <span>{isApproving ? "Approving..." : "Approve Program"}</span>
            </button>
          </div>
        </div>
      </Modal>

      {/* Rejection Modal */}
      <Modal
        isOpen={!!showRejectModal}
        title="Reject Program"
        modalCloseClick={handleRejectCancel}
        modalHeader={true}
        classes={{ modal: "h-full", modalDialog: "h-fit w-full md:max-w-xl", modalContent: "" }}
        disableCancel={isRejecting}
      >
        <div className="space-y-4">
          <p className="text-text-secondary">
            Please provide a reason for rejecting this program:
          </p>
          <textarea
            value={rejectionReason}
            onChange={(e) => setRejectionReason(e.target.value)}
            placeholder="Enter rejection reason..."
            disabled={isRejecting}
            className="w-full p-3 border border-border rounded-md text-text bg-input focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none disabled:opacity-50 disabled:cursor-not-allowed"
            rows={4}
          />
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleRejectCancel}
              disabled={isRejecting}
              className="px-4 py-2 border border-border text-text-secondary rounded-md hover:bg-background-hover disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              onClick={handleRejectConfirm}
              disabled={!rejectionReason.trim() || isRejecting}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-red-300 disabled:cursor-not-allowed transition-colors duration-200 flex items-center space-x-2"
            >
              {isRejecting && (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              )}
              <span>{isRejecting ? "Rejecting..." : "Reject Program"}</span>
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Modal */}
      <Modal
        isOpen={!!showDeleteModal}
        title="Delete Program"
        modalCloseClick={handleDeleteCancel}
        modalHeader={true}
        classes={{ modal: "h-full", modalDialog: "h-fit w-full md:max-w-xl", modalContent: "" }}
        disableCancel={isDeleting}
      >
        <div className="space-y-4">
          <p className="text-text-secondary">
            Are you sure you want to delete this program? This action cannot be undone.
          </p>
          <p className="text-text-secondary">
            Please provide a reason for deleting this program:
          </p>
          <textarea
            value={deleteReason}
            onChange={(e) => setDeleteReason(e.target.value)}
            placeholder="Enter deletion reason..."
            disabled={isDeleting}
            className="w-full p-3 border border-border rounded-md text-text bg-input focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none disabled:opacity-50 disabled:cursor-not-allowed"
            rows={4}
          />
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleDeleteCancel}
              disabled={isDeleting}
              className="px-4 py-2 border border-border text-text-secondary rounded-md hover:bg-background-hover disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              onClick={handleDeleteConfirm}
              disabled={!deleteReason.trim() || isDeleting}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-red-300 disabled:cursor-not-allowed transition-colors duration-200 flex items-center space-x-2"
            >
              {isDeleting && (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              )}
              <span>{isDeleting ? "Deleting..." : "Delete Program"}</span>
            </button>
          </div>
        </div>
      </Modal>

      {/* Enrollment Details Modal */}
      {showEnrollmentModal && (
        <EnrollmentDetailsModal
          isOpen={!!showEnrollmentModal}
          onClose={() => setShowEnrollmentModal(null)}
          programId={showEnrollmentModal.programId}
          enrollmentIds={showEnrollmentModal.enrollmentIds}
        />
      )}
    </div>
  );
};

export default ProgramTable;
