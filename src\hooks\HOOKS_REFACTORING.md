# Hooks Refactoring Summary

## Overview
Moved React Query hooks from individual page components to dedicated hook files in the `/src/hooks/` directory for better reusability and organization.

## Hooks Created

### 1. useProgramDetails
**Location**: `src/hooks/useProgramDetails/`
- **Hook**: `useProgramDetails(programId: string | undefined)`
- **Purpose**: Fetches detailed program information
- **Endpoint**: `/v2/api/kanglink/custom/public/program/${programId}`
- **Returns**: `ProgramDetailsResponse` with complete program data
- **Features**: Fresh data on mount, window focus refetch

### 2. useProgramReviews  
**Location**: `src/hooks/useProgramReviews/`
- **Hook**: `useProgramReviews(programId, params?)`
- **Purpose**: Fetches program reviews with pagination
- **Endpoint**: `/v2/api/kanglink/custom/public/program/${programId}/reviews`
- **Returns**: `ProgramReviewsResponse` with reviews and pagination
- **Features**: Fresh data on mount, window focus refetch

### 3. useTrainerDetails
**Location**: `src/hooks/useTrainerDetails/`
- **Hook**: `useTrainerDetails(trainerId: string | null)`
- **Purpose**: Fetches detailed trainer information
- **Endpoint**: `/v2/api/kanglink/custom/public/trainer/${trainerId}`
- **Returns**: `TrainerDetailsResponse` with extended trainer data
- **Features**: Fresh data on mount, window focus refetch

### 4. useTrainerPrograms
**Location**: `src/hooks/useTrainerPrograms/`
- **Hook**: `useTrainerPrograms(trainerId, params?)`
- **Purpose**: Fetches programs by a specific trainer
- **Endpoint**: `/v2/api/kanglink/custom/public/trainer/${trainerId}/programs`
- **Returns**: `TrainerProgramsResponse` with programs and pagination
- **Features**: Fresh data on mount, window focus refetch

## Hook Configuration

All hooks use the same fresh data configuration:
```typescript
{
  staleTime: 0, // Always fetch fresh data
  refetchOnMount: true, // Refetch when component mounts
  refetchOnWindowFocus: true, // Refetch when window gains focus
}
```

## Files Modified

### Pages Updated
1. **ViewAthleteProgramPage.tsx**
   - Removed local `useProgramDetails` and `useProgramReviews` hooks
   - Added imports from new hook directories
   - Updated type imports and interfaces

2. **ViewAthleteTrainerDetailsPage.tsx**
   - Removed local `useTrainerDetails` and `useTrainerPrograms` hooks
   - Added imports from new hook directories
   - Cleaned up unused imports

### Hook Structure
Each hook follows this directory structure:
```
src/hooks/
├── useProgramDetails/
│   ├── useProgramDetails.tsx
│   └── index.ts
├── useProgramReviews/
│   ├── useProgramReviews.tsx
│   └── index.ts
├── useTrainerDetails/
│   ├── useTrainerDetails.tsx
│   └── index.ts
└── useTrainerPrograms/
    ├── useTrainerPrograms.tsx
    └── index.ts
```

## Benefits

1. **Reusability**: Hooks can now be used across multiple components
2. **Organization**: Better separation of concerns
3. **Maintainability**: Centralized data fetching logic
4. **Type Safety**: Exported TypeScript interfaces
5. **Consistency**: Standardized fresh data fetching approach

## Usage Examples

```typescript
// In any component
import { useProgramDetails } from "@/hooks/useProgramDetails";
import { useProgramReviews } from "@/hooks/useProgramReviews";
import { useTrainerDetails } from "@/hooks/useTrainerDetails";
import { useTrainerPrograms } from "@/hooks/useTrainerPrograms";

// Use the hooks
const { data: program, isLoading, error } = useProgramDetails(programId);
const { data: reviews } = useProgramReviews(programId, { page: 1, limit: 10 });
const { data: trainer } = useTrainerDetails(trainerId);
const { data: programs } = useTrainerPrograms(trainerId);
```

## Type Exports

Each hook exports its response types:
- `ProgramDetailsResponse`
- `ReviewResponse`, `ProgramReviewsResponse`
- `TrainerDetailsResponse`
- `TrainerProgramResponse`, `TrainerProgramsResponse`

## Fresh Data Strategy

All hooks implement the fresh data strategy to ensure users always see the latest information when:
- Navigating back to pages
- Switching browser tabs
- Component remounting

This resolves the stale data issue where users had to refresh the entire app to see updates.

## Future Considerations

- These hooks can be extended with additional features like optimistic updates
- Error handling can be centralized in the hooks
- Loading states can be standardized
- Cache invalidation strategies can be implemented at the hook level
