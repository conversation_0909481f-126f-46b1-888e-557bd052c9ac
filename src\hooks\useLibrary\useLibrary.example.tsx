import React, { useState } from "react";
import { useLibrary } from "./useLibrary";

// Example component showing how to use the useLibrary hook
export const LibraryExample: React.FC = () => {
  const [selectedType, setSelectedType] = useState<"exercise" | "video">("exercise");
  const [searchTerm, setSearchTerm] = useState("");
  const [newItemName, setNewItemName] = useState("");
  const [newItemUrl, setNewItemUrl] = useState(""); // For videos

  // Use the library hook
  const {
    // Data
    libraryData,
    adminLibraryData,
    trainerLibraryData,
    pagination,
    
    // Loading states
    isLoading,
    isLoadingAdmin,
    isLoadingTrainer,
    isCreating,
    isUpdating,
    isDeleting,
    
    // Actions
    createLibraryItem,
    updateLibraryItem,
    deleteLibraryItem,
    
    // Pagination & filtering
    updatePagination,
    searchLibraryItems,
    filterByType,
    clearFilters,
    refreshAll
  } = useLibrary({ 
    libraryType: selectedType,
    autoFetch: true 
  });

  // Handle search
  const handleSearch = () => {
    searchLibraryItems(searchTerm);
  };

  // Handle create new item
  const handleCreate = async () => {
    if (!newItemName.trim()) return;

    const newItem = {
      name: newItemName,
      ...(selectedType === "video" && { url: newItemUrl })
    };

    try {
      await createLibraryItem(newItem);
      setNewItemName("");
      setNewItemUrl("");
    } catch (error) {
      console.error("Failed to create item:", error);
    }
  };

  // Handle update item
  const handleUpdate = async (id: string | number, name: string) => {
    try {
      await updateLibraryItem(id, { name });
    } catch (error) {
      console.error("Failed to update item:", error);
    }
  };

  // Handle delete item
  const handleDelete = async (id: string | number) => {
    if (window.confirm("Are you sure you want to delete this item?")) {
      try {
        await deleteLibraryItem(id);
      } catch (error) {
        console.error("Failed to delete item:", error);
      }
    }
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    updatePagination({ page });
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Library Management</h1>

      {/* Type Selector */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">Library Type:</label>
        <select
          value={selectedType}
          onChange={(e) => setSelectedType(e.target.value as "exercise" | "video")}
          className="border rounded px-3 py-2"
        >
          <option value="exercise">Exercises</option>
          <option value="video">Videos</option>
        </select>
      </div>

      {/* Search */}
      <div className="mb-4 flex gap-2">
        <input
          type="text"
          placeholder={`Search ${selectedType}s...`}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="border rounded px-3 py-2 flex-1"
        />
        <button
          onClick={handleSearch}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Search
        </button>
        <button
          onClick={clearFilters}
          className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
        >
          Clear
        </button>
      </div>

      {/* Filters */}
      <div className="mb-4 flex gap-2">
        <button
          onClick={() => filterByType(1)}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          Admin Created
        </button>
        <button
          onClick={() => filterByType(2)}
          className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
        >
          Trainer Created
        </button>
        <button
          onClick={refreshAll}
          className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600"
        >
          Refresh All
        </button>
      </div>

      {/* Create New Item */}
      <div className="mb-6 p-4 border rounded">
        <h3 className="text-lg font-semibold mb-3">Create New {selectedType}</h3>
        <div className="flex gap-2 mb-2">
          <input
            type="text"
            placeholder={`${selectedType} name`}
            value={newItemName}
            onChange={(e) => setNewItemName(e.target.value)}
            className="border rounded px-3 py-2 flex-1"
          />
          {selectedType === "video" && (
            <input
              type="url"
              placeholder="Video URL"
              value={newItemUrl}
              onChange={(e) => setNewItemUrl(e.target.value)}
              className="border rounded px-3 py-2 flex-1"
            />
          )}
          <button
            onClick={handleCreate}
            disabled={isCreating || !newItemName.trim()}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {isCreating ? "Creating..." : "Create"}
          </button>
        </div>
      </div>

      {/* Library Data */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* All Items */}
        <div>
          <h3 className="text-lg font-semibold mb-3">All {selectedType}s</h3>
          {isLoading ? (
            <p>Loading...</p>
          ) : (
            <div className="space-y-2">
              {libraryData.map((item: any) => (
                <div key={item.id} className="p-3 border rounded">
                  <div className="font-medium">{item.name}</div>
                  <div className="text-sm text-gray-500">
                    Type: {item.type === 1 ? "Admin" : "Trainer"}
                  </div>
                  {selectedType === "video" && item.url && (
                    <div className="text-sm text-blue-500 truncate">{item.url}</div>
                  )}
                  <div className="mt-2 flex gap-2">
                    <button
                      onClick={() => handleUpdate(item.id, `${item.name} (updated)`)}
                      disabled={isUpdating}
                      className="text-sm bg-yellow-500 text-white px-2 py-1 rounded"
                    >
                      Update
                    </button>
                    <button
                      onClick={() => handleDelete(item.id)}
                      disabled={isDeleting}
                      className="text-sm bg-red-500 text-white px-2 py-1 rounded"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {/* Pagination */}
          <div className="mt-4 flex justify-between items-center">
            <span className="text-sm text-gray-500">
              Page {pagination.page} of {pagination.num_pages} ({pagination.total} total)
            </span>
            <div className="flex gap-2">
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page <= 1}
                className="px-3 py-1 border rounded disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page >= pagination.num_pages}
                className="px-3 py-1 border rounded disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        </div>

        {/* Admin Items */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Admin {selectedType}s</h3>
          {isLoadingAdmin ? (
            <p>Loading...</p>
          ) : (
            <div className="space-y-2">
              {adminLibraryData.map((item: any) => (
                <div key={item.id} className="p-3 border rounded bg-green-50">
                  <div className="font-medium">{item.name}</div>
                  {selectedType === "video" && item.url && (
                    <div className="text-sm text-blue-500 truncate">{item.url}</div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Trainer Items */}
        <div>
          <h3 className="text-lg font-semibold mb-3">My {selectedType}s</h3>
          {isLoadingTrainer ? (
            <p>Loading...</p>
          ) : (
            <div className="space-y-2">
              {trainerLibraryData.map((item: any) => (
                <div key={item.id} className="p-3 border rounded bg-purple-50">
                  <div className="font-medium">{item.name}</div>
                  {selectedType === "video" && item.url && (
                    <div className="text-sm text-blue-500 truncate">{item.url}</div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LibraryExample;
