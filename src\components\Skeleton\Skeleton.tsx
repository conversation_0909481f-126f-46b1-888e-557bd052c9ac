import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

interface SkeletonLoaderProps {
  className?: string;
  count?: number;
  counts?: number[];
  circle?: boolean;
}

const SkeletonLoader = ({
  className = "",
  count = 5,
  counts = [2, 1, 3, 1, 1],
  circle = false,
}: SkeletonLoaderProps) => {
  const { state } = useTheme();
  const mode = state?.theme || "light";

  return (
    <div
      className={`flex overflow-hidden flex-col gap-5 p-4 w-full max-h-screen h-fit min-h-fit ${className}`}
    >
      <SkeletonTheme
        baseColor={THEME_COLORS[mode].TEXT_DISABLED}
        highlightColor={THEME_COLORS[mode].BACKGROUND_HOVER}
      >
        {/* <Skeleton circle width={60} height={60} /> */}
        {Array.from({ length: count }).map((_, index) => (
          <Skeleton
            key={`${_}${index}`}
            count={counts[index] ?? 1}
            height={
              counts[index] && counts[index] > 1
                ? 25
                : index + 1 === count
                  ? 25
                  : 80
            }
            circle={circle}
            style={{ marginBottom: "0.6rem" }}
          />
        ))}
      </SkeletonTheme>
    </div>
  );
};

export default SkeletonLoader;
