import React, { useState, useEffect } from "react";
import { Modal } from "@/components/Modal";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useLibrary } from "@/hooks/useLibrary/useLibrary";
import { Exercise } from "@/interfaces";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import { useProfile } from "@/hooks/useProfile";
import DeleteConfirmationModal from "@/components/Library/DeleteConfirmationModal";
import AddExerciseModal from "@/components/Trainer/AddExerciseModal";
import EditExerciseModal from "@/components/Trainer/EditExerciseModal";

interface ExerciseLibraryModalProps {
    isOpen: boolean;
    onClose: () => void;
}

const ExerciseLibraryModal: React.FC<ExerciseLibraryModalProps> = ({
    isOpen,
    onClose,
}) => {
    const { showToast } = useContexts();
    const [searchTerm, setSearchTerm] = useState("");
    const [hoveredExercise, setHoveredExercise] = useState<number | null>(null);
    const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
    const [exerciseToDelete, setExerciseToDelete] = useState<Exercise | null>(null);
    const [showAddExerciseModal, setShowAddExerciseModal] = useState(false);
    const [showEditExerciseModal, setShowEditExerciseModal] = useState(false);
    const [exerciseToEdit, setExerciseToEdit] = useState<Exercise | null>(null);
    const { profile } = useProfile();
    const userId = profile?.id;
    // Exercise library hook for user's exercises (type = 2)
    const {
        libraryData: exerciseData,
        isLoading,
        error,
        searchLibraryItems: searchExercises,
        deleteLibraryItem: deleteExercise,
        createLibraryItem: createExercise,
        updateLibraryItem: updateExercise,
        isCreating,
        isUpdating,
    } = useLibrary({
        libraryType: "exercise",
        initialPagination: {
            page: 1,
            limit: 1000,
            filters: { type: 2, ...(userId && { user_id: Number(userId) }) }, // User's exercises
        },
        disable: {
            type_two: false,
            type_one: true,
        },
    });

    // Filter exercises based on search term
    const filteredExercises = exerciseData?.filter((exercise: Exercise) =>
        exercise.name?.toLowerCase().includes(searchTerm.toLowerCase())
    ) || [];

    // Handle search
    const handleSearch = (value: string) => {
        setSearchTerm(value);
        if (value.trim()) {
            searchExercises(value.trim());
        } else {
            searchExercises(value);
        }

    };

    // Handle delete exercise confirmation
    const handleDeleteExerciseClick = (exercise: Exercise) => {
        setExerciseToDelete(exercise);
        setShowDeleteConfirmation(true);
    };

    // Handle delete exercise
    const handleDeleteExercise = async () => {
        if (!exerciseToDelete) return;

        try {
            await deleteExercise(exerciseToDelete.id as number);
            showToast("Exercise deleted successfully", 3000, ToastStatusEnum.SUCCESS);
        } catch (error: any) {
            const message = error?.message || "Failed to delete exercise";
            showToast(message, 3000, ToastStatusEnum.ERROR);
        }
    };

    // Handle edit exercise
    const handleEditExercise = (exercise: Exercise) => {
        setExerciseToEdit(exercise);
        setShowEditExerciseModal(true);
    };

    // Handle update exercise
    const handleUpdateExercise = async (exerciseId: number, exerciseName: string, videoUrl?: string) => {
        try {
            await updateExercise(exerciseId, {
                name: exerciseName,
                type: 2,
                user_id: userId,
                video_url: videoUrl,
            });
        } catch (error) {
            // Error is already handled by the useLibrary hook
            console.error("Error updating exercise:", error);
        }
    };

    // Handle add new exercise
    const handleAddExercise = () => {
        setShowAddExerciseModal(true);
    };

    // Handle create exercise
    const handleCreateExercise = async (exerciseName: string, videoUrl?: string) => {
        try {
            await createExercise({
                name: exerciseName,
                type: 2,
                user_id: userId,// Trainer created
                video_url: videoUrl,
            });
        } catch (error) {
            // Error is already handled by the useLibrary hook
            console.error("Error creating exercise:", error);
        }
    };

    return (
        <>
            <Modal
                isOpen={isOpen}
                modalCloseClick={onClose}
                title="My Exercises"
                modalHeader
                classes={{
                    modalDialog: "!w-full !px-0 md:!w-[32rem] !h-fit",
                    modalContent: "!z-10 !px-0 overflow-hidden !pt-0 !mt-0",
                    modal: "h-full",
                }}
            >
                <div className="p-6">
                    {/* Header with Add Button */}
                    <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-semibold text-text"></h3>
                        <InteractiveButton
                            type="button"
                            onClick={handleAddExercise}
                            className="w-8 h-8 p-2 rounded-full bg-green-500 text-white flex items-center justify-center hover:bg-green-600 transition-colors"
                        >
                            <svg
                                width="16"
                                height="16"
                                viewBox="0 0 16 16"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M8 1V15M1 8H15"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                />
                            </svg>
                        </InteractiveButton>
                    </div>

                    {/* Search Bar */}
                    <div className="relative mb-4">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg
                                className="h-5 w-5 text-gray-400"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                />
                            </svg>
                        </div>
                        <input
                            type="text"
                            placeholder="Search"
                            value={searchTerm}
                            onChange={(e) => handleSearch(e.target.value)}
                            className="w-full pl-10 pr-4 py-2 border border-border rounded-md bg-background text-text placeholder-text-secondary focus:border-primary focus:ring-0 transition-colors"
                        />
                    </div>

                    {/* Exercise List */}
                    <div className="space-y-2 max-h-96 overflow-y-auto">
                        {isLoading ? (
                            <div className="text-center py-8">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                                <p className="text-text-secondary">Loading exercises...</p>
                            </div>
                        ) : error ? (
                            <div className="text-center py-8">
                                <p className="text-red-500">Error loading exercises</p>
                            </div>
                        ) : filteredExercises.length === 0 ? (
                            <div className="text-center py-8">
                                <p className="text-text-secondary">
                                    {searchTerm ? "No exercises found" : "No exercises yet"}
                                </p>
                            </div>
                        ) : (
                            filteredExercises.map((exercise: Exercise) => (
                                <div
                                    key={exercise.id}
                                    className="relative p-3 border-b border-border last:border-b-0 hover:bg-background-secondary transition-colors"
                                    onMouseEnter={() => setHoveredExercise(exercise.id as number)}
                                    onMouseLeave={() => setHoveredExercise(null)}
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2">
                                            <span className="text-text font-medium">
                                                {exercise.name || "Unnamed Exercise"}
                                            </span>
                                            {exercise.video_url && (
                                                <div className="flex items-center space-x-1 text-green-500">
                                                    <svg
                                                        width="14"
                                                        height="14"
                                                        viewBox="0 0 14 14"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            d="M7 1.16667C3.77833 1.16667 1.16667 3.77833 1.16667 7C1.16667 10.2217 3.77833 12.8333 7 12.8333C10.2217 12.8333 12.8333 10.2217 12.8333 7C12.8333 3.77833 10.2217 1.16667 7 1.16667ZM5.83333 9.33333L9.33333 7L5.83333 4.66667V9.33333Z"
                                                            fill="currentColor"
                                                        />
                                                    </svg>
                                                    <span className="text-xs">Video</span>
                                                </div>
                                            )}
                                        </div>

                                        {/* Edit and Delete buttons - visible on hover */}
                                        {hoveredExercise === exercise.id && (
                                            <div className="flex items-center space-x-2">
                                                <InteractiveButton
                                                    type="button"
                                                    onClick={() => handleEditExercise(exercise)}
                                                    className="p-1 border-none text-blue-500 hover:text-blue-600 hover:bg-transparent transition-colors"
                                                    title="Edit"
                                                >
                                                    <svg
                                                        width="16"
                                                        height="16"
                                                        viewBox="0 0 16 16"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            d="M11.25 1.5L14.5 4.75L5.75 13.5H2.5V10.25L11.25 1.5Z"
                                                            stroke="currentColor"
                                                            strokeWidth="1.5"
                                                            strokeLinecap="round"
                                                            strokeLinejoin="round"
                                                        />
                                                    </svg>
                                                </InteractiveButton>

                                                <InteractiveButton
                                                    type="button"
                                                    onClick={() => handleDeleteExerciseClick(exercise)}
                                                    className="p-1 border-none text-red-500 hover:text-red-600 hover:bg-transparent transition-colors"
                                                    title="Delete"
                                                >
                                                    <svg
                                                        width="16"
                                                        height="16"
                                                        viewBox="0 0 16 16"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            d="M2 4H3.33333H14"
                                                            stroke="currentColor"
                                                            strokeWidth="1.5"
                                                            strokeLinecap="round"
                                                            strokeLinejoin="round"
                                                        />
                                                        <path
                                                            d="M12.3333 4V13.3333C12.3333 13.687 12.1929 14.0261 11.9428 14.2761C11.6928 14.5262 11.3537 14.6667 11 14.6667H5C4.64638 14.6667 4.30724 14.5262 4.05719 14.2761C3.80714 14.0261 3.66667 13.687 3.66667 13.3333V4M5.33333 4V2.66667C5.33333 2.31305 5.47381 1.97391 5.72386 1.72386C5.97391 1.47381 6.31305 1.33333 6.66667 1.33333H9.33333C9.68695 1.33333 10.0261 1.47381 10.2761 1.72386C10.5262 1.97391 10.6667 2.31305 10.6667 2.66667V4"
                                                            stroke="currentColor"
                                                            strokeWidth="1.5"
                                                            strokeLinecap="round"
                                                            strokeLinejoin="round"
                                                        />
                                                    </svg>
                                                </InteractiveButton>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ))
                        )}
                    </div>

                    {/* Footer */}
                    <div className="flex justify-end mt-6 pt-4 border-t border-border">
                        <InteractiveButton
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 bg-background border border-border text-text rounded-md hover:bg-background-secondary transition-colors"
                        >
                            Back
                        </InteractiveButton>
                    </div>
                </div>
            </Modal>

            {/* Delete Confirmation Modal */}
            <DeleteConfirmationModal
                isOpen={showDeleteConfirmation}
                onClose={() => {
                    setShowDeleteConfirmation(false);
                    setExerciseToDelete(null);
                }}
                onConfirm={handleDeleteExercise}
                title="Delete Exercise"
                message="Are you sure you want to delete this exercise?"
                itemName={exerciseToDelete?.name}
            />

            {/* Add Exercise Modal */}
            <AddExerciseModal
                isOpen={showAddExerciseModal}
                onClose={() => setShowAddExerciseModal(false)}
                onAddExercise={handleCreateExercise}
                isLoading={isCreating}
            />

            {/* Edit Exercise Modal */}
            <EditExerciseModal
                isOpen={showEditExerciseModal}
                onClose={() => {
                    setShowEditExerciseModal(false);
                    setExerciseToEdit(null);
                }}
                exercise={exerciseToEdit}
                onUpdateExercise={handleUpdateExercise}
                isLoading={isUpdating}
            />
        </>
    );
};

export default ExerciseLibraryModal; 