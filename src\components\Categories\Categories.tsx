import { useState } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface CategoriesProps {
  selectedCategory?: string;
  onCategoryChange?: (categoryId: string) => void;
  className?: string;
}

const Categories = ({
  selectedCategory = "all",
  onCategoryChange,
  className = "",
}: CategoriesProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [activeCategory, setActiveCategory] = useState(selectedCategory);

  const categories: Category[] = [
    { id: "all", name: "All Categories", slug: "all" },
    { id: "body-building", name: "Body Building", slug: "body-building" },
    {
      id: "endurance-training",
      name: "Endurance Training",
      slug: "endurance-training",
    },
    { id: "hiit", name: "HIIT", slug: "hiit" },
    {
      id: "strength-training",
      name: "Strength Training",
      slug: "strength-training",
    },
    { id: "cross-fit", name: "Cross Fit", slug: "cross-fit" },
    {
      id: "flexibility-training",
      name: "Flexibility Training",
      slug: "flexibility-training",
    },
    { id: "calisthenics", name: "Calisthenics", slug: "calisthenics" },
    { id: "yoga", name: "Yoga", slug: "yoga" },
  ];

  const containerStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
    borderBottomColor: THEME_COLORS[mode].BORDER,
  };

  const handleCategoryClick = (categoryId: string) => {
    setActiveCategory(categoryId);
    onCategoryChange?.(categoryId);
  };

  return (
    <div
      className={`w-full h-14 border-b transition-colors duration-200 ${className}`}
      style={containerStyles}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
        <div className="flex items-center h-full">
          {/* Desktop Categories - Horizontal Scroll */}
          <div className="hidden md:flex items-center space-x-8 overflow-x-auto scrollbar-hide w-full">
            {categories.map((category) => {
              const isActive = activeCategory === category.id;
              return (
                <button
                  key={category.id}
                  onClick={() => handleCategoryClick(category.id)}
                  className={`
                    relative flex-shrink-0 py-4 px-2 text-base font-medium transition-all duration-200
                    hover:opacity-80 focus:outline-none focus:ring-0 
                    ${isActive ? "font-medium" : "font-normal"}
                  `}
                  style={{
                    color: isActive
                      ? THEME_COLORS[mode].PRIMARY
                      : THEME_COLORS[mode].TEXT,
                  }}
                >
                  {category.name}
                  {isActive && (
                    <div
                      className="absolute bottom-0 left-0 right-0 h-0.5 transition-colors duration-200"
                      style={{ backgroundColor: THEME_COLORS[mode].PRIMARY }}
                    />
                  )}
                </button>
              );
            })}
          </div>

          {/* Mobile Categories - Dropdown */}
          <div className="md:hidden w-full">
            <select
              value={activeCategory}
              onChange={(e) => handleCategoryClick(e.target.value)}
              className="w-full h-10 px-3 rounded border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary"
              style={{
                backgroundColor: THEME_COLORS[mode].INPUT,
                borderColor: THEME_COLORS[mode].BORDER,
                color: THEME_COLORS[mode].TEXT,
              }}
            >
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Categories;
