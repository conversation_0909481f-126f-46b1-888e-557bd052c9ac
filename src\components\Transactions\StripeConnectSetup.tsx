import React, { useState } from "react";
import { useStripeConnectStatus } from "@/hooks/useTrainerTransactions";
import {
  CreditCard,
  Plus,
  Edit,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Trash2,
} from "lucide-react";

interface StripeConnectSetupProps {
  onRefresh?: () => void;
}

// Helper function to get country name from code
const getCountryName = (code: string): string => {
  const countries: Record<string, string> = {
    CA: "Canada",
    US: "United States",
    GB: "United Kingdom",
    AU: "Australia",
    DE: "Germany",
    FR: "France",
    IT: "Italy",
    ES: "Spain",
    NL: "Netherlands",
    SE: "Sweden",
    NO: "Norway",
    DK: "Denmark",
    FI: "Finland",
    CH: "Switzerland",
    AT: "Austria",
    BE: "Belgium",
    IE: "Ireland",
    PT: "Portugal",
    LU: "Luxembourg",
    SG: "Singapore",
    HK: "Hong Kong",
    JP: "Japan",
    NZ: "New Zealand",
  };
  return countries[code] || code;
};

const StripeConnectSetup: React.FC<StripeConnectSetupProps> = ({
  onRefresh,
}) => {
  const {
    status,
    loading,
    error,
    refetch,
    setupStripeConnect,
    deleteStripeConnect,
  } = useStripeConnectStatus();
  const [settingUp, setSettingUp] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(() => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("stripe_connect_country") || "CA";
    }
    return "CA";
  });
  const [businessType, setBusinessType] = useState(() => {
    if (typeof window !== "undefined") {
      return (
        localStorage.getItem("stripe_connect_business_type") || "individual"
      );
    }
    return "individual";
  });

  const handleRefresh = () => {
    refetch();
    onRefresh?.();
  };

  const handleSetupStripeConnect = async () => {
    setSettingUp(true);
    try {
      const currentUrl = window.location.origin + window.location.pathname;
      const setupData = await setupStripeConnect({
        country: selectedCountry,
        business_type: businessType,
        return_url: `${currentUrl}?setup=complete`,
        refresh_url: `${currentUrl}?setup=refresh`,
      });

      if (setupData?.onboarding_url) {
        // Redirect to Stripe Connect onboarding
        window.location.href = setupData.onboarding_url;
      }
    } catch {
      // Error is handled by the hook
    } finally {
      setSettingUp(false);
    }
  };

  const handleDeleteStripeConnect = async () => {
    setDeleting(true);
    try {
      await deleteStripeConnect();
      setShowDeleteConfirm(false);
      handleRefresh();
    } catch {
      // Error is handled by the hook
    } finally {
      setDeleting(false);
    }
  };

  const confirmDelete = () => {
    setShowDeleteConfirm(true);
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(false);
  };

  const handleCountryChange = (country: string) => {
    setSelectedCountry(country);
    if (typeof window !== "undefined") {
      localStorage.setItem("stripe_connect_country", country);
    }
  };

  const handleBusinessTypeChange = (type: string) => {
    setBusinessType(type);
    if (typeof window !== "undefined") {
      localStorage.setItem("stripe_connect_business_type", type);
    }
  };

  if (loading) {
    return (
      <div className="rounded-lg shadow-sm border border-border bg-secondary p-6 dark:bg-neutral-800 dark:border-[#3a3a3a] animate-pulse">
        <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-40 mb-4"></div>
        <div className="h-16 bg-gray-300 dark:bg-gray-600 rounded"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg shadow-sm border border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-red-600 dark:text-red-400">
            Payment Settings
          </h3>
          <button
            onClick={handleRefresh}
            className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 font-medium"
          >
            Retry
          </button>
        </div>
        <div className="flex items-center">
          <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
          <span className="ml-2 text-sm text-red-600 dark:text-red-400">
            Failed to load payment settings
          </span>
        </div>
      </div>
    );
  }

  const renderAccountStatus = () => {
    if (!status?.has_stripe_connect) {
      return (
        <div className="space-y-4">
          <div className="flex items-center space-x-3 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            <div className="flex-1">
              <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Payment account not set up
              </p>
              <p className="text-sm text-yellow-600 dark:text-yellow-400">
                You need to set up a payment account to receive withdrawals
              </p>
            </div>
          </div>

          {/* Configuration Form */}
          <div className="space-y-4 p-4 bg-background dark:bg-neutral-900 rounded-lg border border-border dark:border-[#3a3a3a]">
            <h4 className="text-sm font-medium text-text dark:text-gray-100">
              Account Configuration
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  Country
                </label>
                <select
                  value={selectedCountry}
                  onChange={(e) => handleCountryChange(e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text"
                  disabled={settingUp}
                >
                  <option value="CA">Canada</option>
                  <option value="US">United States</option>
                  <option value="GB">United Kingdom</option>
                  <option value="AU">Australia</option>
                  <option value="DE">Germany</option>
                  <option value="FR">France</option>
                  <option value="IT">Italy</option>
                  <option value="ES">Spain</option>
                  <option value="NL">Netherlands</option>
                  <option value="SE">Sweden</option>
                  <option value="NO">Norway</option>
                  <option value="DK">Denmark</option>
                  <option value="FI">Finland</option>
                  <option value="CH">Switzerland</option>
                  <option value="AT">Austria</option>
                  <option value="BE">Belgium</option>
                  <option value="IE">Ireland</option>
                  <option value="PT">Portugal</option>
                  <option value="LU">Luxembourg</option>
                  <option value="SG">Singapore</option>
                  <option value="HK">Hong Kong</option>
                  <option value="JP">Japan</option>
                  <option value="NZ">New Zealand</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  Business Type
                </label>
                <select
                  value={businessType}
                  onChange={(e) => handleBusinessTypeChange(e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text"
                  disabled={settingUp}
                >
                  <option value="individual">Individual</option>
                  <option value="company">Company</option>
                </select>
              </div>
            </div>
          </div>

          <button
            onClick={handleSetupStripeConnect}
            disabled={settingUp}
            className="w-full flex items-center justify-center px-4 py-3 bg-primary text-white rounded-lg hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            {settingUp ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Setting up...
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                Set Up Payment Account
              </>
            )}
          </button>
        </div>
      );
    }

    if (!status.onboarded || !status.details_submitted) {
      return (
        <div className="space-y-4">
          <div className="flex items-center space-x-3 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            <div className="flex-1">
              <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Account setup incomplete
              </p>
              <p className="text-sm text-yellow-600 dark:text-yellow-400">
                Please complete your Stripe Connect onboarding to receive
                payments
              </p>
            </div>
          </div>

          {/* Configuration Form for incomplete setup */}
          <div className="space-y-4 p-4 bg-background dark:bg-neutral-900 rounded-lg border border-border dark:border-[#3a3a3a]">
            <h4 className="text-sm font-medium text-text dark:text-gray-100">
              Update Configuration
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  Country
                </label>
                <select
                  value={selectedCountry}
                  onChange={(e) => handleCountryChange(e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text"
                  disabled={settingUp}
                >
                  <option value="CA">Canada</option>
                  <option value="US">United States</option>
                  <option value="GB">United Kingdom</option>
                  <option value="AU">Australia</option>
                  <option value="DE">Germany</option>
                  <option value="FR">France</option>
                  <option value="IT">Italy</option>
                  <option value="ES">Spain</option>
                  <option value="NL">Netherlands</option>
                  <option value="SE">Sweden</option>
                  <option value="NO">Norway</option>
                  <option value="DK">Denmark</option>
                  <option value="FI">Finland</option>
                  <option value="CH">Switzerland</option>
                  <option value="AT">Austria</option>
                  <option value="BE">Belgium</option>
                  <option value="IE">Ireland</option>
                  <option value="PT">Portugal</option>
                  <option value="LU">Luxembourg</option>
                  <option value="SG">Singapore</option>
                  <option value="HK">Hong Kong</option>
                  <option value="JP">Japan</option>
                  <option value="NZ">New Zealand</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  Business Type
                </label>
                <select
                  value={businessType}
                  onChange={(e) => handleBusinessTypeChange(e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text"
                  disabled={settingUp}
                >
                  <option value="individual">Individual</option>
                  <option value="company">Company</option>
                </select>
              </div>
            </div>
          </div>

          <button
            onClick={handleSetupStripeConnect}
            disabled={settingUp}
            className="w-full flex items-center justify-center px-4 py-3 bg-primary text-white rounded-lg hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            {settingUp ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Redirecting...
              </>
            ) : (
              <>
                <ExternalLink className="w-4 h-4 mr-2" />
                Complete Setup
              </>
            )}
          </button>
        </div>
      );
    }

    if (!status.payouts_enabled) {
      return (
        <div className="space-y-4">
          <div className="flex items-center space-x-3 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            <div className="flex-1">
              <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Payouts not enabled
              </p>
              <p className="text-sm text-yellow-600 dark:text-yellow-400">
                Your account is being reviewed. Payouts will be enabled once
                approved.
              </p>
            </div>
          </div>
        </div>
      );
    }

    // Account is fully set up and ready
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-3 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
          <div className="flex-1">
            <p className="text-sm font-medium text-green-800 dark:text-green-200">
              Payment account ready
            </p>
            <p className="text-sm text-green-600 dark:text-green-400">
              Your account is set up and ready to receive payments
            </p>
          </div>
        </div>

        <div className="rounded-lg shadow-sm border border-border bg-secondary p-4 flex items-center justify-between dark:bg-neutral-800 dark:border-[#3a3a3a]">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded flex items-center justify-center">
              <CreditCard className="w-4 h-4 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <span className="text-base font-medium text-text dark:text-gray-100">
                Stripe Connect Account
              </span>
              <p className="text-sm text-text-secondary">
                Account ID: {status.stripe_connect_account_id?.slice(-8)} •{" "}
                {getCountryName(selectedCountry)}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={handleSetupStripeConnect}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors duration-200"
              title="Edit account"
            >
              <Edit className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            </button>
            <button
              onClick={confirmDelete}
              className="p-2 hover:bg-red-100 dark:hover:bg-red-900/30 rounded transition-colors duration-200"
              title="Delete account"
            >
              <Trash2 className="w-4 h-4 text-red-500 dark:text-red-400" />
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-text dark:text-gray-100">
          Payment Settings
        </h3>
        <button
          onClick={handleRefresh}
          className="text-sm text-text-secondary hover:text-text font-medium"
        >
          Refresh
        </button>
      </div>
      {renderAccountStatus()}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-background dark:bg-neutral-800 rounded-lg p-6 max-w-md w-full mx-4 border border-border dark:border-[#3a3a3a]">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mr-3">
                <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
              </div>
              <h3 className="text-lg font-medium text-text dark:text-gray-100">
                Delete Stripe Connect Account
              </h3>
            </div>
            <p className="text-text-secondary mb-6">
              Are you sure you want to delete your Stripe Connect account? This
              action cannot be undone and you will need to set up a new account
              to receive payments.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelDelete}
                disabled={deleting}
                className="px-4 py-2 text-sm font-medium text-text-secondary bg-background border border-border rounded-md hover:bg-background-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteStripeConnect}
                disabled={deleting}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent disabled:opacity-50"
              >
                {deleting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                    Deleting...
                  </>
                ) : (
                  "Delete Account"
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StripeConnectSetup;
