import { useMemo } from "react";
import { useQueryClient, useQuery } from "@tanstack/react-query";
import { useCustomModelQuery } from "@/query/shared/customModel";
import {
  DashboardStats,
  Notification,
  Activity,
  StatsResponse,
  NotificationsResponse,
  ActivitiesResponse,
} from "@/interfaces/model.interface";
import { useProfile } from "@/hooks/useProfile";

export interface UseTrainerDashboardProps {
  enabled?: boolean;
  refreshInterval?: number; // in milliseconds
}

export interface UseTrainerDashboardReturn {
  stats: DashboardStats | null;
  notifications: Notification[];
  activities: Activity[];
  isLoading: boolean;
  isStatsLoading: boolean;
  isNotificationsLoading: boolean;
  isActivitiesLoading: boolean;
  error: string | null;
  refetch: () => void;
  refetchStats: () => void;
  markNotificationAsRead: (notificationId: number) => Promise<void>;
  markAllNotificationsAsRead: () => Promise<void>;
}

export const useTrainerDashboard = ({
  enabled = true,
  refreshInterval,
}: UseTrainerDashboardProps = {}): UseTrainerDashboardReturn => {
  const { profile } = useProfile();
  const queryClient = useQueryClient();
  const customQuery = useCustomModelQuery();

  // Fetch dashboard stats
  const {
    data: statsResponse,
    isLoading: isStatsLoading,
    error: statsError,
    refetch: refetchStats,
  } = useQuery({
    queryKey: ["trainer-dashboard-stats"],
    queryFn: async () => {
      const response = await customQuery.mutateAsync({
        endpoint: "/v2/api/kanglink/custom/trainer/dashboard/stats",
        method: "GET",
      });
      return response as StatsResponse;
    },
    enabled: enabled && !!profile?.id,
    refetchInterval: refreshInterval,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes (updated from cacheTime)
  });

  // Fetch notifications
  const {
    data: notificationsResponse,
    isLoading: isNotificationsLoading,
    error: notificationsError,
    refetch: refetchNotifications,
  } = useQuery({
    queryKey: ["trainer-dashboard-notifications"],
    queryFn: async () => {
      const response = await customQuery.mutateAsync({
        endpoint: "/v2/api/kanglink/custom/trainer/dashboard/notifications",
        method: "GET",
      });
      return response as NotificationsResponse;
    },
    enabled: enabled && !!profile?.id,
    refetchInterval: refreshInterval,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 3 * 60 * 1000, // 3 minutes (updated from cacheTime)
  });

  // Fetch activities
  const {
    data: activitiesResponse,
    isLoading: isActivitiesLoading,
    error: activitiesError,
    refetch: refetchActivities,
  } = useQuery({
    queryKey: ["trainer-dashboard-activities"],
    queryFn: async () => {
      const response = await customQuery.mutateAsync({
        endpoint: "/v2/api/kanglink/custom/trainer/activities",
        method: "GET",
        params: {
          page: 1,
          limit: 10,
          visibility: "trainer_only",
        },
      });
      return response as ActivitiesResponse;
    },
    enabled: enabled && !!profile?.id,
    refetchInterval: refreshInterval,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 3 * 60 * 1000, // 3 minutes (updated from cacheTime)
  });

  // Extract data from responses
  const stats = useMemo((): DashboardStats | null => {
    if (!statsResponse || (statsResponse as any)?.error) return null;
    return (statsResponse as any)?.data || null;
  }, [statsResponse]);

  const notifications = useMemo((): Notification[] => {
    if (!notificationsResponse || (notificationsResponse as any)?.error)
      return [];
    
    // Handle the backend response structure
    const response = notificationsResponse as any;
    if (response?.data?.notifications) {
      return response.data.notifications;
    }
    
    // Fallback for different response structures
    if (Array.isArray(response?.data)) {
      return response.data;
    }
    
    return [];
  }, [notificationsResponse]);

  const activities = useMemo((): Activity[] => {
    if (!activitiesResponse || (activitiesResponse as any)?.error) return [];
    
    // Handle the backend response structure
    const response = activitiesResponse as any;
    if (response?.data?.activities) {
      return response.data.activities;
    }
    
    // Fallback for different response structures
    if (Array.isArray(response?.data)) {
      return response.data;
    }
    
    return [];
  }, [activitiesResponse]);

  // Combined error handling
  const error = useMemo(() => {
    if (statsError) return String(statsError);
    if (notificationsError) return String(notificationsError);
    if (activitiesError) return String(activitiesError);
    return null;
  }, [statsError, notificationsError, activitiesError]);

  // Mark notification as read
  const markNotificationAsRead = async (notificationId: number) => {
    try {
      await customQuery.mutateAsync({
        endpoint: `/v2/api/kanglink/custom/trainer/dashboard/notifications/${notificationId}/read`,
        method: "PUT",
      });
      // Refetch notifications to update the UI
      refetchNotifications();
    } catch (error) {
      console.error("Error marking notification as read:", error);
      throw error;
    }
  };

  // Mark all notifications as read
  const markAllNotificationsAsRead = async () => {
    try {
      await customQuery.mutateAsync({
        endpoint:
          "/v2/api/kanglink/custom/trainer/dashboard/notifications/read-all",
        method: "PUT",
      });
      // Refetch notifications to update the UI
      refetchNotifications();
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      throw error;
    }
  };

  // Refetch all data
  const refetch = () => {
    queryClient.invalidateQueries({
      queryKey: ["trainer-dashboard-stats"],
    });
    queryClient.invalidateQueries({
      queryKey: ["trainer-dashboard-notifications"],
    });
    queryClient.invalidateQueries({
      queryKey: ["trainer-dashboard-activities"],
    });
    refetchStats();
    refetchNotifications();
    refetchActivities();
  };

  return {
    stats,
    notifications,
    activities,
    isLoading: isStatsLoading || isNotificationsLoading || isActivitiesLoading,
    isStatsLoading,
    isNotificationsLoading,
    isActivitiesLoading,
    error,
    refetch,
    refetchStats,
    markNotificationAsRead,
    markAllNotificationsAsRead,
  };
};

export default useTrainerDashboard;
