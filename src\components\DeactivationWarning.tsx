import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faExclamationTriangle } from "@fortawesome/free-solid-svg-icons";

interface DeactivationWarningProps {
  message: string;
  className?: string;
}

export const DeactivationWarning: React.FC<DeactivationWarningProps> = ({ 
  message, 
  className = "" 
}) => {
  return (
    <div className={`fixed z-[9999] max-w-7xl mx-auto bottom-0 inset-x-0 w-full px-4`}>
    <div className={` w-full bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4 ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <FontAwesomeIcon
            icon={faExclamationTriangle}
            className="h-5 w-5 text-yellow-400"
          />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-yellow-800">
            Account Deactivated
          </h3>
          <div className="mt-2 text-sm text-yellow-700">
            <p>{message}</p>
          </div>
        </div>
      </div>
    </div>
    </div>
  );
}; 