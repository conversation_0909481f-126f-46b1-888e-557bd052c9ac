# Frontend API Guide: Public Program Endpoints

This guide provides everything frontend developers need to integrate with the public program endpoints.

## 🚀 Quick Start

### Base URL

```
http://localhost:5172 (development)
https://your-domain.com (production)
```

### Authentication

❌ **No authentication required** - These are public endpoints

## 📋 Endpoints Overview

| Endpoint                                             | Method | Purpose             |
| ---------------------------------------------------- | ------ | ------------------- |
| `/v2/api/kanglink/custom/public/program/:id`         | GET    | Get program details |
| `/v2/api/kanglink/custom/public/program/:id/reviews` | GET    | Get program reviews |

---

## 🎯 1. Get Program Details

### Endpoint

```
GET /v2/api/kanglink/custom/public/program/:program_id
```

### Purpose

Retrieve complete program information including trainer details, pricing, and splits.

### Parameters

- `program_id` (path): The program ID (number)

### Response Format

```typescript
interface ProgramDetailsResponse {
  error: false;
  message: string;
  data: {
    id: number;
    user_id: number;
    program_name: string;
    type_of_program: string;
    program_description: string;
    target_levels: string[];
    currency: string;
    days_for_preview: number;
    image: string | null;
    track_progress: boolean;
    allow_comments: boolean;
    allow_private_messages: boolean;
    duration: string | null; // e.g., "4 Weeks", "1 Week" - max weeks across splits
    rating: number; // 0-5
    review_count: number;
    price: number | null; // Minimum price from splits
    splits: Split[];
    trainer: Trainer;
    created_at: string;
    updated_at: string;
  };
}

interface Split {
  id: number;
  program_id: number;
  equipment_required: string | null;
  title: string;
  full_price: number | null;
  subscription: number | null;
  created_at: string;
  updated_at: string;
}

interface Trainer {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  photo: string | null;
}
```

### Example Usage

```javascript
// Fetch program details
async function fetchProgramDetails(programId) {
  try {
    const response = await fetch(
      `/v2/api/kanglink/custom/public/program/${programId}`
    );
    const data = await response.json();

    if (data.error) {
      throw new Error(data.message);
    }

    return data.data;
  } catch (error) {
    console.error("Failed to fetch program:", error);
    throw error;
  }
}

// Usage
const program = await fetchProgramDetails(123);
console.log(`Program: ${program.program_name} by ${program.trainer.full_name}`);
```

### React Component Example

```jsx
import React, { useState, useEffect } from "react";

function ProgramCard({ programId }) {
  const [program, setProgram] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function loadProgram() {
      try {
        const response = await fetch(
          `/v2/api/kanglink/custom/public/program/${programId}`
        );
        const data = await response.json();

        if (data.error) {
          setError(data.message);
        } else {
          setProgram(data.data);
        }
      } catch (err) {
        setError("Failed to load program");
      } finally {
        setLoading(false);
      }
    }

    loadProgram();
  }, [programId]);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!program) return <div>Program not found</div>;

  return (
    <div className="program-card">
      <img src={program.image} alt={program.program_name} />
      <h3>{program.program_name}</h3>
      <p>{program.program_description}</p>
      <div className="trainer">
        <img src={program.trainer.photo} alt={program.trainer.full_name} />
        <span>{program.trainer.full_name}</span>
      </div>
      <div className="rating">
        ⭐ {program.rating.toFixed(1)} ({program.review_count} reviews)
      </div>
      <div className="duration">
        {program.duration && `Duration: ${program.duration}`}
      </div>
      <div className="price">
        {program.price ? `From ${program.currency} ${program.price}` : "Free"}
      </div>
    </div>
  );
}
```

---

## 📝 2. Get Program Reviews

### Endpoint

```
GET /v2/api/kanglink/custom/public/program/:program_id/reviews
```

### Purpose

Retrieve paginated public reviews for a program.

### Parameters

- `program_id` (path): The program ID (number)
- `page` (query, optional): Page number (default: 1)
- `limit` (query, optional): Items per page (default: 20, max: 50)
- `sort_by` (query, optional): Sort field - `created_at` or `rating` (default: `created_at`)
- `sort_order` (query, optional): Sort order - `asc` or `desc` (default: `desc`)

### Response Format

```typescript
interface ReviewsResponse {
  error: false;
  message: string;
  data: Review[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    num_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

interface Review {
  id: number;
  user_id: number;
  program_id: number;
  content: string;
  rating: number; // 1-5
  attachments: any[];
  is_edited: boolean;
  user: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
    full_name: string;
    photo: string | null;
  };
  created_at: string;
  updated_at: string;
}
```

### Example Usage

```javascript
// Fetch program reviews
async function fetchProgramReviews(programId, options = {}) {
  const params = new URLSearchParams({
    page: options.page || 1,
    limit: options.limit || 20,
    sort_by: options.sortBy || "created_at",
    sort_order: options.sortOrder || "desc",
  });

  try {
    const response = await fetch(
      `/v2/api/kanglink/custom/public/program/${programId}/reviews?${params}`
    );
    const data = await response.json();

    if (data.error) {
      throw new Error(data.message);
    }

    return data;
  } catch (error) {
    console.error("Failed to fetch reviews:", error);
    throw error;
  }
}

// Usage
const reviewsData = await fetchProgramReviews(123, {
  page: 1,
  limit: 10,
  sortBy: "rating",
  sortOrder: "desc",
});
```

### React Component Example

```jsx
import React, { useState, useEffect } from "react";

function ProgramReviews({ programId }) {
  const [reviews, setReviews] = useState([]);
  const [pagination, setPagination] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    async function loadReviews() {
      setLoading(true);
      try {
        const response = await fetch(
          `/v2/api/kanglink/custom/public/program/${programId}/reviews?page=${currentPage}&limit=10`
        );
        const data = await response.json();

        if (!data.error) {
          setReviews(data.data);
          setPagination(data.pagination);
        }
      } catch (error) {
        console.error("Failed to load reviews:", error);
      } finally {
        setLoading(false);
      }
    }

    loadReviews();
  }, [programId, currentPage]);

  const renderStars = (rating) => {
    return "⭐".repeat(rating) + "☆".repeat(5 - rating);
  };

  if (loading) return <div>Loading reviews...</div>;

  return (
    <div className="program-reviews">
      <h3>Reviews ({pagination?.total || 0})</h3>

      {reviews.map((review) => (
        <div key={review.id} className="review">
          <div className="review-header">
            <img src={review.user.photo} alt={review.user.full_name} />
            <div>
              <strong>{review.user.full_name}</strong>
              <div className="rating">{renderStars(review.rating)}</div>
            </div>
            <span className="date">
              {new Date(review.created_at).toLocaleDateString()}
            </span>
          </div>
          <p>{review.content}</p>
          {review.is_edited && <small>Edited</small>}
        </div>
      ))}

      {pagination && pagination.num_pages > 1 && (
        <div className="pagination">
          <button
            disabled={!pagination.has_prev}
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            Previous
          </button>
          <span>
            Page {pagination.page} of {pagination.num_pages}
          </span>
          <button
            disabled={!pagination.has_next}
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
}
```

## 🚨 Error Handling

All endpoints return consistent error responses:

```typescript
interface ErrorResponse {
  error: true;
  message: string;
}
```

### Common Error Codes

- `400`: Invalid parameters (e.g., invalid program ID, invalid sort parameters)
- `404`: Program not found or not published
- `500`: Internal server error

### Error Handling Example

```javascript
async function handleApiCall(apiFunction) {
  try {
    return await apiFunction();
  } catch (error) {
    if (error.status === 404) {
      // Program not found
      showNotification("Program not found", "error");
    } else if (error.status === 400) {
      // Invalid parameters
      showNotification("Invalid request", "error");
    } else {
      // Server error
      showNotification("Something went wrong", "error");
    }
    throw error;
  }
}
```

## 🎨 UI Integration Tips

### Program Card Display

- Use `program.price` for the main price display
- Show `program.rating` with stars (⭐)
- Display `program.review_count` next to rating
- Use `program.trainer.photo` and `program.trainer.full_name` for trainer info
- Show `program.duration` to indicate program length (represents max weeks across splits since athletes subscribe to individual splits)

### Reviews Section

- Implement pagination for better performance
- Show star ratings visually
- Include user photos and names
- Add sorting options (newest first, highest rated first)

### Loading States

- Show skeleton loaders while fetching data
- Handle empty states gracefully
- Provide retry mechanisms for failed requests

This API is designed to be frontend-friendly with consistent response formats and comprehensive data for building rich user interfaces.

## 🧪 Testing

The endpoints are thoroughly tested with the custom test suite:

```bash
# Run the public program API tests specifically
node mtpbk/test_runner.js --pattern public_program_api

# Run all tests
node mtpbk/test_runner.js
```

**Test File:** `mtpbk/custom/ksl_be/tests/routes/public_program_api.test.js`
