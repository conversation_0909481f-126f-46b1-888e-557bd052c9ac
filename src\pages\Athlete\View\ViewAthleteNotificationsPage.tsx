import { useState, useEffect } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import MkdSDK from "@/utils/MkdSDK";
import { NotificationService, Notification } from "@/utils/NotificationService";
import { 
  BellIcon, 
  CheckIcon, 
  XMarkIcon,
  FunnelIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from "@heroicons/react/24/outline";

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: number) => void;
  mode: string;
}

const NotificationItem = ({ notification, onMarkAsRead, mode: _mode }: NotificationItemProps) => {
  const { state } = useTheme();
  const theme = state?.theme;

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'enrollment':
        return '🎯';
      case 'payment':
        return '💳';
      case 'progress':
        return '📈';
      case 'system':
        return '🔔';
      case 'trainer':
        return '👨‍🏫';
      default:
        return '📢';
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'enrollment':
        return 'bg-green-100 text-green-800';
      case 'payment':
        return 'bg-blue-100 text-blue-800';
      case 'progress':
        return 'bg-purple-100 text-purple-800';
      case 'system':
        return 'bg-yellow-100 text-yellow-800';
      case 'trainer':
        return 'bg-indigo-100 text-indigo-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div
      className={`p-4 border rounded-lg transition-all duration-200 hover:shadow-md ${
        !notification.is_read ? 'border-l-4 border-l-primary' : ''
      }`}
      style={{
        backgroundColor: THEME_COLORS[theme].SECONDARY,
        borderColor: THEME_COLORS[theme].BORDER,
      }}
    >
      <div className="flex items-start space-x-3">
        {/* Notification Icon */}
        <div className="flex-shrink-0">
          <div className="w-10 h-10 rounded-full flex items-center justify-center text-lg">
            {getNotificationIcon(notification.notification_type)}
          </div>
        </div>

        {/* Notification Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <h3
                className={`text-sm font-medium ${
                  !notification.is_read ? 'font-semibold' : ''
                }`}
                style={{ color: THEME_COLORS[theme].TEXT }}
              >
                {notification.title}
              </h3>
              <span
                className={`px-2 py-1 text-xs rounded-full ${getNotificationColor(notification.notification_type)}`}
              >
                {notification.notification_type}
              </span>
            </div>
            
            {/* Mark as Read Button */}
            {!notification.is_read && (
              <button
                onClick={() => onMarkAsRead(notification.id)}
                className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                style={{ color: THEME_COLORS[theme].TEXT_SECONDARY }}
              >
                <CheckIcon className="w-4 h-4" />
              </button>
            )}
          </div>
          
          <p
            className="text-sm mt-1"
            style={{ color: THEME_COLORS[theme].TEXT_SECONDARY }}
          >
            {notification.message}
          </p>
          
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center space-x-4 text-xs">
              {notification.sender_name && (
                <span style={{ color: THEME_COLORS[theme].TEXT_SECONDARY }}>
                  From: {notification.sender_name}
                </span>
              )}
              <span style={{ color: THEME_COLORS[theme].TEXT_SECONDARY }}>
                {NotificationService.formatTimeAgo(notification.created_at)}
              </span>
            </div>
            
            {notification.is_read && (
              <span
                className="text-xs px-2 py-1 rounded-full"
                style={{ 
                  backgroundColor: THEME_COLORS[theme].BACKGROUND_SECONDARY,
                  color: THEME_COLORS[theme].TEXT_SECONDARY 
                }}
              >
                Read
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const ViewAthleteNotificationsPage = () => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalNotifications, setTotalNotifications] = useState(0);
  const [unreadCount, setUnreadCount] = useState(0);
  const [filter, setFilter] = useState<'all' | 'unread'>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);

  const notificationService = new NotificationService(new MkdSDK());

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await notificationService.getNotifications({
        page: currentPage,
        limit: 20,
        unread_only: filter === 'unread',
        category: categoryFilter !== 'all' ? categoryFilter : undefined,
      });

      setNotifications(response.notifications);
      setTotalPages(response.pagination.total_pages);
      setTotalNotifications(response.pagination.total);
      setUnreadCount(response.unread_count || 0);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch notifications');
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId: number) => {
    try {
      await notificationService.markAsRead(notificationId);
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, is_read: true, read_at: new Date().toISOString() }
            : notification
        )
      );
      
      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (err) {
      console.error('Failed to mark notification as read:', err);
    }
  };

  const markAllAsRead = async () => {
    try {
      await notificationService.markAllAsRead();
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => ({
          ...notification,
          is_read: true,
          read_at: new Date().toISOString()
        }))
      );
      
      setUnreadCount(0);
    } catch (err) {
      console.error('Failed to mark all notifications as read:', err);
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, [currentPage, filter, categoryFilter]);

  const handleFilterChange = (newFilter: 'all' | 'unread') => {
    setFilter(newFilter);
    setCurrentPage(1);
  };

  const handleCategoryChange = (category: string) => {
    setCategoryFilter(category);
    setCurrentPage(1);
  };

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'enrollment', label: 'Enrollment' },
    { value: 'payment', label: 'Payment' },
    { value: 'progress', label: 'Progress' },
    { value: 'system', label: 'System' },
    { value: 'trainer', label: 'Trainer' },
  ];

  if (loading && notifications.length === 0) {
    return (
      <div
        className="min-h-screen w-full p-4 sm:p-6 lg:p-8 transition-colors duration-200 flex items-center justify-center"
        style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY }}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" 
               style={{ borderColor: THEME_COLORS[mode].PRIMARY }}></div>
          <p style={{ color: THEME_COLORS[mode].TEXT }}>Loading notifications...</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen w-full p-4 sm:p-6 lg:p-8 transition-colors duration-200"
      style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY }}
    >
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <BellIcon 
              className="w-8 h-8" 
              style={{ color: THEME_COLORS[mode].PRIMARY }}
            />
            <div>
              <h1
                className="text-2xl font-bold"
                style={{ color: THEME_COLORS[mode].TEXT }}
              >
                Notifications
              </h1>
              <p
                className="text-sm"
                style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
              >
                {totalNotifications} total • {unreadCount} unread
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="p-2 rounded-lg border transition-colors"
              style={{ 
                backgroundColor: THEME_COLORS[mode].SECONDARY,
                borderColor: THEME_COLORS[mode].BORDER,
                color: THEME_COLORS[mode].TEXT
              }}
            >
              <FunnelIcon className="w-5 h-5" />
            </button>

            {/* Mark All as Read */}
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                style={{ 
                  backgroundColor: THEME_COLORS[mode].PRIMARY,
                  color: 'white'
                }}
              >
                Mark all as read
              </button>
            )}
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div
            className="p-4 rounded-lg border"
            style={{ 
              backgroundColor: THEME_COLORS[mode].SECONDARY,
              borderColor: THEME_COLORS[mode].BORDER
            }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Status Filter */}
              <div>
                <label
                  className="block text-sm font-medium mb-2"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  Status
                </label>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleFilterChange('all')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      filter === 'all' 
                        ? 'text-white' 
                        : ''
                    }`}
                    style={{
                      backgroundColor: filter === 'all' 
                        ? THEME_COLORS[mode].PRIMARY 
                        : THEME_COLORS[mode].BACKGROUND_SECONDARY,
                      color: filter === 'all' 
                        ? 'white' 
                        : THEME_COLORS[mode].TEXT
                    }}
                  >
                    All
                  </button>
                  <button
                    onClick={() => handleFilterChange('unread')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      filter === 'unread' 
                        ? 'text-white' 
                        : ''
                    }`}
                    style={{
                      backgroundColor: filter === 'unread' 
                        ? THEME_COLORS[mode].PRIMARY 
                        : THEME_COLORS[mode].BACKGROUND_SECONDARY,
                      color: filter === 'unread' 
                        ? 'white' 
                        : THEME_COLORS[mode].TEXT
                    }}
                  >
                    Unread ({unreadCount})
                  </button>
                </div>
              </div>

              {/* Category Filter */}
              <div>
                <label
                  className="block text-sm font-medium mb-2"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  Category
                </label>
                <select
                  value={categoryFilter}
                  onChange={(e) => handleCategoryChange(e.target.value)}
                  className="w-full px-3 py-2 rounded-lg border text-sm transition-colors"
                  style={{
                    backgroundColor: THEME_COLORS[mode].SECONDARY,
                    borderColor: THEME_COLORS[mode].BORDER,
                    color: THEME_COLORS[mode].TEXT
                  }}
                >
                  {categories.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div
            className="p-4 rounded-lg border"
            style={{ 
              backgroundColor: '#fef2f2',
              borderColor: '#fecaca',
              color: '#dc2626'
            }}
          >
            <div className="flex items-center space-x-2">
              <XMarkIcon className="w-5 h-5" />
              <span>{error}</span>
            </div>
          </div>
        )}

        {/* Notifications List */}
        <div className="space-y-3">
          {notifications.length === 0 ? (
            <div
              className="text-center py-12"
              style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
            >
              <BellIcon className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No notifications</h3>
              <p className="text-sm">
                {filter === 'unread' 
                  ? "You're all caught up! No unread notifications."
                  : "You don't have any notifications yet."
                }
              </p>
            </div>
          ) : (
            notifications.map(notification => (
              <NotificationItem
                key={notification.id}
                notification={notification}
                onMarkAsRead={markAsRead}
                mode={mode}
              />
            ))
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm" style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}>
              Page {currentPage} of {totalPages}
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                className="p-2 rounded-lg border transition-colors disabled:opacity-50"
                style={{ 
                  backgroundColor: THEME_COLORS[mode].SECONDARY,
                  borderColor: THEME_COLORS[mode].BORDER,
                  color: THEME_COLORS[mode].TEXT
                }}
              >
                <ChevronLeftIcon className="w-5 h-5" />
              </button>
              
              <button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                className="p-2 rounded-lg border transition-colors disabled:opacity-50"
                style={{ 
                  backgroundColor: THEME_COLORS[mode].SECONDARY,
                  borderColor: THEME_COLORS[mode].BORDER,
                  color: THEME_COLORS[mode].TEXT
                }}
              >
                <ChevronRightIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ViewAthleteNotificationsPage; 