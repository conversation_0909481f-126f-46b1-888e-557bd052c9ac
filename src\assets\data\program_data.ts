import { generateUUID } from "@/utils";

export const programStepOneData = {
  program_name: "Fitness Program",
  type_of_program: "Body building",
  program_description: "Fitness program for beginners",
  payment_plan: ["one_time"],
  track_progress: true,
  allow_comments: true,
  allow_private_messages: true,
  target_levels: ["beginner", "intermediate", "expert"],
  split_program: 3,
  splits: [
    {
      title: "Split 1",
      full_price: 100,
      subscription: 0,
      split_id: generateUUID(),
    },
    {
      title: "Split 2",
      full_price: 200,
      subscription: 0,
      split_id: generateUUID(),
    },
    {
      title: "Split 3",
      full_price: 300,
      subscription: 0,
      split_id: generateUUID(),
    },
  ],
  currency: "USD",
  days_for_preview: 7,
};

// Filter options
export const dateFilterOptions = [
  { value: "Today", label: "Today" },
  { value: "Yesterday", label: "Yesterday" },
  { value: "Last 7 days", label: "Last 7 days" },
  { value: "Last 30 days", label: "Last 30 days" },
  { value: "All time", label: "All time" },
];

export const statusFilterOptions = [
  { value: "All", label: "All" },
  { value: "Pending Approval", label: "Pending Approval" },
  { value: "Live", label: "Live" },
  // { value: "Completed", label: "Completed" },
  { value: "Rejected", label: "Rejected" },
  // { value: "Draft", label: "Draft" },
  // { value: "Archived", label: "Archived" },
  { value: "Deleted", label: "Deleted" },
];

// Athlete Types
export interface Athlete {
  id: number;
  name: string;
  dateJoined: string;
  status: "Active" | "Inactive";
  enrollments: number;
}

// Dummy athlete data for development
export const dummyAthletes: Athlete[] = [
  {
    id: 1,
    name: "Joy",
    dateJoined: "2024-06-02",
    status: "Active",
    enrollments: 3,
  },
  {
    id: 2,
    name: "Josh",
    dateJoined: "2024-06-02",
    status: "Inactive",
    enrollments: 1,
  },
  {
    id: 3,
    name: "Frank",
    dateJoined: "2024-06-02",
    status: "Active",
    enrollments: 2,
  },
  {
    id: 4,
    name: "Josh",
    dateJoined: "2024-06-02",
    status: "Active",
    enrollments: 4,
  },
  {
    id: 5,
    name: "Sarah",
    dateJoined: "2024-06-01",
    status: "Active",
    enrollments: 2,
  },
  {
    id: 6,
    name: "Emma",
    dateJoined: "2024-05-30",
    status: "Inactive",
    enrollments: 1,
  },
  {
    id: 7,
    name: "Mike",
    dateJoined: "2024-05-29",
    status: "Active",
    enrollments: 5,
  },
  {
    id: 8,
    name: "Alex",
    dateJoined: "2024-05-28",
    status: "Active",
    enrollments: 3,
  },
  {
    id: 9,
    name: "Lisa",
    dateJoined: "2024-05-27",
    status: "Inactive",
    enrollments: 2,
  },
  {
    id: 10,
    name: "Maria",
    dateJoined: "2024-05-26",
    status: "Active",
    enrollments: 1,
  },
];

// Athlete filter options
export const athleteStatusFilterOptions = [
  { value: "All", label: "All" },
  { value: "Active", label: "Active" },
  { value: "Inactive", label: "Inactive" },
];

// Trainer Types
export interface Trainer {
  id: number;
  name: string;
  dateAdded: string;
  status: "Active" | "Inactive";
  programs: number;
}

// Dummy trainer data for development
export const dummyTrainers: Trainer[] = [
  {
    id: 1,
    name: "Joy",
    dateAdded: "2024-06-02",
    status: "Active",
    programs: 3,
  },
  {
    id: 2,
    name: "Josh",
    dateAdded: "2024-06-02",
    status: "Inactive",
    programs: 1,
  },
  {
    id: 3,
    name: "Frank",
    dateAdded: "2024-06-02",
    status: "Active",
    programs: 2,
  },
  {
    id: 4,
    name: "Josh",
    dateAdded: "2024-06-02",
    status: "Active",
    programs: 4,
  },
  {
    id: 5,
    name: "Sarah",
    dateAdded: "2024-06-01",
    status: "Active",
    programs: 2,
  },
  {
    id: 6,
    name: "Emma",
    dateAdded: "2024-05-30",
    status: "Inactive",
    programs: 1,
  },
  {
    id: 7,
    name: "Mike",
    dateAdded: "2024-05-29",
    status: "Active",
    programs: 5,
  },
  {
    id: 8,
    name: "Alex",
    dateAdded: "2024-05-28",
    status: "Active",
    programs: 3,
  },
  {
    id: 9,
    name: "Lisa",
    dateAdded: "2024-05-27",
    status: "Inactive",
    programs: 2,
  },
  {
    id: 10,
    name: "Maria",
    dateAdded: "2024-05-26",
    status: "Active",
    programs: 1,
  },
];

// Trainer filter options
export const trainerStatusFilterOptions = [
  { value: "All", label: "All" },
  { value: "Active", label: "Active" },
  { value: "Inactive", label: "Inactive" },
];

// Date filter options for trainers (using "This Week" as default to match design)
export const trainerDateFilterOptions = [
  { value: "This Week", label: "This Week" },
  { value: "Today", label: "Today" },
  { value: "Yesterday", label: "Yesterday" },
  { value: "Last 7 days", label: "Last 7 days" },
  { value: "Last 30 days", label: "Last 30 days" },
  { value: "All time", label: "All time" },
];

// Workout Program Data Structure
export interface WorkoutExercise {
  id: string;
  name: string;
  sets: string;
  reps: string;
  rest: string;
  weight: string;
  duration?: string;
  progress?: number;
  description: string;
  muscleGroups: string[];
  thumbnailUrl?: string;
  videoUrl?: string;
  isCompleted?: boolean;
  isCompact?: boolean;
}

export interface WorkoutSession {
  id: string;
  name: string;
  duration: string;
  exercises: WorkoutExercise[];
  isCollapsed?: boolean;
}

export interface WorkoutDay {
  id: string;
  dayNumber: number;
  name: string;
  sessions: WorkoutSession[];
  isCollapsed?: boolean;
}

export interface WorkoutWeek {
  id: string;
  weekNumber: number;
  name: string;
  days: WorkoutDay[];
  equipmentRequired: string;
  isCollapsed?: boolean;
}

export interface WorkoutProgram {
  id: string;
  name: string;
  description: string;
  weeks: WorkoutWeek[];
}

export const workoutProgramData: WorkoutProgram = {
  id: "prog-1",
  name: "Upper Body Strength Program",
  description: "A comprehensive 4-week upper body strength training program",
  weeks: [
    {
      id: "week-1",
      weekNumber: 1,
      name: "Foundation Week",
      isCollapsed: false,
      equipmentRequired:
        "Dumbbells (adjustable 10-50lbs), resistance bands, pull-up bar, exercise mat. Optional: kettlebells for advanced variations. Ensure proper form and safety throughout your workout session.",
      days: [
        {
          id: "day-1",
          dayNumber: 1,
          name: "Upper Body Strength",
          isCollapsed: false,
          sessions: [
            {
              id: "session-1",
              name: "Compound Movements",
              duration: "45 mins",
              isCollapsed: false,
              exercises: [
                {
                  id: "ex-1",
                  name: "Push-ups",
                  sets: "4",
                  reps: "12-15",
                  rest: "60s",
                  weight: "Body",
                  duration: "2:45",
                  progress: 35,
                  description:
                    "Start in plank position with hands slightly wider than shoulders. Lower body until chest nearly touches floor, then push back up. Keep core tight and body in straight line throughout movement.",
                  muscleGroups: ["Chest", "Triceps", "Shoulders"],
                  thumbnailUrl: "https://placehold.co/1134x250",
                  isCompleted: false,
                },
                {
                  id: "ex-2",
                  name: "Dumbbell Rows",
                  sets: "4",
                  reps: "10-12",
                  rest: "90s",
                  weight: "25lbs",
                  duration: "2:45",
                  progress: 0,
                  description:
                    "Bend at hips and knees, keeping back straight. Pull dumbbells to sides of torso, squeezing shoulder blades together. Lower with control.",
                  muscleGroups: ["Back", "Biceps", "Rear Delts"],
                  thumbnailUrl: "https://placehold.co/1134x250",
                  isCompleted: false,
                },
                {
                  id: "ex-3",
                  name: "Bench Press",
                  sets: "3",
                  reps: "10-12",
                  rest: "60s",
                  weight: "50lbs",
                  duration: "2:45",
                  progress: 0,
                  description:
                    "Lie on bench with feet flat on ground. Lower bar to chest with control, then press back up. Keep core tight throughout movement.",
                  muscleGroups: ["Chest", "Triceps", "Shoulders"],
                  thumbnailUrl: "https://placehold.co/1134x250",
                  isCompleted: false,
                },
                {
                  id: "ex-4",
                  name: "Deadlift",
                  sets: "3",
                  reps: "8-10",
                  rest: "120s",
                  weight: "60lbs",
                  description:
                    "Stand with feet hip-width apart, grip bar with hands just outside legs. Keep back straight, drive through heels to lift.",
                  muscleGroups: ["Back", "Glutes", "Hamstrings"],
                  thumbnailUrl: "https://placehold.co/128x80",
                  isCompleted: false,
                  isCompact: true,
                },
              ],
            },
            {
              id: "session-2",
              name: "Isolation Work",
              duration: "30 mins",
              isCollapsed: true,
              exercises: [
                {
                  id: "ex-5",
                  name: "Bicep Curls",
                  sets: "3",
                  reps: "12-15",
                  rest: "45s",
                  weight: "15lbs",
                  description:
                    "Stand with dumbbells at sides. Curl weights up while keeping elbows stationary.",
                  muscleGroups: ["Biceps"],
                  thumbnailUrl: "https://placehold.co/128x80",
                  isCompleted: false,
                  isCompact: true,
                },
                {
                  id: "ex-6",
                  name: "Tricep Extensions",
                  sets: "3",
                  reps: "12-15",
                  rest: "45s",
                  weight: "12lbs",
                  description:
                    "Hold dumbbell overhead with both hands. Lower behind head, then extend back up.",
                  muscleGroups: ["Triceps"],
                  thumbnailUrl: "https://placehold.co/128x80",
                  isCompleted: false,
                  isCompact: true,
                },
              ],
            },
          ],
        },
        {
          id: "day-2",
          dayNumber: 2,
          name: "Rest Day",
          isCollapsed: true,
          sessions: [],
        },
        {
          id: "day-3",
          dayNumber: 3,
          name: "Pull Focus",
          isCollapsed: true,
          sessions: [
            {
              id: "session-3",
              name: "Pull Movements",
              duration: "40 mins",
              isCollapsed: true,
              exercises: [
                {
                  id: "ex-7",
                  name: "Pull-ups",
                  sets: "3",
                  reps: "8-12",
                  rest: "90s",
                  weight: "Body",
                  description:
                    "Hang from bar with palms facing away. Pull body up until chin clears bar.",
                  muscleGroups: ["Back", "Biceps"],
                  thumbnailUrl: "https://placehold.co/128x80",
                  isCompleted: false,
                  isCompact: true,
                },
                {
                  id: "ex-8",
                  name: "Lat Pulldowns",
                  sets: "3",
                  reps: "10-12",
                  rest: "60s",
                  weight: "40lbs",
                  description:
                    "Sit at lat pulldown machine. Pull bar down to chest while squeezing shoulder blades.",
                  muscleGroups: ["Back", "Biceps"],
                  thumbnailUrl: "https://placehold.co/128x80",
                  isCompleted: false,
                  isCompact: true,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: "week-2",
      weekNumber: 2,
      name: "Progressive Week",
      isCollapsed: true,
      equipmentRequired:
        "Dumbbells (adjustable 10-50lbs), resistance bands, pull-up bar, exercise mat. Optional: kettlebells for advanced variations. Ensure proper form and safety throughout your workout session.",
      days: [
        {
          id: "day-4",
          dayNumber: 1,
          name: "Upper Body Power",
          isCollapsed: true,
          sessions: [
            {
              id: "session-4",
              name: "Power Movements",
              duration: "50 mins",
              isCollapsed: true,
              exercises: [
                {
                  id: "ex-9",
                  name: "Explosive Push-ups",
                  sets: "4",
                  reps: "8-10",
                  rest: "90s",
                  weight: "Body",
                  description:
                    "Perform push-ups with explosive upward movement, hands leaving ground if possible.",
                  muscleGroups: ["Chest", "Triceps", "Shoulders"],
                  thumbnailUrl: "https://placehold.co/128x80",
                  isCompleted: false,
                  isCompact: true,
                },
                {
                  id: "ex-10",
                  name: "Medicine Ball Slams",
                  sets: "4",
                  reps: "12",
                  rest: "60s",
                  weight: "20lbs",
                  description:
                    "Hold medicine ball overhead, slam down with full force, catch on bounce.",
                  muscleGroups: ["Core", "Shoulders", "Back"],
                  thumbnailUrl: "https://placehold.co/128x80",
                  isCompleted: false,
                  isCompact: true,
                },
              ],
            },
          ],
        },
        {
          id: "day-5",
          dayNumber: 2,
          name: "Rest Day",
          isCollapsed: true,
          sessions: [],
        },
      ],
    },
    {
      id: "week-3",
      weekNumber: 3,
      name: "Intensity Week",
      isCollapsed: true,
      equipmentRequired:
        "Dumbbells (adjustable 10-50lbs), resistance bands, pull-up bar, exercise mat. Optional: kettlebells for advanced variations. Ensure proper form and safety throughout your workout session.",
      days: [
        {
          id: "day-6",
          dayNumber: 1,
          name: "Heavy Lifting",
          isCollapsed: true,
          sessions: [
            {
              id: "session-5",
              name: "Strength Focus",
              duration: "60 mins",
              isCollapsed: true,
              exercises: [
                {
                  id: "ex-11",
                  name: "Heavy Bench Press",
                  sets: "5",
                  reps: "5",
                  rest: "180s",
                  weight: "80lbs",
                  description:
                    "Focus on heavy weight with perfect form. Lower slowly, press explosively.",
                  muscleGroups: ["Chest", "Triceps", "Shoulders"],
                  thumbnailUrl: "https://placehold.co/128x80",
                  isCompleted: false,
                  isCompact: true,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: "week-4",
      weekNumber: 4,
      name: "Peak Week",
      isCollapsed: true,
      equipmentRequired:
        "Dumbbells (adjustable 10-50lbs), resistance bands, pull-up bar, exercise mat. Optional: kettlebells for advanced variations. Ensure proper form and safety throughout your workout session.",
      days: [
        {
          id: "day-7",
          dayNumber: 1,
          name: "Max Effort",
          isCollapsed: true,
          sessions: [
            {
              id: "session-6",
              name: "Peak Performance",
              duration: "45 mins",
              isCollapsed: true,
              exercises: [
                {
                  id: "ex-12",
                  name: "Max Push-ups",
                  sets: "3",
                  reps: "Max",
                  rest: "120s",
                  weight: "Body",
                  description:
                    "Perform maximum repetitions with perfect form until failure.",
                  muscleGroups: ["Chest", "Triceps", "Shoulders"],
                  thumbnailUrl: "https://placehold.co/128x80",
                  isCompleted: false,
                  isCompact: true,
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};
