import React from "react";

interface TrainerAthletePaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const TrainerAthletePagination: React.FC<TrainerAthletePaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
}) => {
  const getVisiblePages = () => {
    const pages = [];
    const maxVisible = 10;

    if (totalPages <= maxVisible) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      if (currentPage > 4) {
        pages.push("...");
      }

      // Show pages around current page
      const start = Math.max(2, currentPage - 2);
      const end = Math.min(totalPages - 1, currentPage + 2);

      for (let i = start; i <= end; i++) {
        if (!pages.includes(i)) {
          pages.push(i);
        }
      }

      if (currentPage < totalPages - 3) {
        pages.push("...");
      }

      // Always show last page
      if (!pages.includes(totalPages)) {
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const handlePageClick = (page: number | string) => {
    if (typeof page === "number" && page !== currentPage) {
      onPageChange(page);
    }
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const handleFirst = () => {
    onPageChange(1);
  };

  const handleLast = () => {
    onPageChange(totalPages);
  };

  const buttonBaseStyles =
    "flex items-center justify-center w-8 h-8 text-sm border transition-colors duration-200";
  const activeStyles = "bg-primary text-white border-primary";
  const inactiveStyles = `bg-background text-text border-border hover:bg-background-hover`;
  const disabledStyles =
    "bg-background text-text-disabled border-border cursor-not-allowed";

  if (totalPages <= 1) {
    return null;
  }

  return (
    <div className="flex items-center justify-center space-x-1 mt-6">
      {/* First page button */}
      <button
        onClick={handleFirst}
        disabled={currentPage === 1}
        className={`${buttonBaseStyles} ${
          currentPage === 1 ? disabledStyles : inactiveStyles
        }`}
        aria-label="First page"
      >
        ≪
      </button>

      {/* Previous page button */}
      <button
        onClick={handlePrevious}
        disabled={currentPage === 1}
        className={`${buttonBaseStyles} ${
          currentPage === 1 ? disabledStyles : inactiveStyles
        }`}
        aria-label="Previous page"
      >
        ‹
      </button>

      {/* Page numbers */}
      {getVisiblePages().map((page, index) => (
        <button
          key={index}
          onClick={() => handlePageClick(page)}
          disabled={page === "..."}
          className={`${buttonBaseStyles} ${
            page === currentPage
              ? activeStyles
              : page === "..."
                ? disabledStyles
                : inactiveStyles
          }`}
          aria-label={page === "..." ? "More pages" : `Page ${page}`}
        >
          {page}
        </button>
      ))}

      {/* Next page button */}
      <button
        onClick={handleNext}
        disabled={currentPage === totalPages}
        className={`${buttonBaseStyles} ${
          currentPage === totalPages ? disabledStyles : inactiveStyles
        }`}
        aria-label="Next page"
      >
        ›
      </button>

      {/* Last page button */}
      <button
        onClick={handleLast}
        disabled={currentPage === totalPages}
        className={`${buttonBaseStyles} ${
          currentPage === totalPages ? disabledStyles : inactiveStyles
        }`}
        aria-label="Last page"
      >
        ≫
      </button>
    </div>
  );
};

export default TrainerAthletePagination;
