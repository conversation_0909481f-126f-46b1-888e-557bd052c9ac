import { useEffect } from "react";

interface UsePageTitleOptions {
  title?: string;
  suffix?: string;
  fallback?: string;
}

/**
 * Custom hook to manage document title
 * @param options Configuration options for the page title
 */
export const usePageTitle = ({
  title,
  suffix = "KSL Fitness",
  fallback = "KSL Fitness"
}: UsePageTitleOptions) => {
  useEffect(() => {
    if (title) {
      document.title = suffix ? `${title} - ${suffix}` : title;
    } else {
      document.title = fallback;
    }

    // Cleanup: Reset title when component unmounts
    return () => {
      document.title = "KSL Fitness";
    };
  }, [title, suffix, fallback]);
};

/**
 * Hook specifically for program pages
 * @param programName The name of the program
 */
export const useProgramPageTitle = (programName?: string) => {
  usePageTitle({
    title: programName,
    suffix: "KSL Fitness",
    fallback: "Program Details - KSL Fitness"
  });
};

/**
 * Hook specifically for trainer pages
 * @param trainerName The name of the trainer
 */
export const useTrainerPageTitle = (trainerName?: string) => {
  usePageTitle({
    title: trainerName,
    suffix: "Trainer Profile - KSL Fitness",
    fallback: "Trainer Profile - KSL Fitness"
  });
};
