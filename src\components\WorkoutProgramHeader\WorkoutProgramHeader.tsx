import { useEffect, useRef, useCallback } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { ClipboardDocumentListIcon } from "@heroicons/react/24/solid";
import {
  SearchableDropdown,
  SearchableDropdownRef,
} from "@/components/SearchableDropdown";
import { useSearchParams } from "react-router-dom";
import { WorkoutWeek, WorkoutDay } from "@/interfaces";

interface WorkoutProgramHeaderProps {
  dayNumber: number;
  splitTitle: string;
  programWeek: string;
  equipmentRequired: string;
  // Week-related props
  weeks?: WorkoutWeek[];
  currentWeekId?: string | null;
  onWeekChange?: (week: WorkoutWeek | null) => void;
  // Day-related props
  currentWeek?: WorkoutWeek | null;
  currentDayId?: string | null;
  onDayChange?: (day: WorkoutDay | null) => void;
}

const WorkoutProgramHeader = ({
  dayNumber,
  splitTitle,
  equipmentRequired,
  weeks = [],
  currentWeekId,
  onWeekChange,
  currentWeek,
  currentDayId,
  onDayChange,
}: WorkoutProgramHeaderProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  // get enrollment, week, and day from searchParams
  const [searchParams, setSearchParams] = useSearchParams();
  const weekId = searchParams.get("week");
  const dayId = searchParams.get("day");

  // Create refs for the SearchableDropdowns
  const weekDropdownRef = useRef<SearchableDropdownRef>(null);
  const dayDropdownRef = useRef<SearchableDropdownRef>(null);

  // Handle week selection
  const handleWeekSelect = useCallback(
    (week: WorkoutWeek | null) => {
      if (onWeekChange) {
        onWeekChange(week);
      }
    },
    [onWeekChange]
  );

  // Handle day selection
  const handleDaySelect = useCallback(
    (day: WorkoutDay | null) => {
      if (onDayChange) {
        onDayChange(day);
      }
    },
    [onDayChange]
  );

  const updateWeekSelection = (week: WorkoutWeek) => {
    weekDropdownRef.current?.updateValue(week);
  };

  const updateDaySelection = (day: WorkoutDay) => {
    dayDropdownRef.current?.updateValue(day);
  };

  // Auto-select week based on URL parameter when weeks data loads
  useEffect(() => {
    if (weekId && weeks && weeks.length > 0 && !currentWeekId) {
      // Find the week that matches the weekId from URL
      const targetWeek = weeks.find(
        (week: WorkoutWeek) => week.id?.toString() === weekId
      );

      if (targetWeek) {
        // Update the dropdown selection
        updateWeekSelection(targetWeek);

        // Notify parent component
        handleWeekSelect(targetWeek);
      }
    }
  }, [weekId, weeks, currentWeekId, handleWeekSelect]);

  // Auto-select day based on URL parameter when current week data loads
  useEffect(() => {
    if (
      dayId &&
      currentWeek?.days &&
      currentWeek.days.length > 0 &&
      !currentDayId
    ) {
      // Find the day that matches the dayId from URL
      const targetDay = currentWeek.days.find(
        (day: WorkoutDay) => day.id?.toString() === dayId
      );

      if (targetDay) {
        // Update the dropdown selection
        updateDaySelection(targetDay);

        // Notify parent component
        handleDaySelect(targetDay);
      }
    }
  }, [dayId, currentWeek, currentDayId, handleDaySelect]);

  const headerStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderColor: THEME_COLORS[mode].BORDER,
    color: THEME_COLORS[mode].TEXT,
  };

  const equipmentSectionStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  return (
    <div
      className="bg-background border border-border rounded-lg shadow-sm mb-6 transition-colors duration-200"
      style={headerStyles}
    >
      {/* Header Section */}
      <div className="p-4 sm:p-6">
        <div className="flex justify-between items-start gap-4">
          <div className="flex-1 grow flex flex-col gap-3">
            {/* Program Title */}
            <div className="md:min-w-[20rem] md:max-w-[20rem] w-full ">
              <h1 className="text-2xl font-bold text-text">Day {dayNumber}</h1>
              <p className="text-lg font-medium text-primary">{splitTitle}</p>
            </div>
          </div>

          {/* Dropdowns Section */}
          <div className="flex flex-col sm:flex-row gap-4 ">
            {/* Week Dropdown */}
            <div className="min-w-[15.625rem] max-w-full w-full sm:w-[15.625rem]">
              <SearchableDropdown
                ref={weekDropdownRef}
                className="w-full"
                uniqueKey="id"
                displaySeparator=""
                display={["title"]}
                placeholder="Select week..."
                label="Week"
                useExternalData={true}
                externalDataLoading={false}
                externalDataOptions={weeks}
                value={currentWeekId as string}
                onSelect={(week: WorkoutWeek, clear: boolean | undefined) => {
                  if (!clear) {
                    // Update search params with selected week ID
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("week", week.id?.toString() || "");
                      return newParams;
                    });
                    return handleWeekSelect(week);
                  }
                  // Clear search params week when clearing selection
                  setSearchParams((prev) => {
                    const newParams = new URLSearchParams(prev);
                    newParams.delete("week");
                    return newParams;
                  });
                  handleWeekSelect(null);
                }}
                showBorder={true}
              />
            </div>

            {/* Day Dropdown */}
            <div className="min-w-[15.625rem] max-w-full w-full sm:w-[15.625rem]">
              <SearchableDropdown
                ref={dayDropdownRef}
                className="w-full"
                uniqueKey="id"
                displaySeparator=""
                display={["title"]}
                placeholder="Select day..."
                label="Day"
                useExternalData={true}
                externalDataLoading={false}
                externalDataOptions={currentWeek?.days || []}
                value={currentDayId as string}
                onSelect={(day: WorkoutDay, clear: boolean | undefined) => {
                  if (!clear) {
                    // Update search params with selected day ID
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("day", day.id?.toString() || "");
                      return newParams;
                    });
                    return handleDaySelect(day);
                  }
                  // Clear search params day when clearing selection
                  setSearchParams((prev) => {
                    const newParams = new URLSearchParams(prev);
                    newParams.delete("day");
                    return newParams;
                  });
                  handleDaySelect(null);
                }}
                showBorder={true}
                disabled={
                  !currentWeek ||
                  !currentWeek.days ||
                  currentWeek.days.length === 0
                }
              />
            </div>
          </div>
          {/* <div
              className="flex items-center gap-2 px-4 py-2 rounded-md border"
              style={weekBadgeStyles}
            >
              <span className="text-base font-medium text-text">
                {programWeek}
              </span>
              <ChevronDownIcon className="w-4 h-4 text-text-secondary" />
            </div>
           */}
        </div>
      </div>

      {/* Equipment Required Section */}
      <div
        className="mx-4 sm:mx-6 mb-4 sm:mb-6 p-4 rounded-md border"
        style={equipmentSectionStyles}
      >
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 mt-0.5">
            <ClipboardDocumentListIcon className="w-6 h-6 text-primary" />
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-text mb-2">
              Equipment Required
            </h3>
            <p className="text-base text-text-secondary leading-relaxed">
              {equipmentRequired}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkoutProgramHeader;
// const q = {
//   id: "sub_1RhxqwBgOlWo0lDUnad4nrgb",
//   object: "subscription",
//   application: null,
//   application_fee_percent: null,
//   automatic_tax: { disabled_reason: null, enabled: false, liability: null },
//   billing_cycle_anchor: **********,
//   billing_cycle_anchor_config: null,
//   billing_mode: { type: "classic" },
//   billing_thresholds: null,
//   cancel_at: null,
//   cancel_at_period_end: false,
//   canceled_at: null,
//   cancellation_details: { comment: null, feedback: null, reason: null },
//   collection_method: "charge_automatically",
//   created: **********,
//   currency: "usd",
//   current_period_end: **********,
//   current_period_start: **********,
//   customer: "cus_ScO9bzD0YvmpQY",
//   days_until_due: null,
//   default_payment_method: "pm_1RhvA8BgOlWo0lDUaCTi9IT5",
//   default_source: null,
//   default_tax_rates: [],
//   description: null,
//   discount: {
//     id: "di_1RhxqwBgOlWo0lDU0c8ouGrz",
//     object: "discount",
//     checkout_session: null,
//     coupon: {
//       id: "HPx8tnp3",
//       object: "coupon",
//       amount_off: null,
//       created: 1751828633,
//       currency: "usd",
//       duration: "forever",
//       duration_in_months: null,
//       livemode: false,
//       max_redemptions: null,
//       metadata: {},
//       name: "Discount for Split 129 - 19% off",
//       percent_off: 19,
//       redeem_by: null,
//       times_redeemed: 1,
//       valid: true,
//     },
//     customer: "cus_ScO9bzD0YvmpQY",
//     end: null,
//     invoice: null,
//     invoice_item: null,
//     promotion_code: null,
//     start: **********,
//     subscription: "sub_1RhxqwBgOlWo0lDUnad4nrgb",
//     subscription_item: null,
//   },
//   discounts: ["di_1RhxqwBgOlWo0lDU0c8ouGrz"],
//   ended_at: null,
//   invoice_settings: { account_tax_ids: null, issuer: { type: "self" } },
//   items: {
//     object: "list",
//     data: [
//       {
//         id: "si_SdEJRBimY2GD6P",
//         object: "subscription_item",
//         billing_thresholds: null,
//         created: **********,
//         current_period_end: **********,
//         current_period_start: **********,
//         discounts: [],
//         metadata: {},
//         plan: {
//           id: "price_1RhGvIBgOlWo0lDUX6u01SbK",
//           object: "plan",
//           active: true,
//           aggregate_usage: null,
//           amount: 1999,
//           amount_decimal: "1999",
//           billing_scheme: "per_unit",
//           created: **********,
//           currency: "usd",
//           interval: "month",
//           interval_count: 1,
//           livemode: false,
//           metadata: {
//             split_id: "129",
//             projectId: "kanglink",
//             program_id: "21",
//             payment_type: "subscription",
//           },
//           meter: null,
//           nickname: "subscription - program 21 - split 129",
//           product: "prod_ScVxRUYIqvbfzX",
//           tiers_mode: null,
//           transform_usage: null,
//           trial_period_days: null,
//           usage_type: "licensed",
//         },
//         price: {
//           id: "price_1RhGvIBgOlWo0lDUX6u01SbK",
//           object: "price",
//           active: true,
//           billing_scheme: "per_unit",
//           created: **********,
//           currency: "usd",
//           custom_unit_amount: null,
//           livemode: false,
//           lookup_key: null,
//           metadata: {
//             split_id: "129",
//             projectId: "kanglink",
//             program_id: "21",
//             payment_type: "subscription",
//           },
//           nickname: "subscription - program 21 - split 129",
//           product: "prod_ScVxRUYIqvbfzX",
//           recurring: {
//             aggregate_usage: null,
//             interval: "month",
//             interval_count: 1,
//             meter: null,
//             trial_period_days: null,
//             usage_type: "licensed",
//           },
//           tax_behavior: "unspecified",
//           tiers_mode: null,
//           transform_quantity: null,
//           type: "recurring",
//           unit_amount: 1999,
//           unit_amount_decimal: "1999",
//         },
//         quantity: 1,
//         subscription: "sub_1RhxqwBgOlWo0lDUnad4nrgb",
//         tax_rates: [],
//       },
//     ],
//     has_more: false,
//     total_count: 1,
//     url: "/v1/subscription_items?subscription=sub_1RhxqwBgOlWo0lDUnad4nrgb",
//   },
//   latest_invoice: "in_1RhxqwBgOlWo0lDUPjeHusLB",
//   livemode: false,
//   metadata: {
//     trainer_id: "11",
//     original_amount: "19.99",
//     has_discount: "true",
//     athlete_id: "33",
//     split_id: "129",
//     stripe_coupon_id: "HPx8tnp3",
//     discount_amount: "3.7981",
//     projectId: "kanglink",
//   },
//   next_pending_invoice_item_invoice: null,
//   on_behalf_of: null,
//   pause_collection: null,
//   payment_settings: {
//     payment_method_options: null,
//     payment_method_types: null,
//     save_default_payment_method: "off",
//   },
//   pending_invoice_item_interval: null,
//   pending_setup_intent: null,
//   pending_update: null,
//   plan: {
//     id: "price_1RhGvIBgOlWo0lDUX6u01SbK",
//     object: "plan",
//     active: true,
//     aggregate_usage: null,
//     amount: 1999,
//     amount_decimal: "1999",
//     billing_scheme: "per_unit",
//     created: **********,
//     currency: "usd",
//     interval: "month",
//     interval_count: 1,
//     livemode: false,
//     metadata: {
//       split_id: "129",
//       projectId: "kanglink",
//       program_id: "21",
//       payment_type: "subscription",
//     },
//     meter: null,
//     nickname: "subscription - program 21 - split 129",
//     product: "prod_ScVxRUYIqvbfzX",
//     tiers_mode: null,
//     transform_usage: null,
//     trial_period_days: null,
//     usage_type: "licensed",
//   },
//   quantity: 1,
//   schedule: null,
//   start_date: **********,
//   status: "incomplete",
//   test_clock: null,
//   transfer_data: null,
//   trial_end: null,
//   trial_settings: {
//     end_behavior: { missing_payment_method: "create_invoice" },
//   },
//   trial_start: null,
// };

{
  /* <SearchableDropdown
                ref={dropdownRef}
                className="w-full"
                uniqueKey="id"
                displaySeparator={" - "}
                display={["program_name", "split_title"]}
                placeholder="Search programs..."
                label="Program"
                useExternalData={true}
                externalDataLoading={enrollmentLoading}
                externalDataOptions={[...(enrollmentData || [])]}
                value={currentProgramId as string}
                onSelect={(
                  program: EnrolledProgram,
                  clear: boolean | undefined
                ) => {
                  if (!clear) {
                    // Update search params with selected program's enrollment ID
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("enrollment", program.id);
                      return newParams;
                    });
                    return handleProgramSelect(program);
                  }
                  // Clear search params enrollment when clearing selection
                  setSearchParams((prev) => {
                    const newParams = new URLSearchParams(prev);
                    newParams.delete("enrollment");
                    return newParams;
                  });
                  handleProgramSelect(null);
                }}
                showBorder={true}
              /> */
}
