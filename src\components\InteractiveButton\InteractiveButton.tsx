import { memo, ReactNode, Ref, useId, useState } from "react";
import { LoaderTypes, MkdLoader } from "@/components/MkdLoader";

interface InteractiveButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  loading?: boolean;
  animate?: boolean;
  disabled?: boolean;
  children: ReactNode;
  type?: "button" | "submit" | "reset";
  className?: string;
  loaderclasses?: string;
  onClick?: (e?: any) => void;
  color?: string;
  loaderType?: LoaderTypes;
  buttonRef?: Ref<HTMLButtonElement>;
  size?: number;
}

const InteractiveButton = ({
  loading = false,
  animate = false,
  disabled,
  children,
  type = "button",
  className = "bg-primary px-[.6125rem] py-[.5625rem]",
  loaderclasses,
  onClick,
  color = "#ffffff",
  loaderType = LoaderTypes.BEAT,
  buttonRef = null,
  size = 10,
  ...rest
}: InteractiveButtonProps) => {
  const id = useId();

  const [animated, setAnimated] = useState(false);

  const onClickHandle = () => {
    if (onClick) {
      onClick();
    }
    if (animate) {
      setAnimated(true);
    }
  };
  return (
    <button
      id={id}
      type={type}
      ref={buttonRef}
      disabled={disabled}
      className={` ${className} ${
        animated && "!animate-wiggle"
      } relative flex min-h-[2.125rem] w-fit min-w-fit items-center justify-center gap-2 overflow-hidden rounded-md border border-primary font-['Inter'] text-sm font-medium leading-none shadow-md hover:bg-primary-hover active:bg-primary-active disabled:bg-primary-disabled disabled:border-primary-disabled disabled:cursor-not-allowed transition-colors duration-200`}
      onAnimationEnd={() => setAnimated(false)}
      onClick={onClickHandle}
      { ...rest }
    >
      <>
        {children}

        {loading && (
          <MkdLoader
            size={size}
            color={color}
            loading={loading}
            type={loaderType}
            className={loaderclasses}
          />
        )}
      </>
    </button>
  );
};

export default memo(InteractiveButton);
