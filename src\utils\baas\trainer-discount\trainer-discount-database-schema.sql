-- ============================================================================
-- Trainer Discount Management Database Schema
-- ============================================================================
-- This file contains the database schema for trainer discount management
-- Each discount is linked to a specific trainer's program

-- ============================================================================
-- PROGRAMS TABLE (Assumed to exist)
-- ============================================================================
-- CREATE TABLE programs (
--   id BIGINT PRIMARY KEY AUTO_INCREMENT,
--   trainer_id BIGINT NOT NULL,
--   name VARCHAR(255) NOT NULL,
--   description TEXT,
--   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--   updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
--   INDEX idx_trainer_id (trainer_id)
-- );

-- ============================================================================
-- PROGRAM PRICING TIERS
-- ============================================================================

-- Subscription pricing tiers
CREATE TABLE program_subscription_tiers (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  program_id BIGINT NOT NULL,
  name VARCHAR(255) NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  billing_cycle ENUM('monthly', 'quarterly', 'yearly') NOT NULL,
  stripe_product_id VARCHAR(255),
  stripe_price_id VARCHAR(255),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE CASCADE,
  INDEX idx_program_id (program_id),
  INDEX idx_stripe_product_id (stripe_product_id),
  INDEX idx_stripe_price_id (stripe_price_id)
);

-- Full payment pricing tiers
CREATE TABLE program_fullpayment_tiers (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  program_id BIGINT NOT NULL,
  name VARCHAR(255) NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  features JSON, -- Array of features
  stripe_product_id VARCHAR(255),
  stripe_price_id VARCHAR(255),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE CASCADE,
  INDEX idx_program_id (program_id),
  INDEX idx_stripe_product_id (stripe_product_id),
  INDEX idx_stripe_price_id (stripe_price_id)
);

-- ============================================================================
-- DISCOUNT SETTINGS
-- ============================================================================

-- Main discount settings for each program
CREATE TABLE program_discount_settings (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  program_id BIGINT NOT NULL UNIQUE,
  affiliate_link VARCHAR(500),
  sale_discount_type ENUM('fixed', 'percentage') DEFAULT 'fixed',
  sale_discount_value DECIMAL(10,2) DEFAULT 0,
  sale_discount_apply_to_all BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by BIGINT, -- trainer_id who made the update
  
  FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE CASCADE,
  INDEX idx_program_id (program_id)
);

-- ============================================================================
-- PROMO CODES
-- ============================================================================

-- Promo codes for programs
CREATE TABLE program_promo_codes (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  program_id BIGINT NOT NULL,
  code VARCHAR(50) NOT NULL,
  discount_type ENUM('fixed', 'percentage') NOT NULL,
  discount_value DECIMAL(10,2) NOT NULL,
  applies_to_subscription BOOLEAN DEFAULT FALSE,
  applies_to_fullpayment BOOLEAN DEFAULT FALSE,
  usage_limit INT DEFAULT NULL, -- NULL means unlimited
  used_count INT DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  expiry_date TIMESTAMP NULL,
  stripe_coupon_id VARCHAR(255), -- Stripe coupon ID
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by BIGINT, -- trainer_id who created the code
  
  FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE CASCADE,
  UNIQUE KEY unique_program_code (program_id, code),
  INDEX idx_program_id (program_id),
  INDEX idx_code (code),
  INDEX idx_stripe_coupon_id (stripe_coupon_id),
  INDEX idx_expiry_date (expiry_date),
  INDEX idx_is_active (is_active)
);

-- ============================================================================
-- INDIVIDUAL TIER DISCOUNTS
-- ============================================================================

-- Subscription tier specific discounts
CREATE TABLE program_subscription_discounts (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  program_id BIGINT NOT NULL,
  tier_id BIGINT NOT NULL,
  discount_type ENUM('fixed', 'percentage') NOT NULL,
  discount_value DECIMAL(10,2) NOT NULL,
  final_price DECIMAL(10,2) NOT NULL, -- Calculated final price
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE CASCADE,
  FOREIGN KEY (tier_id) REFERENCES program_subscription_tiers(id) ON DELETE CASCADE,
  UNIQUE KEY unique_program_tier (program_id, tier_id),
  INDEX idx_program_id (program_id),
  INDEX idx_tier_id (tier_id)
);

-- Full payment tier specific discounts
CREATE TABLE program_fullpayment_discounts (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  program_id BIGINT NOT NULL,
  tier_id BIGINT NOT NULL,
  discount_type ENUM('fixed', 'percentage') NOT NULL,
  discount_value DECIMAL(10,2) NOT NULL,
  final_price DECIMAL(10,2) NOT NULL, -- Calculated final price
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE CASCADE,
  FOREIGN KEY (tier_id) REFERENCES program_fullpayment_tiers(id) ON DELETE CASCADE,
  UNIQUE KEY unique_program_tier (program_id, tier_id),
  INDEX idx_program_id (program_id),
  INDEX idx_tier_id (tier_id)
);

-- ============================================================================
-- PROMO CODE USAGE TRACKING
-- ============================================================================

-- Track promo code usage
CREATE TABLE promo_code_usage (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  promo_code_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL, -- athlete/customer who used the code
  order_id BIGINT, -- reference to order/purchase
  discount_amount DECIMAL(10,2) NOT NULL,
  used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (promo_code_id) REFERENCES program_promo_codes(id) ON DELETE CASCADE,
  INDEX idx_promo_code_id (promo_code_id),
  INDEX idx_user_id (user_id),
  INDEX idx_order_id (order_id),
  INDEX idx_used_at (used_at)
);

-- ============================================================================
-- DISCOUNT AUDIT LOG
-- ============================================================================

-- Audit trail for discount changes
CREATE TABLE discount_audit_log (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  program_id BIGINT NOT NULL,
  action_type ENUM('CREATE', 'UPDATE', 'DELETE', 'ACTIVATE', 'DEACTIVATE') NOT NULL,
  entity_type ENUM('DISCOUNT_SETTINGS', 'PROMO_CODE', 'SUBSCRIPTION_DISCOUNT', 'FULLPAYMENT_DISCOUNT') NOT NULL,
  entity_id BIGINT, -- ID of the affected entity
  old_values JSON, -- Previous values (for updates)
  new_values JSON, -- New values
  changed_by BIGINT NOT NULL, -- trainer_id who made the change
  change_reason VARCHAR(500), -- Optional reason for the change
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE CASCADE,
  INDEX idx_program_id (program_id),
  INDEX idx_changed_by (changed_by),
  INDEX idx_created_at (created_at),
  INDEX idx_entity_type_id (entity_type, entity_id)
);

-- ============================================================================
-- VIEWS FOR EASY DATA RETRIEVAL
-- ============================================================================

-- View for complete discount information
CREATE VIEW program_discount_overview AS
SELECT 
  p.id as program_id,
  p.name as program_name,
  p.trainer_id,
  pds.affiliate_link,
  pds.sale_discount_type,
  pds.sale_discount_value,
  pds.sale_discount_apply_to_all,
  pds.updated_at as discount_updated_at,
  
  -- Promo code info
  ppc.id as promo_code_id,
  ppc.code as promo_code,
  ppc.discount_type as promo_discount_type,
  ppc.discount_value as promo_discount_value,
  ppc.applies_to_subscription,
  ppc.applies_to_fullpayment,
  ppc.usage_limit,
  ppc.used_count,
  ppc.is_active as promo_is_active,
  ppc.expiry_date as promo_expiry_date
  
FROM programs p
LEFT JOIN program_discount_settings pds ON p.id = pds.program_id
LEFT JOIN program_promo_codes ppc ON p.id = ppc.program_id AND ppc.is_active = TRUE;

-- View for subscription pricing with discounts
CREATE VIEW subscription_pricing_with_discounts AS
SELECT 
  pst.id as tier_id,
  pst.program_id,
  pst.name as tier_name,
  pst.price as original_price,
  pst.currency,
  pst.billing_cycle,
  
  psd.discount_type,
  psd.discount_value,
  psd.final_price,
  
  CASE 
    WHEN psd.discount_type = 'fixed' THEN pst.price - psd.discount_value
    WHEN psd.discount_type = 'percentage' THEN pst.price * (1 - psd.discount_value / 100)
    ELSE pst.price
  END as calculated_final_price,
  
  CASE 
    WHEN psd.discount_value > 0 THEN 
      ROUND(((pst.price - psd.final_price) / pst.price) * 100, 2)
    ELSE 0
  END as savings_percentage
  
FROM program_subscription_tiers pst
LEFT JOIN program_subscription_discounts psd ON pst.id = psd.tier_id AND psd.is_active = TRUE
WHERE pst.is_active = TRUE;

-- View for full payment pricing with discounts
CREATE VIEW fullpayment_pricing_with_discounts AS
SELECT 
  pft.id as tier_id,
  pft.program_id,
  pft.name as tier_name,
  pft.price as original_price,
  pft.currency,
  pft.features,
  
  pfd.discount_type,
  pfd.discount_value,
  pfd.final_price,
  
  CASE 
    WHEN pfd.discount_type = 'fixed' THEN pft.price - pfd.discount_value
    WHEN pfd.discount_type = 'percentage' THEN pft.price * (1 - pfd.discount_value / 100)
    ELSE pft.price
  END as calculated_final_price,
  
  CASE 
    WHEN pfd.discount_value > 0 THEN 
      ROUND(((pft.price - pfd.final_price) / pft.price) * 100, 2)
    ELSE 0
  END as savings_percentage
  
FROM program_fullpayment_tiers pft
LEFT JOIN program_fullpayment_discounts pfd ON pft.id = pfd.tier_id AND pfd.is_active = TRUE
WHERE pft.is_active = TRUE;

-- ============================================================================
-- SAMPLE DATA INSERTION
-- ============================================================================

-- Sample program (assuming programs table exists)
-- INSERT INTO programs (id, trainer_id, name, description) VALUES 
-- (123, 456, 'Advanced Fitness Program', 'Comprehensive fitness training program');

-- Sample subscription tiers
-- INSERT INTO program_subscription_tiers (program_id, name, price, billing_cycle) VALUES
-- (123, 'Monthly Subscription', 5.00, 'monthly'),
-- (123, 'Quarterly Subscription', 7.00, 'quarterly');

-- Sample full payment tiers
-- INSERT INTO program_fullpayment_tiers (program_id, name, price, features) VALUES
-- (123, 'Full Price - Basic', 15.00, '["Basic access", "Email support"]'),
-- (123, 'Full Price - Premium', 20.00, '["Premium access", "Priority support", "Extra content"]');

-- Sample discount settings
-- INSERT INTO program_discount_settings (program_id, affiliate_link, sale_discount_type, sale_discount_value, sale_discount_apply_to_all) VALUES
-- (123, 'www.affiliate-link.com', 'fixed', 5.00, TRUE);

-- Sample promo code
-- INSERT INTO program_promo_codes (program_id, code, discount_type, discount_value, applies_to_subscription, applies_to_fullpayment, usage_limit, expiry_date) VALUES
-- (123, 'SAVE20', 'percentage', 20.00, TRUE, FALSE, 100, '2024-12-31 23:59:59');
