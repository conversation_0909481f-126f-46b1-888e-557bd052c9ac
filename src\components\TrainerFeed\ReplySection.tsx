import React from "react";
import { ReplySectionProps } from "./types";

const ReplySection: React.FC<ReplySectionProps> = ({ replies, is_visible }) => {
  if (!is_visible) return null;

  return (
    <div className="space-y-4 mb-6">
      {replies.length > 0 && (
        <div className="space-y-4">
          {replies.map((reply) => (
            <div
              key={reply.id}
              className="flex space-x-3 pl-4 border-primary/20"
            >
              <img
                src={reply.author_avatar}
                alt={reply.author_name}
                className="w-10 h-10 rounded-full object-cover flex-shrink-0 ring-2 ring-border"
              />
              <div className="flex-1">
                <div className="bg-background-hover rounded-xl p-4 shadow-sm">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="font-semibold text-text text-sm">
                      {reply.author_name}
                    </span>
                    {reply.is_trainer && (
                      <span className="text-xs bg-primary text-white px-2 py-1 rounded-full font-medium">
                        Trainer
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-text leading-relaxed">
                    {reply.content}
                  </p>
                </div>
                <p className="text-xs text-text-disabled mt-2 ml-4">
                  {reply.timestamp}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ReplySection;
