import React from "react";
import SplitCard from "./SplitCard";

// Example usage of the enhanced SplitCard with enrollment integration
const SplitCardExample: React.FC = () => {
  // Example program data
  const exampleProgram = {
    id: 1,
    name: "Strength Training Program",
    description: "Complete strength training program for all levels",
    // ... other program properties
  };

  const exampleSplit = {
    id: 202,
    title: "Beginner Split",
    description:
      "Perfect for beginners starting their fitness journey. This comprehensive program includes progressive overload, proper form instruction, and structured progression to help you build strength safely and effectively.",
    subscriptionPrice: 29.99,
    buyPrice: 99.99,
    paymentPlan: ["monthly", "one_time"],
  };

  return (
    <div className="p-6 max-w-md mx-auto">
      <h2 className="text-2xl font-bold text-text mb-6">
        Enhanced Split Card with Enrollment
      </h2>

      <SplitCard
        splitId={exampleSplit.id}
        programId={exampleProgram.id}
        splitName={exampleSplit.title}
        description={exampleSplit.description}
        subscriptionPrice={exampleSplit.subscriptionPrice}
        buyPrice={exampleSplit.buyPrice}
        paymentPlan={exampleSplit.paymentPlan}
        // Optional custom handlers (if not provided, will use built-in Stripe integration)
        // onSubscribe={() => console.log("Custom subscribe handler")}
        // onBuy={() => console.log("Custom buy handler")}
      />

      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
          Features:
        </h3>
        <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
          <li>• Automatic login state detection</li>
          <li>• Enrollment eligibility checking</li>
          <li>• Real-time pricing from API</li>
          <li>• Stripe payment integration with 3D Secure</li>
          <li>• Payment authentication handling</li>
          <li>• Enrollment status polling</li>
          <li>• Automatic redirect after payment</li>
          <li>• Enrollment status display</li>
          <li>• Error handling and user feedback</li>
        </ul>
      </div>
    </div>
  );
};

export default SplitCardExample;
