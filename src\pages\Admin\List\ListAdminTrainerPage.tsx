import React, { useState, useEffect, useMemo } from "react";
import { useContexts } from "@/hooks/useContexts";
import { TrainerFilters, TrainerTable } from "@/components/Trainers";
import { useGetPaginateQuery } from "@/query/shared/listModel";
import { Models } from "@/utils/baas";
import { User, Trainer } from "@/interfaces/model.interface";
import { useUpdateModelMutation } from "@/query/shared";
import { RoleEnum, ToastStatusEnum } from "@/utils/Enums";
import { QueryKey, useQueryClient } from "@tanstack/react-query";

const TRAINERS_QUERY_KEY: QueryKey = ["trainers"];

const ListAdminTrainerPage: React.FC = () => {
  const { globalDispatch, showToast } = useContexts();
  const queryClient = useQueryClient();
  
  const { mutateAsync: updateModel } = useUpdateModelMutation(Models.USER, RoleEnum.SUPER_ADMIN, {
    onMutate: async ({ id, payload }: { id: number; payload: Record<string, any> }) => {
      await queryClient.cancelQueries({ queryKey: TRAINERS_QUERY_KEY });
      const previousTrainers = queryClient.getQueryData<Trainer[]>(TRAINERS_QUERY_KEY);
      queryClient.setQueryData<Trainer[]>(TRAINERS_QUERY_KEY, (old = []) =>
        old.map((trainer) =>
          trainer.id === id ? { ...trainer, ...payload } : trainer
        )
      );
      return { previousTrainers };
    },
    onError: (err: any, _variables: any, context: { previousTrainers?: Trainer[] } | undefined) => {
      if (context?.previousTrainers) {
        queryClient.setQueryData(TRAINERS_QUERY_KEY, context.previousTrainers);
      }
      showToast(
        err?.response?.data?.message || err?.message || "Failed to update trainer status",
        5000,
        ToastStatusEnum.ERROR
      );
    },
    onSuccess: (_data: any, variables: { id: number; payload: Record<string, any> }) => {
      const statusValue = variables?.payload?.status === 1 ? "active" : variables?.payload?.status === 2 ? "suspended" : "inactive";
      showToast(
        `Trainer status updated to ${statusValue} successfully`,
        5000,
        ToastStatusEnum.SUCCESS
      );
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: TRAINERS_QUERY_KEY });
    },
  })
  // State management 
  const [searchTerm, setSearchTerm] = useState("");
  const [dateFilter, setDateFilter] = useState("This Week");
  const [statusFilter, setStatusFilter] = useState("All");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10); // Match the design showing 4 items

  // Build query options for API call
  const queryOptions = useMemo(() => {
    const options: any = {
      size: pageSize,
      page: currentPage,
      join: ["program", "enrollment"],
      filter: ["role_id,eq,trainer"], // Filter for trainers only
    };

    // Add search filter if search term exists
    if (searchTerm.trim()) {
      options.filter.push(`data,cs,${searchTerm.trim()}`);
    }

    // Add status filter if not "All"
    if (statusFilter !== "All") {
      const statusValue = statusFilter === "Active" ? 1 : 0;
      options.filter.push(`status,eq,${statusValue}`);
    }

    // Add date filter logic here if needed
    // For now, we'll skip date filtering

    return options;
  }, [currentPage, pageSize, searchTerm, statusFilter]);

  // Fetch trainers data using pagination
  const {
    data: trainersData,
    isLoading,
    error,
    refetch,
  } = useGetPaginateQuery(Models.USER, queryOptions, null, {
    enabled: true,
  });

  // Transform User data to Trainer format
  const trainers: Trainer[] = useMemo(() => {
    if (!trainersData?.data) return [];

    return trainersData.data.map((user: User) => ({
      ...user,
      id: user.id as number,
      dateAdded: user.created_at
        ? new Date(user.created_at).toISOString().split("T")[0]
        : "Unknown",
      name:
        JSON.parse(user?.data || "")?.full_name ||
        `${user.first_name || ""} ${user.last_name || ""}`.trim(),
      programs: user?.program?.length || 0, // This would need to be calculated from program data
      enrollments: user?.enrollment,
    }));
  }, [trainersData?.data]);

  // Pagination info from API response
  const totalPages = trainersData?.num_pages || 1;

  // Set path in global state for navigation highlighting
  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "trainers",
      },
    });
  }, [globalDispatch]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter, dateFilter]);

  const handleApplyFilter = () => {
    // Reset to first page when applying filters
    setCurrentPage(1);
    // Refetch data with new filters
    refetch();
  };

  const handleViewList = (trainerId: number) => {
    console.log("View programs for trainer:", trainerId);
    // TODO: Navigate to trainer programs list page
  };

  const handleEditTrainer = (trainerId: number) => {
    console.log("Edit trainer:", trainerId);
    // TODO: Navigate to edit trainer page
  };

  const handleStatusChange = (
    trainerId: number,
    newStatus: Trainer["status"]
  ) => {
    updateModel({
      id: trainerId,
      payload: { status: newStatus },
    });
  };

  return (
    <div className="w-full bg-background p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h1 className="text-2xl font-bold text-text">Trainer Management</h1>
        </div>

        {/* Filters Section */}
        <TrainerFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          dateFilter={dateFilter}
          setDateFilter={setDateFilter}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          onApplyFilter={handleApplyFilter}
        />

        {/* Trainers Table Section */}
        {isLoading ? (
          <div className="bg-background border border-border rounded-lg shadow-sm p-8 text-center">
            <p className="text-text">Loading trainers...</p>
          </div>
        ) : error ? (
          <div className="bg-background border border-border rounded-lg shadow-sm p-8 text-center">
            <p className="text-red-600">
              Error loading trainers. Please try again.
            </p>
          </div>
        ) : (
          <TrainerTable
            trainers={trainers}
            currentPage={currentPage}
            totalPages={totalPages}
            pageSize={pageSize}
            onPageChange={setCurrentPage}
            onViewList={handleViewList}
            onEditTrainer={handleEditTrainer}
            onStatusChange={handleStatusChange}
          />
        )}
      </div>
    </div>
  );
};

export default ListAdminTrainerPage;
