import React, { useCallback, useEffect, useState } from "react";
import { Routes, Route } from "react-router-dom";
// import { AuthContext } from "@/context/Auth";
// import { GlobalContext } from "@/context/Global";

import PrivateRoute from "./PrivateRoutes";
import PublicRoute from "./PublicRoutes";
import { PublicWrapper } from "@/components/PublicWrapper";
import { NotFoundPage } from "@/pages/404";
import { SnackBar } from "@/components/SnackBar";
import { SessionExpiredModal } from "@/components/SessionExpiredModal";

// generatePagesRoutes
import { AdminWrapper } from "@/components/AdminWrapper";
import { TrainerWrapper } from "@/components/TrainerWrapper";
import { AthleteWrapper } from "@/components/AthleteWrapper";

import {
  AdminForgotPage,
  AdminLoginPage,
  AdminProfilePage,
  AdminResetPage,
  MagicLoginVerifyPage,
  UserMagicLoginPage,
  AdminSignUpPage,
  TestComponents,
  PlaygroundPage,
  ViewAdminDashboardPage,
  // Additional Admin Pages
  ViewAdminProfilePage,
  ListAdminLibraryPage,
  ListAdminRefundPage,
  ListAdminTransactionPage,
  ListAdminTrainerPage,
  ListAdminAthletePage,
  ListAdminProgramPage,
  // Trainer Pages
  AddTrainerProgramPage,
  EditTrainerProgramPage,
  ViewTrainerProgramPage,
  ViewTrainerProfilePage,
  ViewTrainerTransactionsPage,
  ViewTrainerFeedPage,
  ViewTrainerDiscountPage,
  ViewTrainerDashboardPage,
  ListTrainerAthleteManagementPage,
  ListTrainerProgramPage,
  ForgotPassword,
  ChangePassword,
  TrainerSignup,
  // Athlete Pages
  ViewAthleteProfilePage,
  ViewAthleteFeedPage,
  ViewAthleteEmrollmentProgressPage,
  ViewAthleteProgramPreviewPage,
  ViewAthleteLibraryPage,
  ViewAthleteProgramPage,
  ViewAthleteTrainerDetailsPage,
  ViewAthleteNotificationsPage,
  HomePage,
  AthleteSignup,
  AthleteProfileCompletion,
  TrainerProfileCompletion,
  // Common Pages
  LoginPage,
  OAuthCallbackPage,
  OAuthTestPage,
  // Verification Pages
  VerifyEmail,
  VerificationSent,
} from "./LazyLoad";

import { useContexts } from "@/hooks/useContexts";
import { RoleEnum } from "@/utils/Enums";
import { LazyLoad } from "@/components/LazyLoad";
import { RouteChangeModal } from "@/components/RouteChangeModal";
import { RoleMap } from "@/utils";

export interface DynamicWrapperProps {
  isAuthenticated?: boolean;
  role?: RoleEnum;
  children: React.ReactNode;
}

export const DynamicWrapper: React.FC<DynamicWrapperProps> = ({
  isAuthenticated,
  role,
  children,
}) => {
  if (!isAuthenticated) {
    return <PublicWrapper>{children}</PublicWrapper>;
  }
  if (isAuthenticated) {
    if (role && [RoleEnum.ADMIN, RoleEnum.SUPER_ADMIN].includes(role)) {
      return <AdminWrapper>{children}</AdminWrapper>;
    }
  }
};

export interface NotFoundProps {
  isAuthenticated?: boolean;
  role?: RoleEnum | null;
}

export const NotFound: React.FC<NotFoundProps> = ({
  isAuthenticated,
  role,
}) => {
  if (!isAuthenticated) {
    return (
      <PublicWrapper>
        <NotFoundPage />
      </PublicWrapper>
    );
  }
  if (isAuthenticated) {
    if (role && [RoleEnum.ADMIN, RoleEnum.SUPER_ADMIN].includes(role)) {
      return (
        <AdminWrapper>
          <NotFoundPage />
        </AdminWrapper>
      );
    }
    if (role && [RoleEnum.TRAINER].includes(role)) {
      return (
        <TrainerWrapper>
          <NotFoundPage />
        </TrainerWrapper>
      );
    }
    if (role && [RoleMap[RoleEnum.ATHLETE]].includes(role)) {
      return (
        <AthleteWrapper>
          <NotFoundPage />
        </AthleteWrapper>
      );
    }
  }
};

export default () => {
  const {
    globalState,
    globalDispatch: dispatch,
    authState: state,
    setGlobalState,
  } = useContexts();

  const isOpen = globalState?.isOpen ?? false;
  const openRouteChangeModal = globalState?.openRouteChangeModal ?? false;

  const [screenSize, setScreenSize] = useState(window.innerWidth);

  // function setDimension(e: Event) {
  //   const target = e.currentTarget as Window;
  //   if (target.innerWidth >= 1024) {
  //     toggleSideBar(true);
  //   } else toggleSideBar(false);
  //   setScreenSize(target.innerWidth);
  // }

  // const toTop = () => {
  //   containerRef.current.scrollTo(0, 0);
  // };

  const portalChange = useCallback(
    (e: any) => {
      // console.log(e);
      if (
        (e.ctrlKey || e.metaKey) &&
        e.shiftKey &&
        e.altKey &&
        ["r", "R", "‰"].includes(e.key)
      ) {
        setGlobalState("openRouteChangeModal", true);
      }
      // console.log("PORTAL CHANGE  >>", openRouteChangeModal, e);
      if (["Escape", "escape", "ESCAPE", "Esc", "esc"].includes(e.key)) {
        setGlobalState("openRouteChangeModal", false);
      }
    },
    [setGlobalState]
  );

  const toggleSideBar = (open: boolean) => {
    if (isOpen && screenSize < 1024) {
      dispatch({
        type: "OPEN_SIDEBAR",
        payload: { isOpen: open },
      });
    } else if (!isOpen && screenSize >= 1024) {
      dispatch({
        type: "OPEN_SIDEBAR",
        payload: { isOpen: open },
      });
    }
  };

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    window.addEventListener(
      "resize",
      (e) => {
        const target = e.currentTarget as Window;
        if (target.innerWidth >= 1024) {
          toggleSideBar(true);
        } else toggleSideBar(false);
        setScreenSize(target.innerWidth);
      },
      { signal }
    );

    return () => {
      controller.abort();
    };
  }, [screenSize]);

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    window.addEventListener("keydown", portalChange, { signal });

    return () => {
      controller.abort();
    };
  }, []);

  return (
    <div
      onClick={() => {
        isOpen ? toggleSideBar(false) : null;
      }}
      className={`h-svh grid grid-cols-1 grid-rows-[auto_1fr] min-h-svh max-h-svh overflow-y-hidden overflow-x-hidden bg-background`}
    >
      <Routes>
        <Route
          path="/admin/profile"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/profile"}
              element={
                <AdminWrapper>
                  <AdminProfilePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/login"
          element={
            <PublicRoute
              path={"/admin/login"}
              element={
                <PublicWrapper role="admin">
                  <AdminLoginPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/sign-up"
          element={
            <PublicRoute
              path={"/admin/sign-up"}
              element={
                <PublicWrapper>
                  <AdminSignUpPage />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/reset"
          element={
            <PublicRoute
              path={"/admin/reset"}
              element={
                <PublicWrapper>
                  <AdminResetPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/magic-login"
          element={
            <PublicRoute
              path={"/magic-login"}
              element={
                <PublicWrapper>
                  <UserMagicLoginPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          path="/magic-login/verify"
          element={
            <PublicRoute
              path={"/magic-login/verify"}
              element={
                <PublicWrapper>
                  <MagicLoginVerifyPage />
                </PublicWrapper>
              }
            />
          }
        />

        {/* Custom Routes */}

        <Route
          path="/test-components"
          element={
            <PublicRoute
              path={"/test-components"}
              element={
                <PublicWrapper>
                  <TestComponents />
                </PublicWrapper>
              }
            />
          }
        />
        {/* PG */}
        <Route
          path="/playground"
          element={
            <PublicRoute
              path={"/playground"}
              element={
                <PublicWrapper>
                  <div className="h-full min-h-full max-h-full overflow-y-auto">
                    <PlaygroundPage />
                  </div>
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/dashboard"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/dashboard"}
              element={
                <AdminWrapper>
                  <ViewAdminDashboardPage />
                </AdminWrapper>
              }
            />
          }
        />

        {/* Additional Admin Routes */}
        <Route
          path="/admin/profile/view"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/profile/view"}
              element={
                <AdminWrapper>
                  <ViewAdminProfilePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/content-libraries"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/content-libraries"}
              element={
                <AdminWrapper>
                  <ListAdminLibraryPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/refunds"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/refunds"}
              element={
                <AdminWrapper>
                  <ListAdminRefundPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/transactions"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/transactions"}
              element={
                <AdminWrapper>
                  <ListAdminTransactionPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/trainers"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/trainers"}
              element={
                <AdminWrapper>
                  <ListAdminTrainerPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/athletes"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/athletes"}
              element={
                <AdminWrapper>
                  <ListAdminAthletePage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/programs"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/programs"}
              element={
                <AdminWrapper>
                  <ListAdminProgramPage />
                </AdminWrapper>
              }
            />
          }
        />

        <Route
          path="/admin/programs/preview/:programId"
          element={
            <PrivateRoute
              access={["admin", "super_admin"]}
              path={"/admin/programs/preview/:programId"}
              element={
                <AdminWrapper>
                  <ViewTrainerProgramPage />
                </AdminWrapper>
              }
            />
          }
        />

        {/* Trainer Routes */}
        <Route
          path="/trainer/add-program"
          element={
            <PrivateRoute
              access={["trainer"]}
              path={"/trainer/add-program"}
              element={
                <TrainerWrapper>
                  <AddTrainerProgramPage />
                </TrainerWrapper>
              }
            />
          }
        />

        <Route
          path="/trainer/view-program/:programId"
          element={
            <PrivateRoute
              access={["trainer", "admin", "super_admin"]}
              path={"/trainer/view-program/:programId"}
              element={
                <TrainerWrapper>
                  <ViewTrainerProgramPage />
                </TrainerWrapper>
              }
            />
          }
        />

        <Route
          path="/trainer/edit-program/:programId"
          element={
            <PrivateRoute
              access={["trainer"]}
              path={"/trainer/edit-program/:programId"}
              element={
                <TrainerWrapper>
                  <EditTrainerProgramPage />
                </TrainerWrapper>
              }
            />
          }
        />

        <Route
          path="/trainer/edit-program/:programId"
          element={
            <PrivateRoute
              access={["trainer"]}
              path={"/trainer/edit-program/:programId"}
              element={
                <TrainerWrapper>
                  <EditTrainerProgramPage />
                </TrainerWrapper>
              }
            />
          }
        />

        <Route
          path="/trainer/profile"
          element={
            <PrivateRoute
              access={["trainer"]}
              path={"/trainer/profile"}
              element={
                <TrainerWrapper>
                  <ViewTrainerProfilePage />
                </TrainerWrapper>
              }
            />
          }
        />

        <Route
          path="/trainer/transactions"
          element={
            <PrivateRoute
              access={["trainer"]}
              path={"/trainer/transactions"}
              element={
                <TrainerWrapper>
                  <ViewTrainerTransactionsPage />
                </TrainerWrapper>
              }
            />
          }
        />

        <Route
          path="/trainer/feed"
          element={
            <PrivateRoute
              access={["trainer"]}
              path={"/trainer/feed"}
              element={
                <TrainerWrapper>
                  <ViewTrainerFeedPage />
                </TrainerWrapper>
              }
            />
          }
        />

        <Route
          path="/trainer/discount/:programId"
          element={
            <PrivateRoute
              access={["trainer"]}
              path={"/trainer/discount/:programId"}
              element={
                <TrainerWrapper>
                  <ViewTrainerDiscountPage />
                </TrainerWrapper>
              }
            />
          }
        />

        <Route
          path="/trainer/dashboard"
          element={
            <PrivateRoute
              access={["trainer"]}
              path={"/trainer/dashboard"}
              element={
                <TrainerWrapper>
                  <ViewTrainerDashboardPage />
                </TrainerWrapper>
              }
            />
          }
        />

        <Route
          path="/trainer/athlete-management"
          element={
            <PrivateRoute
              access={["trainer"]}
              path={"/trainer/athlete-management"}
              element={
                <TrainerWrapper>
                  <ListTrainerAthleteManagementPage />
                </TrainerWrapper>
              }
            />
          }
        />

        <Route
          path="/trainer/programs"
          element={
            <PrivateRoute
              access={["trainer"]}
              path={"/trainer/programs"}
              element={
                <TrainerWrapper>
                  <ListTrainerProgramPage />
                </TrainerWrapper>
              }
            />
          }
        />

        <Route
          path="/forgot-password"
          element={
            <PublicRoute
              path={"/forgot-password"}
              element={
                <PublicWrapper>
                  <ForgotPassword />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/change-password"
          element={
            <PublicRoute
              // access={["trainer"]}
              path={"/change-password"}
              element={
                <PublicWrapper>
                  <ChangePassword />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/trainer/signup"
          element={
            <PublicRoute
              path={"/trainer/signup"}
              element={
                <PublicWrapper>
                  <TrainerSignup />
                </PublicWrapper>
              }
            />
          }
        />

        {/* Athlete Routes */}
        <Route
          path="/athlete/profile"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/athlete/profile"}
              element={
                <AthleteWrapper>
                  <ViewAthleteProfilePage />
                </AthleteWrapper>
              }
            />
          }
        />

        <Route
          path="/athlete/feed"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/athlete/feed"}
              element={
                <AthleteWrapper>
                  <ViewAthleteFeedPage />
                </AthleteWrapper>
              }
            />
          }
        />

        <Route
          path="/athlete/programs/enrolled/access"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/athlete/programs/enrolled/access"}
              element={
                <AthleteWrapper>
                  <ViewAthleteEmrollmentProgressPage />
                </AthleteWrapper>
              }
            />
          }
        />

        <Route
          path="/athlete/program-preview/:splitId"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/athlete/program-preview/:splitId"}
              element={
                <AthleteWrapper>
                  <ViewAthleteProgramPreviewPage />
                </AthleteWrapper>
              }
            />
          }
        />

        <Route
          path="/athlete/library"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/athlete/library"}
              element={
                <AthleteWrapper>
                  <ViewAthleteLibraryPage />
                </AthleteWrapper>
              }
            />
          }
        />

        <Route
          path="/athlete/program/:programId"
          element={
            <PublicRoute
              path={"/athlete/program/:programId"}
              element={
                <AthleteWrapper>
                  <ViewAthleteProgramPage />
                </AthleteWrapper>
              }
            />
          }
        />

        <Route
          path="/athlete/trainer-details"
          element={
            <PublicRoute
              path={"/athlete/trainer-details"}
              element={
                <AthleteWrapper>
                  <ViewAthleteTrainerDetailsPage />
                </AthleteWrapper>
              }
            />
          }
        />

        <Route
          path="/athlete/notifications"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/athlete/notifications"}
              element={
                <AthleteWrapper>
                  <ViewAthleteNotificationsPage />
                </AthleteWrapper>
              }
            />
          }
        />

        <Route
          path="/"
          element={
            <PublicRoute
              path={"/"}
              element={
                <AthleteWrapper>
                  <HomePage />
                </AthleteWrapper>
              }
            />
          }
        />

        <Route
          path="/athlete/signup"
          element={
            <PublicRoute
              path={"/athlete/signup"}
              element={
                <PublicWrapper>
                  <AthleteSignup />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/athlete/profile-completion"
          element={
            <PrivateRoute
              access={["member"]}
              path={"/athlete/profile-completion"}
              element={
                <PublicWrapper role="pending">
                  <AthleteProfileCompletion />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/trainer/profile-completion"
          element={
            <PrivateRoute
              access={["trainer"]}
              path={"/trainer/profile-completion"}
              element={
                <PublicWrapper role="pending">
                  <TrainerProfileCompletion />
                </PublicWrapper>
              }
            />
          }
        />

        {/* Common Routes */}
        <Route
          path="/login"
          element={
            <PublicRoute
              path={"/login"}
              element={
                <PublicWrapper>
                  <LoginPage />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/login/oauth"
          element={
            <PublicRoute
              path={"/login/oauth"}
              element={
                <PublicWrapper>
                  <OAuthCallbackPage />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/oauth-test"
          element={
            <PublicRoute
              path={"/oauth-test"}
              element={
                <PublicWrapper>
                  <OAuthTestPage />
                </PublicWrapper>
              }
            />
          }
        />

        {/* Verification Routes */}
        <Route
          path="/verify-email"
          element={
            <PublicRoute
              path={"/verify-email"}
              element={
                <PublicWrapper>
                  <VerifyEmail />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path="/verification-sent"
          element={
            <PublicRoute
              path={"/verification-sent"}
              element={
                <PublicWrapper>
                  <VerificationSent />
                </PublicWrapper>
              }
            />
          }
        />

        <Route
          path={"*"}
          element={
            <PublicRoute
              path={"*"}
              element={
                <NotFound
                  isAuthenticated={state?.isAuthenticated}
                  role={state?.role as RoleEnum | null}
                />
              }
            />
          }
        />
      </Routes>
      <SessionExpiredModal />
      <SnackBar />

      <LazyLoad>
        <RouteChangeModal
          isOpen={openRouteChangeModal}
          onClose={() => setGlobalState("openRouteChangeModal", false)}
          options={[
            ...(state?.isAuthenticated
              ? [
                  // Athlete Routes (for authenticated users)
                  { name: "Home", route: "/" },
                  { name: "Athlete Profile", route: "/athlete/profile" },
                  { name: "Athlete Feed", route: "/athlete/feed" },
                  {
                    name: "Athlete Programs Access",
                    route: "/athlete/programs/enrolled/access",
                  },
                  { name: "Athlete Library", route: "/athlete/library" },
                  {
                    name: "Athlete Trainer Details",
                    route: "/athlete/trainer-details",
                  },
                  { name: "Playground", route: "/playground" },
                  {
                    name: "athlete program preview by id",
                    route: "/athlete/program-preview/:id",
                  },
                  {
                    name: "Athlete program by id",
                    route: "/athlete/program/:programId",
                  },
                ]
              : [
                  // Public Routes
                  { name: "Home", route: "/" },
                  { name: "Admin Login", route: "/admin/login" },
                  { name: "Login", route: "/login" },
                  { name: "Athlete Signup", route: "/athlete/signup" },
                  { name: "Trainer Signup", route: "/trainer/signup" },
                  { name: "Athlete Library", route: "/athlete/library" },
                  { name: "Playground", route: "/playground" },
                ]),
          ]}
          title="Change Route"
        />
      </LazyLoad>
    </div>
  );
};
