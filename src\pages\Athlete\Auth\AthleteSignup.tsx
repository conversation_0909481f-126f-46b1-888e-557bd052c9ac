import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Container } from "@/components/Container";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { MkdPasswordInput } from "@/components/MkdPasswordInput";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useNavigate, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";

import { ToastStatusEnum } from "@/utils/Enums";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faFacebook,
  faGoogle,
  faInstagram,
  faLinkedin,
} from "@fortawesome/free-brands-svg-icons";
import {
  faCalendarDays,
  faChevronDown,
} from "@fortawesome/free-solid-svg-icons";

const FITNESS_GOALS = [
  "Weight Loss",
  "Muscle Gain",
  "Endurance",
  "Flexibility",
  "General Fitness",
  "Sports Performance",
];

// Age validation constants
// Change MINIMUM_AGE to adjust the minimum age requirement
const MINIMUM_AGE = 18; // Minimum age requirement

const schema = yup.object().shape({
  fullName: yup.string().required("Full Name is required"),
  email: yup.string().email("Invalid email").required("Email is required"),
  password: yup
    .string()
    .min(6, "Password must be at least 6 characters")
    .required("Password is required"),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("password")], "Passwords must match")
    .required("Confirm your password"),
  dob: yup.string().required("Date of Birth is required"),
  level: yup.string().required("Select your level"),
  fitnessGoals: yup.string().required("Select at least one fitness goal"),
  terms: yup.boolean().oneOf([true], "You must agree to the terms"),
});

const AthleteSignup = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(false);
  const [socialAuthState, setSocialAuthState] = useState({
    loading: false,
    provider: "",
  });

  // SDK and context hooks
  const { sdk } = useSDK({ role: "member" }); // Athletes have role "member"
  const { showToast } = useContexts();

  // Get redirect URI from URL params
  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get("redirect_uri");

  const socialIcons = [
    {
      icon: faFacebook,
      label: "Facebook",
      provider: "facebook",
      color: "#1877F2",
    },
    {
      icon: faGoogle,
      label: "Google",
      provider: "google",
      color: "#DB4437",
    },
    // {
    //   icon: faInstagram,
    //   label: "Instagram",
    //   provider: "instagram",
    //   color: "#E4405F",
    // },
    {
      icon: faLinkedin,
      label: "LinkedIn",
      provider: "linkedin",
      color: "#0A66C2",
    },
  ];

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    setError,
    clearErrors,
    watch,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      fullName: "",
      email: "",
      password: "",
      confirmPassword: "",
      dob: "",
      level: "",
      fitnessGoals: "",
      terms: false,
    },
  });

  // Watch form fields for validation
  const { dob, confirmPassword, password } = watch();

  const [selectedGoals, setSelectedGoals] = useState<string[]>([]);

  const handleToggleGoal = (goal: string) => {
    setSelectedGoals((prev) =>
      prev.includes(goal) ? prev.filter((g) => g !== goal) : [...prev, goal]
    );
  };

  const handleRemoveGoal = (goal: string) => {
    setSelectedGoals((prev) => prev.filter((g) => g !== goal));
  };

  // Real-time age validation function
  const validateAge = (dateValue: string) => {
    if (!dateValue) {
      clearErrors("dob");
      return;
    }

    const selectedDate = new Date(dateValue);
    const today = new Date();
    const age = today.getFullYear() - selectedDate.getFullYear();
    const monthDiff = today.getMonth() - selectedDate.getMonth();
    
    // Adjust age if birthday hasn't occurred this year
    const actualAge = monthDiff < 0 || (monthDiff === 0 && today.getDate() < selectedDate.getDate()) 
      ? age - 1 
      : age;
    
    // Check if date is in the future
    if (selectedDate > today) {
      setError("dob", {
        type: "manual",
        message: "Date of birth cannot be in the future.",
      });
      return;
    }
    
    // Check minimum age
    if (actualAge < MINIMUM_AGE) {
      setError("dob", {
        type: "manual",
        message: `You must be at least ${MINIMUM_AGE} years old. You are currently ${actualAge} years old.`,
      });
      return;
    }
    
    // Clear error if validation passes
    clearErrors("dob");
  };

  // Real-time password confirmation validation function
  const validateConfirmPassword = (confirmPasswordValue: string) => {
    const passwordValue = watch("password");
    
    if (!confirmPasswordValue) {
      clearErrors("confirmPassword");
      return;
    }
    
    if (confirmPasswordValue !== passwordValue) {
      setError("confirmPassword", {
        type: "manual",
        message: "Passwords do not match.",
      });
      return;
    }
    
    // Clear error if validation passes
    clearErrors("confirmPassword");
  };

  const onSubmit = async (data: yup.InferType<typeof schema>) => {
    try {
      // Age validation
      const today = new Date();
      const birthDate = new Date(data.dob);
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      
      if (age < 18) {
        showToast("You must be at least 18 years old to register.", 4000, ToastStatusEnum.ERROR);
        return;
      }

      setIsLoading(true);
      
      // Prepare all profile data to send with registration
      const profileData = {
        full_name: data.fullName,
        dob: data.dob,
        level: data.level,
        fitness_goals: selectedGoals,
        terms: data.terms,
        profile_update: true,
      };

      const result: any = await sdk.request({
        endpoint: '/v1/api/kanglink/member/lambda/register',
        method: 'POST',
        body: {
          email: data.email,
          password: data.password,
          role: "member",
          data: profileData
        }
      });

      if (!result.error) {
        showToast("Registration successful! Please check your email to verify your account.", 4000, ToastStatusEnum.SUCCESS);
        navigate('/verification-sent');
      } else {
        showToast(result.message || "Registration failed", 4000, ToastStatusEnum.ERROR);
      }
    } catch (error: any) {
      console.error("Registration error:", error);
      showToast(error?.response?.data?.message || error?.message || "Registration failed", 4000, ToastStatusEnum.ERROR);
    } finally {
      setIsLoading(false);
    }
  };

  const socialLogin = async (type: string) => {
    try {
      setSocialAuthState((prev) => {
        return {
          ...prev,
          loading: true,
          provider: type,
        };
      });
      const result = await sdk.oauthLoginApi(type, "member");
      window.open(result, "_self");
    } catch (error: any) {
      showToast(
        error?.response?.data?.message
          ? error?.response?.data?.message
          : error?.message,
        4000,
        ToastStatusEnum.ERROR
      );
    } finally {
      setSocialAuthState((prev) => {
        return {
          ...prev,
          loading: false,
        };
      });
    }
  };

  useEffect(() => {
    setValue("fitnessGoals", selectedGoals.join(","));
  }, [selectedGoals, setValue]);

  // Validate age when dob value changes
  useEffect(() => {
    if (dob) {
      validateAge(dob);
    } else {
      clearErrors("dob");
    }
  }, [dob]);

  // Validate confirm password when password or confirmPassword changes
  useEffect(() => {
    if (confirmPassword) {
      validateConfirmPassword(confirmPassword);
    }
  }, [password, confirmPassword]);

  return (
    <Container className="bg-background min-h-screen">
      <main className="flex-1 flex items-center justify-center px-4 py-8">
        <div className="w-full max-w-2xl flex flex-col items-center">
          {/* Card */}
          <div className="w-full bg-secondary border border-border shadow-lg rounded-lg px-8 py-10 sm:px-12 sm:py-14">
            {/* Title */}
            <div className="text-center mb-10">
              <h1 className="text-3xl font-bold text-text">
                Signup as Athlete
              </h1>
            </div>
            {/* Form */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
              {/* 2-column grid for large screens */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Full Name */}
                <MkdInputV2
                  name="fullName"
                  register={register}
                  errors={errors}
                  required
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className="text-text">
                      Full Name
                    </MkdInputV2.Label>
                    <MkdInputV2.Field
                      placeholder="Enter Full Name"
                      className="bg-input border-border text-text placeholder:text-text-disabled"
                    />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
                {/* Email */}
                <MkdInputV2
                  name="email"
                  type="email"
                  register={register}
                  errors={errors}
                  required
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className="text-text">
                      Email
                    </MkdInputV2.Label>
                    <MkdInputV2.Field
                      placeholder="Enter Email Address"
                      className="bg-input border-border text-text placeholder:text-text-disabled"
                    />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
                {/* Password */}
                <MkdPasswordInput
                  label="Password"
                  placeholder="Enter Password"
                  register={register}
                  errors={errors}
                  name="password"
                  inputClassName="bg-input border-border text-text placeholder:text-text-disabled"
                  labelClassName="text-text"
                  onChange={() => {
                    // Trigger confirm password validation when password changes
                    if (confirmPassword) {
                      validateConfirmPassword(confirmPassword);
                    }
                  }}
                />
                {/* Confirm Password */}
                <MkdPasswordInput
                  label="Confirm Password"
                  placeholder="Enter Password"
                  register={register}
                  errors={errors}
                  name="confirmPassword"
                  inputClassName="bg-input border-border text-text placeholder:text-text-disabled"
                  labelClassName="text-text"
                  onChange={(e) => validateConfirmPassword(e.target.value)}
                />
                {/* Date of Birth */}
                <MkdInputV2
                  name="dob"
                  type="date"
                  register={register}
                  errors={errors}
                  required
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className="text-text">
                      Date of Birth
                    </MkdInputV2.Label>
                    <div
                      className="relative cursor-pointer"
                      onClick={() => {
                        // Find the date input within this container and trigger click
                        const container = document
                          .querySelector('[name="dob"]')
                          ?.closest(".relative");
                        const input = container?.querySelector(
                          'input[type="date"]'
                        ) as HTMLInputElement;
                        if (input) {
                          input.focus();
                          input.click();
                          // Fallback for browsers that support showPicker
                          if (input.showPicker) {
                            try {
                              input.showPicker();
                            } catch (e) {
                              // Ignore errors if showPicker is not supported
                            }
                          }
                        }
                      }}
                    >
                      <MkdInputV2.Field
                        placeholder="mm/dd/yyyy"
                        className="bg-input border-border text-text placeholder:text-text-disabled pr-10 [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-inner-spin-button]:hidden [&::-webkit-outer-spin-button]:hidden cursor-pointer"
                        onChange={(e) => validateAge((e.target as HTMLInputElement).value)}
                        onBlur={(e) => validateAge((e.target as HTMLInputElement).value)}
                        onInput={(e) => validateAge((e.target as HTMLInputElement).value)}
                      />
                      <FontAwesomeIcon
                        icon={faCalendarDays}
                        className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 pointer-events-none text-icon"
                      />
                    </div>
                    <MkdInputV2.Error />
                    <p className="mt-1 text-xs text-text-disabled">
                      You must be at least {MINIMUM_AGE} years old to register
                    </p>
                  </MkdInputV2.Container>
                </MkdInputV2>
                {/* Select Level */}
                <MkdInputV2
                  name="level"
                  type="select"
                  register={register}
                  errors={errors}
                  required
                  options={["beginner", "intermediate", "expert"]}
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className="text-text">
                      Select Level
                    </MkdInputV2.Label>
                    <div className="relative">
                      <MkdInputV2.Field
                        placeholder="Choose your level"
                        className="bg-none border-border text-text placeholder:text-text-disabled pr-10"
                      />
                      <FontAwesomeIcon
                        icon={faChevronDown}
                        className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 pointer-events-none text-icon"
                      />
                    </div>
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>
              {/* Fitness Goals - Multi-select with chips */}
              <div>
                <label className="block text-text font-bold mb-2">
                  Fitness Goals
                </label>
                <div className="hidden flex-wrap gap-2 mb-2">
                  {selectedGoals.map((goal) => (
                    <span
                      key={goal}
                      className="flex items-center bg-primary text-secondary rounded-full px-3 py-1 text-xs font-medium shadow-sm"
                    >
                      {goal}
                      <button
                        type="button"
                        aria-label={`Remove ${goal}`}
                        className="ml-2 text-secondary hover:text-secondary/80 focus:outline-none"
                        onClick={() => handleRemoveGoal(goal)}
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
                <div className="flex flex-wrap gap-2">
                  {FITNESS_GOALS.map((goal) => (
                    <button
                      key={goal}
                      type="button"
                      className={`px-3 py-1 rounded-full border text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 ${
                        selectedGoals.includes(goal)
                          ? "bg-primary text-secondary border-primary"
                          : "bg-input text-text hover:bg-input/80 border-border"
                      }`}
                      aria-pressed={selectedGoals.includes(goal)}
                      onClick={() => handleToggleGoal(goal)}
                    >
                      {goal}
                    </button>
                  ))}
                </div>
                {/* Hidden input for react-hook-form */}
                <input
                  type="hidden"
                  {...register("fitnessGoals", {
                    validate: () =>
                      selectedGoals.length > 0 ||
                      "Select at least one fitness goal",
                  })}
                  value={selectedGoals.join(",")}
                />
                {errors.fitnessGoals && (
                  <p className="mt-2 text-xs text-red-500">
                    {errors.fitnessGoals.message as string}
                  </p>
                )}
                <p className="mt-2 text-xs text-text-disabled">
                  Select all fitness goals that apply to your training
                  preferences.
                </p>
              </div>
              {/* Terms and Conditions */}
              <div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...register("terms")}
                    className="rounded border-border bg-input text-primary focus:ring-primary"
                  />
                  <span className="text-sm text-text">
                    I agree to the
                    <a href="#" className="text-primary underline mx-1">
                      Terms of Use
                    </a>
                    and
                    <a href="#" className="text-primary underline mx-1">
                      Privacy Policy
                    </a>
                  </span>
                </div>
                {errors.terms && (
                  <p className="mt-2 text-xs text-red-500">
                    {errors.terms.message as string}
                  </p>
                )}
              </div>
              {/* Submit Button */}
              <InteractiveButton
                type="submit"
                loading={isLoading}
                className="w-full h-12 mt-2 rounded font-semibold text-base transition-colors bg-primary text-secondary hover:bg-primary-hover"
                disabled={isLoading}
              >
                Sign Up as Athlete
              </InteractiveButton>
              {/* Social Signup */}
              <div className="mt-10">
                <div className="text-center mb-6 text-sm text-text">
                  Sign up using your account with
                </div>
                <div className="grid grid-cols-1 gap-4">
                  {socialIcons.map((socialIcon) => (
                    <InteractiveButton
                      key={socialIcon.provider}
                      type="button"
                      onClick={() => socialLogin(socialIcon.provider)}
                       className="w-full border h-12 !text-text !border-border bg-white dark:bg-neutral-800 hover:!text-white flex items-center justify-center gap-2"
                      disabled={
                        socialAuthState.loading ||
                        socialIcon.provider == "instagram"
                      }
                      loading={
                        socialAuthState.loading &&
                        socialIcon.provider == socialAuthState.provider
                      }
                    >
                      <FontAwesomeIcon
                        icon={socialIcon.icon}
                        className="h-5 w-5"
                        style={{ color: socialIcon.color }}
                      />
                      {socialAuthState.loading &&
                      socialIcon.provider == socialAuthState.provider ? null : (
                        <span className="text-sm font-medium ">
                          {socialIcon.label}
                        </span>
                      )}
                    </InteractiveButton>
                  ))}
                </div>
              </div>
            </form>
          </div>
        </div>
      </main>
    </Container>
  );
};

export default AthleteSignup;
