# Final API Structure for Program Management

## Overview

This document provides the complete API structure for the trainer program creation and management system, including the data structures that will be sent from the frontend.

## Base URL

```
https://api.kanglink.com/v2/api/kanglink/custom/trainer
```

## Authentication

All endpoints require Bearer token authentication:

```
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

## API Endpoints

### 1. Save Program as Draft

**Endpoint:** `POST /programs/draft`

**Purpose:** Save a program as draft (can be incomplete)

**Request Body:**

```json
{
  "stepOneData": {
    "program_name": "Advanced Strength Training",
    "type_of_program": "Body building",
    "program_description": "A comprehensive strength training program...",
    "payment_plan": ["monthly", "one_time"],
    "track_progress": true,
    "allow_comments": true,
    "allow_private_messages": false,
    "target_levels": ["intermediate", "expert"],
    "split_program": 2,
    "splits": [
      {
        "title": "Upper Body Focus",
        "full_price": 99.99,
        "subscription": 19.99,
        "split_id": "split-uuid-1"
      },
      {
        "title": "Lower Body Focus",
        "full_price": 89.99,
        "subscription": 17.99,
        "split_id": "split-uuid-2"
      }
    ],
    "currency": "USD",
    "days_for_preview": 3
  },
  "stepTwoData": {
    "program_split": "split-uuid-1",
    "description": "Detailed program description...",
    "splitConfigurations": {
      "split-uuid-1": [
        {
          "id": "week-1",
          "title": "Week 1",
          "equipment_required": "Dumbbells, Barbell",
          "split_id": "split-uuid-1",
          "days": [
            {
              "id": "day-1",
              "title": "Day 1",
              "is_rest_day": false,
              "week_id": "week-1",
              "sessions": [
                {
                  "id": "session-1",
                  "title": "Upper Body Session",
                  "session_letter": "A",
                  "session_number": 1,
                  "linked_session_id": null,
                  "day_id": "day-1",
                  "exercises": [
                    {
                      "id": "exercise-1",
                      "sets": "3",
                      "reps_or_time": "8-12",
                      "reps_time_type": "reps",
                      "exercise_details": "Bench Press",
                      "rest_duration_minutes": 2,
                      "rest_duration_seconds": 30,
                      "label": "A",
                      "label_number": "1",
                      "is_linked": false,
                      "exercise_order": 1,
                      "user_id": 0,
                      "session_id": "session-1",
                      "exercise_id": "exercise-uuid-1",
                      "video_id": "video-uuid-1"
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Program saved as draft successfully",
  "data": {
    "programId": 123,
    "status": "draft",
    "createdAt": "2024-01-15T10:30:00Z",
    "validationErrors": null
  }
}
```

### 2. Publish Program

**Endpoint:** `POST /programs/publish`

**Purpose:** Create and immediately publish a program (must be complete and valid)

**Request Body:** Same as Save Draft endpoint

**Response:**

```json
{
  "success": true,
  "message": "Program published successfully",
  "data": {
    "programId": 123,
    "status": "published",
    "publishedAt": "2024-01-15T10:30:00Z",
    "programUrl": "https://kanglink.com/programs/123",
    "validationErrors": null
  }
}
```

### 3. Update Existing Program

**Endpoint:** `PUT /programs/{programId}`

**Purpose:** Update an existing program (draft or published)

**Request Body:**

```json
{
  "stepOneData": {
    /* Same structure as above */
  },
  "stepTwoData": {
    /* Same structure as above */
  },
  "status": "draft" // or "published"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Program updated successfully",
  "data": {
    "programId": 123,
    "status": "draft",
    "updatedAt": "2024-01-15T10:30:00Z",
    "validationErrors": null
  }
}
```

### 4. Get Program for Editing

**Endpoint:** `GET /programs/{programId}`

**Purpose:** Retrieve complete program data for editing

**Response:**

```json
{
  "success": true,
  "data": {
    "program": {
      "programId": 123,
      "stepOneData": {
        /* Complete step one data */
      },
      "stepTwoData": {
        /* Complete step two data */
      },
      "status": "draft",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    },
    "lastModified": "2024-01-15T10:30:00Z",
    "canEdit": true
  }
}
```

## Data Structure Details

### Exercise Instance Structure

Each exercise in the system follows this structure:

```json
{
  "id": "unique-exercise-instance-id",
  "sets": "3",
  "reps_or_time": "8-12",
  "reps_time_type": "reps", // "reps" | "time"
  "exercise_details": "Exercise name or description",
  "rest_duration_minutes": 2,
  "rest_duration_seconds": 30,
  "label": "A", // For linked exercises (A, B, C, etc.)
  "label_number": "1", // Order within label group (1, 2, 3, etc.)
  "is_linked": false,
  "exercise_order": 1, // Sequential order 1 to n
  "user_id": 0,
  "session_id": "parent-session-id",
  "exercise_id": "reference-to-exercise-library",
  "video_id": "reference-to-video-library"
}
```

### Exercise Linking System

- `label`: Groups linked exercises (A, B, C, etc.)
- `label_number`: Orders exercises within a group (1, 2, 3, etc.)
- `exercise_order`: Sequential ordering across all exercises (1 to n)
- `is_linked`: Boolean indicating if exercise is part of a linked group

### Validation Rules

1. **Step One Validation:**
   - program_name: Required, max 100 characters
   - type_of_program: Required, from predefined list
   - program_description: Required, max 500 characters
   - payment_plan: Required, array with at least one value
   - target_levels: Required, array with at least one value
   - split_program: Required, minimum 1
   - splits: Required, array matching split_program count

2. **Step Two Validation:**
   - Each split must have at least one week
   - Each week must have at least one day
   - Non-rest days must have at least one session
   - Each session must have at least one exercise

### Error Responses

```json
{
  "success": false,
  "message": "Validation failed",
  "data": {
    "validationErrors": [
      "Week 'Week 1' must have at least one day",
      "Session 'Session 1' must have at least one exercise"
    ]
  }
}
```

## Implementation Notes

1. **Database Schema:** Follow hierarchical structure with proper foreign key relationships
2. **Exercise Ordering:** Maintain exercise_order field for sequential ordering
3. **Linked Exercises:** Use label and label_number for grouping and ordering
4. **Status Management:** Support draft/published status transitions
5. **Validation:** Implement comprehensive validation on both frontend and backend
6. **Error Handling:** Provide detailed validation errors for user feedback

## Frontend Integration

The frontend sends data using the `useCustomModelQuery` hook:

```typescript
const { mutate: saveProgramMutation } = useCustomModelQuery();

saveProgramMutation({
  endpoint: "/v2/api/kanglink/custom/trainer/programs/draft",
  method: "POST",
  body: { stepOneData, stepTwoData },
});
```

This structure ensures complete data integrity and supports the full program creation workflow.

## Additional API Endpoints

### 5. Delete Program

**Endpoint:** `DELETE /programs/{programId}`

**Response:**

```json
{
  "success": true,
  "message": "Program deleted successfully",
  "data": {
    "programId": 123,
    "deletedAt": "2024-01-15T10:30:00Z"
  }
}
```

### 6. Duplicate Program

**Endpoint:** `POST /programs/{programId}/duplicate`

**Request Body:**

```json
{
  "newProgramName": "Copy of Advanced Strength Training",
  "copyPricing": true,
  "copyStructure": true
}
```

**Response:**

```json
{
  "success": true,
  "message": "Program duplicated successfully",
  "data": {
    "originalProgramId": 123,
    "newProgramId": 124,
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

### 7. Validate Program Data

**Endpoint:** `POST /programs/validate`

**Request Body:** Same as Save Draft endpoint

**Response:**

```json
{
  "success": true,
  "data": {
    "isValid": true,
    "errors": [],
    "warnings": ["Consider adding more exercises to Session 1"]
  }
}
```

## Database Schema

### Core Tables

#### programs

```sql
CREATE TABLE programs (
  id SERIAL PRIMARY KEY,
  trainer_id INTEGER NOT NULL,
  program_name VARCHAR(100) NOT NULL,
  type_of_program VARCHAR(50) NOT NULL,
  program_description TEXT NOT NULL,
  payment_plan JSON NOT NULL, -- ["monthly", "one_time"]
  track_progress BOOLEAN DEFAULT false,
  allow_comments BOOLEAN DEFAULT false,
  allow_private_messages BOOLEAN DEFAULT false,
  target_levels JSON NOT NULL, -- ["beginner", "intermediate", "expert"]
  split_program INTEGER NOT NULL,
  currency VARCHAR(3) NOT NULL DEFAULT 'USD',
  days_for_preview INTEGER NOT NULL DEFAULT 1,
  status VARCHAR(20) NOT NULL DEFAULT 'draft', -- 'draft', 'published'
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  published_at TIMESTAMP NULL,
  FOREIGN KEY (trainer_id) REFERENCES users(id)
);
```

#### program_splits

```sql
CREATE TABLE program_splits (
  id SERIAL PRIMARY KEY,
  program_id INTEGER NOT NULL,
  split_id VARCHAR(50) NOT NULL,
  title VARCHAR(100) NOT NULL,
  full_price DECIMAL(10,2),
  subscription DECIMAL(10,2),
  split_order INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE CASCADE
);
```

#### program_weeks

```sql
CREATE TABLE program_weeks (
  id SERIAL PRIMARY KEY,
  split_id INTEGER NOT NULL,
  week_id VARCHAR(50) NOT NULL,
  title VARCHAR(100) NOT NULL,
  equipment_required TEXT,
  week_order INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (split_id) REFERENCES program_splits(id) ON DELETE CASCADE
);
```

#### program_days

```sql
CREATE TABLE program_days (
  id SERIAL PRIMARY KEY,
  week_id INTEGER NOT NULL,
  day_id VARCHAR(50) NOT NULL,
  title VARCHAR(100) NOT NULL,
  is_rest_day BOOLEAN DEFAULT false,
  day_order INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (week_id) REFERENCES program_weeks(id) ON DELETE CASCADE
);
```

#### program_sessions

```sql
CREATE TABLE program_sessions (
  id SERIAL PRIMARY KEY,
  day_id INTEGER NOT NULL,
  session_id VARCHAR(50) NOT NULL,
  title VARCHAR(100) NOT NULL,
  session_letter VARCHAR(1) NOT NULL,
  session_number INTEGER NOT NULL,
  linked_session_id VARCHAR(50),
  session_order INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (day_id) REFERENCES program_days(id) ON DELETE CASCADE
);
```

#### exercise_instances

```sql
CREATE TABLE exercise_instances (
  id SERIAL PRIMARY KEY,
  session_id INTEGER NOT NULL,
  exercise_instance_id VARCHAR(50) NOT NULL,
  sets VARCHAR(20) NOT NULL,
  reps_or_time VARCHAR(20) NOT NULL,
  reps_time_type VARCHAR(10) NOT NULL, -- 'reps' or 'time'
  exercise_details TEXT NOT NULL,
  rest_duration_minutes INTEGER DEFAULT 0,
  rest_duration_seconds INTEGER DEFAULT 0,
  label VARCHAR(1), -- A, B, C for linked exercises
  label_number VARCHAR(10), -- 1, 2, 3 within label group
  is_linked BOOLEAN DEFAULT false,
  exercise_order INTEGER NOT NULL, -- Sequential 1 to n
  exercise_id VARCHAR(50), -- Reference to exercise library
  video_id VARCHAR(50), -- Reference to video library
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES program_sessions(id) ON DELETE CASCADE
);
```

### Resource Tables

#### exercises (Exercise Library)

```sql
CREATE TABLE exercises (
  id SERIAL PRIMARY KEY,
  exercise_id VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(200) NOT NULL,
  type INTEGER NOT NULL, -- 1 = system, 2 = user-created
  user_id INTEGER, -- NULL for system exercises
  category VARCHAR(100),
  muscle_groups JSON,
  equipment JSON,
  instructions TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### videos (Video Library)

```sql
CREATE TABLE videos (
  id SERIAL PRIMARY KEY,
  video_id VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(200) NOT NULL,
  type INTEGER NOT NULL, -- 1 = system, 2 = user-uploaded
  url TEXT NOT NULL,
  user_id INTEGER, -- NULL for system videos
  duration INTEGER, -- in seconds
  thumbnail_url TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## Key Implementation Points

1. **Hierarchical Deletion:** Use CASCADE deletes to maintain data integrity
2. **Order Fields:** All entities have order fields for proper sequencing
3. **UUID References:** Use VARCHAR(50) for frontend-generated UUIDs
4. **JSON Fields:** Store arrays as JSON for payment_plan, target_levels, etc.
5. **Resource Separation:** Separate exercise/video libraries from instances
6. **User Ownership:** Track user_id for custom exercises and videos
7. **Status Management:** Support draft/published workflow
8. **Timestamps:** Track creation, updates, and publication times
