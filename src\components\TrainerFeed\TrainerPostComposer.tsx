import React, { useState } from "react";
import { TrainerPostComposerProps } from "./types";
import { CreatePostFeed } from "@/interfaces/model.interface";
import { useContexts } from "@/hooks/useContexts";
import { useCreateModelMutation } from "@/query/shared";
import { Models } from "@/utils/baas";

const TrainerPostComposer: React.FC<TrainerPostComposerProps> = ({
  profile = {
    photo: "https://placehold.co/48x48",
    full_name: "Trainer Name",
  },
  content,
  on_content_change,
  on_submit,
  post_type,
  on_post_type_change,
  selected_program,
  on_post_created,
}) => {
  // const { mutateAsync: createPost } = useCustomModelQuery();
  const { mutateAsync: createPost, isPending: isCreatingPost } =
    useCreateModelMutation(Models.POST_FEED, {
      showToast: true,
    });
  const { showToast } = useContexts();

  const post_type_mapping: Record<string, string> = {
    Announcement: "announcement",
    Update: "update",
    Question: "question",
  };

  const handleSubmit = async () => {
    if (!content.trim() || !selected_program?.id || !profile?.id) {
      showToast("Please fill in all required fields", 4000, "error" as any);
      return;
    }
    // Map post type from UI to API format

    const api_post_type = post_type_mapping[post_type] || "update";

    const post_payload: CreatePostFeed = {
      user_id: profile.id,
      program_id: selected_program.id,
      post_type: api_post_type as any,
      content: content.trim(),
      is_private: false,
      visibility_scope: "program_members",
    };

    const response = await createPost(post_payload);

    if (response?.error) {
      throw new Error(response.message || "Failed to create post");
    }

    showToast("Post created successfully!", 3000, "success" as any);

    // Clear the form
    on_content_change("");

    // Call the original submit handler and post created callback
    on_submit();
    if (on_post_created) {
      on_post_created(response.data || response.model);
    }
  };

  return (
    <section className="bg-background rounded-[0.375rem] shadow-sm border border-border p-6 hover:shadow-md transition-shadow duration-200">
      <div className="flex space-x-4">
        <img
          src={profile.photo || "https://placehold.co/48x48"}
          alt="Trainer"
          className="w-12 h-12 rounded-full object-cover flex-shrink-0 ring-2 ring-border"
        />
        <div className="flex-1 space-y-4">
          <div>
            <h3 className="font-semibold text-text mb-1">
              {profile.full_name || "Trainer Name"}
            </h3>
            <p className="text-sm text-text-disabled">
              Post an update to the "
              {selected_program?.program_name || "selected program"}" feed
            </p>
          </div>

          <textarea
            value={content}
            onChange={(e) => on_content_change(e.target.value)}
            placeholder="What's on your mind?"
            rows={4}
            className="w-full px-4 py-3 border border-border rounded-xl bg-background text-text placeholder-text-disabled focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 resize-none text-[15px] leading-relaxed"
          />

          <div className="flex flex-col sm:flex-row sm:items-center justify-end gap-4">
            <div className="flex justify-end items-center space-x-6">
              <button className="flex items-center space-x-2 text-text-disabled hover:text-primary transition-colors duration-200 px-3 py-2 rounded-lg hover:bg-background-hover">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="15"
                  height="16"
                  viewBox="0 0 15 16"
                  fill="none"
                >
                  <g clipPath="url(#clip0_94_266)">
                    <path
                      d="M11.7559 2.6188C10.9934 1.8563 9.75586 1.8563 8.99336 2.6188L3.24336 8.3688C1.92773 9.68442 1.92773 11.8157 3.24336 13.1313C4.55898 14.4469 6.69023 14.4469 8.00586 13.1313L12.7559 8.3813C13.0965 8.04067 13.6527 8.04067 13.9934 8.3813C14.334 8.72192 14.334 9.27817 13.9934 9.6188L9.24336 14.3688C7.24336 16.3688 4.00586 16.3688 2.00586 14.3688C0.00585938 12.3688 0.00585938 9.1313 2.00586 7.1313L7.75586 1.3813C9.20273 -0.0655762 11.5465 -0.0655762 12.9934 1.3813C14.4402 2.82817 14.4402 5.17192 12.9934 6.6188L7.49336 12.1188C6.59961 13.0125 5.14961 13.0125 4.25586 12.1188C3.36211 11.225 3.36211 9.77505 4.25586 8.8813L8.75586 4.3813C9.09648 4.04067 9.65273 4.04067 9.99336 4.3813C10.334 4.72192 10.334 5.27817 9.99336 5.6188L5.49336 10.1188C5.28398 10.3282 5.28398 10.6719 5.49336 10.8813C5.70273 11.0907 6.04648 11.0907 6.25586 10.8813L11.7559 5.3813C12.5184 4.6188 12.5184 3.3813 11.7559 2.6188Z"
                      fill="#757B8A"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_94_266">
                      <path d="M0.375 0H14.375V16H0.375V0Z" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </button>

              <select
                value={post_type}
                onChange={(e) => on_post_type_change(e.target.value)}
                className="px-4 py-2 border border-border rounded-lg bg-background text-text text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 font-medium"
              >
                <option value="Announcement">📢 Announcement</option>
                <option value="Update">📝 Update</option>
                <option value="Question">❓ Question</option>
              </select>
            </div>

            <button
              onClick={handleSubmit}
              disabled={!content.trim() || isCreatingPost}
              className="px-8 py-3 bg-primary text-white rounded-xl hover:bg-primary-hover disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-semibold text-sm shadow-sm hover:shadow-md"
            >
              {isCreatingPost ? "Posting..." : "Post"}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrainerPostComposer;
