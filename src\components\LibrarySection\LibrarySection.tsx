import { ReactNode } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

interface LibrarySectionProps {
  title: string;
  children: ReactNode;
  gridType?: 'courses' | 'trainers' | 'programs';
  className?: string;
}

const LibrarySection = ({ 
  title, 
  children, 
  gridType = 'courses',
  className = "" 
}: LibrarySectionProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const getGridClasses = () => {
    switch (gridType) {
      case 'courses':
        return "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6";
      case 'trainers':
        return "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6";
      case 'programs':
        return "flex gap-4 overflow-x-auto scrollbar-hide pb-4";
      default:
        return "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6";
    }
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Section Title */}
      <h2
        className="text-xl font-semibold mb-6 transition-colors duration-200"
        style={{ color: THEME_COLORS[mode].TEXT }}
      >
        {title}
      </h2>

      {/* Content Grid */}
      <div className={getGridClasses()}>
        {children}
      </div>
    </div>
  );
};

export default LibrarySection;
