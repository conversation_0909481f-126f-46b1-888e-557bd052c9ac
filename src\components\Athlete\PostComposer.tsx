import { useState, useRef } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import {
  ChevronDownIcon,
  PhotoIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";

interface PostComposerProps {
  userAvatar?: string;
  userName?: string;
  placeholder?: string;
  onPost?: (
    content: string,
    isPrivate: boolean,
    isAnonymous: boolean,
    attachments?: string[]
  ) => void;
}

const PostComposer = ({
  userAvatar = "https://placehold.co/48x48",
  userName = "Athlete Name",
  placeholder = 'Post an update to the "project name" feed',
  onPost,
}: PostComposerProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const { sdk } = useSDK();
  const { showToast } = useContexts();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [content, setContent] = useState("");
  const [isPrivate, setIsPrivate] = useState(false);
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    // Limit to 4 images total
    const remainingSlots = 4 - selectedImages.length;
    const filesToAdd = files.slice(0, remainingSlots);

    if (files.length > remainingSlots) {
      showToast(
        `You can only upload ${remainingSlots} more image(s). Maximum 4 images allowed.`,
        3000,
        ToastStatusEnum.WARNING
      );
    }

    // Create preview URLs for new images
    const newPreviewUrls: string[] = [];
    filesToAdd.forEach((file) => {
      const previewUrl = URL.createObjectURL(file);
      newPreviewUrls.push(previewUrl);
    });

    setSelectedImages((prev) => [...prev, ...filesToAdd]);
    setImagePreviewUrls((prev) => [...prev, ...newPreviewUrls]);
  };

  const handleRemoveImage = (index: number) => {
    // Revoke the object URL to free memory
    URL.revokeObjectURL(imagePreviewUrls[index]);

    setSelectedImages((prev) => prev.filter((_, i) => i !== index));
    setImagePreviewUrls((prev) => prev.filter((_, i) => i !== index));
  };

  const uploadImages = async (): Promise<string[]> => {
    if (selectedImages.length === 0) return [];

    setIsUploading(true);
    const uploadedUrls: string[] = [];

    try {
      for (const image of selectedImages) {
        const formData = new FormData();
        formData.append("file", image);

        const result = await sdk?.uploadImage(formData);
        if (result && !result.error && result.url) {
          uploadedUrls.push(result.url);
        } else {
          throw new Error("Failed to upload image");
        }
      }
      return uploadedUrls;
    } catch (error) {
      console.error("Error uploading images:", error);
      showToast("Failed to upload images", 3000, ToastStatusEnum.ERROR);
      return [];
    } finally {
      setIsUploading(false);
    }
  };

  const handlePost = async () => {
    if (!content.trim() && selectedImages.length === 0) {
      showToast(
        "Please add some content or images to your post",
        3000,
        ToastStatusEnum.WARNING
      );
      return;
    }

    try {
      const uploadedImageUrls = await uploadImages();
      onPost?.(content, isPrivate, isAnonymous, uploadedImageUrls);

      // Reset form
      setContent("");
      setSelectedImages([]);
      // Clean up preview URLs
      imagePreviewUrls.forEach((url) => URL.revokeObjectURL(url));
      setImagePreviewUrls([]);
    } catch (error) {
      console.error("Error creating post:", error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
      handlePost();
    }
  };

  return (
    <div
      className="rounded-lg shadow-sm border p-4 sm:p-6 mb-6 transition-all duration-200"
      style={{
        backgroundColor: THEME_COLORS[mode].CARD_BG,
        borderColor: THEME_COLORS[mode].BORDER,
      }}
    >
      {/* User Info */}
      <div className="flex items-start gap-3 sm:gap-4 mb-4">
        <div className="flex-shrink-0">
          <img
            src={userAvatar}
            alt={userName}
            className="w-10 h-10 sm:w-12 sm:h-12 rounded-full object-cover border-2"
            style={{ borderColor: THEME_COLORS[mode].BORDER }}
          />
        </div>
        <div className="flex-1 min-w-0">
          <h3
            className="text-sm sm:text-base font-medium mb-1 transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            {userName}
          </h3>
          <p
            className="text-xs sm:text-sm transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
          >
            {placeholder}
          </p>
        </div>
      </div>

      {/* Text Area */}
      <div className="mb-4">
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          onKeyDown={handleKeyPress}
          placeholder="What's on your mind?"
          rows={4}
          className="w-full p-3 sm:p-4 rounded-lg border resize-none transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
          style={{
            backgroundColor: THEME_COLORS[mode].INPUT,
            borderColor: THEME_COLORS[mode].BORDER,
            color: THEME_COLORS[mode].TEXT,
          }}
        />
      </div>

      {/* Image Previews */}
      {imagePreviewUrls.length > 0 && (
        <div className="mb-4">
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
            {imagePreviewUrls.map((url, index) => (
              <div key={index} className="relative group">
                <img
                  src={url}
                  alt={`Preview ${index + 1}`}
                  className="w-full h-20 sm:h-24 object-cover rounded-lg border"
                  style={{ borderColor: THEME_COLORS[mode].BORDER }}
                />
                <button
                  type="button"
                  onClick={() => handleRemoveImage(index)}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600"
                >
                  <XMarkIcon className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        {/* Left Controls */}
        <div className="flex items-center gap-3">
          {/* Anonymous Toggle */}
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              checked={isAnonymous}
              onChange={(e) => setIsAnonymous(e.target.checked)}
              className="w-3 h-3 rounded border transition-colors duration-200"
              style={{
                backgroundColor: isAnonymous
                  ? THEME_COLORS[mode].PRIMARY
                  : THEME_COLORS[mode].INPUT,
                borderColor: THEME_COLORS[mode].BORDER,
              }}
            />
            <span
              className="text-xs sm:text-sm transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              Anonymous
            </span>
          </label>

          {/* Attachment Button */}
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            disabled={selectedImages.length >= 4}
            className="p-2 rounded-lg transition-colors duration-200 hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed"
            style={{
              backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
              color: THEME_COLORS[mode].TEXT_SECONDARY,
            }}
            title={
              selectedImages.length >= 4
                ? "Maximum 4 images allowed"
                : "Add images"
            }
          >
            <PhotoIcon className="w-4 h-4" />
          </button>

          {/* Hidden File Input */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple
            onChange={handleImageSelect}
            className="hidden"
          />
        </div>

        {/* Right Controls */}
        <div className="flex items-center gap-2 sm:gap-3">
          {/* Privacy Dropdown */}
          <div className="relative">
            <button
              type="button"
              onClick={() => setIsPrivate(!isPrivate)}
              className="flex items-center gap-2 px-3 py-2 rounded-lg border text-xs sm:text-sm transition-all duration-200 hover:opacity-80"
              style={{
                backgroundColor: THEME_COLORS[mode].INPUT,
                borderColor: THEME_COLORS[mode].BORDER,
                color: THEME_COLORS[mode].TEXT_SECONDARY,
              }}
            >
              <span>{isPrivate ? "Private" : "Public"}</span>
              <ChevronDownIcon className="w-3 h-3" />
            </button>
          </div>

          {/* Post Button */}
          <button
            onClick={handlePost}
            disabled={
              (!content.trim() && selectedImages.length === 0) || isUploading
            }
            className="px-4 sm:px-6 py-2 rounded-lg text-xs sm:text-sm font-semibold text-white transition-all duration-200 hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            style={{
              backgroundColor:
                (content.trim() || selectedImages.length > 0) && !isUploading
                  ? THEME_COLORS[mode].PRIMARY
                  : THEME_COLORS[mode].PRIMARY + "50",
            }}
          >
            {isUploading && (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            )}
            {isUploading ? "Posting..." : "Post"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PostComposer;
