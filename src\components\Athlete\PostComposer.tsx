import { useState } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { ChevronDownIcon, PhotoIcon } from "@heroicons/react/24/outline";

interface PostComposerProps {
  userAvatar?: string;
  userName?: string;
  placeholder?: string;
  onPost?: (content: string, isPrivate: boolean, isAnonymous: boolean) => void;
}

const PostComposer = ({
  userAvatar = "https://placehold.co/48x48",
  userName = "Athlete Name",
  placeholder = 'Post an update to the "project name" feed',
  onPost,
}: PostComposerProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [content, setContent] = useState("");
  const [isPrivate, setIsPrivate] = useState(false);
  const [isAnonymous, setIsAnonymous] = useState(false);

  const handlePost = () => {
    if (content.trim()) {
      onPost?.(content, isPrivate, isAnonymous);
      setContent("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
      handlePost();
    }
  };

  return (
    <div
      className="rounded-lg shadow-sm border p-4 sm:p-6 mb-6 transition-all duration-200"
      style={{
        backgroundColor: THEME_COLORS[mode].CARD_BG,
        borderColor: THEME_COLORS[mode].BORDER,
      }}
    >
      {/* User Info */}
      <div className="flex items-start gap-3 sm:gap-4 mb-4">
        <div className="flex-shrink-0">
          <img
            src={userAvatar}
            alt={userName}
            className="w-10 h-10 sm:w-12 sm:h-12 rounded-full object-cover border-2"
            style={{ borderColor: THEME_COLORS[mode].BORDER }}
          />
        </div>
        <div className="flex-1 min-w-0">
          <h3
            className="text-sm sm:text-base font-medium mb-1 transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            {userName}
          </h3>
          <p
            className="text-xs sm:text-sm transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
          >
            {placeholder}
          </p>
        </div>
      </div>

      {/* Text Area */}
      <div className="mb-4">
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          onKeyDown={handleKeyPress}
          placeholder="What's on your mind?"
          rows={4}
          className="w-full p-3 sm:p-4 rounded-lg border resize-none transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
          style={{
            backgroundColor: THEME_COLORS[mode].INPUT,
            borderColor: THEME_COLORS[mode].BORDER,
            color: THEME_COLORS[mode].TEXT,
          }}
        />
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        {/* Left Controls */}
        <div className="flex items-center gap-3">
          {/* Anonymous Toggle */}
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              checked={isAnonymous}
              onChange={(e) => setIsAnonymous(e.target.checked)}
              className="w-3 h-3 rounded border transition-colors duration-200"
              style={{
                backgroundColor: isAnonymous
                  ? THEME_COLORS[mode].PRIMARY
                  : THEME_COLORS[mode].INPUT,
                borderColor: THEME_COLORS[mode].BORDER,
              }}
            />
            <span
              className="text-xs sm:text-sm transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              Anonymous
            </span>
          </label>

          {/* Attachment Button */}
          <button
            type="button"
            className="p-2 rounded-lg transition-colors duration-200 hover:opacity-80"
            style={{
              backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
              color: THEME_COLORS[mode].TEXT_SECONDARY,
            }}
          >
            <PhotoIcon className="w-4 h-4" />
          </button>
        </div>

        {/* Right Controls */}
        <div className="flex items-center gap-2 sm:gap-3">
          {/* Privacy Dropdown */}
          <div className="relative">
            <button
              type="button"
              onClick={() => setIsPrivate(!isPrivate)}
              className="flex items-center gap-2 px-3 py-2 rounded-lg border text-xs sm:text-sm transition-all duration-200 hover:opacity-80"
              style={{
                backgroundColor: THEME_COLORS[mode].INPUT,
                borderColor: THEME_COLORS[mode].BORDER,
                color: THEME_COLORS[mode].TEXT_SECONDARY,
              }}
            >
              <span>{isPrivate ? "Private" : "Public"}</span>
              <ChevronDownIcon className="w-3 h-3" />
            </button>
          </div>

          {/* Post Button */}
          <button
            onClick={handlePost}
            disabled={!content.trim()}
            className="px-4 sm:px-6 py-2 rounded-lg text-xs sm:text-sm font-semibold text-white transition-all duration-200 hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
            style={{
              backgroundColor: content.trim()
                ? THEME_COLORS[mode].PRIMARY
                : THEME_COLORS[mode].PRIMARY + "50",
            }}
          >
            Post
          </button>
        </div>
      </div>
    </div>
  );
};

export default PostComposer;
