import React from "react";
import { useTransactionStats } from "@/hooks/useTrainerTransactions";

interface TransactionStatsCardsProps {
  onRefresh?: () => void;
}

const TransactionStatsCards: React.FC<TransactionStatsCardsProps> = ({
  onRefresh,
}) => {
  const { stats, loading, error, refetch } = useTransactionStats();

  const handleRefresh = () => {
    refetch();
    onRefresh?.();
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, index) => (
          <div
            key={index}
            className="rounded-lg shadow-sm border border-border bg-secondary p-6 flex flex-col gap-4 dark:bg-neutral-800 dark:border-[#3a3a3a] animate-pulse"
          >
            <div className="flex flex-col gap-2">
              <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-20"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg shadow-sm border border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="text-red-600 dark:text-red-400">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <span className="ml-2 text-sm text-red-600 dark:text-red-400">
              Failed to load transaction stats
            </span>
          </div>
          <button
            onClick={handleRefresh}
            className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 font-medium"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const statsData = [
    {
      title: "Total Earned",
      value: formatCurrency(stats.total_earnings, stats.currency),
      description: "All processed commissions",
      color: "text-green-600 dark:text-green-400",
    },
    {
      title: "Pending Payout",
      value: formatCurrency(stats.pending_payouts, stats.currency),
      description: `Funds waiting for ${stats.payout_time_hours}h payout delay`,
      color: "text-yellow-600 dark:text-yellow-400",
    },
    {
      title: "Available to Withdraw",
      value: formatCurrency(stats.available_to_withdraw, stats.currency),
      description: "Ready for withdrawal",
      color: "text-blue-600 dark:text-blue-400",
    },
    {
      title: "Withdrawn",
      value: formatCurrency(stats.withdrawn, stats.currency),
      description: "Already processed payouts",
      color: "text-gray-600 dark:text-gray-400",
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      {statsData.map((stat, index) => (
        <div
          key={index}
          className="rounded-lg shadow-sm border border-border bg-secondary p-6 flex flex-col gap-4 dark:bg-neutral-800 dark:border-[#3a3a3a] hover:shadow-md transition-shadow duration-200"
        >
          <div className="flex flex-col gap-2">
            <span className={`text-3xl font-bold ${stat.color}`}>
              {stat.value}
            </span>
            <div className="flex flex-col gap-1">
              <span className="text-sm font-medium text-text dark:text-gray-100">
                {stat.title}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {stat.description}
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default TransactionStatsCards;
