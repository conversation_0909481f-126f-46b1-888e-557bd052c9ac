import { useState, useEffect } from 'react';
import MkdSDK from '@/utils/MkdSDK';
import { useCustomModelQuery } from '@/query/shared';

interface Video {
  id: number;
  name: string;
  url: string;
  video_type: string;
  video_category: number;
}

interface Exercise {
  id: number;
  exercise_name: string;
  exercise_type: number;
  exercise_category: number;
  sets: string;
  reps_or_time: string;
  reps_time_type: string;
  exercise_details: string;
  rest_duration_minutes: number;
  rest_duration_seconds: number;
  label: string;
  label_number: string;
  is_linked: boolean;
  exercise_order: number;
  video: Video | null;
}

interface Session {
  id: number;
  title: string;
  session_order: number;
  exercises_count: number;
  created_at: string;
  updated_at: string;
  exercises: Exercise[];
}

interface Day {
  id: number;
  title: string;
  is_rest_day: boolean;
  day_order: number;
  sessions_count: number;
  created_at: string;
  updated_at: string;
  sessions: Session[];
}

interface Week {
  id: number;
  title: string;
  week_order: number;
  days_count: number;
  created_at: string;
  updated_at: string;
  days: Day[];
}

interface Trainer {
  id: number;
  full_name: string;
  first_name: string;
  last_name: string;
  photo: string;
  email: string;
}

interface Program {
  id: number;
  name: string;
  description: string;
  type: string;
  currency: string;
  image_url: string;
  status: string;
  approval_date: string;
  created_at: string;
  updated_at: string;
  trainer: Trainer;
}

interface Split {
  id: number;
  title: string;
  equipment_required: string;
  full_price: number;
  subscription: number;
  created_at: string;
  updated_at: string;
  program: Program;
  weeks: Week[];
}

interface ProgramPreviewData {
  split: Split;
}

interface UseProgramPreviewReturn {
  data: ProgramPreviewData | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useProgramPreview = (splitId: string | number): UseProgramPreviewReturn => {
  const [data, setData] = useState<ProgramPreviewData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { mutateAsync: customModelQuery } = useCustomModelQuery()

  const fetchProgramPreview = async () => {
    if (!splitId) {
      setError('Split ID is required');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await customModelQuery({
        endpoint: `/v2/api/kanglink/custom/preview/program/${splitId}`,
        method: 'GET',
      });

      if (response.error) {
        setError(response.message || 'Failed to fetch program preview');
      } else {
        setData(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProgramPreview();
  }, [splitId]);

  const refetch = () => {
    fetchProgramPreview();
  };

  return { data, loading, error, refetch };
}; 