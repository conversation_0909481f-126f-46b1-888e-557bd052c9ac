import React from "react";
import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

interface ProgramPreviewSkeletonProps {
  /** Custom className for the container */
  className?: string;
  /** Number of weeks to show in program structure */
  weeksCount?: number;
  /** Number of days per week to show */
  daysPerWeek?: number;
}

const ProgramPreviewSkeleton: React.FC<ProgramPreviewSkeletonProps> = ({
  className = "",
  weeksCount = 3,
  daysPerWeek = 4,
}) => {
  const { state } = useTheme();
  const mode = state?.theme || "light";

  return (
    <div
      className={`max-h-full h-full min-h-full overflow-hidden py-5 flex flex-col ${className}`}
    >
      <SkeletonTheme
        baseColor={THEME_COLORS[mode].TEXT_DISABLED}
        highlightColor={THEME_COLORS[mode].BACKGROUND_HOVER}
      >
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex-1 overflow-auto">
          {/* Header Skeleton */}
          <div className="mb-4 sm:mb-6 flex-shrink-0">
            <Skeleton height={32} width="60%" className="mb-2" />
          </div>

          {/* Main Content Card Skeleton */}
          <div className="bg-background rounded-lg border border-border shadow-sm overflow-hidden flex-shrink-0">
            <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
              {/* Course Details Section Skeleton */}

              {/* Program Structure Section Skeleton */}
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                  <Skeleton height={20} width="25%" />
                  <Skeleton height={32} width="150px" />
                </div>

                {/* Weeks Structure Skeleton - Reduced for viewport fit */}
                <div className="space-y-3">
                  {Array.from({ length: Math.min(weeksCount, 1) }).map(
                    (_, weekIndex) => (
                      <div
                        key={weekIndex}
                        className="border border-border rounded-lg"
                      >
                        {/* Week Header */}
                        <div className="p-3 bg-background-secondary border-b border-border">
                          <div className="flex items-center justify-between">
                            <Skeleton height={16} width="30%" />
                            <Skeleton height={12} width={12} circle />
                          </div>
                        </div>

                        {/* Days in Week - Reduced */}
                        <div className="p-3 space-y-2">
                          {Array.from({ length: Math.min(daysPerWeek, 2) }).map(
                            (_, dayIndex) => (
                              <div
                                key={dayIndex}
                                className="border border-border rounded-md"
                              >
                                {/* Day Header */}
                                <div className="p-2 bg-input border-b border-border">
                                  <div className="flex items-center justify-between">
                                    <Skeleton height={14} width="25%" />
                                    <Skeleton height={10} width={10} circle />
                                  </div>
                                </div>

                                {/* Sessions in Day - Reduced */}
                                <div className="p-2 space-y-1">
                                  {Array.from({ length: 1 }).map(
                                    (_, sessionIndex) => (
                                      <div
                                        key={sessionIndex}
                                        className="border border-border rounded"
                                      >
                                        {/* Session Header */}
                                        <div className="p-2 bg-background-hover border-b border-border">
                                          <div className="flex items-center justify-between">
                                            <Skeleton height={12} width="20%" />
                                            <Skeleton
                                              height={8}
                                              width={8}
                                              circle
                                            />
                                          </div>
                                        </div>

                                        {/* Exercises in Session - Reduced */}
                                        <div className="p-2 space-y-1">
                                          {Array.from({ length: 2 }).map(
                                            (_, exerciseIndex) => (
                                              <div
                                                key={exerciseIndex}
                                                className="flex items-center gap-2 p-1 bg-background rounded"
                                              >
                                                <Skeleton
                                                  height={24}
                                                  width={24}
                                                />
                                                <div className="flex-1 space-y-1">
                                                  <Skeleton
                                                    height={10}
                                                    width="60%"
                                                  />
                                                  <Skeleton
                                                    height={8}
                                                    width="40%"
                                                  />
                                                </div>
                                                <Skeleton
                                                  height={8}
                                                  width={8}
                                                  circle
                                                />
                                              </div>
                                            )
                                          )}
                                        </div>
                                      </div>
                                    )
                                  )}
                                </div>
                              </div>
                            )
                          )}
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </SkeletonTheme>
    </div>
  );
};

export default ProgramPreviewSkeleton;
// {/* Action Buttons Skeleton */}
// <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 justify-end mt-4 sm:mt-6 flex-shrink-0 px-4 sm:px-6 lg:px-8 pb-4">
//   <Skeleton height={32} width={80} />
//   <Skeleton height={32} width={80} />
// </div>

// <div className="space-y-4">
//                 <Skeleton height={24} width="30%" className="mb-3" />

//                 {/* Two-column layout for course details */}
//                 <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-8">
//                   {/* Left Column */}
//                   <div className="space-y-3">
//                     {Array.from({ length: 3 }).map((_, index) => (
//                       <div
//                         key={index}
//                         className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1"
//                       >
//                         <Skeleton height={16} width="40%" />
//                         <Skeleton height={16} width="50%" />
//                       </div>
//                     ))}
//                   </div>

//                   {/* Right Column */}
//                   <div className="space-y-3">
//                     {Array.from({ length: 3 }).map((_, index) => (
//                       <div
//                         key={index}
//                         className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1"
//                       >
//                         <Skeleton height={16} width="35%" />
//                         <Skeleton height={16} width="45%" />
//                       </div>
//                     ))}
//                   </div>
//                 </div>
//               </div>
