# Admin Dashboard API Documentation

## Overview

This document provides comprehensive API specifications for the admin dashboard system. The dashboard provides overview statistics, user management, content moderation, and refund request management functionality.

## Base URL

```
https://api.kanglink.com/v2/api/kanglink/custom/admin
```

## Authentication

All endpoints require Bearer token authentication with admin or super_admin role:

```
Authorization: Bearer {jwt_token}
```

---

## API Endpoints

### 1. Get Dashboard Statistics

**Endpoint:** `GET /dashboard/stats`

**Purpose:** Retrieve overview statistics for the admin dashboard

**Query Parameters:**

- `period` (string, optional, default: "30d") - Time period for statistics ("7d", "30d", "90d", "1y")
- `timezone` (string, optional) - Timezone for date calculations (e.g., "America/New_York")

**Response:**

```json
{
  "success": true,
  "data": {
    "athletes": {
      "total": 120,
      "newThisMonth": 15,
      "activeThisMonth": 98,
      "growthPercentage": 12.5
    },
    "trainers": {
      "total": 60,
      "newThisMonth": 5,
      "activeThisMonth": 52,
      "growthPercentage": 8.3,
      "pendingApproval": 3
    },
    "programs": {
      "total": 245,
      "pendingApproval": 12,
      "publishedThisMonth": 18,
      "rejectedThisMonth": 2
    },
    "refunds": {
      "pendingRequests": 5,
      "processedThisMonth": 23,
      "totalAmountPending": 1250.0,
      "averageProcessingTime": "2.5 days"
    },
    "revenue": {
      "totalThisMonth": 45600.0,
      "totalLastMonth": 42300.0,
      "growthPercentage": 7.8,
      "currency": "USD"
    },
    "lastUpdated": "2024-01-15T10:30:00Z"
  }
}
```

---

### 2. Get Dashboard Alerts

**Endpoint:** `GET /dashboard/alerts`

**Purpose:** Retrieve system alerts and notifications for admin attention

**Query Parameters:**

- `priority` (string, optional) - Filter by priority ("high", "medium", "low")
- `type` (string, optional) - Filter by alert type ("user_flagged", "program_approval", "refund_request", "system")
- `limit` (number, optional, default: 10) - Number of alerts to return

**Response:**

```json
{
  "success": true,
  "data": {
    "alerts": [
      {
        "id": "alert_123",
        "type": "program_approval",
        "priority": "medium",
        "title": "Program Approval Pending",
        "message": "5 new program approval pending",
        "count": 5,
        "actionUrl": "/admin/programs?status=pending",
        "createdAt": "2024-01-15T09:00:00Z",
        "isRead": false
      },
      {
        "id": "alert_124",
        "type": "user_flagged",
        "priority": "high",
        "title": "Low-Rated Trainers",
        "message": "2 new Trainer with 1 Star rating",
        "count": 2,
        "actionUrl": "/admin/trainers?rating=1",
        "createdAt": "2024-01-15T08:30:00Z",
        "isRead": false
      }
    ],
    "totalUnread": 7,
    "lastChecked": "2024-01-15T10:30:00Z"
  }
}
```

---

### 3. Get Athletes List

**Endpoint:** `GET /athletes`

**Purpose:** Retrieve paginated list of athletes for management

**Query Parameters:**

- `page` (number, optional, default: 1) - Page number
- `limit` (number, optional, default: 20) - Items per page
- `search` (string, optional) - Search by name or email
- `status` (string, optional) - Filter by status ("active", "inactive", "suspended")
- `joinedAfter` (string, optional) - Filter by join date (ISO date)
- `sortBy` (string, optional, default: "createdAt") - Sort field
- `sortOrder` (string, optional, default: "desc") - Sort order ("asc", "desc")

**Response:**

```json
{
  "success": true,
  "data": {
    "athletes": [
      {
        "id": "athlete_123",
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "profileImage": "https://cdn.example.com/profiles/athlete_123.jpg",
        "status": "active",
        "joinedAt": "2024-01-10T14:30:00Z",
        "lastActiveAt": "2024-01-15T09:45:00Z",
        "programsEnrolled": 3,
        "totalSpent": 299.99,
        "flaggedReports": 0,
        "verificationStatus": "verified"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 120,
      "totalPages": 6,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

---

### 4. Get Trainers List

**Endpoint:** `GET /trainers`

**Purpose:** Retrieve paginated list of trainers for management

**Query Parameters:**

- `page` (number, optional, default: 1) - Page number
- `limit` (number, optional, default: 20) - Items per page
- `search` (string, optional) - Search by name or email
- `status` (string, optional) - Filter by status ("active", "pending", "suspended", "rejected")
- `rating` (number, optional) - Filter by minimum rating (1-5)
- `sortBy` (string, optional, default: "createdAt") - Sort field
- `sortOrder` (string, optional, default: "desc") - Sort order ("asc", "desc")

**Response:**

```json
{
  "success": true,
  "data": {
    "trainers": [
      {
        "id": "trainer_456",
        "firstName": "Jane",
        "lastName": "Smith",
        "email": "<EMAIL>",
        "profileImage": "https://cdn.example.com/profiles/trainer_456.jpg",
        "status": "active",
        "joinedAt": "2023-12-15T10:00:00Z",
        "lastActiveAt": "2024-01-15T11:20:00Z",
        "programsCreated": 8,
        "totalEarnings": 5600.0,
        "averageRating": 4.7,
        "totalReviews": 45,
        "verificationStatus": "verified",
        "specializations": ["Strength Training", "Weight Loss"]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 60,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

---

### 5. Get Programs List (Content Moderation)

**Endpoint:** `GET /programs`

**Purpose:** Retrieve programs for content moderation and approval

**Query Parameters:**

- `page` (number, optional, default: 1) - Page number
- `limit` (number, optional, default: 20) - Items per page
- `status` (string, optional) - Filter by status ("pending", "approved", "rejected", "published")
- `trainerId` (string, optional) - Filter by trainer ID
- `search` (string, optional) - Search by program name
- `submittedAfter` (string, optional) - Filter by submission date (ISO date)
- `sortBy` (string, optional, default: "submittedAt") - Sort field
- `sortOrder` (string, optional, default: "desc") - Sort order ("asc", "desc")

**Response:**

```json
{
  "success": true,
  "data": {
    "programs": [
      {
        "id": "program_789",
        "name": "Advanced Strength Training",
        "description": "A comprehensive strength training program...",
        "trainer": {
          "id": "trainer_456",
          "firstName": "Jane",
          "lastName": "Smith",
          "email": "<EMAIL>"
        },
        "status": "pending",
        "submittedAt": "2024-01-14T16:30:00Z",
        "reviewedAt": null,
        "reviewedBy": null,
        "category": "Strength Training",
        "duration": "12 weeks",
        "difficulty": "Advanced",
        "price": {
          "oneTime": 199.99,
          "monthly": 29.99
        },
        "flaggedContent": [],
        "moderationNotes": ""
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 245,
      "totalPages": 13,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

---

### 6. Get Refund Requests

**Endpoint:** `GET /refunds`

**Purpose:** Retrieve refund requests for processing

**Query Parameters:**

- `page` (number, optional, default: 1) - Page number
- `limit` (number, optional, default: 20) - Items per page
- `status` (string, optional) - Filter by status ("pending", "approved", "rejected", "processed")
- `requestedAfter` (string, optional) - Filter by request date (ISO date)
- `amountMin` (number, optional) - Filter by minimum amount
- `amountMax` (number, optional) - Filter by maximum amount
- `sortBy` (string, optional, default: "requestedAt") - Sort field
- `sortOrder` (string, optional, default: "desc") - Sort order ("asc", "desc")

**Response:**

```json
{
  "success": true,
  "data": {
    "refunds": [
      {
        "id": "refund_101",
        "athlete": {
          "id": "athlete_123",
          "firstName": "John",
          "lastName": "Doe",
          "email": "<EMAIL>"
        },
        "program": {
          "id": "program_789",
          "name": "Advanced Strength Training",
          "trainer": {
            "id": "trainer_456",
            "firstName": "Jane",
            "lastName": "Smith"
          }
        },
        "transaction": {
          "id": "txn_555",
          "amount": 199.99,
          "currency": "USD",
          "paymentMethod": "credit_card",
          "processedAt": "2024-01-10T14:30:00Z"
        },
        "refundAmount": 199.99,
        "reason": "Not satisfied with program content",
        "status": "pending",
        "requestedAt": "2024-01-14T10:15:00Z",
        "reviewedAt": null,
        "reviewedBy": null,
        "adminNotes": "",
        "eligibilityCheck": {
          "isEligible": true,
          "daysFromPurchase": 4,
          "refundPolicy": "7-day refund policy",
          "programProgress": "15%"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 28,
      "totalPages": 2,
      "hasNext": true,
      "hasPrev": false
    },
    "summary": {
      "totalPendingAmount": 1250.0,
      "averageProcessingTime": "2.5 days",
      "approvalRate": 85.5
    }
  }
}
```

---

### 7. Approve/Reject Program

**Endpoint:** `POST /programs/{programId}/review`

**Purpose:** Approve or reject a program submission

**Path Parameters:**

- `programId` (string) - Program ID to review

**Request Body:**

```json
{
  "action": "approve", // "approve" or "reject"
  "notes": "Program meets all quality standards",
  "flaggedContent": [], // Array of content issues if rejecting
  "requiresChanges": false, // If true, program goes back to trainer for edits
  "publicationDate": "2024-01-16T00:00:00Z" // Optional: schedule publication
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "programId": "program_789",
    "status": "approved",
    "reviewedAt": "2024-01-15T11:30:00Z",
    "reviewedBy": "admin_001",
    "notes": "Program meets all quality standards",
    "notificationSent": true
  }
}
```

---

### 8. Process Refund Request

**Endpoint:** `POST /refunds/{refundId}/process`

**Purpose:** Approve or reject a refund request

**Path Parameters:**

- `refundId` (string) - Refund request ID to process

**Request Body:**

```json
{
  "action": "approve", // "approve" or "reject"
  "refundAmount": 199.99, // Can be partial refund
  "adminNotes": "Approved as per 7-day refund policy",
  "refundMethod": "original_payment", // "original_payment" or "store_credit"
  "processingFee": 0.0 // Optional processing fee to deduct
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "refundId": "refund_101",
    "status": "approved",
    "refundAmount": 199.99,
    "processingFee": 0.0,
    "netRefund": 199.99,
    "processedAt": "2024-01-15T11:45:00Z",
    "processedBy": "admin_001",
    "estimatedRefundDate": "2024-01-17T00:00:00Z",
    "transactionId": "refund_txn_777"
  }
}
```

---

### 9. Update User Status

**Endpoint:** `POST /users/{userId}/status`

**Purpose:** Update user status (suspend, activate, etc.)

**Path Parameters:**

- `userId` (string) - User ID to update

**Request Body:**

```json
{
  "status": "suspended", // "active", "suspended", "banned"
  "reason": "Violation of terms of service",
  "duration": "30d", // Optional: suspension duration
  "notifyUser": true,
  "adminNotes": "Multiple reports of inappropriate behavior"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "userId": "user_123",
    "previousStatus": "active",
    "newStatus": "suspended",
    "updatedAt": "2024-01-15T12:00:00Z",
    "updatedBy": "admin_001",
    "suspensionEndsAt": "2024-02-14T12:00:00Z",
    "notificationSent": true
  }
}
```
