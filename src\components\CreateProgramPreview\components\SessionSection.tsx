import React from "react";
import { ChevronUpIcon } from "@/assets/svgs";
import { ExerciseCard } from "./index";

interface SessionSectionProps {
  session: any;
  sessionIndex: number;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  collapsedStates: { [key: string]: boolean };
  onToggleItemCollapse: (id: string) => void;
}

const SessionSection: React.FC<SessionSectionProps> = ({
  session,
  sessionIndex,
  isCollapsed,
  onToggleCollapse,
  collapsedStates,
  onToggleItemCollapse,
}) => {
  // Generate exercise labels using label and label_number fields
  const generateExerciseLabel = (exercise: any) => {
    // Use the label and label_number fields from the exercise data
    if (exercise.label && exercise.label_number) {
      return `${exercise.label}${exercise.label_number}`;
    }

    // Fallback to exercise_order if label fields are not available
    if (exercise.exercise_order) {
      const baseLabel = String.fromCharCode(
        64 + Math.ceil(exercise.exercise_order / 3)
      ); // A, B, C, etc.
      const labelNumber = ((exercise.exercise_order - 1) % 3) + 1;
      return exercise.is_linked ? `${baseLabel}${labelNumber}` : baseLabel;
    }

    // Final fallback to simple alphabetical ordering
    return "A";
  };

  // Render link connector between exercises
  const renderLinkConnector = (exerciseIndex: number, exercises: any[]) => {
    const currentExercise = exercises[exerciseIndex];
    const nextExercise = exercises[exerciseIndex + 1];

    // Check if current and next exercises are linked using snake_case fields
    const isLinked =
      currentExercise?.linked_exercise_id === nextExercise?.id ||
      nextExercise?.linked_exercise_id === currentExercise?.id ||
      (currentExercise?.label &&
        nextExercise?.label &&
        currentExercise.label === nextExercise.label);

    if (!isLinked || exerciseIndex === exercises.length - 1) {
      return null;
    }

    return (
      <div className="flex justify-center py-3">
        <div className="flex items-center">
          <div className="w-0.5 h-3 bg-border"></div>
          <div className="w-9 h-10 bg-background border border-border rounded-full flex items-center justify-center">
            <div className="w-5 h-4 flex items-center justify-center">
              <div className="w-5 h-3.5 bg-primary rounded-sm"></div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Session Header */}
      <div className="flex items-center justify-between">
        <button
          onClick={onToggleCollapse}
          className="flex items-center gap-2 text-base text-text hover:text-text-hover"
        >
          <span>{session.name || `Session ${sessionIndex + 1}`}</span>
        </button>

        <button
          onClick={onToggleCollapse}
          className="w-4 h-4 flex items-center justify-center text-text-secondary hover:text-text"
        >
          <ChevronUpIcon
            className={`w-4 h-4 transition-transform ${
              isCollapsed ? "rotate-180" : ""
            }`}
            stroke="currentColor"
          />
        </button>
      </div>

      {/* Session Content */}
      {!isCollapsed && (
        <div className="ml-4 space-y-4">
          {session.exercises && session.exercises.length > 0 ? (
            session.exercises.map((exercise: any, exerciseIndex: number) => (
              <React.Fragment key={exercise.id || `exercise-${exerciseIndex}`}>
                <ExerciseCard
                  exercise={exercise}
                  exerciseLabel={generateExerciseLabel(exercise)}
                  isCollapsed={
                    collapsedStates[`exercise-${exercise.id}`] || false
                  }
                  onToggleCollapse={() =>
                    onToggleItemCollapse(`exercise-${exercise.id}`)
                  }
                />

                {/* Link connector between exercises */}
                {renderLinkConnector(exerciseIndex, session.exercises)}
              </React.Fragment>
            ))
          ) : (
            <div className="text-text-secondary text-sm">
              No exercises defined for this session.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SessionSection;
