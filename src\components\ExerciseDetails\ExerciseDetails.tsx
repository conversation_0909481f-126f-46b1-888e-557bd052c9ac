import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { InformationCircleIcon } from "@heroicons/react/24/solid";

interface ExerciseDetailsProps {
  description: string;
  muscleGroups: string[];
}

const ExerciseDetails = ({ description, muscleGroups }: ExerciseDetailsProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const sectionStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  const tagStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderColor: THEME_COLORS[mode].BORDER,
    color: THEME_COLORS[mode].TEXT,
  };

  return (
    <div 
      className="bg-background-secondary border border-border rounded-md p-4 mb-4 transition-colors duration-200"
      style={sectionStyles}
    >
      {/* Header */}
      <div className="flex items-center gap-3 mb-3">
        <InformationCircleIcon className="w-4 h-4 text-primary flex-shrink-0" />
        <h4 className="text-base font-semibold text-text">
          Exercise Details
        </h4>
      </div>

      {/* Description */}
      <p className="text-base text-text-secondary leading-relaxed mb-4">
        {description}
      </p>

      {/* Muscle Groups */}
      <div className="flex flex-wrap gap-2">
        {muscleGroups.map((muscle, index) => (
          <span
            key={index}
            className="px-3 py-1 rounded-md text-sm font-normal border transition-colors duration-200"
            style={tagStyles}
          >
            {muscle}
          </span>
        ))}
      </div>
    </div>
  );
};

export default ExerciseDetails;
