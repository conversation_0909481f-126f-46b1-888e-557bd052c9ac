# Instagram OAuth Troubleshooting Guide

## Error: "Invalid Request: Request parameters are invalid: Invalid platform app"

This error occurs when there are issues with your Instagram app configuration or OAuth request parameters. Here are the most common causes and solutions:

## 1. Instagram App Type Issues

### Problem
Instagram has different app types, and not all support OAuth login:
- **Instagram Basic Display** - For personal use, accessing your own Instagram data
- **Instagram Graph API** - For business use, requires Facebook Business verification
- **Legacy Instagram API** - Deprecated

### Solution
Make sure you're using the correct Instagram app type:

1. **For Personal/Testing Use**: Use Instagram Basic Display API
   - Go to [Facebook Developers](https://developers.facebook.com/)
   - Create a new app → Select "Consumer" → Add "Instagram Basic Display" product

2. **For Business Use**: Use Instagram Graph API (requires business verification)
   - Create a Facebook app → Add "Instagram Graph API" product
   - Complete business verification process

## 2. App Configuration Issues

### Required Settings for Instagram Basic Display

1. **Valid OAuth Redirect URIs**:
   ```
   https://your-backend-domain.com/v2/api/lambda/instagram/code
   ```

2. **App Review Status**:
   - For testing: Add test users in App Review → Roles
   - For production: Submit for app review

3. **Instagram App Secret**:
   - Make sure you're using the Instagram App Secret, not Facebook App Secret

### Configuration Check
```javascript
// In your backend config
{
  instagram: {
    client_id: "YOUR_INSTAGRAM_APP_ID", // Not Facebook App ID
    client_secret: "YOUR_INSTAGRAM_APP_SECRET", // Not Facebook App Secret
    redirect_url: "https://your-backend.com/v2/api/lambda/instagram/code"
  }
}
```

## 3. OAuth URL Issues

### Current Implementation
The OAuth URL should be:
```
https://api.instagram.com/oauth/authorize?
  client_id=YOUR_INSTAGRAM_APP_ID&
  redirect_uri=YOUR_REDIRECT_URI&
  scope=user_profile,user_media&
  response_type=code&
  state=YOUR_STATE
```

### Common Issues
- Using Facebook OAuth URL instead of Instagram
- Incorrect client_id (using Facebook App ID instead of Instagram App ID)
- Mismatched redirect_uri (must exactly match configured URI)
- Invalid scope (Instagram Basic Display only supports specific scopes)

## 4. Valid Scopes for Instagram Basic Display

Only these scopes are supported:
- `user_profile` - Access to user's profile info
- `user_media` - Access to user's media

**Invalid scopes that cause errors:**
- `email` - Not supported by Instagram Basic Display
- `user_friends` - Not supported
- Any Facebook-specific scopes

## 5. Test User Requirements

For Instagram Basic Display apps in development mode:

1. **Add Test Users**:
   - Go to App Review → Roles in Facebook Developer Console
   - Add Instagram accounts as "Instagram Testers"
   - Test users must accept the invitation

2. **Instagram Account Requirements**:
   - Must be a personal Instagram account (not business)
   - Account must accept the test invitation
   - Account must be active and not restricted

## 6. Quick Fix Steps

### Step 1: Verify App Configuration
1. Go to [Facebook Developers Console](https://developers.facebook.com/)
2. Select your app → Instagram Basic Display
3. Check "Basic Display" settings:
   - Valid OAuth Redirect URIs must include your backend URL
   - Instagram App ID and Secret are correctly configured

### Step 2: Update Backend Configuration
```javascript
// Make sure your config uses Instagram credentials, not Facebook
{
  instagram: {
    client_id: "INSTAGRAM_APP_ID", // From Instagram Basic Display settings
    client_secret: "INSTAGRAM_APP_SECRET", // From Instagram Basic Display settings
    redirect_url: "https://your-backend.com/v2/api/lambda/instagram/code"
  }
}
```

### Step 3: Test with Valid User
1. Add your Instagram account as a test user
2. Accept the test invitation in Instagram
3. Try the OAuth flow again

### Step 4: Check OAuth URL
The generated URL should look like:
```
https://api.instagram.com/oauth/authorize?client_id=*********&redirect_uri=https%3A//your-backend.com/v2/api/lambda/instagram/code&scope=user_profile%2Cuser_media&response_type=code&state=...
```

## 7. Alternative: Use Facebook Login for Instagram

If you continue having issues with Instagram Basic Display, you can use Facebook Login to access Instagram data:

1. Use Facebook OAuth with Instagram permissions
2. Request `instagram_basic` permission during Facebook login
3. Access Instagram data through Facebook Graph API

## 8. Development vs Production

### Development Mode
- Only works with test users
- Limited to 25 test users
- No app review required

### Production Mode
- Requires app review submission
- Must provide detailed use case
- Can be used by any Instagram user

## 9. Common Error Messages and Solutions

| Error | Cause | Solution |
|-------|-------|----------|
| "Invalid platform app" | Wrong app type or configuration | Use Instagram Basic Display app |
| "Invalid client_id" | Using Facebook App ID instead of Instagram | Use Instagram App ID from Basic Display settings |
| "Invalid redirect_uri" | Mismatched redirect URI | Ensure exact match with configured URI |
| "Invalid scope" | Using unsupported scopes | Only use `user_profile,user_media` |
| "User not authorized" | User not added as test user | Add user as Instagram Tester |

## 10. Testing the Fix

After making changes:

1. Clear browser cache and cookies
2. Test with a fresh incognito/private browser window
3. Ensure the test Instagram account has accepted the test invitation
4. Check browser developer tools for any additional error details

If you're still experiencing issues, the problem is likely with the Instagram app configuration in Facebook Developer Console rather than the code implementation.
