import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";

// Types for Stripe PaymentMethod data (updated to match backend API)
export interface StripeCard {
  id: string;
  object: string;
  type: string;
  card: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
    funding?: string;
    country?: string;
  };
  customer: string | null;
  created: number;
}

export interface CustomerCardsResponse {
  payment_methods: StripeCard[];
  has_more: boolean;
}

export interface CreateCardRequest {
  user_id: string;
  sourceToken: string;
}

export interface CreateCardResponse {
  card: StripeCard;
  message: string;
}

export interface AttachPaymentMethodRequest {
  payment_method_id: string;
}

export interface AttachPaymentMethodResponse {
  payment_method: StripeCard;
  customer_id: string;
  message: string;
}

export const useCustomerCards = () => {
  const { sdk } = useSDK();
  const { showToast, tokenExpireError, authState } = useContexts();
  const queryClient = useQueryClient();

  // Check if user is logged in
  const isLoggedIn = authState.isAuthenticated;

  // Get customer's Stripe PaymentMethods
  const useGetCustomerCards = (enabled: boolean = true) => {
    return useQuery({
      queryKey: ["customer-payment-methods"],
      queryFn: async () => {
        try {
          const response = await sdk.getCustomerPaymentMethods({
            type: "card",
            limit: 10,
          });

          if (response.error) {
            throw new Error(
              response.message || "Failed to fetch payment methods"
            );
          }

          return response.data as CustomerCardsResponse;
        } catch (error: any) {
          console.error("Error fetching customer payment methods:", error);
          const message =
            error?.response?.data?.message ||
            error?.message ||
            "Failed to fetch payment methods";
          tokenExpireError(message);
          throw error;
        }
      },
      enabled: isLoggedIn && enabled,
      retry: 2,
      retryDelay: 1000,
    });
  };

  // Create a new customer card
  const useCreateCustomerCard = () => {
    return useMutation({
      mutationFn: async (cardData: CreateCardRequest) => {
        if (!isLoggedIn) {
          throw new Error("User must be logged in to create a card");
        }

        try {
          const response = await sdk.createCustomerStripeCard(cardData);

          if (response.error) {
            throw new Error(response.message || "Failed to create card");
          }

          return response.data as CreateCardResponse;
        } catch (error: any) {
          console.error("Error creating customer card:", error);
          const message =
            error?.response?.data?.message ||
            error?.message ||
            "Failed to create card";
          throw new Error(message);
        }
      },
      onSuccess: (_data) => {
        showToast("Card added successfully!", 5000, ToastStatusEnum.SUCCESS);
        // Invalidate and refetch payment methods
        queryClient.invalidateQueries({
          queryKey: ["customer-payment-methods"],
        });
      },
      onError: (error: any) => {
        const message = error?.message || "Failed to create card";
        showToast(message, 5000, ToastStatusEnum.ERROR);
        tokenExpireError(message);
      },
    });
  };

  // Set default card
  const useSetDefaultCard = () => {
    return useMutation({
      mutationFn: async (cardId: string) => {
        if (!isLoggedIn) {
          throw new Error("User must be logged in to set default card");
        }

        try {
          const response = await sdk.setStripeCustomerDefaultCard(cardId);

          if (response.error) {
            throw new Error(response.message || "Failed to set default card");
          }

          return response.data;
        } catch (error: any) {
          console.error("Error setting default card:", error);
          const message =
            error?.response?.data?.message ||
            error?.message ||
            "Failed to set default card";
          throw new Error(message);
        }
      },
      onSuccess: () => {
        showToast(
          "Default card updated successfully!",
          5000,
          ToastStatusEnum.SUCCESS
        );
        // Invalidate and refetch payment methods
        queryClient.invalidateQueries({
          queryKey: ["customer-payment-methods"],
        });
      },
      onError: (error: any) => {
        const message = error?.message || "Failed to set default card";
        showToast(message, 5000, ToastStatusEnum.ERROR);
      },
    });
  };

  // Delete a customer card
  const useDeleteCustomerCard = () => {
    return useMutation({
      mutationFn: async (cardId: string) => {
        if (!isLoggedIn) {
          throw new Error("User must be logged in to delete a card");
        }

        try {
          const response = await sdk.deleteCustomerStripeCard(cardId);

          if (response.error) {
            throw new Error(response.message || "Failed to delete card");
          }

          return response.data;
        } catch (error: any) {
          console.error("Error deleting customer card:", error);
          const message =
            error?.response?.data?.message ||
            error?.message ||
            "Failed to delete card";
          throw new Error(message);
        }
      },
      onSuccess: () => {
        showToast("Card deleted successfully!", 5000, ToastStatusEnum.SUCCESS);
        // Invalidate and refetch payment methods
        queryClient.invalidateQueries({
          queryKey: ["customer-payment-methods"],
        });
      },
      onError: (error: any) => {
        const message = error?.message || "Failed to delete card";
        showToast(message, 5000, ToastStatusEnum.ERROR);
      },
    });
  };

  // Attach a PaymentMethod to customer
  const useAttachPaymentMethod = () => {
    return useMutation({
      mutationFn: async (attachData: AttachPaymentMethodRequest) => {
        if (!isLoggedIn) {
          throw new Error("User must be logged in to attach payment method");
        }

        try {
          const response = await sdk.attachPaymentMethodToCustomer(attachData);

          if (response.error) {
            throw new Error(
              response.message || "Failed to attach payment method"
            );
          }

          return response.data as AttachPaymentMethodResponse;
        } catch (error: any) {
          console.error("Error attaching payment method:", error);
          const message =
            error?.response?.data?.message ||
            error?.message ||
            "Failed to attach payment method";
          throw new Error(message);
        }
      },
      onSuccess: (_data) => {
        showToast(
          "Payment method attached successfully!",
          5000,
          ToastStatusEnum.SUCCESS
        );
        // Invalidate and refetch payment methods
        queryClient.invalidateQueries({
          queryKey: ["customer-payment-methods"],
        });
      },
      onError: (error: any) => {
        const message = error?.message || "Failed to attach payment method";
        showToast(message, 5000, ToastStatusEnum.ERROR);
        tokenExpireError(message);
      },
    });
  };

  return {
    isLoggedIn,
    useGetCustomerCards,
    useCreateCustomerCard,
    useAttachPaymentMethod,
    useSetDefaultCard,
    useDeleteCustomerCard,
  };
};

export default useCustomerCards;
