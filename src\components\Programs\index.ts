import { lazy } from "react";

export const Programs = lazy(() => import("./Programs.tsx"));
export const ProgramFilters = lazy(() => import("./ProgramFilters"));
export const ProgramTable = lazy(() => import("./ProgramTable"));
export const ProgramHeader = lazy(() => import("./ProgramHeader"));
export const InfoCard = lazy(() => import("./InfoCard"));
export const ExerciseItem = lazy(() => import("./ExerciseItem"));
export const DayCard = lazy(() => import("./DayCard"));
export const SubscriptionCard = lazy(() => import("./SubscriptionCard"));
export const EnrollmentDetailsModal = lazy(
  () => import("./EnrollmentDetailsModal")
);

export * from "./types";
