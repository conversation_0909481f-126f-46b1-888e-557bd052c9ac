import React, { useState } from "react";
import { Modal } from "@/components/Modal/Modal";
import { InteractiveButton } from "@/components/InteractiveButton";
import {
  useAdminRefundDetail,
  useRefundDecision,
  useProcessRefund,
} from "@/hooks/useAdminRefunds";
import { useQueryClient } from "@tanstack/react-query";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";

interface RefundDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  refundId: number | null;
}

const RefundDetailModal: React.FC<RefundDetailModalProps> = ({
  isOpen,
  onClose,
  refundId,
}) => {
  const [adminNotes, setAdminNotes] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [showRejectForm, setShowRejectForm] = useState(false);

  const queryClient = useQueryClient();
  const { showToast } = useContexts();

  // Fetch refund details
  const {
    data: refundDetail,
    isLoading,
    error,
  } = useAdminRefundDetail(refundId);

  // Hooks for actions
  const { makeDecision } = useRefundDecision();
  const { processRefund } = useProcessRefund();

  const refund = refundDetail?.data;

  const handleApprove = async () => {
    if (!refund) return;

    setIsProcessing(true);
    try {
      await makeDecision(refund.id, {
        decision: "approve",
        admin_notes: adminNotes || undefined,
      });

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["admin-refund-requests"] });
      queryClient.invalidateQueries({
        queryKey: ["admin-refund-detail", refund.id],
      });

      showToast(
        "Refund request approved successfully",
        5000,
        ToastStatusEnum.SUCCESS
      );
      onClose();
    } catch (error: any) {
      showToast(
        error.message || "Failed to approve refund",
        5000,
        ToastStatusEnum.ERROR
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!refund || !adminNotes.trim()) {
      showToast(
        "Please provide a reason for rejection",
        5000,
        ToastStatusEnum.ERROR
      );
      return;
    }

    setIsProcessing(true);
    try {
      await makeDecision(refund.id, {
        decision: "reject",
        admin_notes: adminNotes,
      });

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["admin-refund-requests"] });
      queryClient.invalidateQueries({
        queryKey: ["admin-refund-detail", refund.id],
      });

      showToast("Refund request rejected", 5000, ToastStatusEnum.SUCCESS);
      onClose();
    } catch (error: any) {
      showToast(
        error.message || "Failed to reject refund",
        5000,
        ToastStatusEnum.ERROR
      );
    } finally {
      setIsProcessing(false);
      setShowRejectForm(false);
    }
  };

  const handleProcessRefund = async () => {
    if (!refund) return;

    setIsProcessing(true);
    try {
      await processRefund(refund.id);

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["admin-refund-requests"] });
      queryClient.invalidateQueries({
        queryKey: ["admin-refund-detail", refund.id],
      });

      showToast("Refund processed successfully", 5000, ToastStatusEnum.SUCCESS);
      onClose();
    } catch (error: any) {
      showToast(
        error.message || "Failed to process refund",
        5000,
        ToastStatusEnum.ERROR
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "approved":
        return "text-blue-600 bg-blue-50 border-blue-200";
      case "rejected":
        return "text-red-600 bg-red-50 border-red-200";
      case "processed":
        return "text-green-600 bg-green-50 border-green-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const canApprove = refund?.status === "pending";
  const canReject = refund?.status === "pending";
  const canProcess = refund?.status === "approved";

  if (isLoading) {
    return (
      <Modal
        isOpen={isOpen}
        modalCloseClick={onClose}
        title="Loading..."
        modalHeader={true}
        classes={{
          modalDialog: "w-full max-w-2xl",
          modal: "",
          modalContent: "",
        }}
      >
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </Modal>
    );
  }

  if (error || !refund) {
    return (
      <Modal
        isOpen={isOpen}
        modalCloseClick={onClose}
        title="Error"
        modalHeader={true}
        classes={{
          modalDialog: "w-full max-w-2xl",
          modal: "",
          modalContent: "",
        }}
      >
        <div className="text-center py-8">
          <p className="text-red-600">
            {error?.message || "Failed to load refund details"}
          </p>
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title="Refund Request Details"
      modalHeader={true}
      classes={{
        modalDialog: "w-full max-w-2xl",
        modal: "",
        modalContent: "",
      }}
    >
      <div className="space-y-6">
        {/* Status Badge */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-text">
            Request #{refund.id}
          </h3>
          <span
            className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(
              refund.status
            )}`}
          >
            {refund.status.charAt(0).toUpperCase() + refund.status.slice(1)}
          </span>
        </div>

        {/* Refund Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Athlete
              </label>
              <p className="text-text">{refund.athlete?.full_name}</p>
              <p className="text-sm text-text-secondary">
                {refund.athlete?.email}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Trainer
              </label>
              <p className="text-text">{refund.trainer?.full_name}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Program
              </label>
              <p className="text-text">{refund.program.name}</p>
              {refund.program.split_name && (
                <p className="text-sm text-text-secondary">
                  {refund.program.split_name}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Amount
              </label>
              <p className="text-lg font-semibold text-text">
                {formatCurrency(refund.amount, refund.currency)}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Enrollment Date
              </label>
              <p className="text-text">
                {formatDate(refund.enrollment.enrollment_date)}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Request Date
              </label>
              <p className="text-text">{formatDate(refund.requested_at)}</p>
            </div>
          </div>
        </div>

        {/* Reason */}
        <div>
          <label className="block text-sm font-medium text-text-secondary mb-2">
            Refund Reason
          </label>
          <div className="bg-background-secondary p-3 rounded-lg">
            <p className="text-text">{refund.reason}</p>
          </div>
        </div>

        {/* Admin Notes (if any) */}
        {refund.admin_notes && (
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Admin Notes
            </label>
            <div className="bg-background-secondary p-3 rounded-lg">
              <p className="text-text">{refund.admin_notes}</p>
            </div>
          </div>
        )}

        {/* Processing Details (if processed) */}
        {refund.status === "processed" && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Processed Date
              </label>
              <p className="text-text">
                {refund.processed_at ? formatDate(refund.processed_at) : "N/A"}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Refund Amount
              </label>
              <p className="text-text">
                {refund.refund_amount
                  ? formatCurrency(refund.refund_amount, refund.currency)
                  : "N/A"}
              </p>
            </div>
          </div>
        )}

        {/* Admin Notes Input (for new decisions) */}
        {(canApprove || canReject || showRejectForm) && (
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Admin Notes{" "}
              {showRejectForm && <span className="text-red-500">*</span>}
            </label>
            <textarea
              value={adminNotes}
              onChange={(e) => setAdminNotes(e.target.value)}
              placeholder={
                showRejectForm
                  ? "Please provide a reason for rejection..."
                  : "Optional notes about this decision..."
              }
              className="w-full p-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
              rows={3}
            />
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-border">
          {showRejectForm ? (
            <>
              <InteractiveButton
                onClick={handleReject}
                disabled={isProcessing || !adminNotes.trim()}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white"
              >
                {isProcessing ? "Rejecting..." : "Confirm Rejection"}
              </InteractiveButton>
              <InteractiveButton
                onClick={() => {
                  setShowRejectForm(false);
                  setAdminNotes("");
                }}
                disabled={isProcessing}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white"
              >
                Cancel
              </InteractiveButton>
            </>
          ) : (
            <>
              {canApprove && (
                <InteractiveButton
                  onClick={handleApprove}
                  disabled={isProcessing}
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                >
                  {isProcessing ? "Approving..." : "Approve Refund"}
                </InteractiveButton>
              )}

              {canReject && (
                <InteractiveButton
                  onClick={() => setShowRejectForm(true)}
                  disabled={isProcessing}
                  className="flex-1 bg-red-600 hover:bg-red-700 text-white"
                >
                  Reject Refund
                </InteractiveButton>
              )}

              {canProcess && (
                <InteractiveButton
                  onClick={handleProcessRefund}
                  disabled={isProcessing}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isProcessing ? "Processing..." : "Process Refund"}
                </InteractiveButton>
              )}

              <InteractiveButton
                onClick={onClose}
                disabled={isProcessing}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white"
              >
                Close
              </InteractiveButton>
            </>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default RefundDetailModal;
