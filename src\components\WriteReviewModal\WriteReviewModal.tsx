import React, { useState, useEffect } from "react";
import { Modal } from "@/components/Modal/Modal";
import { StarRating } from "@/components/StarRating";
import { MkdButton } from "@/components/MkdButton";
import MkdInputV2 from "@/components/MkdInputV2";
import {
  useReviewSubmission,
  ReviewSubmissionData,
} from "@/hooks/useReviewSubmission";
import { useUserReview } from "@/hooks/useUserReview";
import { useEnrollment } from "@/hooks/useEnrollment";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";

interface WriteReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  programId: number | string;
  programName: string;
  onReviewSubmitted?: () => void;
}

const WriteReviewModal: React.FC<WriteReviewModalProps> = ({
  isOpen,
  onClose,
  programId,
  programName,
  onReviewSubmitted,
}) => {
  const [rating, setRating] = useState<number>(0);
  const [content, setContent] = useState<string>("");
  const [errors, setErrors] = useState<{
    rating?: string;
    content?: string;
    subscription?: string;
  }>({});

  const { submitReview, isSubmitting, isLoggedIn } = useReviewSubmission();
  const { useCheckProgramSubscription } = useEnrollment();
  const subscriptionStatus = useCheckProgramSubscription(programId);
  const {
    existingReview,
    updateUserReview,
    deleteUserReview,
    isUpdatingReview,
    isDeletingReview,
    refetchUserReview,
  } = useUserReview(programId);

  // Add custom API hook for feed-style posting
  const { mutateAsync: customRequest } = useCustomModelQuery({
    showToast: false,
  });
  const { showToast } = useContexts();
  const [isSubmittingCustom, setIsSubmittingCustom] = useState(false);

  // Reset form when modal opens/closes or when existing review changes
  useEffect(() => {
    if (isOpen) {
      if (existingReview) {
        // Load existing review data
        setRating(existingReview.rating);
        setContent(existingReview.content);
      } else {
        // Reset form for new review
        setRating(0);
        setContent("");
      }
      setErrors({});
    }
  }, [isOpen, existingReview]);

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: typeof errors = {};

    if (!isLoggedIn) {
      newErrors.subscription = "You must be logged in to write a review";
      setErrors(newErrors);
      return false;
    }

    if (subscriptionStatus.isLoading) {
      newErrors.subscription = "Checking subscription status...";
      setErrors(newErrors);
      return false;
    }

    if (!subscriptionStatus.isSubscribed) {
      newErrors.subscription =
        "You must be subscribed to this program to write a review";
      setErrors(newErrors);
      return false;
    }

    if (rating === 0) {
      newErrors.rating = "Please select a rating";
    }

    if (!content.trim()) {
      newErrors.content = "Please write a review";
    } else if (content.trim().length < 10) {
      newErrors.content = "Review must be at least 10 characters long";
    } else if (content.trim().length > 1000) {
      newErrors.content = "Review cannot exceed 1000 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // New function using feed-style API
  const submitReviewWithFeedAPI = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmittingCustom(true);
    try {
      // Get the first active subscription for split_id
      const activeSplit = subscriptionStatus.subscriptions[0];

      const result = await customRequest({
        endpoint: "/v2/api/kanglink/custom/trainer/feed",
        method: "POST",
        body: {
          program_id: programId,
          split_id: activeSplit?.split_id || null,
          post_type: "review",
          content: content.trim(),
          rating,
          is_private: false,
          is_anonymous: false,
          visibility_scope: "public",
        },
      });

      if (result.error) {
        throw new Error(result.message || "Failed to submit review");
      }

      showToast(
        "Review submitted successfully!",
        3000,
        ToastStatusEnum.SUCCESS
      );
      onClose();
      onReviewSubmitted?.();
      refetchUserReview();
    } catch (error: any) {
      const message = error?.message || "Failed to submit review";
      showToast(message, 4000, ToastStatusEnum.ERROR);
    } finally {
      setIsSubmittingCustom(false);
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      if (existingReview) {
        // Update existing review
        await updateUserReview(existingReview.id, {
          content: content.trim(),
          rating,
        });
      } else {
        // Use feed-style API for new reviews
        await submitReviewWithFeedAPI();
        return; // Exit early since submitReviewWithFeedAPI handles everything
      }

      onClose();
      onReviewSubmitted?.();

      refetchUserReview();
    } catch (error) {
      // Error is handled by the hook's toast system
    }
  };

  const handleClose = () => {
    if (
      !isSubmitting &&
      !isUpdatingReview &&
      !isDeletingReview &&
      !isSubmittingCustom
    ) {
      onClose();
    }
  };

  const handleDelete = async () => {
    if (!existingReview) return;

    try {
      await deleteUserReview(existingReview.id);
      onClose();
      onReviewSubmitted?.();
      refetchUserReview();
    } catch (error) {
      // Error is handled by the hook's toast system
    }
  };

  const characterCount = content.length;
  const maxCharacters = 1000;

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={handleClose}
      modalHeader={true}
      title={`Write a Review for ${programName}`}
      classes={{
        modal: "h-full",
        modalDialog: "max-w-2xl w-full h-50vh",
      }}
      disableCancel={isSubmitting}
    >
      <div className="space-y-6 w-full">
        {/* Subscription Status Check */}
        {!isLoggedIn && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700 text-sm">
              You must be logged in to write a review.
            </p>
          </div>
        )}

        {isLoggedIn && subscriptionStatus.isLoading && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-blue-700 text-sm">
              Checking subscription status...
            </p>
          </div>
        )}

        {isLoggedIn &&
          !subscriptionStatus.isLoading &&
          !subscriptionStatus.isSubscribed && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-yellow-700 text-sm">
                You must be subscribed to this program to write a review.
              </p>
            </div>
          )}

        {/* Rating Section */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text">
            Rating <span className="text-red-500">*</span>
          </label>
          <div className="flex items-center space-x-2">
            <StarRating
              rating={rating}
              onRatingChange={setRating}
              size="lg"
              interactive={true}
            />
            <span className="text-sm text-text-secondary">
              {rating > 0
                ? `${rating} star${rating !== 1 ? "s" : ""}`
                : "Select rating"}
            </span>
          </div>
          {errors.rating && (
            <p className="text-red-500 text-sm">{errors.rating}</p>
          )}
        </div>

        {/* Review Content */}
        <MkdInputV2
          name="content"
          type="textarea"
          value={content}
          onChange={(e) => setContent(e.target.value)}
          errors={errors}
          required
          disabled={isSubmitting || isSubmittingCustom}
        >
          <MkdInputV2.Container>
            <div className="flex justify-between">
              <MkdInputV2.Label>Your Review</MkdInputV2.Label>
              <span className="text-sm text-text-secondary">
                {characterCount}/{maxCharacters}
              </span>
            </div>
            <MkdInputV2.Field
              placeholder="Share your experience with this program..."
              rows="4"
              className={`min-h-[120px] ${
                characterCount > maxCharacters ? "border-red-500" : ""
              }`}
            />
            <MkdInputV2.Error />
          </MkdInputV2.Container>
        </MkdInputV2>

        {/* Action Buttons */}
        <div className="flex justify-between pt-4 border-t border-border">
          <div>
            {existingReview && (
              <MkdButton
                onClick={handleDelete}
                disabled={isDeletingReview}
                className="px-4 py-2 text-sm !text-red-500 font-medium text-red-600 bg-red-50 border border-red-500 hover:bg-red-600 hover:!text-white disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isDeletingReview ? "Deleting..." : "Delete Review"}
              </MkdButton>
            )}
          </div>
          <div className="flex space-x-3">
            <MkdButton
              onClick={handleClose}
              disabled={
                isSubmitting ||
                isUpdatingReview ||
                isDeletingReview ||
                isSubmittingCustom
              }
              className="px-4 py-2 text-sm font-medium text-muted-foreground bg-background border border-border hover:bg-background-hover"
            >
              Cancel
            </MkdButton>
            <MkdButton
              onClick={handleSubmit}
              disabled={
                isSubmitting ||
                isSubmittingCustom ||
                isUpdatingReview ||
                isDeletingReview ||
                !isLoggedIn ||
                !subscriptionStatus.isSubscribed
              }
              className="px-4 py-2 text-sm font-medium bg-primary hover:bg-primary-hover text-white disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting || isSubmittingCustom || isUpdatingReview
                ? existingReview
                  ? "Updating..."
                  : "Submitting..."
                : existingReview
                  ? "Update Review"
                  : "Submit Review"}
            </MkdButton>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default WriteReviewModal;
