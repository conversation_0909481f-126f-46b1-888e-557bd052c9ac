import { useState } from "react";
import { Notification } from "../../utils/NotificationService";
import NotificationService from "../../utils/NotificationService";

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (notificationId: number) => Promise<void>;
}

const NotificationItem = ({
  notification,
  onMarkAsRead,
}: NotificationItemProps) => {
  const [markAsRead, setMarkAsRead] = useState(notification.is_read);

  const handleMarkAsRead = async () => {
    try {
      await onMarkAsRead(notification.id);
      setMarkAsRead(true);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const getNotificationType = (type: string) => {
    const typeMap: Record<string, string> = {
      exercise_completed: 'success',
      day_completed: 'success',
      week_completed: 'success',
      program_completed: 'success',
      milestone_reached: 'success',
      new_enrollment: 'info',
      payment_received: 'success',
      program_updated: 'warning',
      athlete_message: 'info',
      system_alert: 'error',
      refund_requested: 'warning',
      refund_approved: 'success',
      refund_rejected: 'error',
      refund_processed: 'success',
    };
    
    return typeMap[type] || 'info';
  };

  const type = getNotificationType(notification.notification_type);
  const icon = NotificationService.getNotificationIcon(notification.notification_type);
  const timeAgo = NotificationService.formatTimeAgo(notification.created_at);

  return (
    <div
      className={`flex items-start py-5 px-2 gap-3 max-w-md text-left ${
        markAsRead ? "text-[#525252]" : "text-black"
      }`}
    >
      <div className="text-2xl">{icon}</div>
      <div className="flex-1">
        <div className="flex items-center justify-between mb-3">
          <span className="font-semibold">{notification.title}</span>
          <div className="flex items-center gap-2">
            {!markAsRead && (
              <span className="w-2 h-2 rounded-full bg-[#6366F1]"></span>
            )}
            <span className="text-sm text-gray-500">{timeAgo}</span>
            <span className="relative">
              <button className="peer cursor-pointer">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                >
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M6.66663 2.66668C6.66663 1.9303 7.26358 1.33334 7.99996 1.33334C8.73634 1.33334 9.33329 1.9303 9.33329 2.66668C9.33329 3.40306 8.73634 4.00001 7.99996 4.00001C7.26358 4.00001 6.66663 3.40306 6.66663 2.66668ZM6.66663 8.00001C6.66663 7.26363 7.26358 6.66668 7.99996 6.66668C8.73634 6.66668 9.33329 7.26363 9.33329 8.00001C9.33329 8.73639 8.73634 9.33334 7.99996 9.33334C7.26358 9.33334 6.66663 8.73639 6.66663 8.00001ZM6.66663 13.3333C6.66663 12.597 7.26358 12 7.99996 12C8.73634 12 9.33329 12.597 9.33329 13.3333C9.33329 14.0697 8.73634 14.6667 7.99996 14.6667C7.26358 14.6667 6.66663 14.0697 6.66663 13.3333Z"
                    fill="#8D8D8D"
                  />
                </svg>
              </button>
              <ul className="absolute right-2 top-[85%] z-20 hidden rounded-lg border border-[#a8a8a8] bg-white p-2 text-sm text-[#525252] shadow-md hover:block focus:block peer-focus:block peer-focus-visible:block whitespace-nowrap">
                <li
                  className="flex cursor-pointer items-center rounded-md px-4 py-3 hover:bg-[#F4F4F4] hover:text[#262626]"
                  onClick={handleMarkAsRead}
                >
                  <span className="mr-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                    >
                      <path
                        d="M3.32197 10C3.32197 6.3181 6.30674 3.33333 9.98863 3.33333C12.2705 3.33333 13.7909 4.30074 15.3203 6.04167H12.9167C12.6866 6.04167 12.5 6.22821 12.5 6.45833C12.5 6.68845 12.6866 6.875 12.9167 6.875H15.8334C16.2936 6.875 16.6667 6.5019 16.6667 6.04167V3.125C16.6667 2.89488 16.4801 2.70833 16.25 2.70833C16.0199 2.70833 15.8334 2.89488 15.8334 3.125V5.36447C14.2399 3.58924 12.5314 2.5 9.98863 2.5C5.8465 2.5 2.48863 5.85786 2.48863 10C2.48863 14.1421 5.8465 17.5 9.98863 17.5C13.2548 17.5 16.0324 15.4124 17.0618 12.5C17.1384 12.283 17.0247 12.0449 16.8078 11.9683C16.5908 11.8916 16.3527 12.0053 16.2761 12.2223C15.3607 14.8121 12.8907 16.6667 9.98863 16.6667C6.30674 16.6667 3.32197 13.6819 3.32197 10Z"
                        fill="#A8A8A8"
                        stroke="#A8A8A8"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </span>
                  <span>Mark as read</span>
                </li>
              </ul>
            </span>
          </div>
        </div>

        <div className="mb-5">{notification.message}</div>
        
        {/* Show sender info if available */}
        {notification.sender_name && (
          <div className="text-sm text-gray-500 mb-2">
            From: {notification.sender_name}
          </div>
        )}
        
        {/* Show category badge */}
        <div className="inline-block">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            NotificationService.getNotificationTypeColor(notification.notification_type)
          } bg-opacity-10`}>
            {NotificationService.getCategoryLabel(notification.category)}
          </span>
        </div>
        
        {/* Show details link if there's related data */}
        {notification.data && Object.keys(notification.data).length > 0 && (
          <div
            className={`${
              markAsRead ? "text-[#525252]" : "text-[#6366F1]"
            } font-semibold cursor-pointer hover:underline mt-2`}
          >
            View Details
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationItem;
