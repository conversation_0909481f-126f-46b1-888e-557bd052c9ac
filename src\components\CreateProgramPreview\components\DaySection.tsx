import React from "react";
import { ChevronUpIcon } from "@/assets/svgs";
import { SessionSection } from "./index";

interface DaySectionProps {
  day: any;
  dayIndex: number;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  collapsedStates: { [key: string]: boolean };
  onToggleItemCollapse: (id: string) => void;
}

const DaySection: React.FC<DaySectionProps> = ({
  day,
  dayIndex,
  isCollapsed,
  onToggleCollapse,
  collapsedStates,
  onToggleItemCollapse,
}) => {
  if (day.is_rest_day) {
    return (
      <div className="flex items-center gap-3 py-2">
        <span className="text-base text-text">
          {day.name || `Day ${dayIndex + 1}`} - Rest Day
        </span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Day Header */}
      <div className="flex items-center justify-between">
        <button
          onClick={onToggleCollapse}
          className="flex items-center gap-2 text-base text-text hover:text-text-hover"
        >
          <span>{day.name || `Day ${dayIndex + 1}`}</span>
        </button>

        <button
          onClick={onToggleCollapse}
          className="w-4 h-4 flex items-center justify-center text-text-secondary hover:text-text"
        >
          <ChevronUpIcon
            className={`w-4 h-4 transition-transform ${isCollapsed ? "rotate-180" : ""}`}
            stroke="currentColor"
          />
        </button>
      </div>

      {/* Day Content */}
      {!isCollapsed && (
        <div className="ml-4 space-y-4">
          {day.sessions && day.sessions.length > 0 ? (
            day.sessions.map((session: any, sessionIndex: number) => (
              <SessionSection
                key={session.id || `session-${sessionIndex}`}
                session={session}
                sessionIndex={sessionIndex}
                isCollapsed={collapsedStates[`session-${session.id}`] || false}
                onToggleCollapse={() =>
                  onToggleItemCollapse(`session-${session.id}`)
                }
                collapsedStates={collapsedStates}
                onToggleItemCollapse={onToggleItemCollapse}
              />
            ))
          ) : (
            <div className="text-text-secondary text-sm">
              No sessions defined for this day.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DaySection;
