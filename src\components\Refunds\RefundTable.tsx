import React from "react";
import { ChevronDown } from "lucide-react";
import { PaginationBar } from "@/components/PaginationBar";
import { InteractiveButton } from "@/components/InteractiveButton";
import { AdminRefundRequest } from "@/hooks/useAdminRefunds";

interface RefundTableProps {
  refunds: AdminRefundRequest[];
  currentPage: number;
  totalPages: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onViewDetails: (refundId: number) => void;
  isLoading?: boolean;
}

const RefundTable: React.FC<RefundTableProps> = ({
  refunds,
  currentPage,
  totalPages,
  pageSize,
  onPageChange,
  onViewDetails,
  isLoading = false,
}) => {
  const getStatusColor = (status: AdminRefundRequest["status"]) => {
    switch (status) {
      case "pending":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "approved":
        return "text-blue-600 bg-blue-50 border-blue-200";
      case "rejected":
        return "text-red-600 bg-red-50 border-red-200";
      case "processed":
        return "text-green-600 bg-green-50 border-green-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "2-digit",
      month: "2-digit",
      day: "2-digit",
    });
  };

  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
    }).format(amount);
  };

  return (
    <div className="bg-background border border-border rounded-lg shadow-sm overflow-hidden">
      {/* Header with Title */}
      <div className="px-4 sm:px-6 py-4 border-b border-border">
        <h2 className="text-2xl font-bold text-text">Refund</h2>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Table Header */}
          <thead className="bg-background-secondary">
            <tr>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">ID</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">Athlete</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">Trainer</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">Program</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">
                    Request Date
                  </span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-sm font-medium text-text">Status</span>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-sm font-medium text-text">Amount</span>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-sm font-medium text-text">Actions</span>
              </th>
            </tr>
          </thead>

          {/* Table Body */}
          <tbody className="divide-y divide-border">
            {isLoading ? (
              // Loading skeleton
              Array.from({ length: pageSize }).map((_, index) => (
                <tr key={`skeleton-${index}`} className="animate-pulse">
                  <td className="px-4 sm:px-6 py-4">
                    <div className="h-4 bg-gray-200 rounded w-12"></div>
                  </td>
                  <td className="px-4 sm:px-6 py-4">
                    <div className="h-4 bg-gray-200 rounded w-24"></div>
                  </td>
                  <td className="px-4 sm:px-6 py-4">
                    <div className="h-4 bg-gray-200 rounded w-24"></div>
                  </td>
                  <td className="px-4 sm:px-6 py-4">
                    <div className="h-4 bg-gray-200 rounded w-32"></div>
                  </td>
                  <td className="px-4 sm:px-6 py-4">
                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                  </td>
                  <td className="px-4 sm:px-6 py-4">
                    <div className="h-6 bg-gray-200 rounded w-16"></div>
                  </td>
                  <td className="px-4 sm:px-6 py-4">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </td>
                  <td className="px-4 sm:px-6 py-4">
                    <div className="h-8 bg-gray-200 rounded w-16"></div>
                  </td>
                </tr>
              ))
            ) : refunds.length === 0 ? (
              // Empty state
              <tr>
                <td colSpan={8} className="px-4 sm:px-6 py-12 text-center">
                  <div className="text-text-secondary">
                    <p className="text-lg font-medium">
                      No refund requests found
                    </p>
                    <p className="text-sm mt-1">
                      Refund requests will appear here when submitted by
                      athletes.
                    </p>
                  </div>
                </td>
              </tr>
            ) : (
              refunds.map((refund) => (
                <tr
                  key={refund.id}
                  className="hover:bg-background-hover transition-colors duration-200"
                >
                  <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                    {refund.id}
                  </td>
                  <td className="px-4 sm:px-6 py-4 text-sm text-text">
                    <div>
                      <p className="font-medium">{refund.athlete?.full_name}</p>
                      <p className="text-xs text-text-secondary">
                        {refund.athlete?.email}
                      </p>
                    </div>
                  </td>
                  <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                    {refund.trainer?.full_name}
                  </td>
                  <td className="px-4 sm:px-6 py-4 text-sm text-text">
                    <div>
                      <p className="font-medium">{refund.program.name}</p>
                      {refund.program.split_name && (
                        <p className="text-xs text-text-secondary">
                          {refund.program.split_name}
                        </p>
                      )}
                    </div>
                  </td>
                  <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                    {formatDate(refund.requested_at)}
                  </td>
                  <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(
                        refund.status
                      )}`}
                    >
                      {refund.status.charAt(0).toUpperCase() +
                        refund.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-4 sm:px-6 py-4 text-sm font-semibold text-text whitespace-nowrap">
                    {formatCurrency(refund.amount, refund.currency)}
                  </td>
                  <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                    <InteractiveButton
                      onClick={() => onViewDetails(refund.id)}
                      type="button"
                      className="!h-9 px-4 bg-transparent border border-primary text-primary text-sm font-normal hover:bg-primary hover:text-white transition-colors"
                    >
                      View
                    </InteractiveButton>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-4 sm:px-6 py-4 border-t border-border">
          <PaginationBar
            currentPage={currentPage}
            pageCount={totalPages}
            pageSize={pageSize}
            canPreviousPage={currentPage > 1}
            canNextPage={currentPage < totalPages}
            updatePageSize={() => {}} // Not needed for this implementation
            updateCurrentPage={onPageChange}
            startSize={pageSize}
            multiplier={1}
            canChangeLimit={false}
          />
        </div>
      )}
    </div>
  );
};

export default RefundTable;
