import { useState, useEffect } from "react";
import NotificationService from "../../utils/NotificationService";
import MkdSDK from "../../utils/MkdSDK";

interface NotificationBellProps {
  onOpenNotifications: () => void;
  className?: string;
}

const NotificationBell = ({ onOpenNotifications, className = "" }: NotificationBellProps) => {
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);

  const sdk = new MkdSDK();
  const notificationService = new NotificationService(sdk);

  useEffect(() => {
    fetchUnreadCount();
    
    // Set up polling for unread count updates
    const interval = setInterval(fetchUnreadCount, 30000); // Check every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  const fetchUnreadCount = async () => {
    try {
      const count = await notificationService.getUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('Error fetching unread count:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <button
      onClick={onOpenNotifications}
      className={`relative p-2 text-gray-600 hover:text-gray-800 transition-colors ${className}`}
      aria-label="Notifications"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9" />
        <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0" />
      </svg>
      
      {/* Unread count badge */}
      {!loading && unreadCount > 0 && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
          {unreadCount > 99 ? '99+' : unreadCount}
        </span>
      )}
      
      {/* Loading indicator */}
      {loading && (
        <span className="absolute -top-1 -right-1 bg-gray-400 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
          <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
        </span>
      )}
    </button>
  );
};

export default NotificationBell; 