import React, { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useContexts } from "@/hooks/useContexts";
import { ProgramFilters, ProgramTable } from "@/components/Programs";
import { AdminProgram } from "@/components/Programs/types";
import { useGetPaginateQuery } from "@/query/shared/listModel";
import { Models } from "@/utils/baas/models";
import { Program } from "@/interfaces/model.interface";
import { useCustomModelQuery } from "@/query/shared";
import { ToastStatusEnum } from "@/utils/Enums";
import { useSDK } from "@/hooks/useSDK";

const ListAdminProgramPage: React.FC = () => {
  const navigate = useNavigate();
  const { globalDispatch, showToast } = useContexts();
  const { mutateAsync: customModelQuery } = useCustomModelQuery();

  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [dateFilter, setDateFilter] = useState("Today");
  const [statusFilter, setStatusFilter] = useState("All");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10); // Match the design showing 4 items
  const { projectId } = useSDK();
  // Build query options for API call
  const queryOptions = useMemo(() => {
    const options: any = {
      size: pageSize,
      page: currentPage,
      filter: [], // Programs don't need role filtering
      join: ["user", "program_discount", "enrollment"], // Join with user to get trainer name
    };

    // Add search filter if search term exists
    if (searchTerm.trim()) {
      options.filter.push(`program_name,cs,${searchTerm.trim()}`);
    }

    // Add status filter if not "All"
    if (statusFilter !== "All") {
      // Map admin status to database status
      const statusMap: Record<string, string> = {
        "Pending Approval": "pending_approval",
        Live: "published",
        // Completed: "archived",
        Rejected: "rejected",
        // Draft: "draft",
      };
      const dbStatus = statusMap[statusFilter] || statusFilter.toLowerCase();
      options.filter.push(`${projectId}_program.status,eq,${dbStatus}`);
    } else {
      options.filter.push(`${projectId}_program.status,neq,draft`);
    }

    // Add date filter logic here if needed
    // For now, we'll skip date filtering

    return options;
  }, [currentPage, pageSize, searchTerm, statusFilter]);

  // Fetch programs data using pagination
  const {
    data: programsData,
    isLoading,
    error,
    refetch,
  } = useGetPaginateQuery(Models.PROGRAM, queryOptions, {
    enabled: true,
  });

  // Transform Program data to AdminProgram format
  const programs: AdminProgram[] = useMemo(() => {
    if (!programsData?.data) return [];

    return programsData.data.map((program: Program) => ({
      ...program,
      id: program.id as number,
      dateCreated: program.created_at
        ? new Date(program.created_at).toISOString().split("T")[0]
        : "Unknown",
      status: mapDatabaseStatusToAdmin(program.status),
      trainer_name:
        JSON.parse(program?.user?.data || "")?.full_name || "Unknown Trainer",
      enrollments: program?.enrollment,
    }));
  }, [programsData?.data]);

  // Helper function to map database status to admin status
  function mapDatabaseStatusToAdmin(dbStatus?: string): AdminProgram["status"] {
    const statusMap: Record<string, AdminProgram["status"]> = {
      pending_approval: "Pending Approval",
      published: "Live",
      // archived: "Completed",
      rejected: "Rejected",
      draft: "Draft",
      archived: "Archived",
      deleted: "Deleted",
    };
    return statusMap[dbStatus || ""] || "Draft";
  }

  // Pagination info from API response
  const totalPages = programsData?.num_pages || 1;

  // Set path in global state for navigation highlighting
  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "programs",
      },
    });
  }, [globalDispatch]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter, dateFilter]);

  const handleApplyFilter = () => {
    // Reset to first page when applying filters
    setCurrentPage(1);
    // Refetch data with new filters
    refetch();
  };

  const handleViewList = (_programId: number, _enrollmentIds: number[]) => {
    // This is handled by the ProgramTable component's internal modal
  };

  const handleEditProgram = (_programId: number) => {
    // TODO: Navigate to edit program page
  };

  const handleStatusChange = (
    _programId: number,
    _newStatus: AdminProgram["status"]
  ) => {
    // TODO: Implement API call to update program status
    // After successful API call, refetch the data
    refetch();
  };

  const handlePreviewProgram = (programId: number) => {
    // Navigate to the ViewTrainerProgramPage for preview
    navigate(`/admin/programs/preview/${programId}`);
  };

  const handleApproveProgram = async (programId: number): Promise<void> => {
    try {
      await customModelQuery({
        endpoint: `/v2/api/kanglink/custom/super_admin/programs/${programId}/approve`,
        method: "PUT",
      });

      showToast("Program approved successfully", 5000, ToastStatusEnum.SUCCESS);

      // Refetch the data to update the table
      refetch();
    } catch (error: any) {
      const message =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to approve program";
      showToast(message, 5000, ToastStatusEnum.ERROR);
      throw error; // Re-throw to let the modal handle the error state
    }
  };

  const handleRejectProgram = async (
    programId: number,
    reason?: string
  ): Promise<void> => {
    try {
      await customModelQuery({
        endpoint: `/v2/api/kanglink/custom/super_admin/programs/${programId}/status`,
        method: "PUT",
        body: {
          status: "rejected",
          rejection_reason: reason || "No reason provided",
        },
      });

      showToast("Program rejected successfully", 5000, ToastStatusEnum.SUCCESS);

      // Refetch the data to update the table
      refetch();
    } catch (error: any) {
      const message =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to reject program";
      showToast(message, 5000, ToastStatusEnum.ERROR);
      throw error; // Re-throw to let the modal handle the error state
    }
  };

  const handleDeleteProgram = async (
    programId: number,
    reason?: string
  ): Promise<void> => {
    try {
      await customModelQuery({
        endpoint: `/v2/api/kanglink/custom/super_admin/programs/${programId}/status`,
        method: "PUT",
        body: {
          status: "deleted",
          rejection_reason: reason || "No reason provided",
        },
      });

      showToast("Program deleted successfully", 5000, ToastStatusEnum.SUCCESS);

      // Refetch the data to update the table
      refetch();
    } catch (error: any) {
      const message =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to delete program";
      showToast(message, 5000, ToastStatusEnum.ERROR);
      throw error; // Re-throw to let the modal handle the error state
    }
  };

  return (
    <div className="w-full bg-background p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h1 className="text-2xl font-bold text-text">Program Management</h1>
        </div>

        {/* Filters Section */}
        <ProgramFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          dateFilter={dateFilter}
          setDateFilter={setDateFilter}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          onApplyFilter={handleApplyFilter}
        />
        {/* Programs Table Section */}
        {isLoading ? (
          <div className="bg-background border border-border rounded-lg shadow-sm p-8 text-center">
            <p className="text-text">Loading programs...</p>
          </div>
        ) : error ? (
          <div className="bg-background border border-border rounded-lg shadow-sm p-8 text-center">
            <p className="text-red-600">
              Error loading programs. Please try again.
            </p>
          </div>
        ) : (
          <ProgramTable
            programs={programs}
            currentPage={currentPage}
            totalPages={totalPages}
            pageSize={pageSize}
            onPageChange={setCurrentPage}
            onViewList={handleViewList}
            onEditProgram={handleEditProgram}
            onPreviewProgram={handlePreviewProgram}
            onApproveProgram={handleApproveProgram}
            onRejectProgram={handleRejectProgram}
            onStatusChange={handleStatusChange}
            onDeleteProgram={handleDeleteProgram}
          />
        )}
      </div>
    </div>
  );
};

export default ListAdminProgramPage;
