import { lazy } from "react";

// Admin Pages
export const AddAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AddAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const ViewAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/View/ViewAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const ListAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/List/ListAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const AdminForgotPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminForgotPage");
  __import.finally(() => {});
  return __import;
});

export const AdminLoginPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminLoginPage");
  __import.finally(() => {});
  return __import;
});

export const AdminProfilePage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminProfilePage");
  __import.finally(() => {});
  return __import;
});

export const AdminResetPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminResetPage");
  __import.finally(() => {});
  return __import;
});

export const AdminSignUpPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const LandingPage = lazy(() => {
  const __import = import("@/pages/Admin/View/LandingPage");
  __import.finally(() => {});
  return __import;
});

export const ViewAdminDashboardPage = lazy(() => {
  const __import = import("@/pages/Admin/View/ViewAdminDashboardPage");
  __import.finally(() => {});
  return __import;
});

// Additional Admin Pages
export const ViewAdminProfilePage = lazy(() => {
  const __import = import("@/pages/Admin/View/ViewAdminProfilePage");
  __import.finally(() => {});
  return __import;
});

export const ViewAdminDashboardPage2 = lazy(() => {
  const __import = import("@/pages/Admin/View/ViewAdminDashboardPage2");
  __import.finally(() => {});
  return __import;
});

export const ListAdminLibraryPage = lazy(() => {
  const __import = import("@/pages/Admin/List/ListAdminLibraryPage");
  __import.finally(() => {});
  return __import;
});

export const ListAdminRefundPage = lazy(() => {
  const __import = import("@/pages/Admin/List/ListAdminRefundPage");
  __import.finally(() => {});
  return __import;
});

export const ListAdminTransactionPage = lazy(() => {
  const __import = import("@/pages/Admin/List/ListAdminTransactionPage");
  __import.finally(() => {});
  return __import;
});

export const ListAdminTrainerPage = lazy(() => {
  const __import = import("@/pages/Admin/List/ListAdminTrainerPage");
  __import.finally(() => {});
  return __import;
});

export const ListAdminAthletePage = lazy(() => {
  const __import = import("@/pages/Admin/List/ListAdminAthletePage");
  __import.finally(() => {});
  return __import;
});

export const ListAdminProgramPage = lazy(() => {
  const __import = import("@/pages/Admin/List/ListAdminProgramPage");
  __import.finally(() => {});
  return __import;
});

// Trainer Pages
export const AddTrainerProgramPage = lazy(() => {
  const __import = import("@/pages/Trainer/Add/AddTrainerProgramPage");
  __import.finally(() => {});
  return __import;
});

export const EditTrainerProgramPage = lazy(() => {
  const __import = import("@/pages/Trainer/Edit/EditTrainerProgramPage");
  __import.finally(() => {});
  return __import;
});

export const ViewTrainerProgramPage = lazy(() => {
  const __import = import("@/pages/Trainer/View/ViewTrainerProgramPage");
  __import.finally(() => {});
  return __import;
});

export const ViewTrainerProfilePage = lazy(() => {
  const __import = import("@/pages/Trainer/View/ViewTrainerProfilePage");
  __import.finally(() => {});
  return __import;
});

export const ViewTrainerTransactionsPage = lazy(() => {
  const __import = import("@/pages/Trainer/View/ViewTrainerTransactionsPage");
  __import.finally(() => {});
  return __import;
});

export const ViewTrainerFeedPage = lazy(() => {
  const __import = import("@/pages/Trainer/View/ViewTrainerFeedPage");
  __import.finally(() => {});
  return __import;
});

export const ViewTrainerDiscountPage = lazy(() => {
  const __import = import("@/pages/Trainer/View/ViewTrainerDiscountPage");
  __import.finally(() => {});
  return __import;
});

export const ViewTrainerDashboardPage = lazy(() => {
  const __import = import("@/pages/Trainer/View/ViewTrainerDashboardPage");
  __import.finally(() => {});
  return __import;
});

export const ListTrainerAthleteManagementPage = lazy(() => {
  const __import = import(
    "@/pages/Trainer/List/ListTrainerAthleteManagementPage"
  );
  __import.finally(() => {});
  return __import;
});

export const ListTrainerProgramPage = lazy(() => {
  const __import = import("@/pages/Trainer/List/ListTrainerProgramPage");
  __import.finally(() => {});
  return __import;
});

export const ForgotPassword = lazy(() => {
  const __import = import("@/pages/Common/Auth/ForgotPassword");
  __import.finally(() => {});
  return __import;
});

export const ChangePassword = lazy(() => {
  const __import = import("@/pages/Common/Auth/ChangePassword");
  __import.finally(() => {});
  return __import;
});

export const TrainerSignup = lazy(() => {
  const __import = import("@/pages/Trainer/Auth/TrainerSignup");
  __import.finally(() => {});
  return __import;
});

// Athlete Pages
export const ViewAthleteProfilePage = lazy(() => {
  const __import = import("@/pages/Athlete/View/ViewAthleteProfilePage");
  __import.finally(() => {});
  return __import;
});

export const ViewAthleteFeedPage = lazy(() => {
  const __import = import("@/pages/Athlete/View/ViewAthleteFeedPage");
  __import.finally(() => {});
  return __import;
});

export const ViewAthleteEmrollmentProgressPage = lazy(() => {
  const __import = import(
    "@/pages/Athlete/View/ViewAthleteEmrollmentProgressPage"
  );
  __import.finally(() => {});
  return __import;
});

export const ViewAthleteProgramPreviewPage = lazy(() => {
  const __import = import("@/pages/Athlete/View/ViewAthleteProgramPreviewPage");
  __import.finally(() => {});
  return __import;
});

export const ViewAthleteLibraryPage = lazy(() => {
  const __import = import("@/pages/Athlete/View/ViewAthleteLibraryPage");
  __import.finally(() => {});
  return __import;
});

export const ViewAthleteProgramPage = lazy(() => {
  const __import = import("@/pages/Athlete/View/ViewAthleteProgramPage");
  __import.finally(() => {});
  return __import;
});

export const ViewAthleteTrainerDetailsPage = lazy(() => {
  const __import = import("@/pages/Athlete/View/ViewAthleteTrainerDetailsPage");
  __import.finally(() => {});
  return __import;
});

export const ViewAthleteNotificationsPage = lazy(() => {
  const __import = import("@/pages/Athlete/View/ViewAthleteNotificationsPage");
  __import.finally(() => {});
  return __import;
});

export const HomePage = lazy(() => {
  const __import = import("@/pages/Athlete/View/HomePage");
  __import.finally(() => {});
  return __import;
});

export const AthleteSignup = lazy(() => {
  const __import = import("@/pages/Athlete/Auth/AthleteSignup");
  __import.finally(() => {});
  return __import;
});

export const AthleteProfileCompletion = lazy(() => {
  const __import = import("@/pages/Athlete/Auth/AthleteProfileCompletion");
  __import.finally(() => {});
  return __import;
});

export const TrainerProfileCompletion = lazy(() => {
  const __import = import("@/pages/Trainer/Auth/TrainerProfileCompletion");
  __import.finally(() => {});
  return __import;
});

// Common Pages
export const LoginPage = lazy(() => {
  const __import = import("@/pages/Common/Auth/LoginPage");
  __import.finally(() => {});
  return __import;
});

export const OAuthCallbackPage = lazy(() => {
  const __import = import("@/pages/Common/Auth/OAuthCallbackPage");
  __import.finally(() => {});
  return __import;
});

export const OAuthTestPage = lazy(() => {
  const __import = import("@/pages/Common/Auth/OAuthTestPage");
  __import.finally(() => {});
  return __import;
});

// Verification Pages
export const VerifyEmail = lazy(() => {
  const __import = import("@/pages/Common/Auth/VerifyEmail");
  __import.finally(() => {});
  return __import;
});

export const VerificationSent = lazy(() => {
  const __import = import("@/pages/Common/Auth/VerificationSent");
  __import.finally(() => {});
  return __import;
});

// Magic Login Pages
export const MagicLoginVerifyPage = lazy(() => {
  const __import = import("@/pages/MagicLogin/MagicLoginVerifyPage");
  __import.finally(() => {});
  return __import;
});

export const UserMagicLoginPage = lazy(() => {
  const __import = import("@/pages/MagicLogin/UserMagicLoginPage");
  __import.finally(() => {});
  return __import;
});

// 404 Page
export const NotFoundPage = lazy(() => {
  const __import = import("@/pages/404/NotFoundPage");
  __import.finally(() => {});
  return __import;
});

// Test/Playground Pages
export const TestComponents = lazy(() => {
  const __import = import("@/pages/PG/Custom/TestComponents");
  __import.finally(() => {});
  return __import;
});

// PLAYGROUND
export const PlaygroundPage = lazy(
  () => import("@/pages/PG/Custom/TestComponents")
);
