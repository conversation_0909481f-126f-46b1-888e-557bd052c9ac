import React, { useState } from "react";
import { Modal } from "@/components/Modal";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEnvelope, faCheckCircle, faExclamationTriangle, faRedo } from "@fortawesome/free-solid-svg-icons";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import { RoleMap } from "@/utils";

interface EmailVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  email: string;
  user_id: string;
  role: string;
}

const EmailVerificationModal: React.FC<EmailVerificationModalProps> = ({
  isOpen,
  onClose,
  email,
  user_id: _user_id,
  role,
}) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const { sdk } = useSDK();
  const { showToast } = useContexts();
  
  const [isResending, setIsResending] = useState(false);

  const containerStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
    borderColor: THEME_COLORS[mode].BORDER,
    color: THEME_COLORS[mode].TEXT,
  };

  const buttonStyles = {
    backgroundColor: THEME_COLORS[mode].PRIMARY,
    color: THEME_COLORS[mode].TEXT_ON_PRIMARY || THEME_COLORS[mode].BACKGROUND,
  };

  const buttonHoverStyles = {
    backgroundColor: THEME_COLORS[mode].PRIMARY_HOVER,
  };

  const secondaryButtonStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderColor: THEME_COLORS[mode].BORDER,
    color: THEME_COLORS[mode].TEXT,
  };

  const secondaryButtonHoverStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_HOVER,
  };

  const resendVerification = async () => {
    try {
      setIsResending(true);
      const result = await sdk.request({
        endpoint: `/v1/api/kanglink/${role}/lambda/resend_verification`,
        method: 'POST',
        body: { email, role }
      });

      if (!result.error) {
        showToast("Verification email sent successfully!", 4000, ToastStatusEnum.SUCCESS);
      } else {
        showToast(result.message || "Failed to resend verification email", 4000, ToastStatusEnum.ERROR);
      }
    } catch (error: any) {
      showToast(
        error?.response?.data?.message || error?.message || "Failed to resend verification email",
        4000,
        ToastStatusEnum.ERROR
      );
    } finally {
      setIsResending(false);
    }
  };

  return (
    <Modal 
      isOpen={isOpen} 
      modalCloseClick={onClose}
      title="Email Verification Required"
      modalHeader={false}
      classes={{ modal: "h-full", modalDialog: "h-full md:h-auto w-full md:max-w-md", modalContent: "" }}
    >
      <div
        className="relative w-full mx-auto shadow-lg rounded-lg px-8 py-10 transition-colors duration-200"
        style={containerStyles}
      >
        {/* Header */}
        <div className="text-center mb-8">
          <FontAwesomeIcon 
            icon={faEnvelope} 
            className="text-4xl mb-4 transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].PRIMARY }}
          />
          <h2
            className="text-2xl font-bold mb-2 transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            Email Verification Required
          </h2>
          <p
            className="text-sm transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
          >
            Please verify your email address to continue
          </p>
        </div>

        {/* Content */}
        <div className="mb-8">
          <div
            className="bg-primary/10 border border-primary/20 rounded-lg p-4 mb-6 transition-colors duration-200"
            style={{ 
              backgroundColor: `${THEME_COLORS[mode].PRIMARY}10`,
              borderColor: `${THEME_COLORS[mode].PRIMARY}20`
            }}
          >
            <div className="flex items-start space-x-3">
              <FontAwesomeIcon 
                icon={faCheckCircle} 
                className="text-green-500 mt-1" 
              />
              <div className="text-left">
                <h3
                  className="font-semibold mb-2 transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  Verification Email Sent
                </h3>
                <ul
                  className="text-sm space-y-1 transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                >
                  <li>• Check your email inbox</li>
                  <li>• Click the verification link</li>
                  <li>• Return here to login</li>
                </ul>
              </div>
            </div>
          </div>

          <div
            className="bg-secondary/10 border border-secondary/20 rounded-lg p-4 mb-6 transition-colors duration-200"
            style={{ 
              backgroundColor: `${THEME_COLORS[mode].BACKGROUND_SECONDARY}10`,
              borderColor: `${THEME_COLORS[mode].BORDER}20`
            }}
          >
            <div className="flex items-start space-x-3">
              <FontAwesomeIcon 
                icon={faExclamationTriangle} 
                className="text-yellow-500 mt-1" 
              />
              <div className="text-left">
                <h3
                  className="font-semibold mb-2 transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  Email Address
                </h3>
                <p
                  className="text-sm transition-colors duration-200"
                  style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                >
                  {email}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="space-y-3">
          <InteractiveButton
            onClick={onClose}
            className="w-full h-12 rounded font-semibold text-base transition-colors duration-200"
            style={buttonStyles}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = buttonHoverStyles.backgroundColor;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = buttonStyles.backgroundColor;
            }}
          >
            I've Verified My Email
          </InteractiveButton>
          
          <InteractiveButton
            onClick={onClose}
            className="w-full h-12 rounded font-semibold text-base transition-colors duration-200 border"
            style={secondaryButtonStyles}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = secondaryButtonHoverStyles.backgroundColor;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = secondaryButtonStyles.backgroundColor;
            }}
          >
            Back to Login
          </InteractiveButton>
        </div>

        {/* Footer */}
        <div className="mt-6 text-center">
          <p
            className="text-sm transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
          >
            Didn't receive the email?
          </p>
          <div className="mt-3">
            <InteractiveButton
              onClick={resendVerification}
              loading={isResending}
              disabled={isResending}
              className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200"
              style={secondaryButtonStyles}
              onMouseEnter={(e) => {
                if (!isResending) {
                  e.currentTarget.style.backgroundColor = secondaryButtonHoverStyles.backgroundColor;
                }
              }}
              onMouseLeave={(e) => {
                if (!isResending) {
                  e.currentTarget.style.backgroundColor = secondaryButtonStyles.backgroundColor;
                }
              }}
              color={THEME_COLORS[mode].PRIMARY}
            >
              <FontAwesomeIcon 
                icon={faRedo} 
                className={`h-4 w-4 ${isResending ? 'animate-spin' : ''}`}
              />
              {isResending ? 'Sending...' : 'Resend Email'}
            </InteractiveButton>
          </div>
          <p
            className="text-sm mt-2 transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
          >
            Check your spam folder or contact support if you need help.
          </p>
        </div>
      </div>
    </Modal>
  );
};

export default EmailVerificationModal; 