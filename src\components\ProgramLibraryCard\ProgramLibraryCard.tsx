import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { StarIcon, HeartIcon, PlayIcon } from "@heroicons/react/24/solid";
import { HeartIcon as HeartOutlineIcon } from "@heroicons/react/24/outline";

interface Program {
  id: string;
  name: string;
  description: string;
  price: number;
  rating: number;
  image: string;
  isFavorite?: boolean;
}

interface ProgramLibraryCardProps {
  program: Program;
  onFavoriteToggle?: (programId: string, isFavorite: boolean) => void;
}

const ProgramLibraryCard = ({
  program,
  onFavoriteToggle,
}: ProgramLibraryCardProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const navigate = useNavigate();
  const [isFavorite, setIsFavorite] = useState(program.isFavorite || false);

  const handleCardClick = () => {
    navigate(`/athlete/program/${program.id}`);
  };

  const handleFavoriteToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    const newFavoriteState = !isFavorite;
    setIsFavorite(newFavoriteState);
    onFavoriteToggle?.(program.id, newFavoriteState);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <StarIcon
        key={index}
        className={`w-4 h-4 ${
          index < rating ? "text-green-400" : "text-gray-300 dark:text-gray-600"
        }`}
      />
    ));
  };

  return (
    <div
      onClick={handleCardClick}
      className="w-72 rounded-md shadow-lg border transition-all duration-200 hover:shadow-xl hover:scale-105 cursor-pointer flex-shrink-0 mx-auto lg:mx-0"
      style={{
        backgroundColor: THEME_COLORS[mode].CARD_BG,
        borderColor: THEME_COLORS[mode].BORDER,
      }}
    >
      {/* Image Container */}
      <div className="relative w-full h-48 rounded-t-md overflow-hidden bg-gray-100 dark:bg-gray-800">
        <img
          src={program.image}
          alt={program.name}
          className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
        />

        {/* Play Button */}
        <div className="absolute bottom-3 right-3 w-10 h-10 bg-white rounded-full shadow-lg border flex items-center justify-center">
          <PlayIcon className="w-3 h-3 text-gray-500 ml-0.5" />
        </div>

        {/* Favorite Button */}
        <button
          onClick={handleFavoriteToggle}
          className="absolute top-3 right-3 w-8 h-8 bg-gray-900/30 rounded-full flex items-center justify-center"
        >
          {isFavorite ? (
            <HeartIcon className="w-4 h-4 text-white" />
          ) : (
            <HeartOutlineIcon className="w-4 h-4 text-white" />
          )}
        </button>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Program Name and Rating Row */}
        <div className="flex items-center justify-between mb-2">
          <h3
            className="text-base font-semibold transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            {program.name}
          </h3>

          {/* Rating */}
          <div className="flex items-center gap-1">
            {renderStars(program.rating)}
          </div>
        </div>

        {/* Description and Price Row */}
        <div className="flex items-center justify-between">
          <p
            className="text-sm transition-colors duration-200 flex-1"
            style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
          >
            {program.description}
          </p>

          {/* Price */}
          <span
            className="text-xl font-bold ml-4 transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            ${program.price}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ProgramLibraryCard;
