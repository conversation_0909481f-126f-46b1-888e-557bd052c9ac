import { useState } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import {
  ChevronDownIcon,
  ChevronRightIcon,
  CheckIcon,
  ClockIcon,
  PlayIcon,
} from "@heroicons/react/24/outline";
import { InteractiveButton } from "@/components/InteractiveButton";

interface CompactExerciseCardProps {
  exerciseId: string;
  exerciseName: string;
  sets: string;
  reps: string;
  rest: string;
  thumbnailUrl?: string;
  isCompleted?: boolean;
  onMarkComplete?: () => void;
}

const CompactExerciseCard = ({
  exerciseId,
  exerciseName,
  sets,
  reps,
  rest,
  thumbnailUrl = "https://placehold.co/128x80",
  isCompleted = false,
  onMarkComplete,
}: CompactExerciseCardProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [isCollapsed, setIsCollapsed] = useState(false);

  const cardStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  const exerciseIdStyles = {
    backgroundColor: THEME_COLORS[mode].PRIMARY,
    color: THEME_COLORS[mode].TEXT_ON_PRIMARY,
  };

  const thumbnailOverlayStyles = {
    backgroundColor: "rgba(0, 0, 0, 0.3)",
  };

  const playButtonStyles = {
    backgroundColor: "rgba(255, 255, 255, 0.9)",
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div
      className="bg-background border border-border rounded-lg p-4 sm:p-6 mb-4 transition-colors duration-200"
      style={cardStyles}
    >
      <div className="flex flex-col lg:flex-row lg:items-center gap-4">
        {/* Left Section - Exercise Info */}
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-3">
            {/* Exercise ID Badge */}
            <div
              className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold"
              style={exerciseIdStyles}
            >
              {exerciseId}
            </div>

            {/* Exercise Name */}
            <h3 className="text-lg font-semibold text-text">{exerciseName}</h3>
          </div>

          {/* Inline Stats */}
          <div className="flex flex-wrap items-center gap-4 text-sm text-text-secondary">
            <div className="flex items-center gap-1">
              <ClockIcon className="w-3.5 h-3.5" />
              <span>{sets} Sets</span>
            </div>
            <div className="flex items-center gap-1">
              <ClockIcon className="w-3.5 h-3.5" />
              <span>{reps} Reps</span>
            </div>
            <div className="flex items-center gap-1">
              <ClockIcon className="w-3.5 h-3.5" />
              <span>{rest} Rest</span>
            </div>
          </div>
        </div>

        {/* Right Section - Thumbnail and Actions */}
        <div className="flex items-center gap-4">
          {/* Video Thumbnail */}
          <div className="relative w-32 h-20 rounded-md overflow-hidden bg-gradient-to-r from-primary to-amber-400 flex-shrink-0">
            <img
              src={thumbnailUrl}
              alt={`${exerciseName} demonstration`}
              className="w-full h-full object-cover"
            />
            <div
              className="absolute inset-0 flex items-center justify-center"
              style={thumbnailOverlayStyles}
            >
              <button
                className="w-6 h-6 rounded-full flex items-center justify-center transition-transform hover:scale-105"
                style={playButtonStyles}
                aria-label="Play exercise video"
              >
                <PlayIcon className="w-3 h-3 text-primary ml-0.5" />
              </button>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <button
              onClick={toggleCollapse}
              className="p-1 hover:bg-background-secondary rounded transition-colors duration-200"
            >
              {isCollapsed ? (
                <ChevronRightIcon className="w-4 h-4 text-text-secondary" />
              ) : (
                <ChevronDownIcon className="w-4 h-4 text-text-secondary" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mark Complete Button */}
      <div className="flex justify-end mt-4">
        <InteractiveButton
          onClick={onMarkComplete}
          className={`px-6 py-3 ${
            isCompleted
              ? "bg-green-600 hover:bg-green-700"
              : "bg-primary hover:bg-primary-hover"
          } text-white`}
          disabled={isCompleted}
        >
          <CheckIcon className="w-4 h-4 mr-2" />
          {isCompleted ? "Completed" : "Mark Complete"}
        </InteractiveButton>
      </div>
    </div>
  );
};

export default CompactExerciseCard;
