import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

interface ProgramHeaderProps {
  courseName: string;
  programDuration: string;
}

const ProgramHeader = ({ courseName, programDuration }: ProgramHeaderProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <div className="space-y-2">
      <h1 
        className="text-2xl font-bold font-inter leading-loose transition-colors duration-200"
        style={{ color: THEME_COLORS[mode].TEXT }}
      >
        {courseName}
      </h1>
      <p 
        className="text-xl font-normal font-inter leading-7 transition-colors duration-200"
        style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
      >
        {programDuration}
      </p>
    </div>
  );
};

export default ProgramHeader;
