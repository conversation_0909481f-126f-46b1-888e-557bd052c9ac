import { useMutation, useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum, RestAPIMethodEnum } from "@/utils/Enums";

// Types based on the API documentation
export interface EnrollmentEligibility {
  can_enroll: boolean;
  reasons: string[];
  payment_options: {
    one_time: {
      available: boolean;
      reason: string | null;
    };
    subscription: {
      available: boolean;
      reason: string | null;
    };
  };
}

export interface SplitPricing {
  split_id: number;
  program_name: string;
  split_title: string;
  currency: string;
  pricing: {
    one_time: {
      amount: number;
      available: boolean;
      stripe_configured: boolean;
      description: string;
    };
    subscription: {
      amount: number;
      available: boolean;
      stripe_configured: boolean;
      description: string;
    };
    minimum: number;
  };
  recommendations: {
    best_value: "one_time" | "subscription" | null;
    savings_months: number | null;
  };
}

export interface CreateEnrollmentRequest {
  split_id: number | string;
  payment_type: "subscription" | "one_time";
  payment_method_id: string;
  affiliate_code?: string;
  coupon_code?: string;
}

export interface AppliedDiscount {
  type: string;
  discount_type: string;
  discount_value: string;
  discount_amount: number;
  source: string;
  coupon_code?: string;
}

export interface EnrollmentResponse {
  enrollment_id: number;
  payment_type: "subscription" | "one_time";
  amount: number;
  currency: string;
  status: string;
  payment_status: string;
  original_amount?: number;
  discount_amount?: number;
  applied_discounts?: AppliedDiscount[];
  affiliate_code?: string;
  commission_tracking?: boolean;
  stripe_subscription_id?: string;
  stripe_payment_intent_id?: string;
}

export interface PaymentAuthenticationResponse {
  error: true;
  message: string;
  requires_action: true;
  payment_intent: {
    id: string;
    client_secret: string;
  };
  subscription_id?: string;
}

export interface EnrollmentStatusResponse {
  status: "active" | "pending" | "payment_failed";
  enrollment_id?: number;
}

export interface AffiliateValidationRequest {
  affiliate_code: string;
  program_id: number | string;
}

export interface AffiliateValidationResponse {
  error: boolean;
  valid: boolean;
  trainer_id?: number;
  message: string;
}

export interface CouponValidationRequest {
  coupon_code: string;
  program_id: number | string;
  split_id: number | string;
  payment_type: "one_time" | "subscription";
  amount: number;
}

export interface CouponValidationResponse {
  error: boolean;
  valid: boolean;
  discount_amount?: number;
  coupon?: {
    id: number;
    code: string;
    discount_type: string;
    discount_value: number;
  };
}

export interface DiscountPreviewRequest {
  split_id: number | string;
  payment_type: "one_time" | "subscription";
  coupon_code?: string;
}

export interface DiscountPreviewResponse {
  error: boolean;
  data: {
    split_id: number;
    program_id: number;
    program_name: string;
    split_title: string;
    payment_type: string;
    original_amount: number;
    final_amount: number;
    total_discount_amount: number;
    savings_percentage: number;
    has_discounts: boolean;
    applied_discounts: AppliedDiscount[];
    coupon_validation?: {
      code: string;
      valid: boolean;
      message: string;
    };
  };
}

export const useEnrollment = () => {
  const { showToast, authState } = useContexts();
  const customModelQuery = useCustomModelQuery();

  // Check if user is logged in
  const isLoggedIn = authState.isAuthenticated;

  // Check enrollment eligibility
  const useCheckEligibility = (splitId: number | string) => {
    const customQuery = useCustomModelQuery();

    return useQuery({
      queryKey: ["enrollment-eligibility", splitId],
      queryFn: async () => {
        const response = await customQuery.mutateAsync({
          endpoint: `/v2/api/kanglink/custom/splits/${splitId}/eligibility`,
          method: RestAPIMethodEnum.GET,
        });

        if (response.error) {
          throw new Error(response.message);
        }

        return response.data;
      },
      enabled: isLoggedIn && !!splitId,
    });
  };

  // Get split pricing (available for everyone, not just logged in users)
  const useGetPricing = (splitId: number | string) => {
    const customQuery = useCustomModelQuery();

    return useQuery({
      queryKey: ["split-pricing", splitId],
      queryFn: async () => {
        const response = await customQuery.mutateAsync({
          endpoint: `/v2/api/kanglink/custom/splits/${splitId}/pricing`,
          method: RestAPIMethodEnum.GET,
        });

        if (response.error) {
          throw new Error(response.message);
        }

        return response.data as SplitPricing;
      },
      enabled: !!splitId, // Only require splitId, not login status
      retry: 3,
      retryDelay: 1000,
    });
  };

  // Create enrollment
  const useCreateEnrollment = () => {
    return useMutation({
      mutationFn: async (enrollmentData: CreateEnrollmentRequest) => {
        if (!isLoggedIn) {
          throw new Error("User must be logged in to enroll");
        }
        // {
        //     "split_id": "73",
        //     "payment_type": "one_time",
        //     "payment_method_id": "pm_1RgZ7kBgOlWo0lDUZUzUJaSk"
        // }
        const response = await customModelQuery.mutateAsync({
          endpoint: "/v2/api/kanglink/custom/athlete/enrollment",
          method: RestAPIMethodEnum.POST,
          body: enrollmentData,
        });

        // Handle different response types based on the API documentation
        if (response.error && (response as any).requires_action) {
          // Payment requires authentication - return the response for handling
          return response as PaymentAuthenticationResponse;
        }

        if (response.error) {
          throw new Error(response.message);
        }

        return response.data as EnrollmentResponse;
      },
      onSuccess: (data: any) => {
        // Only show success toast for completed enrollments
        if (data && !data.requires_action) {
          showToast(
            "Enrollment created successfully!",
            5000,
            ToastStatusEnum.SUCCESS
          );
        }
      },
      onError: (error: any) => {
        const message = error?.message || "Failed to create enrollment";
        showToast(message, 5000, ToastStatusEnum.ERROR);
      },
    });
  };

  // Get user enrollments
  const useGetUserEnrollments = () => {
    const customQuery = useCustomModelQuery();

    return useQuery({
      queryKey: ["user-enrollments"],
      queryFn: async () => {
        const response = await customQuery.mutateAsync({
          endpoint: "/v2/api/kanglink/custom/athlete/enrollments",
          method: RestAPIMethodEnum.GET,
        });

        if (response.error) {
          throw new Error(response.message);
        }

        return response.data;
      },
      enabled: isLoggedIn,
    });
  };

  // Check enrollment status (for polling after authentication)
  const useCheckEnrollmentStatus = () => {
    return useMutation({
      mutationFn: async (subscriptionId: string) => {
        if (!isLoggedIn) {
          throw new Error("User must be logged in to check enrollment status");
        }

        const response = await customModelQuery.mutateAsync({
          endpoint: `/v2/api/kanglink/custom/athlete/enrollment/status/${subscriptionId}`,
          method: RestAPIMethodEnum.GET,
        });

        if (response.error) {
          throw new Error(response.message);
        }

        return response.data as EnrollmentStatusResponse;
      },
    });
  };

  // Cancel enrollment
  const useCancelEnrollment = () => {
    return useMutation({
      mutationFn: async (enrollmentId: number) => {
        if (!isLoggedIn) {
          throw new Error("User must be logged in to cancel enrollment");
        }

        const response = await customModelQuery.mutateAsync({
          endpoint: `/v2/api/kanglink/custom/enrollment/${enrollmentId}/cancel`,
          method: RestAPIMethodEnum.POST,
          body: {},
        });

        if (response.error) {
          throw new Error(response.message);
        }

        return response;
      },
      onSuccess: () => {
        showToast(
          "Enrollment cancelled successfully",
          5000,
          ToastStatusEnum.SUCCESS
        );
      },
      onError: (error: any) => {
        const message = error?.message || "Failed to cancel enrollment";
        showToast(message, 5000, ToastStatusEnum.ERROR);
      },
    });
  };

  // Validate affiliate code
  const useValidateAffiliateCode = () => {
    return useMutation({
      mutationFn: async (data: AffiliateValidationRequest) => {
        const response = await customModelQuery.mutateAsync({
          endpoint: "/v2/api/kanglink/custom/affiliate/validate",
          method: RestAPIMethodEnum.POST,
          body: data,
        });

        return response as AffiliateValidationResponse;
      },
    });
  };

  // Validate coupon code
  const useValidateCouponCode = () => {
    return useMutation({
      mutationFn: async (data: CouponValidationRequest) => {
        const response = await customModelQuery.mutateAsync({
          endpoint: "/v2/api/kanglink/custom/coupon/validate",
          method: RestAPIMethodEnum.POST,
          body: data,
        });

        return response as CouponValidationResponse;
      },
    });
  };

  // Get discount preview
  const useGetDiscountPreview = () => {
    return useMutation({
      mutationFn: async (data: DiscountPreviewRequest) => {
        const queryParams = new URLSearchParams();
        queryParams.append("split_id", data.split_id.toString());
        queryParams.append("payment_type", data.payment_type);
        if (data.coupon_code) {
          queryParams.append("coupon_code", data.coupon_code);
        }

        const response = await customModelQuery.mutateAsync({
          endpoint: `/v2/api/kanglink/custom/discount/preview?${queryParams.toString()}`,
          method: RestAPIMethodEnum.GET,
        });

        if (response.error) {
          throw new Error(response.message);
        }

        return response as DiscountPreviewResponse;
      },
      retry: false,
    });
  };

  // Check if user is subscribed to a specific program
  const useCheckProgramSubscription = (programId: number | string) => {
    const enrollmentsQuery = useGetUserEnrollments();

    return useMemo(() => {
      if (!isLoggedIn || !enrollmentsQuery.data) {
        return {
          isSubscribed: false,
          isLoading: enrollmentsQuery.isLoading,
          error: enrollmentsQuery.error,
          subscriptions: [],
        };
      }

      // Handle different data structures
      let allEnrollments = [];

      if (Array.isArray(enrollmentsQuery.data)) {
        // Data is a flat array
        allEnrollments = enrollmentsQuery.data;
      } else if (
        enrollmentsQuery.data &&
        typeof enrollmentsQuery.data === "object"
      ) {
        // Data has categories (owned, subscribed, etc.)
        allEnrollments = [
          ...(enrollmentsQuery.data.owned || []),
          ...(enrollmentsQuery.data.subscribed || []),
        ];
      }

      const programSubscriptions = allEnrollments.filter((enrollment) => {
        // Check program ID match (handle both string and number)
        const programIdMatch =
          enrollment.program_id === Number(programId) ||
          enrollment.program_id === String(programId) ||
          String(enrollment.program_id) === String(programId);

        // Check if enrollment is valid (very flexible status checking)
        const isValidEnrollment =
          // Active status
          enrollment.status === "active" ||
          // Paid status
          enrollment.payment_status === "paid" ||
          // Pending but paid
          (enrollment.status === "pending" &&
            enrollment.payment_status === "paid") ||
          // One-time purchase that's paid
          (enrollment.payment_type === "one_time" &&
            enrollment.payment_status === "paid") ||
          // Subscription that's active or paid
          (enrollment.payment_type === "subscription" &&
            (enrollment.status === "active" ||
              enrollment.payment_status === "paid")) ||
          // Any enrollment that's not explicitly cancelled, expired, or failed
          (enrollment.status !== "cancelled" &&
            enrollment.status !== "expired" &&
            enrollment.payment_status !== "failed" &&
            enrollment.payment_status !== "refunded");

        return programIdMatch && isValidEnrollment;
      });

      return {
        isSubscribed: programSubscriptions.length > 0,
        isLoading: enrollmentsQuery.isLoading,
        error: enrollmentsQuery.error,
        subscriptions: programSubscriptions,
      };
    }, [
      enrollmentsQuery.data,
      enrollmentsQuery.isLoading,
      enrollmentsQuery.error,
      programId,
      isLoggedIn,
    ]);
  };

  return {
    isLoggedIn,
    useCheckEligibility,
    useGetPricing,
    useCreateEnrollment,
    useCheckEnrollmentStatus,
    useGetUserEnrollments,
    useCancelEnrollment,
    useValidateAffiliateCode,
    useValidateCouponCode,
    useGetDiscountPreview,
    useCheckProgramSubscription,
  };
};

export default useEnrollment;
