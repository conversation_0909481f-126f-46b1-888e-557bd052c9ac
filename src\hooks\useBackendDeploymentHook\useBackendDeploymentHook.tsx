import { useCustomModelQuery } from "@/query/shared";
import { useState } from "react";

import { useContexts } from "@/hooks/useContexts";

import { InteractiveButton } from "@/components/InteractiveButton";
import { DisplayDomainUrl } from "@/components/DisplayDomainUrl";

import { FaCloudUploadAlt, FaNodeJs } from "react-icons/fa";
import { SiNodedotjs } from "react-icons/si";

interface UseBackendDeploymentHookProps {
  project: any;
}

export const useBackendDeploymentHook = ({
  project,
}: UseBackendDeploymentHookProps) => {
  const {
    globalState: { settings, nodes, roles, routes },
  } = useContexts();
  const { mutateAsync: customRequest } = useCustomModelQuery();

  const config = {
    settings,
    nodes,
    roles,
    routes,
  };

  const [deployment, _setDeployment] = useState({}) as any;

  const [loading, setLoading] = useState({
    setup: false,
    create_repo: false,
    create_jenkins_job: false,
  });

  const [_backendBranches, _setBackendBranches] = useState([]);

  const handleSetup = async () => {
    try {
      setLoading((prev) => ({
        ...prev,
        setup: true,
      }));
      const result = await customRequest({
        endpoint: `/v1/api/{{project}}/custom/build/backend`,
        method: "POST",
        body: {
          project: project?.slug,
          config: JSON.stringify(config),
        },
      });

      if (result.error) {
        throw new Error(result.message);
      }

      setLoading((prev) => ({
        ...prev,
        setup: false,
      }));
    } catch {
      setLoading((prev) => ({
        ...prev,
        setup: false,
      }));
    }
  };

  const handleCreateJenkinsJob = async () => {
    try {
      setLoading((prev) => ({
        ...prev,
        create_jenkins_job: true,
      }));
      const result = await customRequest({
        endpoint: `/v1/api/{{project}}/custom/job/backend`,
        method: "POST",
        body: {
          project: project?.slug,
          branch: "master",
        },
      });

      if (result.error) {
        throw new Error(result.message);
      }

      setLoading((prev) => ({
        ...prev,
        create_jenkins_job: false,
      }));
    } catch {
      setLoading((prev) => ({
        ...prev,
        create_jenkins_job: false,
      }));
    }
  };

  const DEPLOY_ITEMS = [
    {
      id: 1,
      name: "Deployment",
      actionBtn: () => (
        <InteractiveButton
          className={`flex items-center rounded-md px-3 py-2 shadow-sm !cursor-default border border-[#C6C6C6] !shadow-green-500 !bg-green-500`}
          disabled={true}
          // onClick={handleInitializeProjectDeployment}
        >
          Initialized
        </InteractiveButton>
      ),
      icon: () => <FaCloudUploadAlt className="h-6 w-6 text-green-500" />,
    },
    // {
    //   id: 11,
    //   name: "Clear Development Images",
    //   actionBtn: () => (
    //     <InteractiveButton
    //       disabled={
    //         isLoading.state == true && isLoading.target == "clear_s3_images"
    //       }
    //       onClick={clearS3Images}
    //     >
    //       {isLoading.state == true && isLoading.target == "clear_s3_images"
    //         ? "Clearing..."
    //         : "Clear"}
    //     </InteractiveButton>
    //   ),
    //   icon: () => <FaCameraRetro className="h-6 w-6 text-red-500" />
    // },

    {
      id: 3,
      name: "Set Up Backend",
      actionBtn: () => (
        <div className="flex items-center gap-4">
          {deployment.has_be_repo ? (
            <DisplayDomainUrl
              text={`http://23.29.118.76:3000/mkdlabs/${project.slug}_backend.git`}
            />
          ) : (
            ""
          )}
          <InteractiveButton
            className={`flex cursor-pointer items-center rounded-md px-3 py-2 shadow-sm ${
              deployment.has_be_repo
                ? "!cursor-not-allowed border border-[#C6C6C6] !bg-green-500"
                : loading.setup
                  ? "!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700"
                  : "bg-primaryBlue text-white"
            } `}
            loading={loading.setup}
            disabled={deployment.has_be_repo || loading.setup}
            onClick={(_e: any) => handleSetup()}
          >
            {deployment.has_be_repo ? "Created" : "Setup"}
          </InteractiveButton>
        </div>
      ),
      icon: () => <FaNodeJs className="h-6 w-6 text-purple-500" />,
    },
    // {
    //   id: 4,
    //   name: "Android repository",
    //   actionBtn: () => (
    //     <div className="flex items-center gap-4">
    //       { deployment.has_android_repo ? (
    //         <DisplayDomainUrl
    //           text={`http://23.29.118.76:3000/mkdlabs/${project.slug}_android.git`}
    //         />
    //       ) : (
    //         ""
    //       )}
    //       <InteractiveButton
    //         className={`flex cursor-pointer items-center rounded-md px-3 py-2 shadow-sm ${
    //            deployment.has_android_repo
    //             ? "!cursor-not-allowed border border-[#C6C6C6] !bg-green-500"
    //             : isLoading.state === true &&
    //                 isLoading.target === "create_android_repo"
    //               ? "!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700"
    //               : "bg-primaryBlue text-white"
    //         } `}
    //         loading={
    //           isLoading.state == true &&
    //           isLoading.target == "create_android_repo"
    //         }
    //         disabled={
    //
    //           deployment.has_android_repo ||
    //           (isLoading.state == true &&
    //             isLoading.target == "create_android_repo")
    //         }
    //         onClick={(_e: any) => handleSetup("android")}
    //       >
    //         { deployment.has_android_repo
    //           ? "Created"
    //           : "Create"}
    //       </InteractiveButton>
    //     </div>
    //   ),
    //   icon: () => <FaAndroid className="h-6 w-6 text-blue-700" />
    // },
    // {
    //   id: 5,
    //   name: "IOS repository",
    //   actionBtn: () => (
    //     <div className="flex items-center gap-4">
    //       { deployment.has_ios_repo ? (
    //         <DisplayDomainUrl
    //           text={`http://23.29.118.76:3000/mkdlabs/${project.slug}_ios.git`}
    //         />
    //       ) : (
    //         ""
    //       )}
    //       <InteractiveButton
    //         className={`flex cursor-pointer items-center rounded-md px-3 py-2 shadow-sm ${
    //            deployment.has_ios_repo
    //             ? "!cursor-not-allowed border border-[#C6C6C6] !bg-green-500"
    //             : isLoading.state === true &&
    //                 isLoading.target === "create_ios_repo"
    //               ? "!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700"
    //               : "bg-primaryBlue text-white"
    //         } `}
    //         loading={
    //           isLoading.state == true && isLoading.target == "create_ios_repo"
    //         }
    //         disabled={
    //
    //           deployment.has_ios_repo ||
    //           (isLoading.state == true && isLoading.target == "create_ios_repo")
    //         }
    //         onClick={(_e: any) => handleSetup("ios")}
    //       >
    //         { deployment.has_ios_repo ? "Created" : "Create"}
    //       </InteractiveButton>
    //     </div>
    //   ),
    //   icon: () => <FaApple className="h-6 w-6 text-green-700" />
    // },
    // {
    //   id: 6,
    //   name: "Domain",
    //   actionBtn: () => {
    //     return !deployment.has_domain ? (
    //       <InteractiveButton
    //         className={`flex cursor-pointer items-center rounded-md bg-primaryBlue px-3 py-2 text-white shadow-sm ${
    //           isLoading.state == true && isLoading.target == "create_domain"
    //             ? "!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700"
    //             : ""
    //         } `}
    //         loading={
    //           isLoading.state == true && isLoading.target == "create_domain"
    //         }
    //         disabled={
    //           isLoading.state == true && isLoading.target == "create_domain"
    //         }
    //         onClick={(_e: any) => handleCreateDomain()}
    //       >
    //         Create
    //       </InteractiveButton>
    //     ) : deployment.has_domain ? (
    //       <InteractiveButton
    //         className={`flex cursor-pointer items-center rounded-md border border-[#C6C6C6] !bg-[#DC2626]  px-3 py-2 shadow-sm ${
    //           isLoading.state == true && isLoading.target == "delete_domain"
    //             ? "!cursor-not-allowed !bg-yellow-700"
    //             : ""
    //         } `}
    //         loading={
    //           isLoading.state == true && isLoading.target == "delete_domain"
    //         }
    //         disabled={
    //           isLoading.state == true && isLoading.target == "delete_domain"
    //         }
    //         onClick={(_e: any) => handleDeleteDomain()}
    //       >
    //         Delete
    //       </InteractiveButton>
    //     ) : null;
    //   },
    //   icon: () => <FaGlobe className="h-6 w-6 text-yellow-700" />
    // },

    {
      id: 8,
      name: "Back-end Jenkins job",
      actionBtn: () => (
        <div className="flex items-center gap-4">
          {deployment.has_be_job ? (
            <DisplayDomainUrl
              text={`http://23.29.118.76:8080/job/${project.slug}_backend/`}
            />
          ) : (
            ""
          )}

          <InteractiveButton
            className={`flex cursor-pointer items-center rounded-md px-3 py-2 shadow-sm ${
              deployment.has_be_job
                ? "!cursor-not-allowed border border-[#C6C6C6] !bg-green-500"
                : loading.create_jenkins_job
                  ? "!cursor-not-allowed border border-[#C6C6C6] !bg-yellow-700"
                  : "bg-primaryBlue text-white"
            } `}
            loading={loading.create_jenkins_job}
            disabled={deployment.has_be_job || loading.create_jenkins_job}
            onClick={(_e: any) => handleCreateJenkinsJob()}
          >
            {deployment?.has_be_job ? "Created" : "Run"}
          </InteractiveButton>
        </div>
      ),
      icon: () => <SiNodedotjs className="h-6 w-6 text-green-900" />,
    },
  ];

  return {
    DEPLOY_ITEMS,
  };
};
