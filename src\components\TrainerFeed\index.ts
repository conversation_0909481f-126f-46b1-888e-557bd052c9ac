import { lazy } from "react";

// Lazy load all TrainerFeed components
export const TrainerFeedHeader = lazy(() => import("./TrainerFeedHeader"));
export const TrainerPostComposer = lazy(() => import("./TrainerPostComposer"));
export const TrainerFeedPost = lazy(() => import("./TrainerFeedPost"));
export const PostActions = lazy(() => import("./PostActions"));
export const CommentInput = lazy(() => import("./CommentInput"));
export const ReplySection = lazy(() => import("./ReplySection"));
export const EmptyFeedState = lazy(() => import("./EmptyFeedState"));
export const ProgramSelector = lazy(() => import("./ProgramSelector"));
export const ReactionButton = lazy(() => import("./ReactionButton"));

// Export types
export * from "./types";
