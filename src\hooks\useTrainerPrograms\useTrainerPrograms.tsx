import { useQuery } from "@tanstack/react-query";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { Program } from "@/interfaces/model.interface";

// Extended program interface for trainer programs
export interface TrainerProgramResponse extends Program {
  price?: number;
  review_count?: number;
}

export interface TrainerProgramsResponse {
  data: TrainerProgramResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    num_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// Custom hook for fetching trainer programs
export const useTrainerPrograms = (
  trainerId: string | null,
  params?: {
    page?: number;
    limit?: number;
    sort_by?: string;
    sort_order?: string;
  }
) => {
  const customQuery = useCustomModelQuery();

  return useQuery({
    queryKey: ["trainer-programs", trainerId, params],
    queryFn: async () => {
      if (!trainerId) throw new Error("Trainer ID is required");

      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.sort_by) queryParams.append("sort_by", params.sort_by);
      if (params?.sort_order)
        queryParams.append("sort_order", params.sort_order);

      const endpoint = `/v2/api/kanglink/custom/public/trainer/${trainerId}/programs${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`;

      const result = await customQuery.mutateAsync({
        endpoint,
        method: "GET",
        requiresAuth: false,
      });

      return result as TrainerProgramsResponse;
    },
    enabled: !!trainerId,
    staleTime: 0, // Always fetch fresh data
    refetchOnMount: true, // Refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });
};
