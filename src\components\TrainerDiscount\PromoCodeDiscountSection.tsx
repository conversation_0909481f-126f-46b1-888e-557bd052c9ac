import { ChevronDown } from "lucide-react";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { InteractiveButton } from "../InteractiveButton";

interface PromoCodeData {
  code: string;
  discount_type: "fixed" | "percentage";
  discount_value: number;
  applies_to: "subscription" | "full_payment" | "both";
  expiry_date: string | null;
  usage_limit: number | null;
}

interface PromoCodeDiscountSectionProps {
  promoCode?: PromoCodeData;
  onChange?: (promoCode: PromoCodeData) => void;
  createDiscount: () => void;
  loading: boolean;
}

const PromoCodeDiscountSection = ({
  promoCode = {
    code: "",
    discount_type: "percentage",
    discount_value: 0,
    applies_to: "subscription",
    expiry_date: null,
    usage_limit: null,
  },
  onChange,
  createDiscount,
  loading = false,
}: PromoCodeDiscountSectionProps) => {
  return (
    <div className="bg-background border border-border rounded-md p-6 shadow-sm">
      <div className="space-y-6">
        <h3 className="text-sm font-medium text-text">
          Create Promo Code Discount:
        </h3>

        {/* Promo Code Input */}
        <MkdInputV2
          name="promoCode"
          type="text"
          value={promoCode.code}
          onChange={(e) => onChange?.({ ...promoCode, code: e.target.value })}
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label className="text-xs text-text">
              Promo Code
            </MkdInputV2.Label>
            <MkdInputV2.Field placeholder="Enter Promo Code" />
          </MkdInputV2.Container>
        </MkdInputV2>

        {/* Applies To */}
        <div>
          <label className="block text-xs text-text mb-2">Applies to:</label>
          <div className="flex gap-4">
            <MkdInputV2
              name="applies_to"
              type="radio"
              onChange={() =>
                onChange?.({ ...promoCode, applies_to: "subscription" })
              }
            >
              <MkdInputV2.Container className="flex h-[1.5rem] items-center justify-center gap-3 transition-colors duration-200">
                <MkdInputV2.Field
                  checked={promoCode.applies_to === "subscription"}
                  className="w-[1.5rem] cursor-pointer rounded-[0.5rem] outline-0 focus:outline-none focus:ring-0 transition-colors duration-200"
                />
                <MkdInputV2.Label className="text-xs text-text">
                  Subscription
                </MkdInputV2.Label>
              </MkdInputV2.Container>
            </MkdInputV2>

            <MkdInputV2
              name="applies_to"
              type="radio"
              onChange={() =>
                onChange?.({ ...promoCode, applies_to: "full_payment" })
              }
            >
              <MkdInputV2.Container className="flex h-[1.5rem] items-center justify-center gap-3 transition-colors duration-200">
                <MkdInputV2.Field
                  checked={promoCode.applies_to === "full_payment"}
                  className="w-[1.5rem] cursor-pointer rounded-[0.5rem] outline-0 focus:outline-none focus:ring-0 transition-colors duration-200"
                />
                <MkdInputV2.Label className="text-xs text-text">
                  Full Payment
                </MkdInputV2.Label>
              </MkdInputV2.Container>
            </MkdInputV2>

            <MkdInputV2
              name="applies_to"
              type="radio"
              onChange={() => onChange?.({ ...promoCode, applies_to: "both" })}
            >
              <MkdInputV2.Container className="flex h-[1.5rem] items-center justify-center gap-3 transition-colors duration-200">
                <MkdInputV2.Field
                  checked={promoCode.applies_to === "both"}
                  className="w-[1.5rem] cursor-pointer rounded-[0.5rem] outline-0 focus:outline-none focus:ring-0 transition-colors duration-200"
                />
                <MkdInputV2.Label className="text-xs text-text">
                  Both
                </MkdInputV2.Label>
              </MkdInputV2.Container>
            </MkdInputV2>
          </div>
        </div>

        {/* Discount Value */}
        <div className="flex gap-2">
          <MkdInputV2
            name="discount_value"
            type="text"
            value={promoCode.discount_value.toString()}
            onChange={(e) =>
              onChange?.({
                ...promoCode,
                discount_value: Number(e.target.value) || 0,
              })
            }
            className="flex-1"
          >
            <MkdInputV2.Container>
              <MkdInputV2.Field placeholder="Enter Value" />
            </MkdInputV2.Container>
          </MkdInputV2>

          <div className="relative">
            <select
              value={promoCode.discount_type}
              onChange={(e) =>
                onChange?.({
                  ...promoCode,
                  discount_type: e.target.value as "fixed" | "percentage",
                })
              }
              className="appearance-none px-3 py-2 pr-8 border border-border rounded-md bg-input text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              style={{
                backgroundImage: "none",
                WebkitAppearance: "none",
                MozAppearance: "none",
              }}
            >
              <option value="percentage">%</option>
              <option value="fixed">$</option>
            </select>
            <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text pointer-events-none" />
          </div>
        </div>

        {/* Create Discount Button */}
        <InteractiveButton
          loading={loading}
          disabled={loading}
          onClick={() => {
            createDiscount();
          }}
          className="px-4 py-2 border border-primary text-primary rounded-md hover:bg-primary hover:text-white transition-colors duration-200 text-sm font-medium"
        >
          + Create Discount
        </InteractiveButton>
      </div>
    </div>
  );
};

export default PromoCodeDiscountSection;
