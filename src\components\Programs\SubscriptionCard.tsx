import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { LockClosedIcon } from "@heroicons/react/24/solid";

interface SubscriptionCardProps {
  onSubscribe?: () => void;
  onBuy?: () => void;
}

const SubscriptionCard = ({ onSubscribe, onBuy }: SubscriptionCardProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <div 
      className="w-full max-w-sm mx-auto lg:max-w-none lg:w-72 rounded-md shadow-lg border p-4 transition-all duration-200"
      style={{
        backgroundColor: THEME_COLORS[mode].SECONDARY,
        borderColor: THEME_COLORS[mode].BORDER,
      }}
    >
      {/* Lock icon */}
      <div className="flex justify-center mb-4">
        <div className="w-12 h-12 flex items-center justify-center">
          <LockClosedIcon 
            className="w-5 h-6 transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
          />
        </div>
      </div>

      {/* Message */}
      <p 
        className="text-xs font-normal font-inter text-center mb-6 transition-colors duration-200"
        style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
      >
        To View Complete Program
      </p>

      {/* Action buttons */}
      <div className="space-y-4">
        {/* Subscribe button */}
        <button
          onClick={onSubscribe}
          className="w-full h-9 rounded border transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2"
          style={{
            backgroundColor: THEME_COLORS[mode].SECONDARY,
            borderColor: THEME_COLORS[mode].PRIMARY,
            color: THEME_COLORS[mode].PRIMARY,
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = THEME_COLORS[mode].BACKGROUND_HOVER;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = THEME_COLORS[mode].SECONDARY;
          }}
        >
          <span className="text-sm font-semibold font-inter">Subscribe</span>
        </button>

        {/* Or text */}
        <div className="text-center">
          <span 
            className="text-xs font-normal font-inter leading-none transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
          >
            or
          </span>
        </div>

        {/* Buy button */}
        <button
          onClick={onBuy}
          className="w-full h-9 rounded transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2"
          style={{
            backgroundColor: THEME_COLORS[mode].PRIMARY,
            color: '#FFFFFF',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = THEME_COLORS[mode].PRIMARY_HOVER;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = THEME_COLORS[mode].PRIMARY;
          }}
        >
          <span className="text-sm font-semibold font-inter">Buy</span>
        </button>
      </div>
    </div>
  );
};

export default SubscriptionCard;
