import { useState, useCallback } from "react";
import { useCreateModelMutation } from "@/query/shared/createModel";
import { useDeleteModelMutation } from "@/query/shared/deleteModel";
import { useProfile } from "@/hooks/useProfile";
import { useContexts } from "@/hooks/useContexts";
import { Models } from "@/utils/baas/models";
import { CreateComment, Comment, Reaction } from "@/interfaces/model.interface";
import { useCustomModelQuery } from "@/query/shared";
import { RestAPIMethodEnum } from "@/utils/Enums";

export interface UsePostInteractionsProps {
  post_id: string | number;
  target_type: "post" | "comment";
  onCommentAdded?: (comment: Comment) => void;
  onCommentDeleted?: (comment_id: string | number) => void;
  onReactionToggled?: (reaction: Reaction | null, is_added: boolean) => void;
}

export interface UsePostInteractionsReturn {
  // Comment functions
  addComment: (content: string, is_private?: boolean) => Promise<void>;
  deleteComment: (comment_id: string | number) => Promise<void>;

  // Reaction functions
  toggleReaction: (
    reaction_type: "like" | "love" | "fire" | "strong"
  ) => Promise<void>;

  fetchComments: () => Promise<void>;
  fetchReactions: () => Promise<void>;

  comments: Comment[] | null;
  reactions: Reaction[] | null;
  current_user_reaction: Reaction | null;

  // Loading states
  loading: {
    is_adding_comment: boolean;
    is_deleting_comment: boolean;
    is_toggling_reaction: boolean;
    is_fetching_comments: boolean;
    is_fetching_reactions: boolean;
  };
}

export const usePostInteractions = ({
  post_id,
  target_type,
}: UsePostInteractionsProps): UsePostInteractionsReturn => {
  const [loading, setLoading] = useState({
    is_adding_comment: false,
    is_deleting_comment: false,
    is_toggling_reaction: false,
    is_fetching_comments: false,
    is_fetching_reactions: false,
  });

  const [user_reaction, setUserReaction] = useState<Reaction | null>(null);
  const [reactions, setReactions] = useState<Reaction[] | null>(null);
  const [comments, setComments] = useState<Comment[] | null>(null);

  const { profile } = useProfile();
  const { showToast, tokenExpireError } = useContexts();

  // Mutations
  const { mutateAsync: createComment } = useCreateModelMutation(
    Models.COMMENT,
    { showToast: false }
  );
  const { mutateAsync: deleteComment } = useDeleteModelMutation(Models.COMMENT);
  const { mutateAsync: fetchRequest } = useCustomModelQuery();

  const fetchComments = useCallback(async () => {
    // Implement fetching comments
    // "/v2/api/kanglink/custom/trainer/feed/:post_id/comments",
    try {
      setLoading((prev) => ({ ...prev, is_fetching_comments: true }));
      const response = await fetchRequest({
        endpoint: `/v2/api/kanglink/custom/trainer/feed/${post_id}/comments?limit=100000000&page=1&include_replies=false`,
        method: "GET",
      });

      if (response?.error) {
        throw new Error(response.message || "Failed to fetch comments");
      }

      const comments = response?.data || response?.list || [];
      setComments(() => comments);
    } catch (error: any) {
      showToast(
        error.message || "Failed to fetch comments",
        4000,
        "error" as any
      );
    } finally {
      setLoading((prev) => ({ ...prev, is_fetching_comments: false }));
    }
  }, [fetchRequest, post_id, showToast]);

  const fetchReactions = useCallback(async () => {
    // Implement fetching reactions
    //  "/v2/api/kanglink/custom/trainer/feed/:target_type/:target_id/reactions",
    try {
      setLoading((prev) => ({ ...prev, is_fetching_reactions: true }));
      const response = await fetchRequest({
        endpoint: `/v2/api/kanglink/custom/trainer/feed/${target_type}/${post_id}/reactions?limit=100000000&page=1`,
        method: "GET",
      });

      if (response?.error) {
        throw new Error(response.message || "Failed to fetch reactions");
      }

      const reactions = response?.data || response?.list || [];
      const current_user_reaction = reactions.find(
        (reaction: Reaction) => reaction.user_id === profile?.id
      );
      setUserReaction(() => current_user_reaction || null);
      setReactions(() => reactions);
    } catch (error: any) {
      const message = error?.response?.data?.message || error?.message;
      showToast(message, 4000, "error" as any);
      tokenExpireError(message);
    } finally {
      setLoading((prev) => ({ ...prev, is_fetching_reactions: false }));
    }
  }, [
    target_type,
    post_id,
    fetchRequest,
    profile?.id,
    showToast,
    tokenExpireError,
  ]);

  const addComment = useCallback(
    async (content: string, is_private: boolean = false) => {
      if (!content.trim() || !profile?.id) {
        showToast("Please enter a comment", 3000, "error" as any);
        return;
      }

      setLoading((prev) => ({ ...prev, is_adding_comment: true }));
      try {
        const comment_payload: CreateComment = {
          user_id: profile.id,
          post_id: post_id,
          content: content.trim(),
          is_private: is_private,
          parent_comment_id: null, // Top-level comment
        };

        const response = await createComment(comment_payload);

        if (response?.error) {
          throw new Error(response.message || "Failed to add comment");
        }

        // Comment created successfully
        showToast("Comment added successfully!", 3000, "success" as any);

        fetchComments();
      } catch (error: any) {
        showToast(
          error.message || "Failed to add comment",
          4000,
          "error" as any
        );
      } finally {
        setLoading((prev) => ({ ...prev, is_adding_comment: false }));
      }
    },
    [post_id, profile?.id, createComment, showToast, fetchComments]
  );

  const deleteCommentHandler = useCallback(
    async (comment_id: string | number) => {
      if (!profile?.id) {
        showToast(
          "You must be logged in to delete comments",
          3000,
          "error" as any
        );
        return;
      }

      setLoading((prev) => ({ ...prev, is_deleting_comment: true }));
      try {
        const response = await deleteComment(comment_id);

        if (response?.error) {
          throw new Error(response.message || "Failed to delete comment");
        }

        fetchComments();
      } catch (error: any) {
        showToast(
          error.message || "Failed to delete comment",
          4000,
          "error" as any
        );
      } finally {
        setLoading((prev) => ({ ...prev, is_deleting_comment: false }));
      }
    },
    [profile?.id, deleteComment, showToast, fetchComments]
  );

  const toggleReaction = useCallback(
    async (reaction_type: "like" | "love" | "fire" | "strong") => {
      if (!profile?.id) {
        showToast("You must be logged in to react", 3000, "error" as any);
        return;
      }

      setLoading((prev) => ({ ...prev, is_toggling_reaction: true }));
      try {
        // Use the new unified reactions endpoint
        const response = await fetchRequest({
          endpoint: `/v2/api/kanglink/custom/trainer/feed/${target_type}/${post_id}/reactions`,
          method: RestAPIMethodEnum.POST,
          requiresAuth: true,
          body: {
            reaction_type: reaction_type,
          },
        });

        if (response?.error) {
          throw new Error(response.message || "Failed to toggle reaction");
        }

        // Refetch reactions to get updated state
        fetchReactions();
      } catch (error: any) {
        showToast(
          error.message || "Failed to update reaction",
          4000,
          "error" as any
        );
      } finally {
        setLoading((prev) => ({ ...prev, is_toggling_reaction: false }));
      }
    },
    [post_id, target_type, profile?.id, fetchRequest, showToast, fetchReactions]
  );

  return {
    addComment,
    deleteComment: deleteCommentHandler,
    toggleReaction,
    loading,
    fetchComments,
    fetchReactions,
    comments,
    reactions,
    current_user_reaction: user_reaction,
  };
};

export default usePostInteractions;
