import React, { useState } from "react";
import { StarRating } from "@/components/StarRating";
import { MkdButton } from "@/components/MkdButton";
import { ChevronDownIcon } from "@heroicons/react/24/outline";

interface ReviewCardProps {
  athleteName: string;
  reviewText: string;
  rating: number;
  onReply?: (reply: string) => void;
}

const ReviewCard: React.FC<ReviewCardProps> = ({
  athleteName,
  reviewText,
  rating,
  onReply,
}) => {
  const [replyText, setReplyText] = useState("");
  const [isExpanded, setIsExpanded] = useState(false);

  const handleReplySubmit = () => {
    if (replyText.trim() && onReply) {
      onReply(replyText);
      setReplyText("");
    }
  };

  return (
    <div className="w-full bg-input rounded-lg shadow-md border border-border">
      <div className="p-4 lg:p-6">
        {/* Header with athlete name and expand button */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-base font-medium text-text">
            {athleteName}
          </h3>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 hover:bg-background-hover rounded transition-colors duration-200"
          >
            <ChevronDownIcon 
              className={`h-4 w-4 text-text transition-transform duration-200 ${
                isExpanded ? "rotate-180" : ""
              }`} 
            />
          </button>
        </div>

        {/* Review content - collapsible */}
        {isExpanded && (
          <>
            {/* Review text */}
            <div className="mb-4 p-4 bg-background rounded-lg border border-border">
              <p className="text-sm text-text-secondary">
                {reviewText || "Review"}
              </p>
            </div>

            {/* Rating */}
            <div className="flex items-center gap-2 mb-6">
              <StarRating rating={rating} size="sm" />
              {rating && <span className="text-base text-text-secondary">{rating}</span>}
            </div>

            {/* Reply section */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-text">
                Write a Reply
              </h4>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={replyText}
                  onChange={(e) => setReplyText(e.target.value)}
                  placeholder="Write a Comment..."
                  className="flex-1 h-10 px-3 bg-input border border-border rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm text-text placeholder-text-disabled"
                />
                <MkdButton
                  onClick={handleReplySubmit}
                  className="h-10 px-4 bg-primary hover:bg-primary-hover text-white text-base font-semibold rounded-r-md rounded-l-none"
                >
                  Post
                </MkdButton>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default ReviewCard;
