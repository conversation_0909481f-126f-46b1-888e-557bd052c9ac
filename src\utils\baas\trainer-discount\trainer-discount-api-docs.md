# Trainer Discount Management API Documentation

## Overview
This document provides comprehensive API specifications for the trainer discount management system. Each discount configuration is linked to a specific trainer's program and affects pricing for both subscription and full payment options.

## Base URL
```
https://api.kanglink.com/v2/api/kanglink/custom/trainer
```

## Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer {jwt_token}
```

---

## API Endpoints

### 1. Get Program Discount Settings

**Endpoint:** `GET /programs/{programId}/discounts`

**Purpose:** Retrieve existing discount configuration for a specific program

**URL Parameters:**
- `programId` (number, required) - The ID of the trainer's program

**Example Request:**
```bash
GET /programs/123/discounts
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "programId": 123,
    "affiliateLink": "www.affiliate-link.com",
    "saleDiscount": {
      "type": "fixed",
      "value": 5,
      "applyToAll": true
    },
    "promoCode": {
      "code": "SAVE20",
      "discountType": "percentage",
      "discountValue": 20,
      "appliesTo": {
        "subscription": true,
        "fullPayment": false
      },
      "isActive": true,
      "expiryDate": "2024-12-31T23:59:59Z",
      "usageLimit": 100,
      "usedCount": 15
    },
    "subscriptionDiscounts": [
      {
        "id": 1,
        "tierId": 1,
        "name": "Monthly Subscription",
        "originalPrice": 5.00,
        "discountType": "fixed",
        "discountValue": 1.00,
        "finalPrice": 4.00
      },
      {
        "id": 2,
        "tierId": 2,
        "name": "Quarterly Subscription",
        "originalPrice": 7.00,
        "discountType": "percentage",
        "discountValue": 10,
        "finalPrice": 6.30
      }
    ],
    "fullPriceDiscounts": [
      {
        "id": 1,
        "tierId": 1,
        "name": "Full Price - Basic",
        "originalPrice": 15.00,
        "discountType": "fixed",
        "discountValue": 11.00,
        "finalPrice": 4.00
      },
      {
        "id": 2,
        "tierId": 2,
        "name": "Full Price - Premium",
        "originalPrice": 20.00,
        "discountType": "fixed",
        "discountValue": 16.00,
        "finalPrice": 4.00
      }
    ],
    "lastUpdated": "2024-01-15T10:30:00Z",
    "updatedBy": "trainer_456"
  }
}
```

---

### 2. Get Program Pricing Tiers

**Endpoint:** `GET /programs/{programId}/pricing`

**Purpose:** Fetch the base pricing structure for subscription and full payment options

**URL Parameters:**
- `programId` (number, required) - The ID of the trainer's program

**Example Request:**
```bash
GET /programs/123/pricing
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "programId": 123,
    "programName": "Advanced Fitness Program",
    "subscriptionTiers": [
      {
        "id": 1,
        "name": "Monthly Subscription",
        "price": 5.00,
        "currency": "USD",
        "billingCycle": "monthly",
        "stripeProductId": "prod_monthly_123",
        "stripePriceId": "price_monthly_123"
      },
      {
        "id": 2,
        "name": "Quarterly Subscription",
        "price": 7.00,
        "currency": "USD",
        "billingCycle": "quarterly",
        "stripeProductId": "prod_quarterly_123",
        "stripePriceId": "price_quarterly_123"
      }
    ],
    "fullPaymentTiers": [
      {
        "id": 1,
        "name": "Full Price - Basic",
        "price": 15.00,
        "currency": "USD",
        "features": ["Basic access", "Email support"],
        "stripeProductId": "prod_basic_123",
        "stripePriceId": "price_basic_123"
      },
      {
        "id": 2,
        "name": "Full Price - Premium",
        "price": 20.00,
        "currency": "USD",
        "features": ["Premium access", "Priority support", "Extra content"],
        "stripeProductId": "prod_premium_123",
        "stripePriceId": "price_premium_123"
      }
    ],
    "currency": "USD",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

---

### 3. Update Program Discount Settings

**Endpoint:** `PUT /programs/{programId}/discounts`

**Purpose:** Save all discount configurations for a program

**URL Parameters:**
- `programId` (number, required) - The ID of the trainer's program

**Request Body:**
```json
{
  "affiliateLink": "www.new-affiliate-link.com",
  "saleDiscount": {
    "type": "percentage",
    "value": 10,
    "applyToAll": true
  },
  "promoCode": {
    "code": "NEWCODE",
    "discountType": "fixed",
    "discountValue": 5,
    "appliesTo": {
      "subscription": true,
      "fullPayment": true
    },
    "expiryDate": "2024-12-31T23:59:59Z",
    "usageLimit": 50
  },
  "subscriptionDiscounts": [
    {
      "tierId": 1,
      "discountType": "fixed",
      "discountValue": 2.00
    },
    {
      "tierId": 2,
      "discountType": "percentage",
      "discountValue": 15
    }
  ],
  "fullPriceDiscounts": [
    {
      "tierId": 1,
      "discountType": "fixed",
      "discountValue": 5.00
    },
    {
      "tierId": 2,
      "discountType": "percentage",
      "discountValue": 25
    }
  ]
}
```

**Example Response:**
```json
{
  "success": true,
  "message": "Discount settings updated successfully",
  "data": {
    "programId": 123,
    "updatedAt": "2024-01-15T10:30:00Z",
    "affectedPricingTiers": 4,
    "stripeUpdated": true,
    "promoCodeCreated": true
  }
}
```

---

### 4. Validate Promo Code

**Endpoint:** `POST /programs/{programId}/promo-codes/validate`

**Purpose:** Check if a promo code is available and valid

**URL Parameters:**
- `programId` (number, required) - The ID of the trainer's program

**Request Body:**
```json
{
  "code": "SAVE20"
}
```

**Success Response:**
```json
{
  "success": true,
  "data": {
    "isValid": true,
    "isAvailable": true,
    "conflicts": [],
    "suggestions": []
  }
}
```

**Error Response (Code Exists):**
```json
{
  "success": false,
  "error": {
    "code": "PROMO_CODE_EXISTS",
    "message": "Promo code already exists for this program",
    "suggestions": ["SAVE21", "SAVE2024", "NEWSAVE20"]
  }
}
```

---

### 5. Calculate Discount Preview

**Endpoint:** `POST /programs/{programId}/discounts/preview`

**Purpose:** Preview the effect of discount changes before saving

**URL Parameters:**
- `programId` (number, required) - The ID of the trainer's program

**Request Body:**
```json
{
  "saleDiscount": {
    "type": "percentage",
    "value": 15,
    "applyToAll": false
  },
  "subscriptionDiscounts": [
    {
      "tierId": 1,
      "discountType": "fixed",
      "discountValue": 1.50
    }
  ],
  "fullPriceDiscounts": [
    {
      "tierId": 1,
      "discountType": "percentage",
      "discountValue": 20
    }
  ]
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "originalRevenue": {
      "subscription": 12.00,
      "fullPayment": 35.00,
      "total": 47.00
    },
    "discountedRevenue": {
      "subscription": 10.20,
      "fullPayment": 29.75,
      "total": 39.95
    },
    "revenueImpact": {
      "amount": -7.05,
      "percentage": -15.0
    },
    "pricingPreview": {
      "subscriptionTiers": [
        {
          "id": 1,
          "originalPrice": 5.00,
          "discountedPrice": 4.25,
          "savings": 0.75,
          "savingsPercentage": 15.0
        }
      ],
      "fullPaymentTiers": [
        {
          "id": 1,
          "originalPrice": 15.00,
          "discountedPrice": 12.75,
          "savings": 2.25,
          "savingsPercentage": 15.0
        }
      ]
    }
  }
}
```

---

### 6. Delete/Deactivate Promo Code

**Endpoint:** `DELETE /programs/{programId}/promo-codes/{codeId}`

**Purpose:** Remove or deactivate an existing promo code

**URL Parameters:**
- `programId` (number, required) - The ID of the trainer's program
- `codeId` (number, required) - The ID of the promo code to delete

**Example Response:**
```json
{
  "success": true,
  "message": "Promo code deactivated successfully",
  "data": {
    "codeId": 456,
    "deactivatedAt": "2024-01-15T10:30:00Z"
  }
}
```

---

## Error Handling

### Common Error Codes:
- `400` - Bad Request (Invalid input data)
- `401` - Unauthorized (Invalid or missing token)
- `403` - Forbidden (Insufficient permissions)
- `404` - Not Found (Program or resource not found)
- `409` - Conflict (Promo code already exists)
- `422` - Unprocessable Entity (Validation errors)
- `500` - Internal Server Error

### Error Response Format:
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      "field": "Specific field error"
    }
  }
}
```

---

## Business Logic Notes

### Discount Calculation Priority:
1. Sale Discount (if applyToAll is true)
2. Individual tier discounts
3. Promo codes (applied at checkout)

### Validation Rules:
- Discount values cannot exceed original price
- Percentage discounts must be between 0-100
- Promo codes must be unique per program
- Expiry dates must be in the future

### Stripe Integration:
- Discount changes should update Stripe products/prices
- Promo codes should be created as Stripe coupons
- Price changes require new Stripe price objects
