import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import { v4 as uuidv4 } from "uuid";
import { ChevronUpIcon } from "@/assets/svgs";
import { Skeleton as SkeletonLoader } from "@/components/Skeleton";
import { StringCaser } from "@/utils/utils";
import { useContexts } from "@/hooks/useContexts";
import { displayName } from "./utils";

const getMaxHeight = (maxHeight: any) => {
  if (maxHeight) {
    return `max-h-[${maxHeight}]`;
  }
  return `max-h-[18.75rem]`;
};

// Ref interface for exposed methods
export interface SearchableDropdownRef {
  updateValue: (value: any) => void;
  refreshOptions: () => void;
  clearValue: () => void;
  openDropdown: () => void;
  closeDropdown: () => void;
  toggleDropdown: () => void;
}

interface SearchableDropdownProps {
  onSelect?: (option: any, clear?: boolean) => void;
  showBorder?: boolean;
  display: string | string[] | { and: string[]; or: string[] };
  value?: string | number | null;
  uniqueKey: string;
  selector?: any;
  disabled?: boolean;
  placeholder?: string;
  label?: string;
  maxHeight?: string;
  height?: string;
  selectedOptions?: any[];
  table?: string;
  errors?: any;
  name?: string;
  className?: string;
  join?: string[];
  filter?: string[];
  mode?: string;
  useExternalData?: boolean;
  externalDataLoading?: boolean;
  externalDataOptions?: any[];
  onReady?: (data: any[]) => void;

  showSearchIcon?: boolean;
  required?: boolean;
  dataRetrievalState?: any;
  displaySeparator?: string;
  customOptions?: {
    show: boolean;
    action?: () => void;
    icon?: JSX.Element;
    children?: string;
  }[];
  onPopoverStateChange?: (show: boolean) => void;
  popoverShown?: boolean;
  footerAction?: React.ReactNode;
}

const SearchableDropdown = forwardRef<
  SearchableDropdownRef,
  SearchableDropdownProps
>(
  (
    {
      onSelect,
      display = "display",
      value,
      uniqueKey,
      disabled = false,
      placeholder = "- search -",
      label = "Select",
      maxHeight = "18.75rem",

      table = "",
      errors,
      name,
      className = "w-[23rem]",
      join = [],
      filter = [],

      useExternalData = false,
      externalDataLoading = false,
      externalDataOptions = [],
      onReady,
      showSearchIcon = false,
      required = false,
      dataRetrievalState,
      displaySeparator = "",
      customOptions = [],
      onPopoverStateChange,
      popoverShown,
      footerAction,
    },
    ref
  ) => {
    const stringCaser = new StringCaser();
    // Refs
    const uniqueId = uuidv4();
    const uniqueClassName = btoa(uniqueId);

    // Context
    const { globalState, getMany: getList } = useContexts();
    const dropdownModel = globalState[dataRetrievalState ?? table];

    // State
    const [options, setOptions] = useState<any[]>([]);
    const [showLists, setShowLists] = useState<boolean>(false);
    const [searchValue, setSearchValue] = useState<string>("");
    const [selectedOption, setSelectedOption] = useState(null);
    const [localOptions, setLocalOptions] = useState<any[]>([]);

    // Expose methods via ref
    useImperativeHandle(
      ref,
      () => ({
        updateValue: (newValue: any) => {
          setSelectedOption(newValue);
          if (onSelect) {
            onSelect(newValue);
          }
        },
        refreshOptions: () => {
          refreshOptions();
        },
        clearValue: () => {
          selectOption(null, true);
        },
        openDropdown: () => {
          setShowLists(true);
        },
        closeDropdown: () => {
          setShowLists(false);
        },
        toggleDropdown: () => {
          setShowLists((prev) => !prev);
        },
      }),
      [onSelect]
    );

    // Hooks
    const memoizedFilter = useMemo(() => filter, [filter]);
    const memoizedExternalDataOptions = useMemo(
      () => JSON.stringify(externalDataOptions),
      [externalDataOptions]
    );

    const getDropdowList = async () => {
      const result = await getList(table, {
        ...{ ...(memoizedFilter?.length ? { filter: memoizedFilter } : null) },
        ...{ ...(join && join?.length ? { join } : null) },
      });
      if (!result.error) {
        if (table === "user") {
          const usersWithFullName = result?.data?.map(
            (item: { first_name: any; last_name: any }) => {
              if (item?.first_name || item?.last_name) {
                return {
                  ...item,
                  full_name: stringCaser.Capitalize(
                    `${item.first_name} ${item.last_name}`,
                    {
                      separator: " ",
                    }
                  ),
                };
              } else {
                return item;
              }
            }
          );
          setOptions(() => [...usersWithFullName]);
          setLocalOptions(() => [...usersWithFullName]);
        } else {
          setOptions(() => [...result?.data]);
          setLocalOptions(() => [...result?.data]);
        }
        if (value) {
          const selectedOption = result?.data.find(
            (option: { [x: string]: string | number }) =>
              option[uniqueKey] == value
          );
          // console.log("value >>", value);
          // console.log("result?.data >>", result?.data);
          // console.log("selectedOption >>", selectedOption);
          if (selectedOption) {
            setSelectedOption(() => selectedOption);
          }
        }

        if (onReady) {
          onReady(result?.data);
        }
      }
    };

    const selectOption = (
      option: any,
      clear = false,
      externalUpdate = true
    ) => {
      // setSelectedOption(option[display])
      if (clear) {
        setSelectedOption(null);
        setSearchValue("");
        if (onSelect) {
          onSelect(null, true);
        }
        setShowLists(false);
        console.log("clearing");
        return;
      }

      if (externalUpdate && !onSelect) {
        return;
      }
      setSelectedOption(option);
      if (externalUpdate) {
        onSelect && onSelect(option);
      }
      if (searchValue) {
        setSearchValue("");
      }
      if (options.length && options?.length > localOptions?.length) {
        setLocalOptions(options);
      }
      if (showLists) {
        setShowLists(false);
      }
      // setShowLists((prev) => !prev);
    };

    function refreshOptions() {
      // console.log("value >>", value);
      if (useExternalData) {
        if (localOptions?.length) {
          const option = localOptions.find((item) => item[uniqueKey] === value);

          if (option) {
            // console.log("localOptions option >>", option);
            selectOption(option, false, false);
          }
        } else if (externalDataOptions?.length) {
          const option = externalDataOptions.find(
            (item) => item[uniqueKey] === value
          );

          if (option) {
            // console.log("externalDataOptions option >>", option);
            selectOption(option, false, false);
          }
        }
        // setOptions(() => [...externalDataOptions]);
        // setLocalOptions(() => [...externalDataOptions]);
      } else {
        getDropdowList();
      }
    }

    const getDisplayValue = useCallback(() => {
      if (showLists || searchValue) {
        return searchValue;
      } else if (selectedOption) {
        return displayName(
          selectedOption,
          display,
          displaySeparator,
          "selectedOption in value"
        );
      } else if (value && options && options?.length) {
        const valueOption = options?.find(
          (opt) => opt[uniqueKey] === Number(value)
        );
        if (valueOption) {
          return displayName(
            valueOption,
            display,
            displaySeparator,
            "options in value"
          );
        } else {
          return "";
        }
      } else {
        return "";
      }
    }, [showLists, value, searchValue, selectOption, options]);

    const onSetSearchValue = useCallback(
      (value: string) => {
        // console.log("onSetSearchValue")
        setSearchValue(value);
        if (value) {
          const matches = options.filter((option) =>
            displayName(option, display, displaySeparator, "search")
              ?.toLowerCase()
              .includes(value?.toLowerCase())
          );
          setLocalOptions(matches);
        } else {
          setLocalOptions(options);
        }
        // setLedgerSelected(false)
      },
      [searchValue]
    );

    useEffect(() => {
      const abortController = new AbortController();

      const handleClick = (e: MouseEvent) => {
        const target = e.target as HTMLElement;
        const dropdownElement = document.querySelector(`.${uniqueClassName}`);

        // Check if click is outside the entire dropdown component
        if (dropdownElement && !dropdownElement.contains(target)) {
          setShowLists(false);
        }
      };

      // Only add listener when dropdown is open
      if (showLists) {
        window.addEventListener("click", handleClick, {
          signal: abortController.signal,
        });
      }

      return () => abortController.abort();
    }, [showLists, uniqueClassName]);

    useEffect(() => {
      // console.log("memoizedExternalDataOptions >>", memoizedExternalDataOptions);
      if (!useExternalData && !options?.length) {
        getDropdowList();
      }
    }, [useExternalData]);

    useEffect(() => {
      if (useExternalData) {
        setOptions(() => [...externalDataOptions]);
        setLocalOptions(() => [...externalDataOptions]);
      }
    }, [useExternalData, memoizedExternalDataOptions]);

    // useEffect(() => {
    //   if (["reactive"].includes(mode)) {
    //     console.log("options?.length >>", options?.length);
    //     // getDropdowList();
    //   }
    // }, [memoizedFilter]);

    useEffect(() => {
      if (onPopoverStateChange) {
        if (!popoverShown && showLists) {
          onPopoverStateChange(true);
        }
      }
    }, [popoverShown, showLists]);
    // console.log("selectedOption >>", selectedOption);
    return (
      <>
        <div className={`relative ${uniqueClassName} ${className}`}>
          {label && (
            <label className="block text-[.875rem] mb-2 text-sm font-bold text-text cursor-pointer">
              {label}
              {required && <sup className="text-[.825rem] text-red-600">*</sup>}
            </label>
          )}
          {/* <div
            className={`text-text-secondary group relative w-full min-w-full max-w-full rounded border text-base shadow-md ${
              showBorder ? "border" : ""}`}
          > */}
          {externalDataLoading || dropdownModel?.loading ? (
            <SkeletonLoader
              count={1}
              counts={[2]}
              className={`!h-[3rem] !max-h-[3rem] !min-h-[3rem] !gap-0 overflow-hidden rounded-lg !bg-[#ebebeb] !p-0 ${className}`}
            />
          ) : (
            <>
              <div
                className={`flex h-[3rem] w-full items-center justify-normal rounded-lg border-2 pl-3 shadow-sm transition-all duration-200 ease-out ${
                  disabled
                    ? " border-border text-text-disabled"
                    : showLists
                      ? " border-primary shadow-md ring-2 ring-border rounded-b-none dark:border-primary"
                      : " border-border hover:border-border-hover "
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  if (!showLists) {
                    setShowLists(true);
                  }
                }}
              >
                {showSearchIcon && !disabled && (
                  <div className="!w-4 ">
                    <svg
                      className="w-4 h-4 text-text-secondary dark:text-gray-400"
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 20 20"
                    >
                      <path
                        stroke="currentColor"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                      />
                    </svg>
                  </div>
                )}
                <div className="grow">
                  <input
                    type="text"
                    disabled={disabled}
                    placeholder={placeholder}
                    id={uniqueId}
                    className={`${
                      disabled
                        ? "bg-transparent text-text-disabled"
                        : "bg-transparent text-text"
                    } showListButton h-full w-full appearance-none truncate rounded-[.625rem] border-0 px-3 py-2 leading-tight placeholder-text-secondary transition-colors duration-200 focus:outline-none focus:outline-0 focus:ring-0 dark:text-white dark:placeholder-gray-400`}
                    // title={selectedOption && displayName(selectedOption, display, "title")}
                    value={getDisplayValue()}
                    onFocus={(e) => {
                      e.stopPropagation();
                      setShowLists(true);
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowLists(true);
                    }}
                    // onBlur={() => showLists && setShowLists(false)}
                    onChange={(e) =>
                      onSetSearchValue(e.target.value.toLowerCase())
                    }
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                      }
                    }}
                  />
                </div>
                {!disabled && (
                  <div className="flex justify-center items-center mr-3">
                    <ChevronUpIcon
                      stroke="currentColor"
                      className={`w-5 h-5 text-text dark:text-white transition-transform duration-200 ease-out ${
                        showLists ? "rotate-0" : "rotate-180"
                      }`}
                    />
                  </div>
                )}
              </div>

              <div
                style={{
                  zIndex: 99999999,
                }}
                className={`absolute top-full w-full rounded-b-lg bg-background backdrop-blur-sm text-sm shadow-2xl ring-1 ring-border focus:outline-none sm:text-sm transition-all duration-200 ease-out transform origin-top ${
                  showLists
                    ? "opacity-100 scale-y-100 translate-y-0 visible"
                    : "opacity-0 scale-y-95 -translate-y-2 invisible"
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                {/* Scrollable options area */}
                <div
                  className={`overflow-y-auto py-3 ${getMaxHeight(maxHeight)}`}
                >
                  <div
                    className={`flex h-[2.8rem] min-h-[2.8rem] text-text cursor-pointer items-center justify-start gap-5 truncate px-4 py-2 text-sm font-medium transition-all duration-150 ease-out hover:bg-background-hover hover:text-text  ${
                      !selectedOption && !value
                        ? "bg-background-secondary text-text font-semibold"
                        : ""
                    }`}
                    onClick={() => selectOption(null, true)}
                  >
                    <span className="text-xs">✕</span>
                    None
                  </div>
                  {customOptions.length &&
                  customOptions.find((customOption) => customOption?.show)
                    ? customOptions?.map((option, optionIndex) => {
                        if (option?.show) {
                          return (
                            <div
                              key={optionIndex}
                              title={
                                option?.children &&
                                typeof option?.children === "string"
                                  ? option?.children
                                  : option?.icon &&
                                      typeof option?.icon === "string"
                                    ? option?.icon
                                    : ""
                              }
                              className={`flex h-[2.8rem] min-h-[2.8rem] cursor-pointer items-center justify-start gap-5 truncate px-4 py-2 text-sm font-medium transition-all duration-150 ease-out hover:bg-background-hover hover:text-text  `}
                              onClick={() => option?.action && option?.action()}
                            >
                              {option?.icon ? option.icon : null}
                              {option?.children ? option.children : null}
                            </div>
                          );
                        }
                      })
                    : null}
                  {localOptions.length
                    ? localOptions?.map((option, index) => {
                        if (option?.searchableType === "section") {
                          return (
                            <div
                              aria-disabled={true}
                              key={index}
                              className={`flex h-[2.8rem] min-h-[2.8rem] w-full items-center justify-start gap-5 truncate bg-background-secondary px-3 py-2 text-sm font-bold capitalize text-text-inverse hover:bg-background-hover hover:text-text active:bg-background-active `}
                            >
                              {option?.display}
                            </div>
                          );
                        }
                        return (
                          <button
                            type="button"
                            key={index}
                            title={
                              option &&
                              displayName(
                                option,
                                display,
                                displaySeparator,
                                "title"
                              )
                            }
                            className={`flex w-full h-[2.8rem] min-h-[2.8rem] cursor-pointer text-text items-center justify-start gap-5 truncate px-4 py-2 text-sm font-medium transition-all duration-150 ease-out hover:bg-background-hover hover:text-text  ${
                              selectedOption &&
                              ((value && value === option[uniqueKey]) ||
                                displayName(
                                  option,
                                  display,
                                  displaySeparator,
                                  "item condition 1"
                                ) ===
                                  displayName(
                                    selectedOption,
                                    display,
                                    displaySeparator,
                                    "item condition 2"
                                  ))
                                ? "bg-background-hover text-text font-semibold border-r-2 border-primary"
                                : ""
                            } `}
                            onClick={() => selectOption(option)}
                          >
                            {displayName(
                              option,
                              display,
                              displaySeparator,
                              "display value"
                            )}
                          </button>
                        );
                      })
                    : null}
                </div>

                {/* Fixed footer action outside scrollable area */}
                {footerAction ? (
                  <div className="border-t border-border bg-background-secondary backdrop-blur-sm">
                    <div className="flex h-[2.8rem] min-h-[2.8rem] cursor-pointer items-center justify-start gap-3 truncate px-4 py-2 text-sm font-medium text-text transition-all duration-150 ease-out hover:bg-background-secondary hover:text-primary">
                      {footerAction}
                    </div>
                  </div>
                ) : null}
              </div>
              {/* </div> */}

              {errors && name && errors?.[name!] && (
                <p className="text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500">
                  {stringCaser.Capitalize(errors?.[name!]?.message, {
                    separator: " ",
                  })}
                </p>
              )}
            </>
          )}
        </div>
      </>
    );
  }
);

export default memo(SearchableDropdown);
