import { useState } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import {
  ChevronDownIcon,
  ChevronRightIcon,
  CalendarDaysIcon,
} from "@heroicons/react/24/outline";
import { WorkoutDay } from "@/assets/data/program_data";

interface DayCardProps {
  day: WorkoutDay;
  weekNumber: number;
  onExerciseComplete: (exerciseId: string) => void;
}

const DayCard = ({
  day,
  weekNumber: _weekNumber,
  onExerciseComplete: _onExerciseComplete,
}: DayCardProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [isCollapsed, setIsCollapsed] = useState(day.isCollapsed || false);

  const cardStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  const headerStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Don't render if it's a rest day with no sessions
  if (day.sessions.length === 0) {
    return (
      <div
        className="bg-background-secondary border border-border rounded-md p-4 transition-colors duration-200"
        style={cardStyles}
      >
        <div className="flex items-center gap-3">
          <CalendarDaysIcon className="w-5 h-5 text-text-secondary" />
          <div>
            <h3 className="text-lg font-semibold text-text">
              Day {day.dayNumber}: {day.name}
            </h3>
            <p className="text-sm text-text-secondary">
              Rest Day - No sessions scheduled
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="bg-background-secondary border border-border rounded-md shadow-sm transition-colors duration-200"
      style={cardStyles}
    >
      {/* Day Header */}
      <button
        onClick={toggleCollapse}
        className="w-full p-4 text-left transition-colors duration-200 hover:bg-background rounded-t-md"
        style={headerStyles}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              {isCollapsed ? (
                <ChevronRightIcon className="w-4 h-4 text-text-secondary" />
              ) : (
                <ChevronDownIcon className="w-4 h-4 text-text-secondary" />
              )}
            </div>
            <CalendarDaysIcon className="w-5 h-5 text-primary" />
            <div>
              <h3 className="text-lg font-semibold text-text">
                Day {day.dayNumber}: {day.name}
              </h3>
            </div>
          </div>

          {/* Day Stats */}
          <div className="flex items-center gap-3 text-sm text-text-secondary">
            <span>{day.sessions.length} Sessions</span>
            <span>
              {day.sessions.reduce(
                (total, session) => total + session.exercises.length,
                0
              )}{" "}
              Exercises
            </span>
          </div>
        </div>
      </button>

      {/* Day Content */}
      {!isCollapsed && (
        <div className="p-4 pt-0 space-y-3">
          {day.sessions.map((_session, index) => (
            // <SessionCard
            //   key={session.id}
            //   session={session}
            //   weekNumber={weekNumber}
            //   dayNumber={day.dayNumber}
            //   onExerciseComplete={onExerciseComplete}
            // />
            <div key={index}></div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DayCard;
