import React, { Component, ErrorInfo, ReactNode } from "react";
import { InteractiveButton } from "@/components/InteractiveButton"; // Using existing button
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Check } from "lucide-react";
import { useTheme } from "@/hooks/useTheme";
import { Theme } from "@/utils/Enums";

interface Props {
  children: ReactNode;
  fallbackMessage?: any;
  isDarkMode?: boolean;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  copied: boolean;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    copied: false,
  };

  public static getDerivedStateFromError(_: Error): State {
    // Update state so the next render will show the fallback UI.
    return { hasError: true, copied: false };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Uncaught error:", error, errorInfo);
    this.setState({ error, errorInfo });
    // You could also log the error to an error reporting service here
    // e.g., Sentry, LogRocket, etc.
  }

  private copyErrorToClipboard = async () => {
    const { error, errorInfo } = this.state;
    const errorText = `Error: ${error?.message || "Unknown error"}\n\nStack Trace:\n${error?.stack || "No stack trace available"}\n\nComponent Stack:\n${errorInfo?.componentStack || "No component stack available"}`;

    try {
      await navigator.clipboard.writeText(errorText);
      this.setState({ copied: true });
      setTimeout(() => this.setState({ copied: false }), 2000);
    } catch (err) {
      console.error("Failed to copy error to clipboard:", err);
    }
  };

  private handleResetError = () => {
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      copied: false,
    });
    // Optionally, you could try to re-render the children or navigate to a safe page
    // For simplicity, we'll just reset the error state, allowing children to re-render.
    // If the error is persistent, it might be caught again.
    // Consider adding a prop to navigate to a specific route on reset.
  };

  public render() {
    if (this.state.hasError) {
      const isDarkMode = this.props.isDarkMode;
      const isDevelopment =
        typeof window !== "undefined" &&
        (location.hostname === "localhost" ||
          location.hostname === "127.0.0.1" ||
          location.hostname.includes("dev") ||
          (window as any).__DEV__ !== false);

      // Theme-aware styles using the specified color scheme
      const containerClasses = isDarkMode
        ? "border-red-800 bg-red-900 text-red-200"
        : "border-red-200 bg-red-50 text-red-800";

      const detailsClasses = isDarkMode
        ? "bg-red-800/20 border-red-700/30"
        : "bg-red-100/50 border-red-300/30";

      const buttonClasses = isDarkMode
        ? "bg-red-800 text-red-100 hover:bg-red-700"
        : "bg-red-600 text-white hover:bg-red-700";

      return (
        <div
          className={`flex flex-col items-center justify-start min-h-svh p-6 border rounded-lg transition-colors duration-200 ${containerClasses}`}
        >
          <AlertTriangle className="h-12 w-12 mb-4" />
          <h2 className="text-xl font-semibold mb-2">
            Oops! Something went wrong.
          </h2>
          <p className="text-center mb-4 text-sm">
            {this.props.fallbackMessage ||
              "We encountered an unexpected error. Please try again."}
          </p>
          {isDevelopment && this.state.error && (
            <details
              className={`mb-4 p-3 border rounded-md w-full max-w-3xl text-left transition-colors duration-200 ${detailsClasses}`}
            >
              <summary className="cursor-pointer font-medium text-xs flex items-center justify-between">
                <span>Error Details (Development Only)</span>
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.copyErrorToClipboard();
                  }}
                  className={`ml-2 p-1 rounded transition-colors duration-200 ${
                    isDarkMode
                      ? "hover:bg-red-700/30 text-red-300"
                      : "hover:bg-red-200/50 text-red-600"
                  }`}
                  title={this.state.copied ? "Copied!" : "Copy error details"}
                >
                  {this.state.copied ? (
                    <Check className="h-3 w-3" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </button>
              </summary>
              <pre className="mt-2 text-xs whitespace-pre-wrap break-all">
                {this.state.error.toString()}
                {this.state.errorInfo &&
                  `\nComponent Stack:\n${this.state.errorInfo.componentStack}`}
              </pre>
            </details>
          )}
          <InteractiveButton
            onClick={this.handleResetError}
            className={`w-fit px-3 ${buttonClasses}`}
          >
            Try Again
          </InteractiveButton>
        </div>
      );
    }

    return this.props.children;
  }
}

// Wrapper component that uses hooks and passes theme info to the class component
const ErrorBoundaryWithTheme: React.FC<Omit<Props, "isDarkMode">> = ({
  children,
  fallbackMessage,
}) => {
  const { state } = useTheme();
  const isDarkMode = state?.theme === Theme.DARK;

  return (
    <ErrorBoundary isDarkMode={isDarkMode} fallbackMessage={fallbackMessage}>
      {children}
    </ErrorBoundary>
  );
};

export default ErrorBoundaryWithTheme;
