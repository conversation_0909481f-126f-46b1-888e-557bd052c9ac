{"name": "mkd-baasv5-frontend", "private": true, "version": "1.10.0", "scripts": {"dev:skip": "vite", "tc": "tsc --noEmit", "lint": "eslint . --fix", "preview": "vite preview", "test": "playwright test", "build": "tsc && vite build", "test:e2e": "playwright test", "dev:test": "vite --port 3002", "test:ui": "playwright test --ui", "dev": "node git-hook-setup.js && vite", "test:unit": "echo \"Unit tests not implemented yet\"", "test:integration": "echo \"Integration tests not implemented yet\"", "tw": "npx tailwindcss -i ./src/index.css -o ./src/output.css --watch", "generate-pwa-assets": "pwa-assets-generator --preset minimal public/brand_logo.png"}, "dependencies": {"@craftjs/core": "^0.2.0-beta.11", "@fontsource/inter": "^5.0.15", "@fontsource/roboto-mono": "^5.0.16", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@fullcalendar/core": "^5.11.3", "@fullcalendar/daygrid": "^5.11.3", "@fullcalendar/interaction": "^5.11.3", "@fullcalendar/list": "^5.11.3", "@fullcalendar/react": "^5.11.2", "@fullcalendar/timegrid": "^5.11.3", "@headlessui/react": "^1.7.14", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.1.0", "@hotjar/browser": "^1.0.9", "@mantine/core": "^6.0.19", "@mantine/hooks": "^6.0.19", "@react-google-maps/api": "^2.19.2", "@stripe/react-stripe-js": "^2.1.0", "@stripe/stripe-js": "^1.52.1", "@tailwindcss/forms": "^0.5.3", "@tanstack/react-query": "^5.67.1", "@tippyjs/react": "^4.2.6", "@uppy/core": "^3.7.1", "@uppy/dashboard": "^3.4.1", "@uppy/drag-drop": "^3.0.2", "@uppy/facebook": "^3.1.3", "@uppy/file-input": "^3.0.3", "@uppy/golden-retriever": "^3.1.0", "@uppy/google-drive": "^3.1.1", "@uppy/image-editor": "^2.1.2", "@uppy/instagram": "^3.1.3", "@uppy/onedrive": "^3.1.1", "@uppy/progress-bar": "^3.0.3", "@uppy/react": "^3.1.2", "@uppy/tus": "^3.4.0", "@uppy/webcam": "^3.3.1", "@uppy/xhr-upload": "^3.5.0", "ace-builds": "^1.4.12", "apexcharts": "^3.40.0", "axios": "^1.5.0", "bootstrap": "^5.2.3", "codemirror": "^5.65.16", "file-saver": "^2.0.5", "framer-motion": "^10.16.4", "fullcalendar": "^5.11.3", "html-to-image": "^1.11.11", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jszip": "^3.10.1", "lucide-react": "^0.475.0", "moment": "^2.29.4", "openai": "^4.24.1", "papaparse": "^5.4.1", "pdfjs-dist": "^3.4.120", "pluralize": "^8.0.0", "pretty-rating-react": "^2.2.0", "qr-scanner": "^1.4.2", "qrcode": "^1.5.3", "react": "^18.2.0", "react-ace": "^10.1.0", "react-addons-update": "^15.6.3", "react-apexcharts": "^1.4.0", "react-calendar": "^4.2.1", "react-codemirror2": "^7.3.0", "react-contenteditable": "^3.3.7", "react-dnd": "^10.0.2", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-google-maps": "^9.4.5", "react-hook-form": "^7.46.1", "react-icons": "^4.11.0", "react-input-emoji": "^5.4.1", "react-loading-skeleton": "^3.3.1", "react-modal": "^3.16.1", "react-modern-calendar-datepicker": "^3.1.6", "react-outside-click-handler": "^1.3.0", "react-pdf": "^7.7.0", "react-quill": "^2.0.0", "react-router": "^6.15.0", "react-router-dom": "^6.11.1", "react-select": "^5.8.0", "react-speech-recognition": "^3.10.0", "react-spinners": "^0.13.8", "react-timeago": "^7.2.0", "react-toggle": "^4.1.3", "react-tooltip": "^5.25.2", "redux": "^4.2.1", "regenerator-runtime": "^0.14.1", "slick-carousel": "^1.8.1", "swiper": "^9.3.1", "tw-elements": "^1.0.0-beta2", "twilio-video": "^2.27.0", "uppy": "^3.20.0", "use-debounce": "^9.0.4", "uuid": "^9.0.1", "xlsx": "^0.18.5", "yup": "^1.2.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@playwright/test": "^1.51.1", "@types/node": "^22.13.1", "@types/prettier": "^3.0.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-toggle": "^4.0.5", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "@vite-pwa/assets-generator": "^0.0.8", "@vitejs/plugin-react": "^4.0.0", "@vitejs/plugin-react-refresh": "^1.3.6", "@vitejs/plugin-react-swc": "^3.8.0", "autoprefixer": "^10.4.14", "eslint": "^8.57.1", "eslint-config-react-app": "^7.0.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "lint-staged": "^13.2.2", "postcss": "^8.4.23", "prettier-plugin-tailwindcss": "^0.2.8", "tailwindcss": "^3.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.2", "typescript-eslint": "^8.26.0", "vite": "^4.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-pwa": "^0.16.4"}}