import { useState } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { StarIcon, HeartIcon } from "@heroicons/react/24/solid";
import { HeartIcon as HeartOutlineIcon } from "@heroicons/react/24/outline";

interface Program {
  id: string;
  name: string;
  description: string;
  price: number;
  rating: number;
  image: string;
  isFavorite?: boolean;
}

interface ProgramGridCardProps {
  program: Program;
  onFavoriteToggle?: (programId: string, isFavorite: boolean) => void;
  onProgramClick?: (programId: string) => void;
}

const ProgramGridCard = ({
  program,
  onFavoriteToggle,
  onProgramClick,
}: ProgramGridCardProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [isFavorite, setIsFavorite] = useState(program.isFavorite || false);

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    const newFavoriteState = !isFavorite;
    setIsFavorite(newFavoriteState);
    onFavoriteToggle?.(program.id, newFavoriteState);
  };

  const handleCardClick = () => {
    onProgramClick?.(program.id);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <StarIcon
        key={index}
        className={`h-4 w-4 ${
          index < rating ? "text-yellow-400" : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <div
      onClick={handleCardClick}
      className="w-full rounded-lg shadow-lg border transition-all duration-200 hover:shadow-xl hover:scale-105 cursor-pointer"
      style={{
        backgroundColor: THEME_COLORS[mode].CARD_BG,
        borderColor: THEME_COLORS[mode].BORDER,
      }}
    >
      {/* Image Container */}
      <div className="relative">
        <div className="w-full h-40 sm:h-48 md:h-44 lg:h-48 xl:h-52 2xl:h-56 rounded-t-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
          <img
            src={program.image}
            alt={program.name}
            className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
          />
        </div>

        {/* Favorite Button */}
        <button
          onClick={handleFavoriteClick}
          className="absolute top-2 right-2 sm:top-3 sm:right-3 w-8 h-8 sm:w-9 sm:h-9 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center hover:bg-black/40 transition-colors duration-200"
        >
          {isFavorite ? (
            <HeartIcon className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
          ) : (
            <HeartOutlineIcon className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
          )}
        </button>
      </div>

      {/* Content */}
      <div className="p-3 sm:p-4 lg:p-4 xl:p-5">
        {/* Program Name */}
        <h3
          className="text-sm sm:text-base lg:text-base xl:text-lg font-semibold mb-2 sm:mb-3 transition-colors duration-200"
          style={{ color: THEME_COLORS[mode].TEXT }}
        >
          {program.name}
        </h3>

        {/* Description */}
        <p
          className="text-xs sm:text-sm lg:text-sm xl:text-base mb-3 sm:mb-4 line-clamp-3 transition-colors duration-200"
          style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
        >
          {program.description}
        </p>

        {/* Rating and Price */}
        <div
          className="pt-2 sm:pt-3 border-t transition-colors duration-200"
          style={{ borderColor: THEME_COLORS[mode].BORDER }}
        >
          <div className="flex items-center justify-between">
            {/* Rating */}
            <div className="flex items-center gap-1">
              {renderStars(program.rating)}
            </div>

            {/* Price */}
            <span
              className="text-sm sm:text-base lg:text-base xl:text-lg font-bold transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              ${program.price}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgramGridCard;
