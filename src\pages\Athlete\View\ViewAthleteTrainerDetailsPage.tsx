import { useSearchParams } from "react-router-dom";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import TrainerHeader from "@/components/TrainerDetails/TrainerHeader";
import TrainerAbout from "@/components/TrainerDetails/TrainerAbout";
import TrainingPlansGrid from "@/components/TrainerDetails/TrainingPlansGrid";
import { useTrainerDetails } from "@/hooks/useTrainerDetails";
import { useTrainerPrograms } from "@/hooks/useTrainerPrograms";
import { useTrainerPageTitle } from "@/hooks/usePageTitle";
import {
  useAthleteFavoritePrograms,
  useAthleteProgramFavorites,
} from "@/hooks/useAthleteFavorites";

const ViewAthleteTrainerDetailsPage = () => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [searchParams] = useSearchParams();
  const trainerId = searchParams.get("id");

  // Initialize favorite hooks
  const { toggleProgramFavorite } = useAthleteProgramFavorites();
  const { data: favoritePrograms } = useAthleteFavoritePrograms();

  // Fetch trainer details and programs
  const {
    data: trainerData,
    isLoading: isLoadingTrainer,
    error: trainerError,
  } = useTrainerDetails(trainerId);

  const {
    data: programsResponse,
    isLoading: isLoadingPrograms,
    error: programsError,
  } = useTrainerPrograms(trainerId, {
    page: 1,
    limit: 20,
    sort_by: "created_at",
    sort_order: "desc",
  });

  // Update page title when trainer data is loaded
  useTrainerPageTitle(
    trainerData?.full_name ||
      (trainerData?.first_name && trainerData?.last_name
        ? `${trainerData.first_name} ${trainerData.last_name}`
        : undefined)
  );

  // Transform API data to match component expectations
  const transformedTrainerData = trainerData
    ? {
        id: trainerData.id?.toString() || "",
        name:
          trainerData.full_name ||
          `${trainerData.first_name || ""} ${trainerData.last_name || ""}`.trim(),
        image: trainerData.photo || "",
        yearsOfExperience: trainerData.experience_years || 0,
        areasOfExpertise: trainerData.specialization || [],
        rating: trainerData.rating || 0,
        about: trainerData.bio ? [trainerData.bio] : [],
        certifications: trainerData.certifications || [],
        review_count: trainerData.review_count || 0,
        program_count: trainerData.program_count || 0,
      }
    : null;

  // Transform programs data to match component expectations
  const trainingPlans =
    programsResponse?.data?.map((program) => ({
      id: program.id?.toString() || "",
      name: program.program_name || "",
      description: program.program_description || "",
      price: program.price || 0,
      level: program.target_levels?.[0] || "Beginner",
      image: program.image || "",
      isFavorite:
        favoritePrograms?.data?.some(
          (fav) => fav.id.toString() === program.id?.toString()
        ) || false,
      rating: program.rating || 0,
      currency: program.currency || "USD",
      splits: program.splits || [],
    })) || [];

  // Handle favorite toggle for training plans (programs)
  const handleFavoriteToggle = async (planId: string, isFavorite: boolean) => {
    try {
      await toggleProgramFavorite(planId, isFavorite);
    } catch (error) {
      // Error is handled by the mutation hook's onError callback
      void error;
    }
  };

  // Loading state for trainer details only
  if (isLoadingTrainer) {
    return (
      <div
        className="transition-colors duration-200 min-h-screen flex items-center justify-center"
        style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND }}
      >
        <div className="text-center">
          <div
            className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4"
            style={{ borderColor: THEME_COLORS[mode].PRIMARY }}
          ></div>
          <p style={{ color: THEME_COLORS[mode].TEXT }}>
            Loading trainer details...
          </p>
        </div>
      </div>
    );
  }

  // Error state for trainer details only (don't block on programs error)
  if (trainerError || !transformedTrainerData) {
    return (
      <div
        className="transition-colors duration-200 min-h-screen flex items-center justify-center"
        style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND }}
      >
        <div className="text-center">
          <p style={{ color: THEME_COLORS[mode].TEXT }}>
            {trainerError?.message || "Trainer not found"}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="transition-colors duration-200"
      style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND }}
    >
      <div className="flex max-w-7xl mx-auto">
        {/* Main Content */}
        <div className="flex-1">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-8 xl:px-12 2xl:px-16 py-4 sm:py-6 md:py-8 lg:py-8 xl:py-12">
            {/* Trainer Header */}
            <TrainerHeader trainer={transformedTrainerData} />

            {/* Trainer About */}
            <TrainerAbout trainer={transformedTrainerData} />

            {/* Training Plans */}
            {isLoadingPrograms ? (
              <div className="mt-8">
                <h2
                  className="text-xl font-semibold mb-4"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  Training Plans
                </h2>
                <div className="text-center py-8">
                  <div
                    className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4"
                    style={{ borderColor: THEME_COLORS[mode].PRIMARY }}
                  ></div>
                  <p style={{ color: THEME_COLORS[mode].TEXT }}>
                    Loading programs...
                  </p>
                </div>
              </div>
            ) : programsError ? (
              <div className="mt-8">
                <h2
                  className="text-xl font-semibold mb-4"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  Training Plans
                </h2>
                <div className="text-center py-8">
                  <p style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}>
                    Unable to load programs at this time.
                  </p>
                </div>
              </div>
            ) : (
              <TrainingPlansGrid
                plans={trainingPlans}
                onFavoriteToggle={handleFavoriteToggle}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewAthleteTrainerDetailsPage;
