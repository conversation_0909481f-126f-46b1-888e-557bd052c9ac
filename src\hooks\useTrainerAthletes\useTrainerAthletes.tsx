import { useState, useEffect, useMemo } from "react";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";

export interface TrainerAthleteFilters {
  athlete_name: string;
  payment_type: string;
  program_name: string;
  progress: string;
}

export interface TrainerAthleteSorting {
  sort_by: string;
  sort_order: string;
}

export interface TrainerAthleteData {
  enrollment_id: number;
  athlete_id: number;
  athlete_name: string;
  athlete_email: string;
  athlete_photo: string;
  program_id: number;
  program_name: string;
  split_id: number;
  split_title: string;
  payment_type: string;
  payment_type_display: string;
  amount: number;
  currency: string;
  enrollment_date: string;
  enrollment_status: string;
  payment_status: string;
  progress_percentage: number;
  progress_status: string;
  total_days_completed: number;
  total_exercises_completed: number;
  last_activity_date: string;
}

export interface TrainerAthletePagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface TrainerAthleteResponse {
  error: boolean;
  message: string;
  data: TrainerAthleteData[];
  pagination: TrainerAthletePagination;
  filters: TrainerAthleteFilters;
  sorting: TrainerAthleteSorting;
}

export const useTrainerAthletes = () => {
  const { showToast } = useContexts();
  const { mutateAsync: customModelQuery, isPending } = useCustomModelQuery({
    showToast: false,
  });

  // State management
  const [athletes, setAthletes] = useState<TrainerAthleteData[]>([]);
  const [pagination, setPagination] = useState<TrainerAthletePagination>({
    page: 1,
    limit: 20,
    total: 0,
    num_pages: 1,
    has_next: false,
    has_prev: false,
  });

  // Filter state
  const [filters, setFilters] = useState<TrainerAthleteFilters>({
    athlete_name: "",
    payment_type: "",
    program_name: "",
    progress: "",
  });

  // Sorting state
  const [sorting, setSorting] = useState<TrainerAthleteSorting>({
    sort_by: "enrollment_date",
    sort_order: "desc",
  });

  // Current page state
  const [currentPage, setCurrentPage] = useState(1);

  // Build query parameters
  const queryParams = useMemo(() => {
    const params = new URLSearchParams();

    // Pagination
    params.append("page", currentPage.toString());
    params.append("limit", pagination.limit.toString());

    // Sorting
    params.append("sort_by", sorting.sort_by);
    params.append("sort_order", sorting.sort_order);

    // Filters
    if (filters.athlete_name.trim()) {
      params.append("athlete_name", filters.athlete_name.trim());
    }
    if (filters.payment_type) {
      params.append("payment_type", filters.payment_type);
    }
    if (filters.program_name.trim()) {
      params.append("program_name", filters.program_name.trim());
    }
    if (filters.progress) {
      params.append("progress", filters.progress);
    }

    return params.toString();
  }, [currentPage, pagination.limit, sorting, filters]);

  // Fetch athletes data
  const fetchAthletes = async () => {
    try {
      const response = await customModelQuery({
        endpoint: `/v2/api/kanglink/custom/trainer/athletes?${queryParams}`,
        method: "GET",
      });

      if (response && !response.error) {
        setAthletes(response.data || []);
        // Handle pagination if it exists in the response
        if ((response as any).pagination) {
          setPagination((response as any).pagination);
        }
      } else {
        throw new Error(response?.message || "Failed to fetch athletes");
      }
    } catch (error: any) {
      const message =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to fetch athletes";
      showToast(message, 5000, ToastStatusEnum.ERROR);
      setAthletes([]);
    }
  };

  // Fetch data when dependencies change
  useEffect(() => {
    fetchAthletes();
  }, [queryParams]);

  // Reset to first page when filters change
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [filters, sorting]);

  // Update filter
  const updateFilter = (key: keyof TrainerAthleteFilters, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Update sorting
  const updateSorting = (sort_by: string, sort_order: string) => {
    setSorting({
      sort_by,
      sort_order,
    });
  };

  // Apply filters (trigger refetch)
  const applyFilters = () => {
    setCurrentPage(1);
    fetchAthletes();
  };

  // Change page
  const changePage = (page: number) => {
    if (page >= 1 && page <= pagination.num_pages) {
      setCurrentPage(page);
    }
  };

  // Refresh data
  const refreshData = () => {
    fetchAthletes();
  };

  return {
    // Data
    athletes,
    pagination,
    filters,
    sorting,
    currentPage,

    // Loading state
    isLoading: isPending,

    // Actions
    updateFilter,
    updateSorting,
    applyFilters,
    changePage,
    refreshData,

    // Computed values
    totalPages: pagination.num_pages,
    hasNextPage: pagination.has_next,
    hasPrevPage: pagination.has_prev,
  };
};
