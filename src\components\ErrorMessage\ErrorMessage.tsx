import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";

interface ErrorMessageProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
}

const ErrorMessage = ({
  title = "Something went wrong",
  message = "We're having trouble loading this content. Please try again.",
  onRetry,
}: ErrorMessageProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <div className="py-8">
      <div className="text-center">
        <ExclamationTriangleIcon
          className="mx-auto h-12 w-12 mb-4"
          style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
        />
        <h3
          className="text-lg font-medium mb-2"
          style={{ color: THEME_COLORS[mode].TEXT }}
        >
          {title}
        </h3>
        <p
          className="text-sm mb-4"
          style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
        >
          {message}
        </p>
        {onRetry && (
          <button
            onClick={onRetry}
            className="px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 hover:opacity-80"
            style={{
              backgroundColor: THEME_COLORS[mode].PRIMARY,
              color: THEME_COLORS[mode].PRIMARY_TEXT,
            }}
          >
            Try Again
          </button>
        )}
      </div>
    </div>
  );
};

export default ErrorMessage;
