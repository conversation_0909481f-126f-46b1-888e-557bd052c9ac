import { useQuery } from "@tanstack/react-query";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { Trainer, Program } from "@/interfaces/model.interface";

// Extended trainer interface for details page
export interface TrainerDetailsResponse extends Trainer {
  experience_years?: number;
  specialization?: string[];
  certifications?: string[];
  review_count?: number;
  program_count?: number;
  bio?: string;
}

// Custom hook for fetching trainer details
export const useTrainerDetails = (trainerId: string | null) => {
  const customQuery = useCustomModelQuery();

  return useQuery({
    queryKey: ["trainer-details", trainerId],
    queryFn: async () => {
      if (!trainerId) throw new Error("Trainer ID is required");

      const result = await customQuery.mutateAsync({
        endpoint: `/v2/api/kanglink/custom/public/trainer/${trainerId}`,
        method: "GET",
        requiresAuth: false,
      });

      return result.data as TrainerDetailsResponse;
    },
    enabled: !!trainerId,
    staleTime: 0, // Always fetch fresh data
    refetchOnMount: true, // Refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });
};
