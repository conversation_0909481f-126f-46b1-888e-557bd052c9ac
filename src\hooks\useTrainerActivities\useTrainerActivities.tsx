import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { Activity, ActivitiesResponse } from "@/interfaces/model.interface";
import { useProfile } from "@/hooks/useProfile";

export interface UseTrainerActivitiesProps {
  enabled?: boolean;
  page?: number;
  limit?: number;
  activity_type?: string;
  visibility?: "all" | "public" | "private" | "trainer_only";
  program_id?: number;
  athlete_id?: number;
  date_from?: string;
  date_to?: string;
  refreshInterval?: number;
}

export interface UseTrainerActivitiesReturn {
  activities: Activity[];
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } | null;
  refetch: () => void;
  fetchActivityStats: () => Promise<any>;
  fetchActivitySummary: () => Promise<any>;
}

export const useTrainerActivities = ({
  enabled = true,
  page = 1,
  limit = 20,
  activity_type,
  visibility = "trainer_only",
  program_id,
  athlete_id,
  date_from,
  date_to,
  refreshInterval,
}: UseTrainerActivitiesProps = {}): UseTrainerActivitiesReturn => {
  const { profile } = useProfile();
  const queryClient = useQueryClient();
  const customQuery = useCustomModelQuery();

  // Build query parameters
  const queryParams: Record<string, any> = {
    page,
    limit,
    visibility,
  };

  if (activity_type) queryParams.activity_type = activity_type;
  if (program_id) queryParams.program_id = program_id;
  if (athlete_id) queryParams.athlete_id = athlete_id;
  if (date_from) queryParams.date_from = date_from;
  if (date_to) queryParams.date_to = date_to;

  // Fetch activities
  const {
    data: activitiesResponse,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["trainer-activities", queryParams],
    queryFn: async () => {
      const response = await customQuery.mutateAsync({
        endpoint: "/v2/api/kanglink/custom/trainer/activities",
        method: "GET",
        params: queryParams,
      });
      return response as ActivitiesResponse;
    },
    enabled: enabled && !!profile?.id,
    refetchInterval: refreshInterval,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 3 * 60 * 1000, // 3 minutes
  });

  // Extract activities and pagination
  const activities = activitiesResponse?.data?.activities || [];
  const pagination = activitiesResponse?.data?.pagination || null;

  // Fetch activity statistics
  const fetchActivityStats = async () => {
    try {
      const params: Record<string, any> = {};
      if (program_id !== undefined) params.program_id = program_id;
      if (date_from !== undefined) params.date_from = date_from;
      if (date_to !== undefined) params.date_to = date_to;

      const response = await customQuery.mutateAsync({
        endpoint: "/v2/api/kanglink/custom/trainer/activities/stats",
        method: "GET",
        params,
      });
      return response;
    } catch (error) {
      console.error("Error fetching activity stats:", error);
      throw error;
    }
  };

  // Fetch activity summary
  const fetchActivitySummary = async () => {
    try {
      const params: Record<string, any> = {};
      if (date_from !== undefined) params.date_from = date_from;
      if (date_to !== undefined) params.date_to = date_to;

      const response = await customQuery.mutateAsync({
        endpoint: "/v2/api/kanglink/custom/trainer/activities/programs",
        method: "GET",
        params,
      });
      return response;
    } catch (error) {
      console.error("Error fetching activity summary:", error);
      throw error;
    }
  };

  return {
    activities,
    isLoading,
    error: error ? String(error) : null,
    pagination,
    refetch,
    fetchActivityStats,
    fetchActivitySummary,
  };
};

export default useTrainerActivities; 