import { useQuery } from "@tanstack/react-query";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { RestAPIMethodEnum } from "@/utils/Enums";
import {
  RefundStatusResponse,
  RefundHistoryResponse,
  SubmitRefundRequestPayload,
  SubmitRefundRequestResponse,
} from "@/interfaces/model.interface";

// Hook to check refund status for a specific enrollment
export const useRefundStatus = (enrollmentId: number | null) => {
  const { mutateAsync } = useCustomModelQuery({ role: "member" });

  return useQuery({
    queryKey: ["refund-status", enrollmentId],
    queryFn: async (): Promise<RefundStatusResponse> => {
      if (!enrollmentId) {
        throw new Error("Enrollment ID is required");
      }

      const response = await mutateAsync({
        method: RestAPIMethodEnum.GET,
        endpoint: `/v2/api/kanglink/custom/athlete/refund/status/${enrollmentId}`,
      });

      if (response.error) {
        throw new Error(response.message || "Failed to fetch refund status");
      }

      return response.data;
    },
    enabled: !!enrollmentId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchInterval: 1000 * 60 * 2, // Refetch every 2 minutes for real-time updates
  });
};

// Hook to get athlete's refund request history
export const useRefundHistory = (
  page: number = 1,
  limit: number = 10,
  status?: string
) => {
  const { mutateAsync } = useCustomModelQuery({ role: "member" });

  return useQuery({
    queryKey: ["refund-history", page, limit, status],
    queryFn: async (): Promise<RefundHistoryResponse> => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      if (status) {
        params.append("status", status);
      }

      const response = await mutateAsync({
        method: RestAPIMethodEnum.GET,
        endpoint: `/v2/api/kanglink/custom/athlete/refund/requests?${params.toString()}`,
      });

      if (response.error) {
        throw new Error(response.message || "Failed to fetch refund history");
      }

      return response.data;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Hook to submit a refund request
export const useSubmitRefundRequest = () => {
  const { mutateAsync } = useCustomModelQuery({
    role: "member",
    showToast: true,
  });

  return {
    submitRefundRequest: async (
      payload: SubmitRefundRequestPayload
    ): Promise<SubmitRefundRequestResponse> => {
      const response = await mutateAsync({
        method: RestAPIMethodEnum.POST,
        endpoint: "/v2/api/kanglink/custom/athlete/refund/request",
        body: payload,
      });

      if (response.error) {
        throw new Error(response.message || "Failed to submit refund request");
      }

      return response.data;
    },
  };
};

// Hook to check if an enrollment is eligible for refund
export const useRefundEligibility = (enrollmentId: number | null) => {
  const {
    data: refundStatus,
    isLoading,
    error,
    refetch,
  } = useRefundStatus(enrollmentId);

  return {
    isEligible: refundStatus?.refund_eligibility?.is_eligible || false,
    reasons: refundStatus?.refund_eligibility?.reasons || [],
    timeRemainingHours:
      refundStatus?.refund_eligibility?.time_remaining_hours || 0,
    payoutTimeLimitHours:
      refundStatus?.refund_eligibility?.payout_time_limit_hours || 24,
    hasExistingRequest: !!refundStatus?.refund_request,
    existingRequest: refundStatus?.refund_request,
    enrollment: refundStatus?.enrollment,
    isLoading,
    error,
    refetch,
  };
};
