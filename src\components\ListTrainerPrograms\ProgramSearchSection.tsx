import React from "react";
import { ProgramSearchSectionProps } from "./types";

const ProgramSearchSection: React.FC<ProgramSearchSectionProps> = ({
  searchTerm,
  statusFilter,
  onSearchChange,
  onStatusChange,
  onApplyFilter,
}) => {
  const statusOptions = [
    "draft",
    "pending_approval",
    // "live",
    "published",
    "rejected",
    // "archived",
  ];

  return (
    <section className="bg-background rounded-lg shadow-sm border border-border p-6">
      <h2 className="text-lg font-semibold text-text mb-6">Search Program</h2>
      <div className="flex flex-col lg:flex-row lg:items-end gap-6">
        <div className="flex-1">
          <label
            className="block text-sm font-semibold text-text mb-2"
            htmlFor="programName"
          >
            Program Name
          </label>
          <input
            type="text"
            id="programName"
            value={searchTerm}
            onChange={onSearchChange}
            placeholder="Search by name"
            className="w-full px-3 max-h-[2.625rem] border border-border rounded-md bg-background text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 hover:border-primary/50"
          />
        </div>
        <div className="flex-1">
          <label
            className="block text-sm font-semibold text-text mb-2"
            htmlFor="status"
          >
            Status
          </label>
          <select
            id="status"
            value={statusFilter}
            onChange={onStatusChange}
            className="w-full px-3 capitalize max-h-[2.625rem] border border-border rounded-md bg-background text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200 hover:border-primary/50 cursor-pointer"
          >
            <option value="">All</option>
            {statusOptions.map((option) => (
              <option key={option} value={option}>
                {option.replace("_", " ")}
              </option>
            ))}
          </select>
        </div>
        <div className="flex-none">
          <button
            type="button"
            onClick={onApplyFilter}
            className="flex w-full whitespace-nowrap md:w-[7.49613rem] h-[2.625rem] justify-center items-center flex-shrink-0 rounded border bg-background text-text hover:bg-background-hover transition-colors duration-200 font-medium"
            style={{
              padding: "0.5975rem 1.05863rem 0.5275rem 1.0625rem",
              borderColor: "var(--border-color)",
              backgroundColor: "var(--background-color)",
              color: "var(--text-color)",
            }}
          >
            Apply Filter
          </button>
        </div>
      </div>
    </section>
  );
};

export default ProgramSearchSection;
