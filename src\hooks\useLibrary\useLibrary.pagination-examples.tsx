import React from "react";
import { useLibrary } from "./useLibrary";

// Example 1: Default pagination (10 items per page)
export const DefaultPaginationExample: React.FC = () => {
  const {
    libraryData,
    pagination,
    isLoading,
    updatePagination
  } = useLibrary({ 
    libraryType: "exercise" 
  });

  return (
    <div>
      <h3>Default Pagination (10 items)</h3>
      {isLoading ? (
        <p>Loading...</p>
      ) : (
        <>
          <ul>
            {libraryData.map((item: any) => (
              <li key={item.id}>{item.name}</li>
            ))}
          </ul>
          <div>
            Page {pagination.page} of {pagination.num_pages}
            <button 
              onClick={() => updatePagination({ page: pagination.page - 1 })}
              disabled={pagination.page <= 1}
            >
              Previous
            </button>
            <button 
              onClick={() => updatePagination({ page: pagination.page + 1 })}
              disabled={pagination.page >= pagination.num_pages}
            >
              Next
            </button>
          </div>
        </>
      )}
    </div>
  );
};

// Example 2: Large page size for dropdowns
export const DropdownExample: React.FC = () => {
  const {
    adminLibraryData,
    trainerLibraryData,
    isLoading
  } = useLibrary({ 
    libraryType: "exercise",
    initialPagination: { limit: 100 } // Get more items for dropdown
  });

  const allExercises = [...adminLibraryData, ...trainerLibraryData];

  return (
    <div>
      <h3>Dropdown with Large Page Size (100 items)</h3>
      <select disabled={isLoading}>
        <option value="">
          {isLoading ? "Loading exercises..." : "Choose exercise..."}
        </option>
        {allExercises.map((exercise: any) => (
          <option key={exercise.id} value={exercise.id}>
            {exercise.name} {exercise.type === 1 ? "(Admin)" : "(My Exercise)"}
          </option>
        ))}
      </select>
      <p>Total exercises loaded: {allExercises.length}</p>
    </div>
  );
};

// Example 3: Small page size for mobile
export const MobileExample: React.FC = () => {
  const {
    libraryData,
    pagination,
    isLoading,
    updatePagination
  } = useLibrary({ 
    libraryType: "video",
    initialPagination: { limit: 5 } // Smaller pages for mobile
  });

  return (
    <div>
      <h3>Mobile Pagination (5 items per page)</h3>
      {isLoading ? (
        <p>Loading...</p>
      ) : (
        <>
          <div className="grid grid-cols-1 gap-2">
            {libraryData.map((video: any) => (
              <div key={video.id} className="p-2 border rounded">
                <h4>{video.name}</h4>
                {video.url && (
                  <a href={video.url} target="_blank" rel="noopener noreferrer">
                    Watch Video
                  </a>
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between mt-4">
            <button 
              onClick={() => updatePagination({ page: pagination.page - 1 })}
              disabled={pagination.page <= 1}
              className="px-3 py-1 bg-blue-500 text-white rounded disabled:opacity-50"
            >
              ← Prev
            </button>
            <span>
              {pagination.page} / {pagination.num_pages}
            </span>
            <button 
              onClick={() => updatePagination({ page: pagination.page + 1 })}
              disabled={pagination.page >= pagination.num_pages}
              className="px-3 py-1 bg-blue-500 text-white rounded disabled:opacity-50"
            >
              Next →
            </button>
          </div>
        </>
      )}
    </div>
  );
};

// Example 4: Custom sorting and filtering with pagination
export const AdvancedExample: React.FC = () => {
  const {
    libraryData,
    pagination,
    isLoading,
    updatePagination,
    searchLibraryItems,
    filterByType,
    clearFilters
  } = useLibrary({ 
    libraryType: "exercise",
    initialPagination: { 
      limit: 20,
      sort: "name:asc" // Sort by name ascending
    }
  });

  return (
    <div>
      <h3>Advanced Example (20 items, sorted by name)</h3>
      
      {/* Search and Filter Controls */}
      <div className="mb-4 space-x-2">
        <input
          type="text"
          placeholder="Search exercises..."
          onChange={(e) => searchLibraryItems(e.target.value)}
          className="px-3 py-1 border rounded"
        />
        <button 
          onClick={() => filterByType(1)}
          className="px-3 py-1 bg-green-500 text-white rounded"
        >
          Admin Only
        </button>
        <button 
          onClick={() => filterByType(2)}
          className="px-3 py-1 bg-purple-500 text-white rounded"
        >
          My Exercises
        </button>
        <button 
          onClick={clearFilters}
          className="px-3 py-1 bg-gray-500 text-white rounded"
        >
          Clear Filters
        </button>
      </div>

      {/* Results */}
      {isLoading ? (
        <p>Loading...</p>
      ) : (
        <>
          <div className="grid grid-cols-2 gap-2 mb-4">
            {libraryData.map((exercise: any) => (
              <div key={exercise.id} className="p-2 border rounded">
                <h4>{exercise.name}</h4>
                <span className="text-sm text-gray-500">
                  {exercise.type === 1 ? "Admin" : "Trainer"} Created
                </span>
              </div>
            ))}
          </div>
          
          {/* Pagination */}
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
              {pagination.total} exercises
            </span>
            <div className="space-x-2">
              <button 
                onClick={() => updatePagination({ page: pagination.page - 1 })}
                disabled={pagination.page <= 1}
                className="px-3 py-1 bg-blue-500 text-white rounded disabled:opacity-50"
              >
                Previous
              </button>
              <span>{pagination.page} / {pagination.num_pages}</span>
              <button 
                onClick={() => updatePagination({ page: pagination.page + 1 })}
                disabled={pagination.page >= pagination.num_pages}
                className="px-3 py-1 bg-blue-500 text-white rounded disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

// Example 5: Different page sizes for different use cases
export const MultipleInstancesExample: React.FC = () => {
  // Small pagination for main list
  const exerciseList = useLibrary({ 
    libraryType: "exercise",
    initialPagination: { limit: 10 }
  });

  // Large pagination for dropdown
  const exerciseDropdown = useLibrary({ 
    libraryType: "exercise",
    initialPagination: { limit: 100 }
  });

  return (
    <div className="space-y-6">
      <div>
        <h3>Exercise List (10 per page)</h3>
        {exerciseList.isLoading ? (
          <p>Loading list...</p>
        ) : (
          <ul>
            {exerciseList.libraryData.map((exercise: any) => (
              <li key={exercise.id}>{exercise.name}</li>
            ))}
          </ul>
        )}
      </div>

      <div>
        <h3>Exercise Dropdown (100 items loaded)</h3>
        <select disabled={exerciseDropdown.isLoading}>
          <option value="">Choose exercise...</option>
          {[...exerciseDropdown.adminLibraryData, ...exerciseDropdown.trainerLibraryData]
            .map((exercise: any) => (
              <option key={exercise.id} value={exercise.id}>
                {exercise.name}
              </option>
            ))}
        </select>
      </div>
    </div>
  );
};

export default {
  DefaultPaginationExample,
  DropdownExample,
  MobileExample,
  AdvancedExample,
  MultipleInstancesExample
};
