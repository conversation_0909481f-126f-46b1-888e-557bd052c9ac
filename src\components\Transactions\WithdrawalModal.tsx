import React, { useState, useEffect } from "react";
import { Modal } from "@/components/Modal/Modal";
import { useWithdrawal, useTransactionStats, useStripeConnectStatus } from "@/hooks/useTrainerTransactions";
import { AlertCircle, DollarSign } from "lucide-react";

interface WithdrawalModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const WithdrawalModal: React.FC<WithdrawalModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const { requestWithdrawal, loading: withdrawalLoading, error: withdrawalError } = useWithdrawal();
  const { stats, refetch: refetchStats } = useTransactionStats();
  const { status: stripeStatus } = useStripeConnectStatus();
  
  const [amount, setAmount] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setAmount("");
      setErrors({});
    }
  }, [isOpen]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    const numAmount = parseFloat(amount);

    if (!amount || amount.trim() === "") {
      newErrors.amount = "Amount is required";
    } else if (isNaN(numAmount) || numAmount <= 0) {
      newErrors.amount = "Amount must be a positive number";
    } else if (stats && numAmount > stats.available_to_withdraw) {
      newErrors.amount = `Amount cannot exceed available balance of ${formatCurrency(stats.available_to_withdraw, stats.currency)}`;
    } else if (numAmount < 1) {
      newErrors.amount = "Minimum withdrawal amount is $1.00";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await requestWithdrawal({
        amount: parseFloat(amount),
        currency: stats?.currency || "USD",
      });

      // Refresh stats to show updated available balance
      refetchStats();
      onSuccess?.();
      onClose();
    } catch (error) {
      // Error is handled by the hook
    }
  };

  const handleAmountChange = (value: string) => {
    // Allow only numbers and decimal point
    const sanitized = value.replace(/[^0-9.]/g, "");
    
    // Prevent multiple decimal points
    const parts = sanitized.split(".");
    if (parts.length > 2) {
      return;
    }
    
    // Limit to 2 decimal places
    if (parts[1] && parts[1].length > 2) {
      return;
    }

    setAmount(sanitized);
    
    // Clear amount error when user starts typing
    if (errors.amount) {
      setErrors(prev => ({ ...prev, amount: "" }));
    }
  };

  const handleCancel = () => {
    setAmount("");
    setErrors({});
    onClose();
  };

  // Check if withdrawal is possible
  const canWithdraw = stripeStatus?.payouts_enabled && stats && stats.available_to_withdraw > 0;

  const renderContent = () => {
    if (!stripeStatus?.has_stripe_connect || !stripeStatus?.payouts_enabled) {
      return (
        <div className="text-center py-6">
          <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-text mb-2">
            Payment Account Required
          </h3>
          <p className="text-text-secondary mb-4">
            You need to set up and verify your payment account before you can withdraw funds.
          </p>
          <button
            onClick={handleCancel}
            className="px-4 py-2 text-sm font-medium text-text-secondary bg-background border border-border rounded-md hover:bg-background-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            Close
          </button>
        </div>
      );
    }

    if (!stats || stats.available_to_withdraw <= 0) {
      return (
        <div className="text-center py-6">
          <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-text mb-2">
            No Funds Available
          </h3>
          <p className="text-text-secondary mb-4">
            You don't have any funds available for withdrawal at this time.
          </p>
          <button
            onClick={handleCancel}
            className="px-4 py-2 text-sm font-medium text-text-secondary bg-background border border-border rounded-md hover:bg-background-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            Close
          </button>
        </div>
      );
    }

    return (
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Available Balance */}
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-green-800 dark:text-green-200">
              Available to Withdraw
            </span>
            <span className="text-lg font-bold text-green-600 dark:text-green-400">
              {formatCurrency(stats.available_to_withdraw, stats.currency)}
            </span>
          </div>
        </div>

        {/* Amount Input */}
        <div>
          <label className="block text-sm font-medium text-text mb-2">
            Withdrawal Amount
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-text-secondary">$</span>
            </div>
            <input
              type="text"
              value={amount}
              onChange={(e) => handleAmountChange(e.target.value)}
              className="w-full pl-8 pr-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-text"
              placeholder="0.00"
              disabled={withdrawalLoading}
            />
          </div>
          {errors.amount && (
            <p className="mt-1 text-sm text-red-600">{errors.amount}</p>
          )}
        </div>

        {/* Quick Amount Buttons */}
        <div className="grid grid-cols-3 gap-2">
          {[25, 50, 100].map((quickAmount) => (
            <button
              key={quickAmount}
              type="button"
              onClick={() => {
                const maxAmount = Math.min(quickAmount, stats.available_to_withdraw);
                handleAmountChange(maxAmount.toString());
              }}
              disabled={withdrawalLoading || quickAmount > stats.available_to_withdraw}
              className="px-3 py-2 text-sm font-medium text-text-secondary bg-background border border-border rounded-md hover:bg-background-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
            >
              ${quickAmount}
            </button>
          ))}
        </div>

        <button
          type="button"
          onClick={() => handleAmountChange(stats.available_to_withdraw.toString())}
          disabled={withdrawalLoading}
          className="w-full px-3 py-2 text-sm font-medium text-text-secondary bg-background border border-border rounded-md hover:bg-background-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Withdraw All Available
        </button>

        {/* Error Display */}
        {withdrawalError && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
              <span className="ml-2 text-sm text-red-600 dark:text-red-400">
                {withdrawalError}
              </span>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={handleCancel}
            disabled={withdrawalLoading}
            className="px-4 py-2 text-sm font-medium text-text-secondary bg-background border border-border rounded-md hover:bg-background-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={withdrawalLoading || !canWithdraw}
            className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {withdrawalLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                Processing...
              </>
            ) : (
              "Withdraw Funds"
            )}
          </button>
        </div>
      </form>
    );
  };

  return (
    <Modal
      isOpen={isOpen}
      title="Withdraw Funds"
      modalHeader={true}
      modalCloseClick={handleCancel}
      classes={{
        modalDialog: "w-full max-w-md",
        modal: "",
        modalContent: "",
      }}
    >
      {renderContent()}
    </Modal>
  );
};

export default WithdrawalModal;
