import { lazy } from "react";

export { default as ChevronDown } from "./Chevron.svg";
export { Spinner } from "./Spinner";
export { DangerIcon } from "./DangerIcon";
export { CloseIcon } from "./CloseIcon";
export { CaretLeft } from "./CaretLeft";
export const ControlIcon = lazy(() => import("./ControlIcon"));
export const KebabIcon = lazy(() => import("./KebabIcon"));
export const TrashIcon = lazy(() => import("./TrashIcon"));
export const ChevronUpIcon = lazy(() => import("./ChevronUpIcon"));
export const LoadCheckIcon = lazy(() => import("./LoadCheckIcon"));
export const XIcon = lazy(() => import("./XIcon"));
export const EditIcon = lazy(() => import("./EditIcon"));
export const PlusIcon = lazy(() => import("./PlusIcon"));
export const ThreeDotsHorizontal = lazy(() => import("./ThreeDotsHorizontal"));
export const DownloadIcon = lazy(() => import("./DownloadIcon"));
export const GitIcon = lazy(() => import("./GitIcon"));
export const SettingIcon = lazy(() => import("./SettingIcon"));
export const SyncIcon = lazy(() => import("./SyncIcon"));
export const PostmanIcon = lazy(() => import("./PostmanIcon"));
export const CaptureSpatialIcon = lazy(() => import("./CaptureSpatialIcon"));
export const RobotIcon = lazy(() => import("./RobotIcon"));
export const FolderIcon = lazy(() => import("./FolderIcon"));
export const FileLineIcon = lazy(() => import("./FileLineIcon"));
export const FileVerticalLineIcon = lazy(
  () => import("./FileVerticalLineIcon")
);
export const FileMemoIcon = lazy(() => import("./FileMemoIcon"));
export const HamburgerMenuIcon = lazy(() => import("./HamburgerMenuIcon"));
export const PdfFileIcon = lazy(() => import("./PdfFileIcon"));
export const FileAudioIcon = lazy(() => import("./FileAudioIcon"));
export const DatabaseIcon = lazy(() => import("./DatabaseIcon"));
export const TreeStructureIcon = lazy(() => import("./TreeStructureIcon"));
export const ReactIcon = lazy(() => import("./ReactIcon"));
export const AudioFileIcon = lazy(() => import("./AudioFileIcon"));
export const RightBendArrow = lazy(() => import("./RightBendArrow"));
export const LeftBendArrow = lazy(() => import("./LeftBendArrow"));
export const AlertCircle = lazy(() => import("./AlertCircle"));
export const NarrowUpArrowIcon = lazy(() => import("./NarrowUpArrowIcon"));
export const CalendarIcon = lazy(() => import("./CalendarIcon"));
export const CsvIcon = lazy(() => import("./CsvIcon"));
export const CircleCheckMarkIcon = lazy(() => import("./CircleCheckMarkIcon"));
export const CloudUploadIcon = lazy(() => import("./CloudUploadIcon"));
