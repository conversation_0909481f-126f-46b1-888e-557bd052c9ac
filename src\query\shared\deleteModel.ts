import { useMutation, useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "../queryKeys";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";

export const useDeleteModelMutation = (
  table: string,
  role?: any,
  config?: any
) => {
  const { tdk } = useSDK({ role });
  const { showToast, tokenExpireError } = useContexts();
  const queryClient = useQueryClient();

  const mutationFn = async (
    id: string | number,
    payload?: Record<string, any>
  ) => {
    const response = await tdk.delete(table, id, payload);
    return response.data ?? response?.model;
  };

  return useMutation({
    mutationFn,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.all, table],
        exact: false,
        refetchType: "all",
      });
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.list, table],
        exact: false,
        refetchType: "all",
      });
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.many, table],
        exact: false,
        refetchType: "all",
      });
      queryClient.invalidateQueries({
        queryKey: [(queryKeys as any)?.[table]?.paginate, table],
        exact: false,
        refetchType: "all",
      });
      if (config?.showToast) {
        showToast("Deleted successfully", 5000, ToastStatusEnum.SUCCESS);
      } else {
        console.log("Deleted successfully");
      }
    },
    onError: (error) => {
      showToast(error.message, 5000, ToastStatusEnum.ERROR);
      tokenExpireError(error.message);
      console.error(error);
    },
  });
};
