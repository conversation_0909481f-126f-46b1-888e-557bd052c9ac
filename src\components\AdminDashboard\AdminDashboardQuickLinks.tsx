import React from "react";
import { useNavigate } from "react-router-dom";

interface QuickLink {
  title: string;
  description: string;
  path: string;
}

interface AdminDashboardQuickLinksProps {
  className?: string;
}

const AdminDashboardQuickLinks: React.FC<AdminDashboardQuickLinksProps> = ({
  className = "",
}) => {
  const navigate = useNavigate();

  const quickLinks: QuickLink[] = [
    {
      title: "Manage Users",
      description: "Links to user to manage users quickly",
      path: "/admin/athletes",
    },
    {
      title: "Content Moderation",
      description: "Check and approve pending program",
      path: "/admin/programs",
    },
    {
      title: "Refund Request",
      description: "Quickly remove all pending refund requests",
      path: "/admin/refunds",
    },
  ];

  const handleLinkClick = (path: string) => {
    navigate(path);
  };

  return (
    <div className={`w-full lg:w-[740px] ${className}`}>
      <h2 className="text-xl font-bold text-text mb-4">Quick Links</h2>
      <div className="flex flex-col gap-4 sm:gap-6">
        {quickLinks.map((link, index) => (
          <button
            key={index}
            onClick={() => handleLinkClick(link.path)}
            className="bg-background-secondary rounded-md shadow-md p-4 border border-border hover:shadow-lg hover:border-primary transition-all duration-200 text-left group"
          >
            <h3 className="text-base font-medium text-text group-hover:text-primary transition-colors duration-200">
              {link.title}
            </h3>
            <p className="text-sm text-text-secondary">{link.description}</p>
          </button>
        ))}
      </div>
    </div>
  );
};

export default AdminDashboardQuickLinks;
