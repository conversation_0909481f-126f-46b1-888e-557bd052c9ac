import { useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useTheme } from "@/hooks/useTheme";

import { THEME_COLORS } from "@/context/Theme";
import { Categories } from "@/components/Categories";
import { TopProgramsGrid } from "@/components/TopProgramsGrid";
import { ProgramsYouMayLikeGrid } from "@/components/ProgramsYouMayLikeGrid";
import { TopTrainersGrid } from "@/components/TopTrainersGrid";
import { TrainersYouMayLikeGrid } from "@/components/TrainersYouMayLikeGrid";
import Programs from "@/components/Programs/Programs";
import { Trainers } from "@/components/Trainers";
import { LoadingSkeleton } from "@/components/LoadingSkeleton";
import { ErrorMessage } from "@/components/ErrorMessage";
import { useLandingPageData } from "@/hooks/useLandingPageData";
import {
  transformProgramsArray,
  transformTrainersArray,
} from "@/utils/landingPageTransformers";
import {
  useAthleteProgramFavorites,
  useAthleteTrainerFavorites,
} from "@/hooks/useAthleteFavorites";

const HomePage = () => {
  const { state } = useTheme();
  const mode = state?.theme;
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const [selectedCategory, setSelectedCategory] = useState("all");

  // Initialize favorite hooks
  const { toggleProgramFavorite } = useAthleteProgramFavorites();
  const { toggleTrainerFavorite } = useAthleteTrainerFavorites();

  // Get search term from URL params
  const searchTerm = searchParams.get("search") || "";

  // Fetch landing page data with category filtering and search
  const {
    isAuthenticated,
    isSearchActive,
    topRatedPrograms,
    programsYouMayLike,
    allPrograms,
    topRatedTrainers,
    trainersYouMayLike,
    allTrainers,
  } = useLandingPageData(selectedCategory, searchTerm);

  const pageStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    color: THEME_COLORS[mode].TEXT,
  };

  // Transform API data to component format
  const topRatedProgramsData = topRatedPrograms.data?.data
    ? transformProgramsArray(topRatedPrograms.data.data)
    : [];

  const programsYouMayLikeData = programsYouMayLike.data?.data
    ? transformProgramsArray(programsYouMayLike.data.data)
    : [];

  const allProgramsData = allPrograms.data?.data
    ? transformProgramsArray(allPrograms.data.data)
    : [];

  const topRatedTrainersData = topRatedTrainers.data?.data
    ? transformTrainersArray(topRatedTrainers.data.data)
    : [];

  const trainersYouMayLikeData = trainersYouMayLike.data?.data
    ? transformTrainersArray(trainersYouMayLike.data.data)
    : [];

  const allTrainersData = allTrainers.data?.data
    ? transformTrainersArray(allTrainers.data.data)
    : [];

  // Loading states
  const isLoadingPrograms = topRatedPrograms.isLoading || allPrograms.isLoading;
  const isLoadingTrainers = topRatedTrainers.isLoading || allTrainers.isLoading;

  // Error states
  const hasErrorPrograms = topRatedPrograms.isError || allPrograms.isError;

  // Get category display name for UI
  const getCategoryDisplayName = (categoryId: string) => {
    const categoryNames: Record<string, string> = {
      all: "All Categories",
      "body-building": "Body Building",
      "endurance-training": "Endurance Training",
      hiit: "HIIT",
      "strength-training": "Strength Training",
      "cross-fit": "Cross Fit",
      "flexibility-training": "Flexibility Training",
      calisthenics: "Calisthenics",
      yoga: "Yoga",
    };
    return categoryNames[categoryId] || "All Categories";
  };

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId);
    console.log("Selected category:", categoryId);
  };

  const handleProgramClick = (programId: string) => {
    navigate(`/athlete/program/${programId}`);
  };

  const handleFavoriteToggle = async (id: string, isFavorite: boolean) => {
    try {
      await toggleProgramFavorite(id, isFavorite);
    } catch (error) {
      console.error("Failed to toggle program favorite:", error);
      // Error is handled by the mutation hook's onError callback
    }
  };

  const handleTrainerFavoriteToggle = async (
    id: string,
    isFavorite: boolean
  ) => {
    try {
      await toggleTrainerFavorite(id, isFavorite);
    } catch (error) {
      console.error("Failed to toggle trainer favorite:", error);
      // Error is handled by the mutation hook's onError callback
    }
  };

  return (
    <div className="transition-colors duration-200" style={pageStyles}>
      {/* Categories Section */}
      <Categories
        selectedCategory={selectedCategory}
        onCategoryChange={handleCategoryChange}
      />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Category Filter Indicators */}
        {(isSearchActive ||
          (selectedCategory && selectedCategory !== "all")) && (
          <div className="mb-6 space-y-2">
            {/* Search Indicator */}
            {isSearchActive && (
              <p
                className="text-sm"
                style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
              >
                Search results for:{" "}
                <span
                  className="font-medium"
                  style={{ color: THEME_COLORS[mode].PRIMARY }}
                >
                  "{searchTerm}"
                </span>
              </p>
            )}

            {/* Category Filter Indicator */}
            {selectedCategory &&
              selectedCategory !== "all" &&
              !isSearchActive && (
                <p
                  className="text-sm"
                  style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
                >
                  Showing results for:{" "}
                  <span
                    className="font-medium"
                    style={{ color: THEME_COLORS[mode].PRIMARY }}
                  >
                    {getCategoryDisplayName(selectedCategory)}
                  </span>
                </p>
              )}
          </div>
        )}
        {/* Top Programs Section - Hidden during search */}
        {!isSearchActive && (
          <>
            {isLoadingPrograms ? (
              <LoadingSkeleton
                title="Top Programs"
                titleWidth="w-48"
                itemCount={6}
                gridCols="grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
              />
            ) : hasErrorPrograms ? (
              <ErrorMessage
                title="Failed to load programs"
                message="We're having trouble loading the top programs. Please try again."
                onRetry={() => {
                  topRatedPrograms.refetch();
                  allPrograms.refetch();
                }}
              />
            ) : (
              <>
                {topRatedProgramsData?.length > 0 ? (
                  <TopProgramsGrid
                    programs={topRatedProgramsData}
                    onFavoriteToggle={handleFavoriteToggle}
                    onProgramClick={handleProgramClick}
                  />
                ) : null}
              </>
            )}
          </>
        )}

        {/* Programs You May Like Section - Only show if authenticated and not searching */}
        {isAuthenticated && !isSearchActive && (
          <>
            {programsYouMayLike.isLoading ? (
              <LoadingSkeleton
                title="Programs You May Like"
                titleWidth="w-56"
                itemCount={6}
                gridCols="grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
              />
            ) : (
              <ProgramsYouMayLikeGrid
                programs={programsYouMayLikeData}
                onFavoriteToggle={handleFavoriteToggle}
                onProgramClick={handleProgramClick}
              />
            )}
          </>
        )}

        {/* All Programs Section */}
        <div className="mb-12">
          {isLoadingPrograms ? (
            <LoadingSkeleton
              title="All Programs"
              titleWidth="w-40"
              itemCount={8}
              gridCols="grid-cols-1 sm:grid-cols-2 lg:grid-cols-4"
            />
          ) : (
            <>
              {allProgramsData?.length > 0 ? (
                <Programs
                  programs={allProgramsData}
                  onFavoriteToggle={handleFavoriteToggle}
                  onProgramClick={handleProgramClick}
                />
              ) : null}
            </>
          )}
        </div>

        {/* Top Trainers Section - Hidden during search */}
        {!isSearchActive && (
          <>
            {isLoadingTrainers ? (
              <LoadingSkeleton
                title="Top Trainers"
                titleWidth="w-40"
                itemCount={6}
                gridCols="grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
              />
            ) : (
              <>
                {topRatedTrainersData?.length > 0 ? (
                  <TopTrainersGrid
                    trainers={topRatedTrainersData}
                    onFavoriteToggle={handleTrainerFavoriteToggle}
                  />
                ) : null}
              </>
            )}
          </>
        )}

        {/* Trainers You May Like Section - Only show if authenticated and not searching */}
        {isAuthenticated && !isSearchActive && (
          <>
            {trainersYouMayLike.isLoading ? (
              <LoadingSkeleton
                title="Trainers You May Like"
                titleWidth="w-56"
                itemCount={6}
                gridCols="grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
              />
            ) : (
              <TrainersYouMayLikeGrid
                trainers={trainersYouMayLikeData}
                onFavoriteToggle={handleTrainerFavoriteToggle}
              />
            )}
          </>
        )}

        {/* All Trainers Section */}
        <div className="mb-12">
          {isLoadingTrainers ? (
            <LoadingSkeleton
              title="All Trainers"
              titleWidth="w-40"
              itemCount={8}
              gridCols="grid-cols-1 sm:grid-cols-2 lg:grid-cols-4"
            />
          ) : (
            <>
              {allTrainersData?.length > 0 ? (
                <Trainers
                  trainers={allTrainersData}
                  onFavoriteToggle={handleTrainerFavoriteToggle}
                />
              ) : null}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default HomePage;
