# useLibrary Hook

A comprehensive React hook for managing exercise and video libraries with full CRUD operations and pagination support.

## Features

- ✅ **CRUD Operations**: Create, Read, Update, Delete library items
- ✅ **Pagination**: Built-in pagination support with customizable page size
- ✅ **Filtering**: Filter by type (admin/trainer created), search by name
- ✅ **Type Safety**: Full TypeScript support with proper interfaces
- ✅ **Caching**: Automatic query caching and invalidation
- ✅ **Error Handling**: Comprehensive error handling with toast notifications
- ✅ **Loading States**: Granular loading states for all operations

## Usage

### Basic Usage

```tsx
import { useLibrary } from "@/hooks/useLibrary";

const MyComponent = () => {
  const {
    libraryData,
    isLoading,
    createLibraryItem,
    updateLibraryItem,
    deleteLibraryItem,
  } = useLibrary({
    libraryType: "exercise", // or "video"
    autoFetch: true,
  });

  // Your component logic here
};
```

### Usage with Custom Page Size (for Dropdowns)

```tsx
import { useLibrary } from "@/hooks/useLibrary";

const DropdownComponent = () => {
  const { adminLibraryData, trainerLibraryData, isLoading } = useLibrary({
    libraryType: "exercise",
    initialPagination: { limit: 100 }, // Larger size for dropdown
  });

  const allExercises = [...adminLibraryData, ...trainerLibraryData];

  return (
    <select disabled={isLoading}>
      <option value="">Choose exercise...</option>
      {allExercises.map((exercise) => (
        <option key={exercise.id} value={exercise.id}>
          {exercise.name}
        </option>
      ))}
    </select>
  );
};
```

### Advanced Usage with Pagination and Filtering

```tsx
import { useLibrary } from "@/hooks/useLibrary";

const LibraryManager = () => {
  const {
    // Data
    libraryData,
    adminLibraryData,
    trainerLibraryData,
    pagination,

    // Loading states
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,

    // Actions
    createLibraryItem,
    updateLibraryItem,
    deleteLibraryItem,

    // Pagination & filtering
    updatePagination,
    searchLibraryItems,
    filterByType,
    clearFilters
  } = useLibrary({ libraryType: "exercise" });

  // Create new exercise
  const handleCreate = async () => {
    await createLibraryItem({
      name: "New Exercise",
      // type and user_id are automatically set
    });
  };

  // Update exercise
  const handleUpdate = async (id: string, name: string) => {
    await updateLibraryItem(id, { name });
  };

  // Delete exercise
  const handleDelete = async (id: string) => {
    await deleteLibraryItem(id);
  };

  // Search exercises
  const handleSearch = (searchTerm: string) => {
    searchLibraryItems(searchTerm);
  };

  // Filter by admin created items
  const showAdminItems = () => {
    filterByType(1);
  };

  // Filter by trainer created items
  const showTrainerItems = () => {
    filterByType(2);
  };

  // Change page
  const handlePageChange = (page: number) => {
    updatePagination({ page });
  };

  return (
    // Your JSX here
  );
};
```

## API Reference

### Hook Parameters

```tsx
interface UseLibraryProps {
  libraryType: "exercise" | "video";
  autoFetch?: boolean; // Default: true
  initialPagination?: Partial<PaginationOptions>; // Custom initial pagination
}

interface PaginationOptions {
  page?: number;
  limit?: number;
  filters?: LibraryFilters;
  sort?: string;
}
```

### Return Values

#### Data

- `libraryData: LibraryItem[]` - All library items based on current filters
- `adminLibraryData: LibraryItem[]` - Admin created items (type 1)
- `trainerLibraryData: LibraryItem[]` - Current trainer's items (type 2)
- `pagination: PaginationInfo` - Pagination information

#### Loading States

- `isLoading: boolean` - Loading main library data
- `isLoadingAdmin: boolean` - Loading admin library data
- `isLoadingTrainer: boolean` - Loading trainer library data
- `isCreating: boolean` - Creating new item
- `isUpdating: boolean` - Updating existing item
- `isDeleting: boolean` - Deleting item
- `isCustomPending: boolean` - Custom operation in progress

#### Actions

- `createLibraryItem(data: Partial<LibraryItem>): Promise<any>` - Create new item
- `updateLibraryItem(id: string | number, data: Partial<LibraryItem>): Promise<any>` - Update item
- `deleteLibraryItem(id: string | number): Promise<any>` - Delete item

#### Pagination & Filtering

- `updatePagination(options: Partial<PaginationOptions>): void` - Update pagination settings
- `searchLibraryItems(searchTerm: string): void` - Search by name
- `filterByType(type: 1 | 2): void` - Filter by type (1=admin, 2=trainer)
- `clearFilters(): void` - Clear all filters

#### Utilities

- `refreshAll(): void` - Refresh all data
- `paginationOptions: PaginationOptions` - Current pagination settings
- `customMutate: Function` - For custom operations

## Data Types

### LibraryItem (Exercise)

```tsx
interface Exercise {
  id?: number | string;
  name?: string;
  type?: number; // 1 = admin created, 2 = trainer created
  user_id?: number | null;
  created_at?: string;
  updated_at?: string;
}
```

### LibraryItem (Video)

```tsx
interface Video {
  id?: number | string;
  name?: string;
  type?: number; // 1 = admin created, 2 = trainer created
  url?: string;
  user_id?: number | null;
  created_at?: string;
  updated_at?: string;
}
```

### PaginationInfo

```tsx
interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  num_pages: number;
}
```

## Examples

See `useLibrary.example.tsx` for a complete working example.

## Notes

- The hook automatically sets `type: 2` and `user_id` when creating items
- Admin libraries (type 1) are read-only for trainers
- All operations include automatic toast notifications
- Query caching is handled automatically with proper invalidation
- The hook uses the current user's profile from `useProfile` hook
