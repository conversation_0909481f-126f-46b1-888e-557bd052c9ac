import { useQuery } from "@tanstack/react-query";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { useProfile } from "@/hooks/useProfile";

// Landing page data interfaces based on API documentation
export interface LandingProgram {
  id: number | string;
  user_id: number | string;
  program_name: string;
  type_of_program: string;
  program_description: string;
  target_levels: string[];
  currency: string;
  days_for_preview: number;
  image: string;
  rating: string;
  review_count: number;
  match_score?: number;
  price: number;
  trainer: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
    full_name: string;
    photo: string;
  };
  created_at: string;
  updated_at: string;
}

export interface LandingTrainer {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  photo: string;
  bio: string;
  specializations: string[];
  qualifications: string[];
  years_of_experience: string;
  rating: string;
  review_count: number;
  program_count: number;
  match_score?: number;
  created_at: string;
  updated_at: string;
}

export interface LandingPagePagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface LandingPageResponse<T> {
  error: boolean;
  message: string;
  data: T[];
  pagination: LandingPagePagination;
}

// Hook for top rated programs
export const useTopRatedPrograms = (params?: {
  page?: number;
  limit?: number;
  category?: string;
  min_rating?: number;
}) => {
  const customQuery = useCustomModelQuery();

  return useQuery({
    queryKey: ["landing", "top-rated-programs", params],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.category) queryParams.append("category", params.category);
      if (params?.min_rating)
        queryParams.append("min_rating", params.min_rating.toString());

      const endpoint = `/v2/api/kanglink/custom/landing/top-rated-programs${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

      const result = await customQuery.mutateAsync({
        endpoint,
        method: "GET",
        requiresAuth: false,
      });

      return result as LandingPageResponse<LandingProgram>;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes cache as per documentation
  });
};

// Hook for programs you may like (requires authentication)
export const useProgramsYouMayLike = (params?: {
  page?: number;
  limit?: number;
  min_rating?: number;
}) => {
  const customQuery = useCustomModelQuery();
  const { profile } = useProfile();
  const isAuthenticated = !!profile;

  return useQuery({
    queryKey: ["landing", "programs-you-may-like", params],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.min_rating)
        queryParams.append("min_rating", params.min_rating.toString());

      const endpoint = `/v2/api/kanglink/custom/landing/programs-you-may-like${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

      const result = await customQuery.mutateAsync({
        endpoint,
        method: "GET",
        requiresAuth: true,
      });

      return result as LandingPageResponse<LandingProgram>;
    },
    enabled: isAuthenticated, // Only run if user is authenticated
    staleTime: 5 * 60 * 1000, // 5 minutes cache for personalized content
  });
};

// Hook for all programs
export const useAllPrograms = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  level?: string;
  min_rating?: number;
  max_rating?: number;
  sort_by?: string;
  sort_order?: string;
  has_preview?: boolean;
}) => {
  const customQuery = useCustomModelQuery();

  return useQuery({
    queryKey: ["landing", "all-programs", params],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.search) queryParams.append("search", params.search);
      if (params?.category) queryParams.append("category", params.category);
      if (params?.level) queryParams.append("level", params.level);
      if (params?.min_rating)
        queryParams.append("min_rating", params.min_rating.toString());
      if (params?.max_rating)
        queryParams.append("max_rating", params.max_rating.toString());
      if (params?.sort_by) queryParams.append("sort_by", params.sort_by);
      if (params?.sort_order)
        queryParams.append("sort_order", params.sort_order);
      if (params?.has_preview !== undefined)
        queryParams.append("has_preview", params.has_preview.toString());

      const endpoint = `/v2/api/kanglink/custom/landing/all-programs${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

      const result = await customQuery.mutateAsync({
        endpoint,
        method: "GET",
        requiresAuth: false,
      });

      return result as LandingPageResponse<LandingProgram>;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes cache
  });
};

// Hook for top rated trainers
export const useTopRatedTrainers = (params?: {
  page?: number;
  limit?: number;
  specialization?: string;
  min_rating?: number;
  min_experience?: number;
}) => {
  const customQuery = useCustomModelQuery();

  return useQuery({
    queryKey: ["landing", "top-rated-trainers", params],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.specialization)
        queryParams.append("specialization", params.specialization);
      if (params?.min_rating)
        queryParams.append("min_rating", params.min_rating.toString());
      if (params?.min_experience)
        queryParams.append("min_experience", params.min_experience.toString());

      const endpoint = `/v2/api/kanglink/custom/landing/top-rated-trainers${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

      const result = await customQuery.mutateAsync({
        endpoint,
        method: "GET",
        requiresAuth: false,
      });

      return result as LandingPageResponse<LandingTrainer>;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes cache as per documentation
  });
};

// Hook for trainers you may like (requires authentication)
export const useTrainersYouMayLike = (params?: {
  page?: number;
  limit?: number;
  min_rating?: number;
}) => {
  const customQuery = useCustomModelQuery();
  const { profile } = useProfile();
  const isAuthenticated = !!profile;

  return useQuery({
    queryKey: ["landing", "trainers-you-may-like", params],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.min_rating)
        queryParams.append("min_rating", params.min_rating.toString());

      const endpoint = `/v2/api/kanglink/custom/landing/trainers-you-may-like${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

      const result = await customQuery.mutateAsync({
        endpoint,
        method: "GET",
        requiresAuth: true,
      });

      return result as LandingPageResponse<LandingTrainer>;
    },
    enabled: isAuthenticated, // Only run if user is authenticated
    staleTime: 5 * 60 * 1000, // 5 minutes cache for personalized content
  });
};

// Hook for all trainers
export const useAllTrainers = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  specialization?: string;
  min_rating?: number;
  max_rating?: number;
  min_experience?: number;
  max_experience?: number;
  sort_by?: string;
  sort_order?: string;
  has_programs?: boolean;
}) => {
  const customQuery = useCustomModelQuery();

  return useQuery({
    queryKey: ["landing", "all-trainers", params],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.search) queryParams.append("search", params.search);
      if (params?.specialization)
        queryParams.append("specialization", params.specialization);
      if (params?.min_rating)
        queryParams.append("min_rating", params.min_rating.toString());
      if (params?.max_rating)
        queryParams.append("max_rating", params.max_rating.toString());
      if (params?.min_experience)
        queryParams.append("min_experience", params.min_experience.toString());
      if (params?.max_experience)
        queryParams.append("max_experience", params.max_experience.toString());
      if (params?.sort_by) queryParams.append("sort_by", params.sort_by);
      if (params?.sort_order)
        queryParams.append("sort_order", params.sort_order);
      if (params?.has_programs !== undefined)
        queryParams.append("has_programs", params.has_programs.toString());

      const endpoint = `/v2/api/kanglink/custom/landing/all-trainers${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

      const result = await customQuery.mutateAsync({
        endpoint,
        method: "GET",
        requiresAuth: false,
      });

      return result as LandingPageResponse<LandingTrainer>;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes cache
  });
};

// Main hook that combines all landing page data
export const useLandingPageData = (
  selectedCategory?: string,
  searchTerm?: string
) => {
  const { profile } = useProfile();
  const isAuthenticated = !!profile;

  // Check if search is active
  const isSearchActive = searchTerm && searchTerm.trim().length > 0;

  // Map category to API format based on Categories component values
  const categoryMapping: Record<string, string> = {
    all: "", // "all" maps to no filter (undefined)
    "body-building": "bodybuilding",
    "endurance-training": "endurance",
    hiit: "hiit",
    "strength-training": "strength",
    "cross-fit": "crossfit",
    "flexibility-training": "flexibility",
    calisthenics: "calisthenics",
    yoga: "yoga",
  };

  const apiCategory =
    selectedCategory &&
    selectedCategory !== "all" &&
    categoryMapping[selectedCategory]
      ? categoryMapping[selectedCategory]
      : undefined;

  // Fetch top rated programs with category filter
  const topRatedPrograms = useTopRatedPrograms({
    limit: 10,
    category: apiCategory,
  });

  // Fetch programs you may like (only if authenticated)
  const programsYouMayLike = useProgramsYouMayLike({ limit: 10 });

  // Fetch all programs with pagination, category filter, and search
  const allPrograms = useAllPrograms({
    limit: 20,
    sort_by: "rating",
    sort_order: "desc",
    category: apiCategory,
    search: searchTerm || undefined,
  });

  // For trainers, we can use specialization filter instead of category
  const trainerSpecialization =
    selectedCategory &&
    selectedCategory !== "all" &&
    categoryMapping[selectedCategory]
      ? categoryMapping[selectedCategory]
      : undefined;

  // Fetch top rated trainers with specialization filter
  const topRatedTrainers = useTopRatedTrainers({
    limit: 10,
    specialization: trainerSpecialization,
  });

  // Fetch trainers you may like (only if authenticated)
  const trainersYouMayLike = useTrainersYouMayLike({ limit: 10 });

  // Fetch all trainers with pagination, specialization filter, and search
  const allTrainers = useAllTrainers({
    limit: 20,
    sort_by: "rating",
    sort_order: "desc",
    specialization: trainerSpecialization,
    search: searchTerm || undefined,
  });

  return {
    isAuthenticated,
    isSearchActive,
    searchTerm,
    topRatedPrograms,
    programsYouMayLike,
    allPrograms,
    topRatedTrainers,
    trainersYouMayLike,
    allTrainers,
  };
};
