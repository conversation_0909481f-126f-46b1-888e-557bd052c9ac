# SplitCard with Enrollment Integration

This enhanced SplitCard component integrates with the Kanglink enrollment API and Stripe payment processing to provide a complete enrollment experience.

## Features

- **Authentication-aware**: Automatically detects if user is logged in
- **Enrollment eligibility**: Checks if user can enroll before showing payment options
- **Real-time pricing**: Fetches current pricing from the API
- **Stripe integration**: Secure payment processing with Stripe
- **Enrollment status**: Shows if user is already enrolled
- **Error handling**: Comprehensive error handling with user-friendly messages
- **Loading states**: Proper loading indicators during API calls

## Usage

### Basic Usage

```tsx
import { SplitCard } from "@/components/SplitCard";

<SplitCard
  program={programData}
  splitId={202}
  splitName="Beginner Split"
  description="Perfect for beginners starting their fitness journey"
  subscriptionPrice={29.99}
  buyPrice={99.99}
  paymentPlan={["monthly", "one_time"]}
/>;
```

### With Custom Handlers

```tsx
<SplitCard
  program={programData}
  splitId={202}
  splitName="Beginner Split"
  description="Perfect for beginners starting their fitness journey"
  subscriptionPrice={29.99}
  buyPrice={99.99}
  paymentPlan={["monthly", "one_time"]}
  onSubscribe={() => {
    // Custom subscription logic
    console.log("Custom subscribe handler");
  }}
  onBuy={() => {
    // Custom purchase logic
    console.log("Custom buy handler");
  }}
/>
```

## Props

| Prop                | Type                     | Required | Description                                                         |
| ------------------- | ------------------------ | -------- | ------------------------------------------------------------------- |
| `program`           | `TransformedProgramData` | Yes      | Program data object                                                 |
| `splitId`           | `number`                 | Yes      | Unique identifier for the split                                     |
| `splitName`         | `string`                 | Yes      | Display name of the split                                           |
| `description`       | `string`                 | Yes      | Split description                                                   |
| `subscriptionPrice` | `number`                 | Yes      | Monthly subscription price                                          |
| `buyPrice`          | `number`                 | Yes      | One-time purchase price                                             |
| `paymentPlan`       | `string[]`               | No       | Available payment options: `["monthly", "one_time"]`                |
| `onSubscribe`       | `() => void`             | No       | Custom subscription handler (overrides built-in Stripe integration) |
| `onBuy`             | `() => void`             | No       | Custom purchase handler (overrides built-in Stripe integration)     |

## Environment Variables

Make sure to set your Stripe publishable key:

```env
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
```

## Setup Instructions

1. **Install Dependencies** (already done):

   ```bash
   npm install @stripe/stripe-js @stripe/react-stripe-js
   ```

2. **Set Environment Variables**:
   Create a `.env` file in your project root and add your Stripe publishable key:

   ```env
   REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_51...
   ```

3. **Backend Configuration**:
   Ensure your backend enrollment API endpoints are properly configured and accessible at:
   - `/v2/api/kanglink/custom/splits/{splitId}/eligibility`
   - `/v2/api/kanglink/custom/splits/{splitId}/pricing`
   - `/v2/api/kanglink/custom/athlete/enrollments`
   - `/v2/api/kanglink/custom/athlete/enroll`

4. **Usage in Components**:
   Import and use the enhanced SplitCard component:

   ```tsx
   import { SplitCard } from "@/components/SplitCard";

   <SplitCard
     splitId={202}
     programId={123}
     splitName="Beginner Split"
     description="Perfect for beginners"
     subscriptionPrice={29.99}
     buyPrice={99.99}
     paymentPlan={["monthly", "one_time"]}
   />;
   ```

## API Integration

The component automatically integrates with these enrollment API endpoints using `@/query/shared/customModel.ts` which handles authentication tokens automatically:

- `GET /splits/{splitId}/eligibility` - Check enrollment eligibility
- `GET /splits/{splitId}/pricing` - Get pricing information
- `GET /athlete/enrollments` - Get user's current enrollments
- `POST /athlete/enroll` - Create new enrollment with Stripe payment

## User States

### Not Logged In

- Shows subscription/purchase buttons with pricing (from API or props)
- Clicking opens login confirmation modal asking user to confirm redirect to login
- Uses React Router navigation with `redirect_uri` parameter for seamless return after login
- User can see pricing without being logged in

### Logged In - Eligible

- Shows normal subscription/purchase buttons with pricing
- Clicking opens Stripe payment modal

### Logged In - Already Enrolled

- Shows "Already Enrolled" status with green indicator
- Buttons are disabled

### Logged In - Not Eligible

- Shows "Not Available" on buttons
- Buttons are disabled
- Toast shows specific reasons why enrollment is not possible

## Error Handling

The component handles various error scenarios:

- Network errors during API calls
- Stripe payment failures
- Invalid payment methods
- Server-side enrollment errors
- Authentication token expiration

All errors are displayed to users via toast notifications with appropriate messaging.

## Dependencies

- `@stripe/stripe-js` - Stripe JavaScript SDK
- `@stripe/react-stripe-js` - Stripe React components
- `@tanstack/react-query` - Data fetching and caching
- Custom hooks: `useEnrollment`, `useToast`

## Related Files

- `useEnrollment.tsx` - Hook for enrollment API calls
- `StripePaymentModal.tsx` - Stripe payment processing modal
- `enrollment_api_documentation.md` - Complete API documentation
