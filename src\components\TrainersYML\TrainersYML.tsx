import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import TrainerCard from "@/components/TrainerCard/TrainerCard";

interface Trainer {
  id: string;
  name: string;
  description: string;
  image: string;
  rating: number;
  startingPrice: number;
  isFavorite?: boolean;
}

const sampleTrainers: Trainer[] = [
  {
    id: "1",
    name: "<PERSON>",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: true,
  },
  {
    id: "2",
    name: "<PERSON>",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: false,
  },
  {
    id: "3",
    name: "Lisa Anderson",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: false,
  },
  {
    id: "4",
    name: "Ryan Wilson",
    description:
      "Description of program that it can be benefits teaches us as if one are in one a trying hobby one activities wellnered now one.",
    image:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    rating: 5,
    startingPrice: 5,
    isFavorite: true,
  },
];

const TrainersYML = () => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <div className="w-full">
      {/* Section Title */}
      <h2
        className="text-2xl font-bold mb-8 transition-colors duration-200"
        style={{ color: THEME_COLORS[mode].TEXT }}
      >
        Trainers You May Like
      </h2>

      {/* Trainers Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {sampleTrainers.map((trainer) => (
          <TrainerCard key={trainer.id} trainer={trainer} />
        ))}
      </div>
    </div>
  );
};

export default TrainersYML;
