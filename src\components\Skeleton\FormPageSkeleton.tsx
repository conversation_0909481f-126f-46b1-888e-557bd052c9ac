import React from "react";
import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

interface FormPageSkeletonProps {
  /** Show page header with title and subtitle */
  showHeader?: boolean;
  /** Number of columns in the form layout */
  columns?: 1 | 2;
  /** Number of form sections to show */
  sections?: number;
  /** Number of fields per section */
  fieldsPerSection?: number;
  /** Show action buttons at the bottom */
  showActions?: boolean;
  /** Custom className for the container */
  className?: string;
}

const FormPageSkeleton: React.FC<FormPageSkeletonProps> = ({
  showHeader = true,
  columns = 2,
  sections = 4,
  fieldsPerSection = 3,
  showActions = true,
  className = "",
}) => {
  const { state } = useTheme();
  const mode = state?.theme || "light";

  return (
    <div
      className={`min-h-screen bg-background-secondary py-6 px-2 sm:px-4 md:px-8 ${className}`}
    >
      <SkeletonTheme
        baseColor={THEME_COLORS[mode].TEXT_DISABLED}
        highlightColor={THEME_COLORS[mode].BACKGROUND_HOVER}
      >
        <div className="max-w-7xl mx-auto">
          {/* Page Header Skeleton */}
          {showHeader && (
            <div className="mb-6">
              <Skeleton height={32} width="40%" className="mb-2" />
              <Skeleton height={16} width="60%" />
            </div>
          )}

          {/* Main Content Container Skeleton */}
          <div className="bg-background rounded-lg shadow-sm border border-border p-4 md:p-6">
            {/* Form Layout */}
            <div
              className={`${columns === 2 ? "flex flex-col lg:flex-row gap-6 lg:gap-8" : "space-y-6"}`}
            >
              {/* Generate columns */}
              {Array.from({ length: columns }).map((_, columnIndex) => (
                <div
                  key={columnIndex}
                  className={`${columns === 2 ? "flex-1" : ""} flex flex-col gap-6`}
                >
                  {/* Generate sections for this column */}
                  {Array.from({ length: Math.ceil(sections / columns) }).map(
                    (_, sectionIndex) => {
                      const actualSectionIndex =
                        columnIndex * Math.ceil(sections / columns) +
                        sectionIndex;
                      if (actualSectionIndex >= sections) return null;

                      return (
                        <div key={sectionIndex} className="space-y-4">
                          {/* Section Title */}
                          <Skeleton
                            height={24}
                            width={`${Math.random() * 20 + 25}%`}
                          />

                          {/* Section Fields */}
                          <div className="space-y-4">
                            {Array.from({ length: fieldsPerSection }).map(
                              (_, fieldIndex) => (
                                <div key={fieldIndex} className="space-y-2">
                                  {/* Field Label */}
                                  <Skeleton
                                    height={16}
                                    width={`${Math.random() * 15 + 20}%`}
                                  />

                                  {/* Field Input */}
                                  {fieldIndex % 4 === 0 ? (
                                    // Textarea field
                                    <Skeleton height={80} width="100%" />
                                  ) : fieldIndex % 3 === 0 ? (
                                    // Select field
                                    <Skeleton height={40} width="100%" />
                                  ) : fieldIndex % 2 === 0 ? (
                                    // Two column input group
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                      <Skeleton height={40} width="100%" />
                                      <Skeleton height={40} width="100%" />
                                    </div>
                                  ) : (
                                    // Regular input
                                    <Skeleton height={40} width="100%" />
                                  )}
                                </div>
                              )
                            )}

                            {/* Optional section button */}
                            {Math.random() > 0.5 && (
                              <Skeleton
                                height={36}
                                width={`${Math.random() * 20 + 25}%`}
                              />
                            )}
                          </div>
                        </div>
                      );
                    }
                  )}
                </div>
              ))}
            </div>

            {/* Action Buttons Skeleton */}
            {showActions && (
              <div className="mt-8 flex justify-end space-x-4">
                <Skeleton height={40} width="15%" />
                <Skeleton height={40} width="20%" />
              </div>
            )}
          </div>
        </div>
      </SkeletonTheme>
    </div>
  );
};

export default FormPageSkeleton;
