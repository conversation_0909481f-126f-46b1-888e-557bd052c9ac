import React from "react";
import { Modal } from "@/components/Modal";
import { LoadingSkeleton } from "@/components/LoadingSkeleton";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { useRefundEligibility } from "@/hooks/useAthleteRefunds";
import { RefundRequest } from "@/interfaces/model.interface";

interface RefundStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  enrollmentId: number;
  onRequestRefund?: () => void;
}

export const RefundStatusModal: React.FC<RefundStatusModalProps> = ({
  isOpen,
  onClose,
  enrollmentId,
  onRequestRefund,
}) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const {
    isEligible,
    reasons,
    timeRemainingHours,
    payoutTimeLimitHours,
    hasExistingRequest,
    existingRequest,
    enrollment,
    isLoading,
    error,
    refetch,
  } = useRefundEligibility(enrollmentId);

  const formatTimeRemaining = (hours: number) => {
    if (hours < 1) {
      const minutes = Math.floor(hours * 60);
      return `${minutes} minute${minutes !== 1 ? "s" : ""}`;
    }
    return `${Math.floor(hours)} hour${Math.floor(hours) !== 1 ? "s" : ""}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900";
      case "approved":
        return "text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900";
      case "rejected":
        return "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900";
      case "processed":
        return "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900";
      default:
        return "text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        );
      case "approved":
        return (
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        );
      case "rejected":
        return (
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        );
      case "processed":
        return (
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        );
      default:
        return null;
    }
  };

  if (error) {
    return (
      <Modal
        isOpen={isOpen}
        modalCloseClick={onClose}
        title="Refund Status"
        modalHeader
        classes={{
          modalDialog: "max-w-md",
          modal: "h-full",
        }}
      >
        <div className="p-6 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-red-600 dark:text-red-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3
            className="text-lg font-semibold mb-2"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            Error Loading Refund Status
          </h3>
          <p
            className="text-sm opacity-70 mb-4"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            {error.message || "Failed to load refund information"}
          </p>
          <div className="flex space-x-3">
            <button
              onClick={() => refetch()}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Try Again
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 rounded-lg text-white transition-colors"
              style={{
                backgroundColor: THEME_COLORS[mode].PRIMARY,
              }}
            >
              Close
            </button>
          </div>
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title="Refund Status"
      modalHeader
      classes={{
        modalDialog: "max-w-lg",
        modal: "h-full",
      }}
    >
      <div className="p-6">
        {/* Refresh Button */}
        <div className="flex justify-end mb-4">
          <button
            onClick={() => refetch()}
            disabled={isLoading}
            className="flex items-center space-x-2 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
            title="Refresh refund status"
          >
            <svg
              className={`w-4 h-4 ${isLoading ? "animate-spin" : ""}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            <span>Refresh</span>
          </button>
        </div>
        {isLoading ? (
          <>
            <div className="md:block hidden">
              <LoadingSkeleton itemCount={3} />
            </div>

            <div className="block md:hidden">
              <LoadingSkeleton itemCount={1} itemHeight="h-16" />
            </div>
          </>
        ) : (
          <>
            {/* Enrollment Information */}
            {enrollment && (
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
                <h3
                  className="font-semibold mb-3"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  Enrollment Details
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span
                      className="opacity-70"
                      style={{ color: THEME_COLORS[mode].TEXT }}
                    >
                      Program:
                    </span>
                    <span style={{ color: THEME_COLORS[mode].TEXT }}>
                      {enrollment.program_name}
                    </span>
                  </div>
                  {enrollment.split_name && (
                    <div className="flex justify-between">
                      <span
                        className="opacity-70"
                        style={{ color: THEME_COLORS[mode].TEXT }}
                      >
                        Split:
                      </span>
                      <span style={{ color: THEME_COLORS[mode].TEXT }}>
                        {enrollment.split_name}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span
                      className="opacity-70"
                      style={{ color: THEME_COLORS[mode].TEXT }}
                    >
                      Amount:
                    </span>
                    <span style={{ color: THEME_COLORS[mode].TEXT }}>
                      {enrollment.currency} {enrollment.amount}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span
                      className="opacity-70"
                      style={{ color: THEME_COLORS[mode].TEXT }}
                    >
                      Enrolled:
                    </span>
                    <span style={{ color: THEME_COLORS[mode].TEXT }}>
                      {formatDate(enrollment.enrollment_date)}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Existing Refund Request */}
            {hasExistingRequest && existingRequest && (
              <div className="mb-6">
                <h3
                  className="font-semibold mb-3"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  Current Refund Request
                </h3>
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <span
                      className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(existingRequest.status)}`}
                    >
                      {getStatusIcon(existingRequest.status)}
                      <span className="capitalize">
                        {existingRequest.status}
                      </span>
                    </span>
                    <span
                      className="text-sm opacity-70"
                      style={{ color: THEME_COLORS[mode].TEXT }}
                    >
                      {formatDate(existingRequest.requested_at)}
                    </span>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div>
                      <span
                        className="font-medium"
                        style={{ color: THEME_COLORS[mode].TEXT }}
                      >
                        Reason:
                      </span>
                      <p
                        className="mt-1 opacity-70"
                        style={{ color: THEME_COLORS[mode].TEXT }}
                      >
                        {existingRequest.reason}
                      </p>
                    </div>

                    {existingRequest.admin_notes && (
                      <div>
                        <span
                          className="font-medium"
                          style={{ color: THEME_COLORS[mode].TEXT }}
                        >
                          Admin Notes:
                        </span>
                        <p
                          className="mt-1 opacity-70"
                          style={{ color: THEME_COLORS[mode].TEXT }}
                        >
                          {existingRequest.admin_notes}
                        </p>
                      </div>
                    )}

                    {existingRequest.processed_at && (
                      <div className="flex justify-between">
                        <span
                          className="opacity-70"
                          style={{ color: THEME_COLORS[mode].TEXT }}
                        >
                          Processed:
                        </span>
                        <span style={{ color: THEME_COLORS[mode].TEXT }}>
                          {formatDate(existingRequest.processed_at)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Refund Eligibility */}
            <div className="mb-6">
              <h3
                className="font-semibold mb-3"
                style={{ color: THEME_COLORS[mode].TEXT }}
              >
                Refund Eligibility
              </h3>

              {isEligible ? (
                <div className="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5">
                      <svg fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-green-800 dark:text-green-200 mb-1">
                        Eligible for Refund
                      </p>
                      <p className="text-sm text-green-600 dark:text-green-300">
                        Time remaining:{" "}
                        {formatTimeRemaining(timeRemainingHours)}
                        <span className="opacity-70">
                          {" "}
                          (out of {payoutTimeLimitHours} hours)
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5">
                      <svg fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium text-red-800 dark:text-red-200 mb-2">
                        Not Eligible for Refund
                      </p>
                      <div className="space-y-1">
                        {reasons.map((reason, index) => (
                          <p
                            key={index}
                            className="text-sm text-red-600 dark:text-red-300"
                          >
                            • {reason}
                          </p>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Close
              </button>

              {isEligible && !hasExistingRequest && onRequestRefund && (
                <button
                  onClick={() => {
                    onClose();
                    onRequestRefund();
                  }}
                  className="flex-1 px-4 py-2 rounded-lg text-white transition-colors"
                  style={{
                    backgroundColor: THEME_COLORS[mode].PRIMARY,
                  }}
                >
                  Request Refund
                </button>
              )}
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default RefundStatusModal;
