import { useProfile } from "./useProfile";

export interface TrainerStatus {
  isDeactivated: boolean;
  isVerified: boolean;
  isActive: boolean;
  canPerformActions: boolean;
  statusMessage: string;
}

export const useTrainerStatus = (): TrainerStatus => {
  const { profile } = useProfile();

  // Check if trainer is deactivated (status 0 but verified)
  const isDeactivated = profile?.status === 0 && profile?.verify === 1;
  
  // Check if trainer is verified
  const isVerified = profile?.verify === 1;
  
  // Check if trainer is active (status 1 and verified)
  const isActive = profile?.status === 1 && profile?.verify === 1;
  
  // Can perform actions only if active
  const canPerformActions = isActive;
  
  // Status message for UI
  const statusMessage = isDeactivated 
    ? "Your account has been deactivated by admin. You can view content but cannot perform actions."
    : isActive 
    ? "Account active"
    : "Account pending verification";

  return {
    isDeactivated,
    isVerified,
    isActive,
    canPerformActions,
    statusMessage,
  };
};

// Utility function to show deactivation warning
export const showDeactivationWarning = (showToast: (message: string, duration?: number, status?: any) => void) => {
  showToast(
    "Your account has been deactivated by admin. You can view content but cannot perform actions.",
    5000,
    "warning"
  );
};

// Utility function to disable actions for deactivated trainers
export const disableActionForDeactivatedTrainer = (
  action: () => void,
  showToast: (message: string, duration?: number, status?: any) => void,
  { isDeactivated }: TrainerStatus
) => {
  if (isDeactivated) {
    showDeactivationWarning(showToast);
    return;
  }
  action();
}; 