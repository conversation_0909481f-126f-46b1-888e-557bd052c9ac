import React from "react";
import { ReviewCard } from "@/components/ReviewCard";
import { ChevronDownIcon } from "@heroicons/react/24/outline";

interface Review {
  id: string;
  athleteName: string;
  reviewText: string;
  rating: number;
}

interface ReviewsSectionProps {
  reviews: Review[];
  onReply?: (reviewId: string, reply: string) => void;
}

const ReviewsSection: React.FC<ReviewsSectionProps> = ({
  reviews,
  onReply,
}) => {
  return (
    <div className="w-full bg-background">
      <div className="p-4 lg:p-6">
        {/* Header with expand button */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg lg:text-xl font-bold text-text">
            Review
          </h2>
          <button className="p-1 hover:bg-background-hover rounded transition-colors duration-200">
            <ChevronDownIcon className="h-4 w-4 text-text" />
          </button>
        </div>
        
        {/* Reviews List */}
        <div className="space-y-6">
          {reviews.map((review) => (
            <ReviewCard
              key={review.id}
              athleteName={review.athleteName}
              reviewText={review.reviewText}
              rating={review.rating}
              onReply={(reply) => onReply?.(review.id, reply)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ReviewsSection;
