import React from "react";
import { ChevronUpIcon } from "@/assets/svgs";
import { DaySection } from "./index";

interface WeekSectionProps {
  week: any;
  weekIndex: number;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  collapsedStates: { [key: string]: boolean };
  onToggleItemCollapse: (id: string) => void;
}

const WeekSection: React.FC<WeekSectionProps> = ({
  week,
  weekIndex,
  isCollapsed,
  onToggleCollapse,
  collapsedStates,
  onToggleItemCollapse,
}) => {
  return (
    <div className="space-y-4">
      {/* Week Header */}
      <div className="flex items-center gap-3">
        <button
          onClick={onToggleCollapse}
          className="flex items-center gap-2 text-base font-medium text-text hover:text-text-hover"
        >
          <span>{week.name || `Week ${weekIndex + 1}`}</span>
          <ChevronUpIcon 
            className={`w-4 h-4 transition-transform ${isCollapsed ? 'rotate-180' : ''}`}
            stroke="currentColor"
          />
        </button>
      </div>

      {/* Week Content */}
      {!isCollapsed && (
        <div className="ml-4 space-y-4">
          {week.days && week.days.length > 0 ? (
            week.days.map((day: any, dayIndex: number) => (
              <DaySection
                key={day.id || `day-${dayIndex}`}
                day={day}
                dayIndex={dayIndex}
                isCollapsed={collapsedStates[`day-${day.id}`] || false}
                onToggleCollapse={() => onToggleItemCollapse(`day-${day.id}`)}
                collapsedStates={collapsedStates}
                onToggleItemCollapse={onToggleItemCollapse}
              />
            ))
          ) : (
            <div className="text-text-secondary text-sm">
              No days defined for this week.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default WeekSection;
