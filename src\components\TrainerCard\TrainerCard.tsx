import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { StarIcon, HeartIcon } from "@heroicons/react/24/solid";
import { HeartIcon as HeartOutlineIcon } from "@heroicons/react/24/outline";

interface Trainer {
  id: string;
  name: string;
  description: string;
  image: string;
  rating: number;
  startingPrice: number;
  isFavorite?: boolean;
}

interface TrainerCardProps {
  trainer: Trainer;
  onFavoriteToggle?: (trainerId: string, isFavorite: boolean) => void;
}

const TrainerCard = ({ trainer, onFavoriteToggle }: TrainerCardProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const navigate = useNavigate();
  const [isFavorite, setIsFavorite] = useState(trainer.isFavorite || false);

  const handleFavoriteToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    const newFavoriteState = !isFavorite;
    setIsFavorite(newFavoriteState);
    onFavoriteToggle?.(trainer.id, newFavoriteState);
  };

  const handleCardClick = () => {
    navigate(`/athlete/trainer-details?id=${trainer.id}`);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <StarIcon
        key={index}
        className={`w-4 h-4 ${
          index < rating ? "text-green-400" : "text-gray-300 dark:text-gray-600"
        }`}
      />
    ));
  };

  return (
    <div
      onClick={handleCardClick}
      className="w-full max-w-sm md:max-w-none lg:w-72 xl:w-80 2xl:w-96 rounded-lg shadow-lg border transition-all duration-200 hover:shadow-xl hover:scale-105 cursor-pointer flex-shrink-0 mx-auto lg:mx-0"
      style={{
        backgroundColor: THEME_COLORS[mode].CARD_BG,
        borderColor: THEME_COLORS[mode].BORDER,
      }}
    >
      {/* Image Container */}
      <div className="relative">
        <div className="w-full h-48 rounded-t-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
          <img
            src={trainer.image || "https://placeholder.pics/svg/300"}
            alt={trainer.name}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Favorite Button */}
        <button
          onClick={handleFavoriteToggle}
          className="absolute top-2 right-2 w-8 h-8 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center hover:bg-black/40 transition-colors duration-200"
        >
          {isFavorite ? (
            <HeartIcon className="w-4 h-4 text-white" />
          ) : (
            <HeartOutlineIcon className="w-4 h-4 text-white" />
          )}
        </button>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Trainer Name */}
        <h3
          className="text-base font-semibold mb-2 transition-colors duration-200"
          style={{ color: THEME_COLORS[mode].TEXT }}
        >
          {trainer.name}
        </h3>

        {/* Description */}
        <p
          className="text-sm mb-4 line-clamp-3 transition-colors duration-200"
          style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
        >
          {trainer.description}
        </p>

        {/* Rating */}
        <div className="flex items-center mb-3">
          {renderStars(trainer.rating)}
        </div>

        {/* Price */}
        <div className="flex items-center">
          <span
            className="text-sm transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
          >
            Programs start from
          </span>
          <span
            className="text-sm font-bold ml-2 transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            ${trainer.startingPrice}
          </span>
        </div>
      </div>
    </div>
  );
};

export default TrainerCard;
