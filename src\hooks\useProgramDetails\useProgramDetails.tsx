import { useQuery } from "@tanstack/react-query";
import { useCustomModelQuery } from "@/query/shared/customModel";
import { Coupon, CouponUsage, Discount, ProgramDiscount } from "@/interfaces";

// Types for program details response
export interface ProgramDetailsResponse {
  id: number;
  user_id: number;
  program_name: string;
  type_of_program: string;
  program_description: string;
  target_levels: string[];
  currency: string;
  days_for_preview: number;
  image: string | null;
  track_progress: boolean;
  allow_comments: boolean;
  allow_private_messages: boolean;
  duration: string | null;
  rating: number;
  review_count: number;
  price: number | null;
  payment_plan: string[];
  splits: {
    id: number;
    program_id: number;
    equipment_required: string | null;
    title: string;
    full_price: number | null;
    subscription: number | null;
    created_at: string;
    updated_at: string;
  }[];

  program_discount?: ProgramDiscount | null;
  discount?: Discount | null;
  coupon?: Coupon | null;
  coupon_usage_stats?: CouponUsage[] | [];
  trainer: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
    full_name: string;
    photo: string | null;
  };
  created_at: string;
  updated_at: string;
}

// Custom hook for fetching program details
export const useProgramDetails = (programId: string | undefined) => {
  const customQuery = useCustomModelQuery();

  return useQuery({
    queryKey: ["program-details", programId],
    queryFn: async () => {
      if (!programId) throw new Error("Program ID is required");

      const result = await customQuery.mutateAsync({
        endpoint: `/v2/api/kanglink/custom/public/program/${programId}`,
        method: "GET",
        requiresAuth: false,
      });

      return result.data as ProgramDetailsResponse;
    },
    enabled: !!programId,
    staleTime: 0, // Always fetch fresh data
    refetchOnMount: true, // Refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });
};
