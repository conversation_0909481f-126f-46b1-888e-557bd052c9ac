import {
  Day,
  ExerciseInstance,
  Program,
  Session,
  Week,
} from "./model.interface";

// Workout Program Data Structure
export interface WorkoutExercise extends ExerciseInstance {
  progress?: {
    is_completed: boolean;
    sets_completed: number;
    reps_completed: string;
    weight_used: string;
    time_taken_seconds: number;
    difficulty_rating: number | null;
    notes: string;
    completed_at: string | null;
  };
}

export interface WorkoutSession extends Session {
  exercise_instances: WorkoutExercise[];
}

export interface WorkoutDay extends Day {
  sessions: WorkoutSession[];
  progress?: {
    is_completed: false;
    total_exercises: 0;
    completed_exercises: 0;
    completed_at: null;
    notes: "";
  };
}

export interface WorkoutWeek extends Week {
  days: WorkoutDay[];
  progress?: {
    total_days: 1;
    completed_days: 0;
    is_completed: false;
  };
}

export interface WorkoutProgram extends Program {
  weeks: WorkoutWeek[];
}
