import { useQuery } from "@tanstack/react-query";
import { useCustomModelQuery } from "@/query/shared/customModel";

// Types for review response
export interface ReviewResponse {
  id: number;
  user_id: number;
  program_id: number;
  content: string;
  rating: number;
  attachments: any[];
  is_edited: boolean;
  user: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
    full_name: string;
    photo: string | null;
  };
  created_at: string;
  updated_at: string;
}

export interface ProgramReviewsResponse {
  data: ReviewResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    num_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// Custom hook for fetching program reviews
export const useProgramReviews = (
  programId: string | undefined,
  params?: {
    page?: number;
    limit?: number;
    sort_by?: string;
    sort_order?: string;
  }
) => {
  const customQuery = useCustomModelQuery();

  return useQuery({
    queryKey: ["program-reviews", programId, params],
    queryFn: async () => {
      if (!programId) throw new Error("Program ID is required");

      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.sort_by) queryParams.append("sort_by", params.sort_by);
      if (params?.sort_order)
        queryParams.append("sort_order", params.sort_order);

      const endpoint = `/v2/api/kanglink/custom/public/program/${programId}/reviews${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`;

      const result = await customQuery.mutateAsync({
        endpoint,
        method: "GET",
        requiresAuth: false,
      });

      return result as ProgramReviewsResponse;
    },
    enabled: !!programId,
    staleTime: 0, // Always fetch fresh data
    refetchOnMount: true, // Refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });
};
