import { useEffect, useState, useRef } from "react";
import {
  CreateProgramStepOne,
  StepOneFormData,
} from "@/components/CreateProgramStepOne";
import { CreateProgramStepTwo } from "@/components/CreateProgramStepTwo";
import { CreateProgramPreview } from "@/components/CreateProgramPreview";
import {
  MkdWizardContainer,
  MkdWizardContainerRef,
} from "@/components/MkdWizardContainer";
import { useContexts } from "@/hooks/useContexts";

const AddTrainerProgram = () => {
  const { globalDispatch } = useContexts();
  const [stepOneData, setStepOneData] = useState<StepOneFormData | null>(null);
  const [stepTwoData, setStepTwoData] = useState<any>(null);
  const wizardRef = useRef<MkdWizardContainerRef>(null);

  // Set the path in global state for navigation highlighting
  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "program",
      },
    });
  }, [globalDispatch]);

  const handleStepOneSubmit = (data: any) => {
    setStepOneData(data);
    // Automatically go to next step after successful submission
    wizardRef.current?.goToNext();
  };

  const handleStepTwoSubmit = (data: any) => {
    setStepTwoData(data);

    // Handle the actual saving/publishing logic here
    const finalData = {
      ...stepOneData,
      ...data,
    };

    if (data.status === "draft") {
      // TODO: Save as draft to API
      console.log("Saving as draft:", finalData);
      // Show success message and go to preview
      // wizardRef.current?.goToNext();
    } else {
      wizardRef.current?.goToNext();
    }
  };

  const handleFinalSubmit = () => {
    // This is now the "Done" action from preview - navigate back to previous page
    window.history.back();
  };

  const handleCancel = () => {
    // TODO: Navigate back or show confirmation
    window.history.back();
  };

  const handleBack = () => {
    wizardRef.current?.goToPrevious();
  };

  return (
    <MkdWizardContainer ref={wizardRef} showButton={false}>
      <CreateProgramStepOne
        componentId={1}
        onSubmit={handleStepOneSubmit}
        onCancel={handleCancel}
        initialData={stepOneData}
      />
      <CreateProgramStepTwo
        componentId={2}
        onSubmit={handleStepTwoSubmit}
        onCancel={handleCancel}
        onBack={handleBack}
        onPreview={handleFinalSubmit}
        stepOneData={stepOneData}
        initialData={stepTwoData}
      />
      <CreateProgramPreview
        componentId={3}
        onSubmit={handleFinalSubmit}
        onCancel={handleBack}
        stepOneData={stepOneData}
        stepTwoData={stepTwoData}
      />
    </MkdWizardContainer>
  );
};

export default AddTrainerProgram;
