import { useParams } from "react-router-dom";
import { useTheme } from "@/hooks/useTheme";
import { useProgramPreview } from "@/hooks/useProgramPreview";
import { THEME_COLORS } from "@/context/Theme";
import { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";
import { useEnrollment } from "@/hooks/useEnrollment";
import { useCustomerCards } from "@/hooks/useCustomerCards";
import { useToast } from "@/hooks/useToast";
import { StripePaymentModal, CardConfirmationModal } from "@/components/SplitCard";
import LoginConfirmationModal from "@/components/SplitCard/LoginConfirmationModal";
import { TransformedProgramData } from "@/interfaces";

// Components
import {
  ProgramHeader,
  InfoCard,
  DayCard,
  SubscriptionCard,
} from "@/components/Programs";
import { ExerciseInstance } from "@/interfaces";

const ViewAthleteProgramPreviewPage = () => {
  const { state } = useTheme();
  const mode = state?.theme;
  const { splitId } = useParams<{ splitId: string }>();
  const { data, loading, error } = useProgramPreview(splitId || '');
  
  // Billing state management
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [isCardConfirmationModalOpen, setIsCardConfirmationModalOpen] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [selectedPaymentType, setSelectedPaymentType] = useState<"subscription" | "one_time">("subscription");
  const [selectedCard, setSelectedCard] = useState<any>(null);
  const [forceNewCard, setForceNewCard] = useState(false);
  const [affiliateCode, setAffiliateCode] = useState<string | null>(null);
  const [couponCode, setCouponCode] = useState<string>("");
  const [discountPreview, setDiscountPreview] = useState<any>(null);
  const [isLoadingDiscount, setIsLoadingDiscount] = useState(false);
  const [couponError, setCouponError] = useState<string | null>(null);
  const [debounceTimeout, setDebounceTimeout] = useState<number | null>(null);
  const [loadingTimeout, setLoadingTimeout] = useState<number | null>(null);
  const currentRequestRef = useRef<string | null>(null);
  
  const { showToast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const {
    isLoggedIn,
    useCheckEligibility,
    useGetPricing,
    useGetUserEnrollments,
    useGetDiscountPreview,
  } = useEnrollment();
  const { useGetCustomerCards } = useCustomerCards();
  const getDiscountPreview = useGetDiscountPreview();

  // Check eligibility and pricing - always call these hooks
  const { data: eligibility, isLoading: eligibilityLoading } = useCheckEligibility(splitId || '');
  const { data: pricing, isLoading: pricingLoading, error: pricingError } = useGetPricing(splitId || '');
  const { data: userEnrollments } = useGetUserEnrollments();
  const { data: customerCards } = useGetCustomerCards(isLoggedIn);

  // Extract affiliate code and coupon code from URL parameters
  useEffect(() => {
    const refCode = searchParams.get("ref");
    if (refCode) {
      setAffiliateCode(refCode);
    }

    const coupon = searchParams.get("coupon");
    if (coupon) {
      setCouponCode(coupon);
      // Don't call handleCouponCodeChange here to avoid infinite loops
    }
  }, [searchParams]);

  // Cleanup function to cancel pending requests
  useEffect(() => {
    return () => {
      currentRequestRef.current = null;
      if (debounceTimeout) {
        window.clearTimeout(debounceTimeout);
      }
      if (loadingTimeout) {
        window.clearTimeout(loadingTimeout);
      }
    };
  }, [debounceTimeout, loadingTimeout]);

  // Re-fetch discount preview when payment type changes
  useEffect(() => {
    currentRequestRef.current = null;
    setIsLoadingDiscount(false);

    if (loadingTimeout) {
      window.clearTimeout(loadingTimeout);
      setLoadingTimeout(null);
    }

    if (couponCode.trim()) {
      fetchDiscountPreview(couponCode, selectedPaymentType);
    }
  }, [selectedPaymentType, couponCode]);

  // Function to fetch discount preview
  const fetchDiscountPreview = async (code: string, paymentType: "subscription" | "one_time") => {
    if (!code.trim()) {
      setDiscountPreview(null);
      setCouponError(null);
      setIsLoadingDiscount(false);
      currentRequestRef.current = null;
      return;
    }

    const requestKey = `${code.trim()}-${paymentType}`;
    if (currentRequestRef.current === requestKey) {
      return;
    }

    currentRequestRef.current = requestKey;
    setIsLoadingDiscount(true);

    const timeoutId = window.setTimeout(() => {
      if (currentRequestRef.current === requestKey) {
        setIsLoadingDiscount(false);
        setCouponError("Request timed out. Please try again.");
        currentRequestRef.current = null;
      }
    }, 10000);
    setLoadingTimeout(timeoutId);

    try {
      const result = await getDiscountPreview.mutateAsync({
        split_id: parseInt(splitId || '0'),
        payment_type: paymentType,
        coupon_code: code.trim(),
      });

      if (result.error) {
        setDiscountPreview(null);
        setCouponError("Invalid or expired coupon code");
      } else {
        if (result.data.coupon_validation && !result.data.coupon_validation.valid) {
          setDiscountPreview(result.data);
          setCouponError(result.data.coupon_validation.message || "Invalid coupon code");
        } else {
          setDiscountPreview(result.data);
          setCouponError(null);
        }
      }
      setIsLoadingDiscount(false);
      if (loadingTimeout) {
        window.clearTimeout(loadingTimeout);
        setLoadingTimeout(null);
      }
    } catch (error: any) {
      setDiscountPreview(null);
      setCouponError(error.message || "Invalid coupon code");
      setIsLoadingDiscount(false);
      if (loadingTimeout) {
        window.clearTimeout(loadingTimeout);
        setLoadingTimeout(null);
      }
    }
  };

  // Handle coupon code input changes with debouncing
  const handleCouponCodeChange = (code: string) => {
    setCouponCode(code);
    setCouponError(null);

    if (debounceTimeout) {
      window.clearTimeout(debounceTimeout);
      setDebounceTimeout(null);
    }
    if (loadingTimeout) {
      window.clearTimeout(loadingTimeout);
      setLoadingTimeout(null);
    }

    currentRequestRef.current = null;

    if (!code.trim()) {
      setDiscountPreview(null);
      setIsLoadingDiscount(false);
      return;
    }

    const timeoutId = window.setTimeout(() => {
      fetchDiscountPreview(code, selectedPaymentType);
      setDebounceTimeout(null);
    }, 500);

    setDebounceTimeout(timeoutId);
  };

  if (loading) {
    return (
      <div
        className="min-h-screen w-full p-4 sm:p-6 lg:p-8 transition-colors duration-200 flex items-center justify-center"
        style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY }}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" 
               style={{ borderColor: THEME_COLORS[mode].PRIMARY }}></div>
          <p style={{ color: THEME_COLORS[mode].TEXT }}>Loading program preview...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="min-h-screen w-full p-4 sm:p-6 lg:p-8 transition-colors duration-200 flex items-center justify-center"
        style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY }}
      >
        <div className="text-center">
          <p style={{ color: THEME_COLORS[mode].TEXT }}>Error: {error}</p>
        </div>
      </div>
    );
  }

  if (!data?.split) {
    return (
      <div
        className="min-h-screen w-full p-4 sm:p-6 lg:p-8 transition-colors duration-200 flex items-center justify-center"
        style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY }}
      >
        <div className="text-center">
          <p style={{ color: THEME_COLORS[mode].TEXT }}>No program data found</p>
        </div>
      </div>
    );
  }

  const { split } = data;
  const { program, weeks } = split;

  // Get the first week for preview (or create a default structure)
  const firstWeek = weeks?.[0];
  const previewDays = firstWeek?.days?.slice(0, 3) || [];

  // Format program description for display
  const formatDescription = (description: string) => {
    if (!description) return ["No description available."];
    
    // Split by paragraphs and limit to 3 paragraphs
    const paragraphs = description.split('\n').filter(p => p.trim());
    return paragraphs.slice(0, 3).map(p => p.trim());
  };

  // Format equipment required for display
  const formatEquipment = (equipment: string) => {
    if (!equipment) return ["No equipment required."];
    
    // Split by commas or newlines and limit to 3 items
    const items = equipment.split(/[,\n]/).filter(item => item.trim());
    return items.slice(0, 3).map(item => item.trim());
  };

  // Convert exercises to the format expected by DayCard
  const convertExercisesForDay = (day: any) => {
    const exercises: Array<ExerciseInstance> = [];
    
    // Get exercises from all sessions in the day
    day.sessions?.forEach((session: any) => {
      session.exercises?.forEach((exercise: any) => {
        exercises.push({
          ...exercise,
        });
      });
    });

    return exercises.slice(0, 3); // Limit to 3 exercises for preview
  };

  // Create fallback pricing object
  const fallbackPricing = {
    split_id: parseInt(splitId || '0'),
    program_name: split.title,
    split_title: split.title,
    currency: "USD",
    pricing: {
      one_time: {
        amount: split.full_price || 0,
        available: split.full_price > 0,
        stripe_configured: true,
        description: "Lifetime access, no updates when trainer modifies split",
      },
      subscription: {
        amount: split.subscription || 0,
        available: split.subscription > 0,
        stripe_configured: true,
        description: "Monthly billing, access with automatic updates",
      },
      minimum: Math.min(split.subscription || 0, split.full_price || 0),
    },
    recommendations: {
      best_value: (split.full_price || 0) < (split.subscription || 0) * 4 ? ("one_time" as const) : ("subscription" as const),
      savings_months: Math.ceil((split.full_price || 0) / (split.subscription || 1)),
    },
  };

  // Use API pricing if available, otherwise use fallback
  const effectivePricing = pricing || (pricingError ? fallbackPricing : null);

  // Check if user is already enrolled
  const isAlreadyEnrolled = userEnrollments?.some(
    (enrollment: any) => enrollment.split_id === parseInt(splitId || '0') && enrollment.status === "active"
  );

  const handleSubscribe = () => {
    if (!isLoggedIn) {
      setSelectedPaymentType("subscription");
      setIsLoginModalOpen(true);
      return;
    }

    if (isAlreadyEnrolled) {
      showToast("You are already enrolled in this program", 5000);
      return;
    }

    if (eligibility && !eligibility.eligibility.can_enroll) {
      showToast(`Cannot enroll: ${eligibility.eligibility.reasons.join(", ")}`, 5000);
      return;
    }

    setSelectedPaymentType("subscription");
    if (customerCards?.payment_methods && customerCards.payment_methods.length > 0) {
      setIsCardConfirmationModalOpen(true);
    } else {
      setSelectedCard(null);
      setForceNewCard(true);
      setIsPaymentModalOpen(true);
    }
  };

  const handleBuy = () => {
    if (!isLoggedIn) {
      setSelectedPaymentType("one_time");
      setIsLoginModalOpen(true);
      return;
    }

    if (isAlreadyEnrolled) {
      showToast("You are already enrolled in this program", 5000);
      return;
    }

    if (eligibility && !eligibility.eligibility.can_enroll) {
      showToast(`Cannot enroll: ${eligibility.eligibility.reasons.join(", ")}`, 5000);
      return;
    }

    setSelectedPaymentType("one_time");
    setSelectedCard(null);
    setForceNewCard(true);
    setIsPaymentModalOpen(true);
  };

  const handleLoginConfirm = () => {
    setIsLoginModalOpen(false);
    const currentPath = location.pathname + location.search;
    const redirectUri = encodeURIComponent(currentPath);
    navigate(`/login?redirect_uri=${redirectUri}`);
  };

  const handleUseExistingCard = (card: any, coupon?: string) => {
    setSelectedCard(card);
    setForceNewCard(false);
    if (coupon) {
      setCouponCode(coupon);
    }
    setIsCardConfirmationModalOpen(false);
    setIsPaymentModalOpen(true);
  };

  const handleAddNewCard = (coupon?: string) => {
    setSelectedCard(null);
    setForceNewCard(true);
    if (coupon) {
      setCouponCode(coupon);
    }
    setIsCardConfirmationModalOpen(false);
    setIsPaymentModalOpen(true);
  };

  const handleBackToCardSelection = () => {
    setIsPaymentModalOpen(false);
    setIsCardConfirmationModalOpen(true);
    setForceNewCard(false);
  };

  const getCurrentPrice = (type: "subscription" | "one_time") => {
    if (effectivePricing?.pricing) {
      return effectivePricing.pricing[type].amount;
    }
    return type === "subscription" ? (split.subscription || 0) : (split.full_price || 0);
  };

  // Create program data for modals
  const programData: TransformedProgramData = {
    id: program.id.toString(),
    courseName: program.name,
    trainerName: program.trainer?.full_name || "Unknown Trainer",
    rating: 0,
    description: formatDescription(program.description),
    payment_plan: split.subscription > 0 ? ["monthly"] : [],
    trainer: {
      id: program.trainer?.id || 0,
      email: program.trainer?.email || "",
      first_name: program.trainer?.first_name || "",
      last_name: program.trainer?.last_name || "",
      full_name: program.trainer?.full_name || "Unknown Trainer",
      photo: program.trainer?.photo || null,
    },
    details: [
      { label: "Duration", value: `${weeks?.length || 0} Weeks` },
      { label: "Type", value: program.type || "Training" },
      { label: "Status", value: program.status || "Active" },
    ],
    splits: [{
      id: split.id.toString(),
      name: split.title,
      description: `Access to the complete ${program.name} program with ${weeks?.length || 0} weeks of training.`,
      subscriptionPrice: split.subscription || 0,
      buyPrice: split.full_price || 0,
    }],
    reviews: [],
    currency: program.currency || "USD",
    image: program.image_url || null,
    review_count: 0,
    days_for_preview: 30,
    approval_date: program.approval_date,
  };

  return (
    <>
      <div
        className="min-h-screen w-full p-4 sm:p-6 lg:p-8 transition-colors duration-200"
        style={{ backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY }}
      >
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Program Header */}
          <ProgramHeader
            courseName={`${program.name}:`}
            programDuration={`${weeks?.length || 0} Weeks Program`}
          />

          {/* Info Cards Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <InfoCard
              title="Project Description"
              content={formatDescription(program.description)}
            />
            <InfoCard
              title="Equipment Required"
              content={formatEquipment(split.equipment_required)}
            />
          </div>

          {/* Days Section */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-6">
            {/* Preview Days */}
            {previewDays.map((day, index) => (
              <DayCard
                key={day.id}
                dayNumber={day.day_order || index + 1}
                weekDay={`Week ${firstWeek?.week_order || 1} Day ${day.day_order || index + 1}`}
                exercises={convertExercisesForDay(day)}
              />
            ))}

            {/* Subscription Card */}
            <SubscriptionCard
              onSubscribe={handleSubscribe}
              onBuy={handleBuy}
            />
          </div>
        </div>
      </div>

      {/* Stripe Payment Modal */}
      {effectivePricing && isPaymentModalOpen && (
        <StripePaymentModal
          isOpen={isPaymentModalOpen}
          onClose={() => setIsPaymentModalOpen(false)}
          splitId={parseInt(splitId || '0')}
          programId={program.id.toString()}
          pricing={effectivePricing}
          paymentType={selectedPaymentType}
          existingCard={selectedCard}
          forceNewCard={forceNewCard}
          affiliateCode={affiliateCode}
          onBackToCardSelection={
            customerCards?.payment_methods && customerCards.payment_methods.length > 0
              ? handleBackToCardSelection
              : undefined
          }
          programData={programData}
          initialCouponCode={couponCode}
          discountPreview={discountPreview}
          isLoadingDiscount={isLoadingDiscount}
          couponError={couponError}
          onCouponCodeChange={handleCouponCodeChange}
        />
      )}

      {/* Card Confirmation Modal */}
      {effectivePricing && isCardConfirmationModalOpen && (
        <CardConfirmationModal
          isOpen={isCardConfirmationModalOpen}
          onClose={() => setIsCardConfirmationModalOpen(false)}
          onUseExistingCard={handleUseExistingCard}
          onAddNewCard={handleAddNewCard}
          splitName={split.title}
          paymentType={selectedPaymentType}
          amount={getCurrentPrice(selectedPaymentType)}
          currency={effectivePricing.currency}
          splitId={parseInt(splitId || '0')}
          programData={programData}
          discountPreview={discountPreview}
          isLoadingDiscount={isLoadingDiscount}
          couponError={couponError}
          onCouponCodeChange={handleCouponCodeChange}
          initialCouponCode={couponCode}
        />
      )}

      {/* Login Confirmation Modal */}
      <LoginConfirmationModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
        onConfirm={handleLoginConfirm}
        actionType={selectedPaymentType}
        splitName={split.title}
        price={getCurrentPrice(selectedPaymentType)}
      />
    </>
  );
};

export default ViewAthleteProgramPreviewPage;
