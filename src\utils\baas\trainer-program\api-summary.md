# Trainer Program APIs - Summary

## Overview
This document provides a concise summary of all APIs needed to implement the AddTrainerProgramPage functionality.

## Base URL
```
https://api.kanglink.com/v2/api/kanglink/custom/trainer
```

## Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer {jwt_token}
```

---

## API Endpoints Summary

### Program Management APIs

| Method | Endpoint | Purpose | Key Request Data | Key Response Data |
|--------|----------|---------|------------------|-------------------|
| `POST` | `/programs/draft` | Save program as draft | stepOneData, stepTwoData | programId, status, createdAt |
| `POST` | `/programs/publish` | Publish completed program | stepOneData, stepTwoData | programId, status, publishedAt, programUrl |
| `PUT` | `/programs/{programId}` | Update existing program | stepOneData, stepTwoData, status | programId, status, updatedAt |
| `GET` | `/programs/{programId}` | Get program for editing | programId (URL param) | Complete program data, canEdit |
| `DELETE` | `/programs/{programId}` | Delete program | programId (URL param) | programId, deletedAt |
| `POST` | `/programs/{programId}/duplicate` | Duplicate program | newProgramName, copyPricing, copyStructure | originalProgramId, newProgramId |
| `POST` | `/programs/validate` | Validate program data | stepOneData, stepTwoData | isValid, errors, warnings |

### Exercise Library APIs

| Method | Endpoint | Purpose | Key Request Data | Key Response Data |
|--------|----------|---------|------------------|-------------------|
| `GET` | `/exercises` | Get exercise library | search, category, muscleGroup (query params) | exercises array, pagination |
| `POST` | `/exercises` | Add custom exercise | name, category, muscleGroups, equipment, saveToLibrary | exercise object, savedToLibrary |

### Video Library APIs

| Method | Endpoint | Purpose | Key Request Data | Key Response Data |
|--------|----------|---------|------------------|-------------------|
| `GET` | `/videos` | Get video library | search (query param) | videos array, pagination |
| `POST` | `/videos/upload` | Upload video file | file, name, saveToLibrary (multipart) | video object, uploadUrl, savedToLibrary |
| `POST` | `/videos` | Add video by URL | name, url, saveToLibrary | video object, savedToLibrary |

### Configuration APIs

| Method | Endpoint | Purpose | Key Request Data | Key Response Data |
|--------|----------|---------|------------------|-------------------|
| `GET` | `/program-types` | Get program categories | None | programTypes array |
| `GET` | `/currencies` | Get available currencies | None | currencies array |

---

## Key Data Structures

### Step One Form Data
```typescript
interface StepOneFormData {
  programName: string;
  typeOfProgram: string;
  programDescription: string;
  paymentPlan: string[]; // ["oneTime", "monthly"]
  trackProgress: boolean;
  allowComments: boolean;
  allowPrivateMessages: boolean;
  targetLevels: string[]; // ["beginner", "intermediate", "expert"]
  splitProgram: number;
  splits: ProgramSplit[];
  currency: string;
  daysForPreview: number;
}
```

### Step Two Form Data
```typescript
interface StepTwoFormData {
  programSplit: string;
  description: string;
  equipmentRequired: string;
  weeks: Week[];
  splitConfigurations: {
    [splitId: string]: Week[];
  };
  status: "draft" | "published";
}
```

### Program Split
```typescript
interface ProgramSplit {
  split_id: string;
  title: string;
  fullPrice?: number;
  subscription?: number;
}
```

### Exercise Structure
```typescript
interface Exercise {
  id: string;
  name: string;
  sets: string;
  repsOrTime: string;
  repsTimeType: "reps" | "time";
  videoUrl: string;
  exerciseDetails: string;
  restDuration: {
    minutes: number;
    seconds: number;
  };
  linkedExerciseId: string | null;
  isLinked: boolean;
}
```

### Program Structure Hierarchy
```
Program
├── Splits (multiple pricing tiers)
│   └── Split Configurations
│       └── Weeks
│           └── Days
│               └── Sessions
│                   └── Exercises
```

---

## Implementation Flow

### 1. Page Load
- Load program types: `GET /program-types`
- Load currencies: `GET /currencies`
- Load exercise library: `GET /exercises`
- Load video library: `GET /videos`

### 2. Step One Completion
- Validate form data locally
- Optionally validate with API: `POST /programs/validate`
- Store data in component state

### 3. Step Two Completion
- Load exercises for dropdowns: `GET /exercises`
- Load videos for dropdowns: `GET /videos`
- Allow adding custom exercises: `POST /exercises`
- Allow adding custom videos: `POST /videos` or `POST /videos/upload`
- Validate complete program: `POST /programs/validate`

### 4. Save as Draft
- Submit to: `POST /programs/draft`
- Show success message
- Navigate to preview

### 5. Publish Program
- Submit to: `POST /programs/publish`
- Show success message with program URL
- Navigate to preview or program list

### 6. Edit Existing Program
- Load program data: `GET /programs/{programId}`
- Populate forms with existing data
- Update with: `PUT /programs/{programId}`

---

## Error Handling

### Common HTTP Status Codes
- `400` - Bad Request (Invalid input data)
- `401` - Unauthorized (Invalid or missing token)
- `403` - Forbidden (Insufficient permissions)
- `404` - Not Found (Program or resource not found)
- `409` - Conflict (Program name already exists)
- `422` - Unprocessable Entity (Validation errors)
- `500` - Internal Server Error

### Validation Rules
- Program name is required and must be unique
- At least one payment plan must be selected
- If "oneTime" selected, fullPrice required for all splits
- If "monthly" selected, subscription price required for all splits
- Each split must have at least one week with valid structure
- Non-rest days must have at least one session
- Each session must have at least one exercise

---

## Integration Points

### File Upload
- Uses existing MkdSDK upload functionality
- Video files uploaded to CDN with automatic thumbnail generation
- Supports both file upload and external URL linking

### Authentication
- All APIs use existing Bearer token authentication
- Token should be included in Authorization header
- Trainer ID extracted from token for data filtering

### Data Persistence
- Draft programs saved immediately for recovery
- Published programs become publicly accessible
- All changes tracked with timestamps
- Support for program versioning and rollback
