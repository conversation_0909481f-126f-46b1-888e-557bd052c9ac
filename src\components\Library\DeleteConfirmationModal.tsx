import React from "react";
import { Modal } from "@/components/Modal";
import { InteractiveButton } from "@/components/InteractiveButton";
import { AlertTriangle } from "lucide-react";

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  itemName?: string;
  isLoading?: boolean;
}

const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  itemName,
  isLoading = false,
}) => {
  return (
    <Modal
      isOpen={isOpen}
      title={title}
      modalCloseClick={onClose}
      modalHeader={true}
      classes={{
        modal: "h-full",
        modalDialog: "w-full max-w-md h-auto",
        modalContent: "",
      }}
    >
      <div className="space-y-6">
        {/* Warning Icon */}
        <div className="flex justify-center">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>
        </div>

        {/* Message */}
        <div className="text-center space-y-2">
          <p className="text-text">{message}</p>
          {itemName && (
            <p className="text-sm text-text-secondary">
              <span className="font-medium">Item:</span> {itemName}
            </p>
          )}
        </div>

        {/* Warning Text */}
        <div className="bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-sm text-red-800 dark:text-red-200">
            <strong>Warning:</strong> This action cannot be undone. The item
            will be permanently deleted.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4">
          <InteractiveButton
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-border text-text hover:bg-background-hover transition-colors"
            disabled={isLoading}
          >
            Cancel
          </InteractiveButton>
          <InteractiveButton
            type="button"
            onClick={onConfirm}
            className="px-4 py-2 bg-red-600 border-red-600 text-white hover:bg-red-700 transition-colors"
            disabled={isLoading}
          >
            {isLoading ? "Deleting..." : "Delete"}
          </InteractiveButton>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteConfirmationModal;
