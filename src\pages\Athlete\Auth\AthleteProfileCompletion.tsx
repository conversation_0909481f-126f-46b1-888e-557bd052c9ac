import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Container } from "@/components/Container";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";

import { ToastStatusEnum } from "@/utils/Enums";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";
import { useProfile } from "@/hooks/useProfile";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCalendarDays,
  faChevronDown,
} from "@fortawesome/free-solid-svg-icons";
import { useUpdateModelMutation } from "@/query/shared";
import { Models } from "@/utils/baas";

const FITNESS_GOALS = [
  "Weight Loss",
  "Muscle Gain",
  "Endurance",
  "Flexibility",
  "General Fitness",
  "Sports Performance",
];

// Age validation constants
const MINIMUM_AGE = 18; // Minimum age requirement

const schema = yup.object().shape({
  fullName: yup.string().required("Full Name is required"),
  dob: yup.string().required("Date of Birth is required"),
  level: yup.string().required("Select your level"),
  fitnessGoals: yup.string().required("Select at least one fitness goal"),
}); 

const AthleteProfileCompletion = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  // SDK and context hooks
  const { sdk } = useSDK({ role: "member" });
  const { mutateAsync: updateModel, isPending: isUpdatingModel } = useUpdateModelMutation(Models.USER);
  const { showToast } = useContexts();
  const { profile, getProfile } = useProfile();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    setError,
    clearErrors,
    watch,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      fullName: "",
      dob: "",
      level: "",
      fitnessGoals: "",
    },
  });

  // Watch form fields for validation
  const { dob } = watch();

  const [selectedGoals, setSelectedGoals] = useState<string[]>([]);

  const handleToggleGoal = (goal: string) => {
    setSelectedGoals((prev) =>
      prev.includes(goal) ? prev.filter((g) => g !== goal) : [...prev, goal]
    );
  };

  const handleRemoveGoal = (goal: string) => {
    setSelectedGoals((prev) => prev.filter((g) => g !== goal));
  };

  // Real-time age validation function
  const validateAge = (dateValue: string) => {
    if (!dateValue) {
      clearErrors("dob");
      return;
    }

    const selectedDate = new Date(dateValue);
    const today = new Date();
    const age = today.getFullYear() - selectedDate.getFullYear();
    const monthDiff = today.getMonth() - selectedDate.getMonth();
    
    // Adjust age if birthday hasn't occurred this year
    const actualAge = monthDiff < 0 || (monthDiff === 0 && today.getDate() < selectedDate.getDate()) 
      ? age - 1 
      : age;
    
    // Check if date is in the future
    if (selectedDate > today) {
      setError("dob", {
        type: "manual",
        message: "Date of birth cannot be in the future.",
      });
      return;
    }
    
    // Check minimum age
    if (actualAge < MINIMUM_AGE) {
      setError("dob", {
        type: "manual",
        message: `You must be at least ${MINIMUM_AGE} years old. You are currently ${actualAge} years old.`,
      });
      return;
    }
    
    // Clear error if validation passes
    clearErrors("dob");
  };

  const onSubmit = async (data: yup.InferType<typeof schema>) => {
    try {
      // Age validation
      const today = new Date();
      const birthDate = new Date(data.dob);
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      
      if (age < 18) {
        showToast("You must be at least 18 years old to register.", 4000, ToastStatusEnum.ERROR);
        return;
      }

      setIsLoading(true);
      
      // Prepare all profile data to send with registration
      const profileData = {
        data: JSON.stringify({
          ...profile?.data,
          full_name: data.fullName,
          first_name: data.fullName.split(" ")[0],
          last_name: data.fullName.split(" ")[1],
          dob: data.dob,
          level: data.level,
          fitness_goals: selectedGoals
        }),
        profile_update: true,
      };

      const result: any = await updateModel({
        id: profile?.id,
        payload: profileData
      });

      if (!result.error) {
        showToast("Profile completed successfully!", 4000, ToastStatusEnum.SUCCESS);
        // Refresh profile data
        await getProfile();
        navigate('/');
      } else {
        showToast(result.message || "Profile completion failed", 4000, ToastStatusEnum.ERROR);
      }
    } catch (error: any) {
      console.error("Profile completion error:", error);
      showToast(error?.response?.data?.message || error?.message || "Profile completion failed", 4000, ToastStatusEnum.ERROR);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    setValue("fitnessGoals", selectedGoals.join(","));
  }, [selectedGoals, setValue]);

  // Validate age when dob value changes
  useEffect(() => {
    if (dob) {
      validateAge(dob);
    } else {
      clearErrors("dob");
    }
  }, [dob]);

  // Populate form with existing profile data
  useEffect(() => {
    if (profile?.data) {
      const profileData = profile.data;
      let parsedData: any = profileData;
      if (typeof profileData === "string") {
        try {
          parsedData = JSON.parse(profileData);
        } catch (e) {
          console.warn("Could not parse profile data:", e);
        }
      }

      setValue("fullName", profile?.full_name || parsedData?.full_name || parsedData?.first_name + " " + parsedData?.last_name || "");
      setValue("dob", parsedData?.dob || profile?.dob || "");
      setValue("level", parsedData?.level || profile?.level || "");
      setSelectedGoals(parsedData?.fitness_goals || profile?.fitness_goals || []);
    }
  }, [profile, setValue]);

  return (
    <Container className="bg-background min-h-screen">
      <main className="flex-1 flex items-center justify-center px-4 py-8">
        <div className="w-full max-w-2xl flex flex-col items-center">
          {/* Card */}
          <div className="w-full bg-secondary border border-border shadow-lg rounded-lg px-8 py-10 sm:px-12 sm:py-14">
            {/* Title */}
            <div className="text-center mb-10">
              <h1 className="text-3xl font-bold text-text">
                Complete Your Profile
              </h1>
              <p className="text-text-disabled mt-2">
                Please provide some additional information to complete your profile
              </p>
            </div>
            {/* Form */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
              {/* 2-column grid for large screens */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Full Name */}
                <MkdInputV2
                  name="fullName"
                  register={register}
                  errors={errors}
                  required
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className="text-text">
                      Full Name
                    </MkdInputV2.Label>
                    <MkdInputV2.Field
                      placeholder="Enter Full Name"
                      className="bg-input border-border text-text placeholder:text-text-disabled"
                    />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
                {/* Date of Birth */}
                <MkdInputV2
                  name="dob"
                  type="date"
                  register={register}
                  errors={errors}
                  required
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className="text-text">
                      Date of Birth
                    </MkdInputV2.Label>
                    <div
                      className="relative cursor-pointer"
                      onClick={() => {
                        // Find the date input within this container and trigger click
                        const container = document
                          .querySelector('[name="dob"]')
                          ?.closest(".relative");
                        const input = container?.querySelector(
                          'input[type="date"]'
                        ) as HTMLInputElement;
                        if (input) {
                          input.focus();
                          input.click();
                          // Fallback for browsers that support showPicker
                          if (input.showPicker) {
                            try {
                              input.showPicker();
                            } catch (e) {
                              // Ignore errors if showPicker is not supported
                            }
                          }
                        }
                      }}
                    >
                      <MkdInputV2.Field
                        placeholder="mm/dd/yyyy"
                        className="bg-input border-border text-text placeholder:text-text-disabled pr-10 [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-inner-spin-button]:hidden [&::-webkit-outer-spin-button]:hidden cursor-pointer"
                        onChange={(e) => validateAge((e.target as HTMLInputElement).value)}
                        onBlur={(e) => validateAge((e.target as HTMLInputElement).value)}
                        onInput={(e) => validateAge((e.target as HTMLInputElement).value)}
                      />
                      <FontAwesomeIcon
                        icon={faCalendarDays}
                        className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 pointer-events-none text-icon"
                      />
                    </div>
                    <MkdInputV2.Error />
                    <p className="mt-1 text-xs text-text-disabled">
                      You must be at least {MINIMUM_AGE} years old
                    </p>
                  </MkdInputV2.Container>
                </MkdInputV2>
                {/* Select Level */}
                <MkdInputV2
                  name="level"
                  type="select"
                  register={register}
                  errors={errors}
                  required
                  options={["beginner", "intermediate", "expert"]}
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className="text-text">
                      Select Level
                    </MkdInputV2.Label>
                    <div className="relative">
                      <MkdInputV2.Field
                        placeholder="Choose your level"
                        className="bg-none border-border text-text placeholder:text-text-disabled pr-10"
                      />
                      <FontAwesomeIcon
                        icon={faChevronDown}
                        className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 pointer-events-none text-icon"
                      />
                    </div>
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>
              {/* Fitness Goals - Multi-select with chips */}
              <div>
                <label className="block text-text font-bold mb-2">
                  Fitness Goals
                </label>
                <div className="hidden flex-wrap gap-2 mb-2">
                  {selectedGoals.map((goal) => (
                    <span
                      key={goal}
                      className="flex items-center bg-primary text-secondary rounded-full px-3 py-1 text-xs font-medium shadow-sm"
                    >
                      {goal}
                      <button
                        type="button"
                        aria-label={`Remove ${goal}`}
                        className="ml-2 text-secondary hover:text-secondary/80 focus:outline-none"
                        onClick={() => handleRemoveGoal(goal)}
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
                <div className="flex flex-wrap gap-2">
                  {FITNESS_GOALS.map((goal) => (
                    <button
                      key={goal}
                      type="button"
                      className={`px-3 py-1 rounded-full border text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 ${
                        selectedGoals.includes(goal)
                          ? "bg-primary text-secondary border-primary"
                          : "bg-input text-text hover:bg-input/80 border-border"
                      }`}
                      aria-pressed={selectedGoals.includes(goal)}
                      onClick={() => handleToggleGoal(goal)}
                    >
                      {goal}
                    </button>
                  ))}
                </div>
                {/* Hidden input for react-hook-form */}
                <input
                  type="hidden"
                  {...register("fitnessGoals", {
                    validate: () =>
                      selectedGoals.length > 0 ||
                      "Select at least one fitness goal",
                  })}
                  value={selectedGoals.join(",")}
                />
                {errors.fitnessGoals && (
                  <p className="mt-2 text-xs text-red-500">
                    {errors.fitnessGoals.message as string}
                  </p>
                )}
                <p className="mt-2 text-xs text-text-disabled">
                  Select all fitness goals that apply to your training
                  preferences.
                </p>
              </div>
              {/* Submit Button */}
              <InteractiveButton
                type="submit"
                loading={isLoading}
                className="w-full h-12 mt-2 rounded font-semibold text-base transition-colors bg-primary text-secondary hover:bg-primary-hover"
                disabled={isLoading}
              >
                Complete Profile
              </InteractiveButton>
            </form>
          </div>
        </div>
      </main>
    </Container>
  );
};

export default AthleteProfileCompletion; 