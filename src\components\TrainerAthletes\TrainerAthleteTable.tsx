import React from "react";
import { TrainerAthleteData } from "./types";

interface TrainerAthleteTableProps {
  athletes: TrainerAthleteData[];
  isLoading: boolean;
}

const TrainerAthleteTable: React.FC<TrainerAthleteTableProps> = ({
  athletes,
  isLoading,
}) => {
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  const formatProgress = (percentage: number, status: string) => {
    return `${percentage.toFixed(1)}% - ${status}`;
  };

  if (isLoading) {
    return (
      <div className="overflow-x-auto rounded-lg shadow-sm border border-border bg-background">
        <div className="p-8 text-center">
          <div className="flex flex-col items-center justify-center space-y-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <div className="text-text">Loading athletes...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto rounded-lg shadow-sm border border-border bg-background">
      <table className="min-w-full divide-y divide-border">
        <thead className="bg-table-header">
          <tr>
            <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
              Athlete Name
              <span className="ml-1 text-gray-400 hidden sm:inline">↕</span>
            </th>
            <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider hidden md:table-cell">
              Type of Purchase
              <span className="ml-1 text-gray-400">↕</span>
            </th>
            <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
              Program
            </th>
            <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider hidden lg:table-cell">
              Date Joined
              <span className="ml-1 text-gray-400">↕</span>
            </th>
            <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
              Progress
            </th>
          </tr>
        </thead>
        <tbody className="bg-background divide-y divide-border">
          {athletes.length > 0 ? (
            athletes.map((athlete, index) => (
              <tr
                key={athlete.enrollment_id}
                className={`hover:bg-table-row-hover transition-colors duration-200 ${
                  index % 2 === 1 ? "bg-table-row-alternate" : ""
                }`}
              >
                <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-text">
                  <div className="flex items-center">
                    {athlete.athlete_photo && (
                      <img
                        className="h-8 w-8 rounded-full mr-3"
                        src={athlete.athlete_photo}
                        alt={athlete.athlete_name}
                        onError={(e) => {
                          (e.target as HTMLImageElement).style.display = "none";
                        }}
                      />
                    )}
                    <div>
                      <div className="font-medium">{athlete.athlete_name}</div>
                      <div className="text-text-secondary text-xs">
                        {athlete.athlete_email}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-text hidden md:table-cell">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                    {athlete.payment_type_display}
                  </span>
                </td>
                <td className="px-3 sm:px-6 py-4 text-sm text-text">
                  <div className="font-medium">{athlete.program_name}</div>
                  <div className="text-text-secondary text-xs">
                    {athlete.split_title}
                  </div>
                  <div className="text-text-secondary text-xs md:hidden mt-1">
                    {athlete.payment_type_display} • {formatDate(athlete.enrollment_date)}
                  </div>
                </td>
                <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-text hidden lg:table-cell">
                  {formatDate(athlete.enrollment_date)}
                </td>
                <td className="px-3 sm:px-6 py-4 text-sm text-text">
                  <div className="max-w-xs lg:max-w-md">
                    <div className="font-medium">
                      {formatProgress(athlete.progress_percentage, athlete.progress_status)}
                    </div>
                    <div className="text-text-secondary text-xs">
                      {athlete.total_days_completed} days • {athlete.total_exercises_completed} exercises
                    </div>
                    {athlete.last_activity_date && (
                      <div className="text-text-secondary text-xs">
                        Last: {formatDate(athlete.last_activity_date)}
                      </div>
                    )}
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={5}
                className="px-3 sm:px-6 py-8 text-center text-sm text-text-secondary"
              >
                <div className="flex flex-col items-center justify-center space-y-2">
                  <div className="text-lg">👥</div>
                  <div>No athletes found</div>
                  <div className="text-xs">
                    Try adjusting your search criteria
                  </div>
                </div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default TrainerAthleteTable;
