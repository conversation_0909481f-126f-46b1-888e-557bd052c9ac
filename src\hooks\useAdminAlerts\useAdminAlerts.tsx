import { useQuery } from "@tanstack/react-query";
import { useSDK } from "@/hooks/useSDK";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import { useCustomModelQuery } from "@/query/shared";

export interface AdminAlert {
  id: number;
  activity_type: string;
  title: string;
  description: string;
  metadata: any;
  actor_name: string;
  actor_email: string;
  created_at: string;
  updated_at: string;
}

export interface AdminAlertStats {
  alert_type_stats: Array<{
    activity_type: string;
    count: number;
  }>;
  recent_alerts_count: number;
  unread_alerts_count: number;
  pending_approval_programs_count: number;
  pending_refund_requests_count: number;
}

export interface PendingProgram {
  id: number;
  program_name: string;
  program_description: string;
  type_of_program: string;
  payment_plan: any;
  currency: string;
  days_for_preview: number;
  image: string;
  status: string;
  trainer_id: number;
  trainer_name: string;
  trainer_email: string;
  created_at: string;
  updated_at: string;
}

export interface RefundRequest {
  id: number;
  enrollment_id: number;
  athlete_id: number;
  trainer_id: number;
  program_id: number;
  split_id: number;
  amount: number;
  currency: string;
  reason: string;
  status: string;
  requested_at: string;
  processed_at: string;
  processed_by: number;
  admin_notes: string;
  stripe_refund_id: string;
  refund_amount: number;
  created_at: string;
  updated_at: string;
  program_name: string;
  athlete_name: string;
  athlete_email: string;
  trainer_name: string;
  trainer_email: string;
}

export interface AdminAlertsResponse {
  alerts: AdminAlert[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface AdminAlertsFilters {
  page?: number;
  limit?: number;
  activity_type?: string;
  date_from?: string;
  date_to?: string;
}

const useAdminAlerts = (filters: AdminAlertsFilters = {}) => {
  const { sdk } = useSDK({ role: "super_admin" });
  const { tokenExpireError, showToast } = useContexts();
  const { mutateAsync: customModelQuery } = useCustomModelQuery()

  const queryFn = async () => {
    try {
      const params = new URLSearchParams();

      if (filters.page) params.append("page", filters.page.toString());
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.activity_type) params.append("activity_type", filters.activity_type);
      if (filters.date_from) params.append("date_from", filters.date_from);
      if (filters.date_to) params.append("date_to", filters.date_to);

      const response = await customModelQuery({
        endpoint: `/v2/api/kanglink/custom/admin/alerts?${params.toString()}`,
        method: "GET"
      });

      if (response.error) {
        throw new Error(response.message || "Failed to fetch admin alerts");
      }

      return response.data as AdminAlertsResponse;
    } catch (error: any) {
      console.error("Error fetching admin alerts:", error);
      const message = error?.message || "Failed to fetch admin alerts";
      showToast(message, 5000, ToastStatusEnum.ERROR);
      tokenExpireError(message);
      throw error;
    }
  };

  return useQuery({
    queryKey: ["admin-alerts", filters],
    queryFn,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
  });
};

const useAdminAlertStats = () => {
  const { sdk } = useSDK({ role: "super_admin" });
  const { tokenExpireError, showToast } = useContexts();
  const { mutateAsync: customModelQuery } = useCustomModelQuery()

  const queryFn = async () => {
    try {
      const response = await customModelQuery({
        endpoint: "/v2/api/kanglink/custom/admin/alerts/stats",
        method: "GET"
      });

      if (response.error) {
        throw new Error(response.message || "Failed to fetch admin alert statistics");
      }

      return response.data as AdminAlertStats;
    } catch (error: any) {
      console.error("Error fetching admin alert stats:", error);
      const message = error?.message || "Failed to fetch admin alert statistics";
      showToast(message, 5000, ToastStatusEnum.ERROR);
      tokenExpireError(message);
      throw error;
    }
  };

  return useQuery({
    queryKey: ["admin-alerts-stats"],
    queryFn,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 60 * 1000, // Refetch every minute
  });
};

const usePendingApprovalPrograms = () => {
  const { sdk } = useSDK({ role: "super_admin" });
  const { tokenExpireError, showToast } = useContexts();
  const { mutateAsync: customModelQuery } = useCustomModelQuery()

  const queryFn = async () => {
    try {
      const response = await customModelQuery({
        endpoint: "/v2/api/kanglink/custom/admin/alerts/pending-programs",
        method: "GET"
      });

      if (response.error) {
        throw new Error(response.message || "Failed to fetch pending approval programs");
      }

      return response.data as { programs: PendingProgram[]; count: number };
    } catch (error: any) {
      console.error("Error fetching pending approval programs:", error);
      const message = error?.message || "Failed to fetch pending approval programs";
      showToast(message, 5000, ToastStatusEnum.ERROR);
      tokenExpireError(message);
      throw error;
    }
  };

  return useQuery({
    queryKey: ["pending-approval-programs"],
    queryFn,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
  });
};

const useRefundRequests = (filters: { status?: string; athlete_id?: number; trainer_id?: number } = {}) => {
  const { sdk } = useSDK({ role: "super_admin" });
  const { tokenExpireError, showToast } = useContexts();
  const { mutateAsync: customModelQuery } = useCustomModelQuery()

  const queryFn = async () => {
    try {
      const params = new URLSearchParams();

      if (filters.status) params.append("status", filters.status);
      if (filters.athlete_id) params.append("athlete_id", filters.athlete_id.toString());
      if (filters.trainer_id) params.append("trainer_id", filters.trainer_id.toString());

      const response = await customModelQuery({
        endpoint: `/v2/api/kanglink/custom/admin/alerts/refund-requests?${params.toString()}`,
        method: "GET"
      });

      if (response.error) {
        throw new Error(response.message || "Failed to fetch refund requests");
      }

      return response.data as { refund_requests: RefundRequest[]; count: number };
    } catch (error: any) {
      console.error("Error fetching refund requests:", error);
      const message = error?.message || "Failed to fetch refund requests";
      showToast(message, 5000, ToastStatusEnum.ERROR);
      tokenExpireError(message);
      throw error;
    }
  };

  return useQuery({
    queryKey: ["refund-requests", filters],
    queryFn,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
  });
};

const useCreateSystemAlert = () => {
  const { sdk } = useSDK({ role: "super_admin" });
  const { tokenExpireError, showToast } = useContexts();
  const { mutateAsync: customModelQuery } = useCustomModelQuery()

  const createSystemAlert = async (data: { title: string; description: string; metadata?: any }) => {
    try {
      const response = await customModelQuery({
        endpoint: "/v2/api/kanglink/custom/admin/alerts/system",
        method: "POST",
        body: data
      });

      if (response.error) {
        throw new Error(response.message || "Failed to create system alert");
      }

      showToast("System alert created successfully", 3000, ToastStatusEnum.SUCCESS);
      return response;
    } catch (error: any) {
      console.error("Error creating system alert:", error);
      const message = error?.message || "Failed to create system alert";
      showToast(message, 5000, ToastStatusEnum.ERROR);
      tokenExpireError(message);
      throw error;
    }
  };

  return { createSystemAlert };
};

export { useAdminAlerts, useAdminAlertStats, usePendingApprovalPrograms, useRefundRequests, useCreateSystemAlert }; 