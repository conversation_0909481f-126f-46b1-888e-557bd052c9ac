/**
 * Trainer Discount Management API Specifications
 * 
 * This file contains all API endpoints, request/response schemas, and data structures
 * needed for the trainer discount page functionality.
 * 
 * Each discount is linked to a specific trainer's program.
 */

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface DiscountType {
  type: 'fixed' | 'percentage';
  value: number;
}

export interface PromoCodeSettings {
  code: string;
  discountType: 'fixed' | 'percentage';
  discountValue: number;
  appliesTo: {
    subscription: boolean;
    fullPayment: boolean;
  };
  isActive: boolean;
  expiryDate?: string;
  usageLimit?: number;
  usedCount?: number;
}

export interface SaleDiscountSettings {
  type: 'fixed' | 'percentage';
  value: number;
  applyToAll: boolean;
}

export interface SubscriptionDiscountItem {
  id: number;
  tierId: number;
  name: string;
  originalPrice: number;
  discountType: 'fixed' | 'percentage';
  discountValue: number;
  finalPrice: number;
}

export interface FullPriceDiscountItem {
  id: number;
  tierId: number;
  name: string;
  originalPrice: number;
  discountType: 'fixed' | 'percentage';
  discountValue: number;
  finalPrice: number;
}

export interface PricingTier {
  id: number;
  name: string;
  price: number;
  currency: string;
  stripeProductId?: string;
  stripePriceId?: string;
}

export interface SubscriptionTier extends PricingTier {
  billingCycle: 'monthly' | 'quarterly' | 'yearly';
}

export interface FullPaymentTier extends PricingTier {
  features: string[];
}

// ============================================================================
// API ENDPOINT SPECIFICATIONS
// ============================================================================

export const TrainerDiscountAPIs = {
  
  // 1. GET PROGRAM DISCOUNT SETTINGS
  GET_PROGRAM_DISCOUNTS: {
    url: "/v2/api/kanglink/custom/trainer/programs/{programId}/discounts",
    method: "GET",
    params: {
      programId: "number"
    },
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json"
    },
    response: {
      success: "boolean",
      data: {
        programId: "number",
        affiliateLink: "string",
        saleDiscount: "SaleDiscountSettings",
        promoCode: "PromoCodeSettings | null",
        subscriptionDiscounts: "SubscriptionDiscountItem[]",
        fullPriceDiscounts: "FullPriceDiscountItem[]",
        lastUpdated: "string (ISO date)",
        updatedBy: "string (user ID)"
      }
    }
  },

  // 2. GET PROGRAM PRICING TIERS
  GET_PROGRAM_PRICING: {
    url: "/v2/api/kanglink/custom/trainer/programs/{programId}/pricing",
    method: "GET",
    params: {
      programId: "number"
    },
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json"
    },
    response: {
      success: "boolean",
      data: {
        programId: "number",
        programName: "string",
        subscriptionTiers: "SubscriptionTier[]",
        fullPaymentTiers: "FullPaymentTier[]",
        currency: "string",
        createdAt: "string (ISO date)",
        updatedAt: "string (ISO date)"
      }
    }
  },

  // 3. UPDATE PROGRAM DISCOUNT SETTINGS
  UPDATE_PROGRAM_DISCOUNTS: {
    url: "/v2/api/kanglink/custom/trainer/programs/{programId}/discounts",
    method: "PUT",
    params: {
      programId: "number"
    },
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json"
    },
    body: {
      affiliateLink: "string",
      saleDiscount: {
        type: "'fixed' | 'percentage'",
        value: "number",
        applyToAll: "boolean"
      },
      promoCode: {
        code: "string",
        discountType: "'fixed' | 'percentage'",
        discountValue: "number",
        appliesTo: {
          subscription: "boolean",
          fullPayment: "boolean"
        },
        expiryDate: "string (ISO date) | null",
        usageLimit: "number | null"
      },
      subscriptionDiscounts: [
        {
          tierId: "number",
          discountType: "'fixed' | 'percentage'",
          discountValue: "number"
        }
      ],
      fullPriceDiscounts: [
        {
          tierId: "number", 
          discountType: "'fixed' | 'percentage'",
          discountValue: "number"
        }
      ]
    },
    response: {
      success: "boolean",
      message: "string",
      data: {
        programId: "number",
        updatedAt: "string (ISO date)",
        affectedPricingTiers: "number",
        stripeUpdated: "boolean",
        promoCodeCreated: "boolean"
      }
    }
  },

  // 4. VALIDATE PROMO CODE
  VALIDATE_PROMO_CODE: {
    url: "/v2/api/kanglink/custom/trainer/programs/{programId}/promo-codes/validate",
    method: "POST",
    params: {
      programId: "number"
    },
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json"
    },
    body: {
      code: "string"
    },
    response: {
      success: "boolean",
      data: {
        isValid: "boolean",
        isAvailable: "boolean",
        conflicts: "string[]",
        suggestions: "string[]"
      }
    },
    errorResponse: {
      success: "boolean",
      error: {
        code: "string",
        message: "string",
        suggestions: "string[]"
      }
    }
  },

  // 5. CALCULATE DISCOUNT PREVIEW
  CALCULATE_DISCOUNT_PREVIEW: {
    url: "/v2/api/kanglink/custom/trainer/programs/{programId}/discounts/preview",
    method: "POST",
    params: {
      programId: "number"
    },
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json"
    },
    body: {
      saleDiscount: {
        type: "'fixed' | 'percentage'",
        value: "number",
        applyToAll: "boolean"
      },
      subscriptionDiscounts: [
        {
          tierId: "number",
          discountType: "'fixed' | 'percentage'",
          discountValue: "number"
        }
      ],
      fullPriceDiscounts: [
        {
          tierId: "number",
          discountType: "'fixed' | 'percentage'",
          discountValue: "number"
        }
      ]
    },
    response: {
      success: "boolean",
      data: {
        originalRevenue: {
          subscription: "number",
          fullPayment: "number",
          total: "number"
        },
        discountedRevenue: {
          subscription: "number",
          fullPayment: "number", 
          total: "number"
        },
        revenueImpact: {
          amount: "number",
          percentage: "number"
        },
        pricingPreview: {
          subscriptionTiers: [
            {
              id: "number",
              originalPrice: "number",
              discountedPrice: "number",
              savings: "number",
              savingsPercentage: "number"
            }
          ],
          fullPaymentTiers: [
            {
              id: "number",
              originalPrice: "number", 
              discountedPrice: "number",
              savings: "number",
              savingsPercentage: "number"
            }
          ]
        }
      }
    }
  },

  // 6. DELETE/DEACTIVATE PROMO CODE
  DELETE_PROMO_CODE: {
    url: "/v2/api/kanglink/custom/trainer/programs/{programId}/promo-codes/{codeId}",
    method: "DELETE",
    params: {
      programId: "number",
      codeId: "number"
    },
    headers: {
      Authorization: "Bearer {token}",
      "Content-Type": "application/json"
    },
    response: {
      success: "boolean",
      message: "string",
      data: {
        codeId: "number",
        deactivatedAt: "string (ISO date)"
      }
    }
  }

};
